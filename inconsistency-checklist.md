# App与Backend不一致问题检查清单

## 🔴 高优先级问题 (立即修复)

### 1. API端点路径不匹配

#### ❌ 课程相关端点
- [ ] **App**: `/lessons` vs **Backend**: `/api/v1/lessons`
- [ ] **App**: `/lessons/{id}` vs **Backend**: `/api/v1/lessons/:id`
- [ ] **App**: `/lessons/progress` vs **Backend**: 缺少此端点
- [ ] **App**: `/lessons/favorites` vs **Backend**: 缺少此端点

#### ❌ 词汇相关端点
- [ ] **App**: `/vocabulary/words` vs **Backend**: `/api/v1/words`
- [ ] **App**: `/vocabulary/words/{id}` vs **Backend**: `/api/v1/words/:id`
- [ ] **App**: `/vocabulary/categories` vs **Backend**: 缺少此端点

#### ❌ 练习相关端点
- [ ] **App**: `/exercises/grammar` vs **Backend**: `/api/v1/grammar/exercises`
- [ ] **App**: `/exercises/speaking` vs **Backend**: `/api/v1/speaking/exercises`
- [ ] **App**: `/exercises/listening` vs **Backend**: `/api/v1/listening/exercises`
- [ ] **App**: `/exercises/word` vs **Backend**: `/api/v1/word/exercises`

#### ❌ 用户相关端点
- [ ] **App**: `/user/profile` vs **Backend**: `/api/v1/user/me`
- [ ] **App**: `/user/settings` vs **Backend**: `/api/v1/user/settings`
- [ ] **App**: `/user/stats` vs **Backend**: `/api/v1/user/stats`

### 2. 认证机制不同步

#### ❌ JWT Token处理
- [ ] **App**: 缺少动态JWT token添加到请求头
- [ ] **App**: 硬编码的headers，没有Authorization处理
- [ ] **Backend**: 期望`Authorization: Bearer <token>`格式

#### ❌ 认证中间件应用
- [ ] **Backend**: 某些需要认证的端点可能缺少中间件保护
- [ ] **App**: 没有处理401认证失败的情况

### 3. API响应格式不统一

#### ❌ 成功响应结构
- [ ] **App期望**: `{success, message, data, error}`
- [ ] **Backend返回**: `{success, message, data, error: {type, code, message, details}, traceId}`

#### ❌ 错误响应结构
- [ ] **App**: 简单的error字符串
- [ ] **Backend**: 复杂的ErrorInfo对象

## 🟡 中优先级问题 (短期修复)

### 4. 数据模型字段不匹配

#### ❌ User模型
```
App端字段              Backend端字段           状态
avatar                avatarUrl              ❌ 字段名不同
currentStreak         stats.currentStreak    ❌ 结构不同
vocabularyCount       stats.vocabularyCount  ❌ 结构不同
points               stats.totalPoints      ❌ 字段名不同
token                不存在                  ❌ Backend缺少
不存在                isActive              ❌ App缺少
```

#### ❌ Exercise模型
```
App端字段              Backend端字段           状态
type (中文枚举值)      type (英文枚举值)       ❌ 枚举值不同
targetPhrase          不存在                  ❌ Backend缺少
audioURL              audioURL               ✅ 相同
instruction           instruction            ✅ 相同
```

#### ❌ Achievement模型
```
App端字段              Backend端字段           状态
titleKey              title                  ❌ 字段名不同
descriptionKey        description            ❌ 字段名不同
type (枚举)           type (枚举)            ❌ 枚举值可能不同
```

#### ❌ PracticeSession模型
```
App端字段              Backend端字段           状态
userID                userID                 ✅ 相同
type (枚举)           type (字符串)          ❌ 类型不同
duration (TimeInterval) duration (int秒)     ❌ 类型不同
isSynced              不存在                  ❌ Backend缺少
```

### 5. 枚举值不一致

#### ❌ ExerciseType枚举
```
App端值                Backend端值
"选择题"               "multiple_choice"
"填空题"               "fill_in_blank"
"翻译"                 "translation"
"听力"                 "listening"
"口语"                 "speaking"
"写作"                 "writing"
```

#### ❌ AchievementType枚举
```
App端值                Backend端值
.streak               "连续学习"
.vocabulary           "词汇量"
.listening            "听力练习"
.speaking             "口语练习"
.lessons              "课程完成"
.points               "积分"
.challenges           "挑战"
.social               "社交"
```

### 6. 错误处理方式不一致

#### ❌ App端错误类型
```swift
enum AppError: Error {
    case invalidResponse
    case noInternetConnection
    case decodingError
    // 简单错误类型
}
```

#### ❌ Backend端错误类型
```go
const (
    ValidationError ErrorType = "VALIDATION_ERROR"
    AuthError      ErrorType = "AUTH_ERROR"
    NotFoundError  ErrorType = "NOT_FOUND_ERROR"
    InternalError  ErrorType = "INTERNAL_ERROR"
    // 详细错误分类
)
```

## 🟢 低优先级问题 (长期优化)

### 7. API版本控制

#### ❌ 版本管理
- [ ] **App**: 没有API版本概念
- [ ] **Backend**: 使用`/api/v1/`前缀
- [ ] 缺少向后兼容性策略

### 8. 数据验证一致性

#### ❌ 字段长度限制
- [ ] **App**: 没有客户端验证
- [ ] **Backend**: 有GORM标签限制
- [ ] 前后端验证规则不同步

#### ❌ 必填字段验证
- [ ] **App**: 依赖UI层验证
- [ ] **Backend**: 使用validation标签
- [ ] 验证错误消息格式不同

### 9. 日期时间格式

#### ❌ 时间格式处理
- [ ] **App**: 使用Date类型和ISO8601
- [ ] **Backend**: 使用time.Time和可能不同的格式
- [ ] 时区处理可能不一致

## 修复进度跟踪

### 已完成 ✅
- [ ] 无

### 进行中 🔄
- [ ] 无

### 待开始 ⏳
- [x] API端点路径统一
- [x] 认证机制同步
- [x] 响应格式标准化
- [x] 数据模型字段对齐
- [x] 枚举值统一
- [x] 错误处理标准化

## 验证清单

### 功能验证
- [ ] 用户登录/注册流程
- [ ] 课程列表获取
- [ ] 练习数据加载
- [ ] 成就系统
- [ ] 用户统计信息
- [ ] 错误处理流程

### 技术验证
- [ ] API调用成功率 > 95%
- [ ] 数据序列化/反序列化无错误
- [ ] 认证token正确传递
- [ ] 错误响应正确解析
- [ ] 所有端点路径匹配

## 注意事项

1. **向后兼容性**: 修改时要考虑现有数据的兼容性
2. **渐进式修复**: 优先修复影响核心功能的问题
3. **测试覆盖**: 每个修复都要有对应的测试用例
4. **文档更新**: 修复后要更新API文档
5. **监控告警**: 设置监控确保修复效果

## 相关文件

### App端关键文件
- `app/LanguageLearningApp/API/APIEndpoint.swift`
- `app/LanguageLearningApp/API/NetworkService.swift`
- `app/LanguageLearningApp/API/APIResponseWrapper.swift`
- `app/LanguageLearningApp/Features/User/Models/User.swift`
- `app/LanguageLearningApp/Models/Exercise.swift`

### Backend端关键文件
- `backend/controllers/v1/router.go`
- `backend/models/user.go`
- `backend/models/exercise.go`
- `backend/utils/response/response.go`
- `backend/middleware/jwt.go`
