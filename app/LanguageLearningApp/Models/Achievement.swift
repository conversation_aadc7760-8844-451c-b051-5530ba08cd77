import Foundation

public struct Achievement: Identifiable, Codable, Sendable {
    public let id: UUID
    public let type: AchievementType
    public let title: String
    public let description: String
    public let icon: String
    public let color: String
    public let requirement: Int
    public let reward: Int

    // 客户端状态字段（不从服务器同步）
    public var progress: Int = 0
    public var isUnlocked: Bool = false
    public var rewardClaimed: Bool = false
    public var unlockedDate: Date?

    // 向后兼容的属性
    public var titleKey: String {
        return title
    }

    public var descriptionKey: String {
        return description
    }

    public enum AchievementType: String, CaseIterable, Codable, Sendable {
        case streak = "连续学习"
        case vocabulary = "词汇量"
        case listening = "听力练习"
        case speaking = "口语练习"
        case lessons = "课程完成"
        case points = "积分"
        case challenges = "挑战"
        case social = "社交"

        public var localizationKey: String {
            switch self {
            case .streak: return "achievement_streak"
            case .vocabulary: return "achievement_vocabulary"
            case .listening: return "achievement_listening"
            case .speaking: return "achievement_speaking"
            case .lessons: return "achievement_lessons"
            case .points: return "achievement_points"
            case .challenges: return "achievement_challenges"
            case .social: return "achievement_social"
            }
        }
    }

    public init(
        id: UUID = UUID(),
        type: AchievementType,
        title: String,
        description: String,
        icon: String,
        color: String,
        requirement: Int,
        reward: Int,
        progress: Int = 0,
        isUnlocked: Bool = false,
        rewardClaimed: Bool = false,
        unlockedDate: Date? = nil
    ) {
        self.id = id
        self.type = type
        self.title = title
        self.description = description
        self.icon = icon
        self.color = color
        self.requirement = requirement
        self.reward = reward
        self.progress = progress
        self.isUnlocked = isUnlocked
        self.rewardClaimed = rewardClaimed
        self.unlockedDate = unlockedDate
    }

    // 向后兼容的初始化方法
    public init(
        id: UUID = UUID(),
        type: AchievementType,
        titleKey: String,
        descriptionKey: String,
        icon: String,
        color: String,
        requirement: Int,
        reward: Int,
        progress: Int = 0,
        isUnlocked: Bool = false,
        rewardClaimed: Bool = false,
        unlockedDate: Date? = nil
    ) {
        self.id = id
        self.type = type
        self.title = titleKey
        self.description = descriptionKey
        self.icon = icon
        self.color = color
        self.requirement = requirement
        self.reward = reward
    }

    public static var sampleAchievements: [Achievement] = [
        Achievement(
            type: .streak,
            title: "连续学习3天",
            description: "连续学习3天，养成良好的学习习惯",
            icon: "flame.fill",
            color: "FF9500",
            requirement: 3,
            reward: 100
        ),
        Achievement(
            type: .vocabulary,
            title: "词汇大师",
            description: "掌握100个新词汇",
            icon: "textformat.abc",
            color: "007AFF",
            requirement: 100,
            reward: 200
        ),
        Achievement(
            type: .listening,
            title: "听力专家",
            description: "完成50次听力练习",
            icon: "ear.fill",
            color: "34C759",
            requirement: 50,
            reward: 150
        ),
        Achievement(
            type: .speaking,
            title: "口语达人",
            description: "完成30次口语练习",
            icon: "mic.fill",
            color: "FF3B30",
            requirement: 30,
            reward: 150
        ),
        Achievement(
            type: .lessons,
            title: "课程完成者",
            description: "完成10个课程",
            icon: "book.fill",
            color: "AF52DE",
            requirement: 10,
            reward: 300
        ),
        Achievement(
            type: .points,
            title: "积分收集者",
            description: "获得1000积分",
            icon: "star.fill",
            color: "FFCC00",
            requirement: 1000,
            reward: 500
        ),
        Achievement(
            type: .challenges,
            title: "挑战者",
            description: "完成5个挑战",
            icon: "trophy.fill",
            color: "FF9500",
            requirement: 5,
            reward: 250
        ),
        Achievement(
            type: .social,
            title: "社交达人",
            description: "帮助10个用户",
            icon: "person.2.fill",
            color: "FF2D55",
            requirement: 10,
            reward: 200
        )
    ]
}