import Foundation

public struct Achievement: Identifiable, Codable, Sendable {
    public let id: UUID
    public let type: AchievementType
    public let titleKey: String
    public let descriptionKey: String
    public let icon: String
    public let color: String
    public let requirement: Int
    public let reward: Int
    public var progress: Int
    public var isUnlocked: Bool
    public var rewardClaimed: Bool
    public var unlockedDate: Date?

    public enum AchievementType: String, CaseIterable, Codable, Sendable {
        case streak = "连续学习"
        case vocabulary = "词汇量"
        case listening = "听力练习"
        case speaking = "口语练习"
        case lessons = "课程完成"
        case points = "积分"
        case challenges = "挑战"
        case social = "社交"

        public var localizationKey: String {
            switch self {
            case .streak: return "achievement_streak"
            case .vocabulary: return "achievement_vocabulary"
            case .listening: return "achievement_listening"
            case .speaking: return "achievement_speaking"
            case .lessons: return "achievement_lessons"
            case .points: return "achievement_points"
            case .challenges: return "achievement_challenges"
            case .social: return "achievement_social"
            }
        }
    }

    public static var sampleAchievements: [Achievement] = [
        Achievement(
            id: UUID(),
            type: .streak,
            titleKey: "ach_streak_3_days_title",
            descriptionKey: "ach_streak_3_days_desc",
            icon: "flame.fill",
            color: "FF9500",
            requirement: 3,
            reward: 100,
            progress: 0,
            isUnlocked: false,
            rewardClaimed: false
        ),
        Achievement(
            id: UUID(),
            type: .vocabulary,
            titleKey: "ach_vocab_master_title",
            descriptionKey: "ach_vocab_master_desc",
            icon: "textformat.abc",
            color: "007AFF",
            requirement: 100,
            reward: 200,
            progress: 0,
            isUnlocked: false,
            rewardClaimed: false
        ),
        Achievement(
            id: UUID(),
            type: .listening,
            titleKey: "ach_listening_pro_title",
            descriptionKey: "ach_listening_pro_desc",
            icon: "ear.fill",
            color: "34C759",
            requirement: 50,
            reward: 150,
            progress: 0,
            isUnlocked: false,
            rewardClaimed: false
        ),
        Achievement(
            id: UUID(),
            type: .speaking,
            titleKey: "ach_speaking_expert_title",
            descriptionKey: "ach_speaking_expert_desc",
            icon: "mic.fill",
            color: "FF3B30",
            requirement: 30,
            reward: 150,
            progress: 0,
            isUnlocked: false,
            rewardClaimed: false
        ),
        Achievement(
            id: UUID(),
            type: .lessons,
            titleKey: "ach_lesson_completer_title",
            descriptionKey: "ach_lesson_completer_desc",
            icon: "book.fill",
            color: "AF52DE",
            requirement: 10,
            reward: 300,
            progress: 0,
            isUnlocked: false,
            rewardClaimed: false
        ),
        Achievement(
            id: UUID(),
            type: .points,
            titleKey: "ach_points_collector_title",
            descriptionKey: "ach_points_collector_desc",
            icon: "star.fill",
            color: "FFCC00",
            requirement: 1000,
            reward: 500,
            progress: 0,
            isUnlocked: false,
            rewardClaimed: false
        ),
        Achievement(
            id: UUID(),
            type: .challenges,
            titleKey: "ach_challenger_title",
            descriptionKey: "ach_challenger_desc",
            icon: "trophy.fill",
            color: "FF9500",
            requirement: 5,
            reward: 250,
            progress: 0,
            isUnlocked: false,
            rewardClaimed: false
        ),
        Achievement(
            id: UUID(),
            type: .social,
            titleKey: "ach_social_butterfly_title",
            descriptionKey: "ach_social_butterfly_desc",
            icon: "person.2.fill",
            color: "FF2D55",
            requirement: 10,
            reward: 200,
            progress: 0,
            isUnlocked: false,
            rewardClaimed: false
        )
    ]
}