import Foundation

struct GrammarExercise: Identifiable, Codable, Equatable, Sendable {
    let id: UUID
    let title: String
    let instruction: String
    let question: String
    let options: [String]
    let correctAnswer: String
    let explanation: String
    let difficulty: CommonDifficulty
    let category: String
    let exampleSentence: String
    let context: String?

    init(id: UUID = UUID(), title: String, instruction: String, question: String, options: [String], correctAnswer: String, explanation: String, difficulty: CommonDifficulty, category: String, exampleSentence: String, context: String? = nil) {
        self.id = id
        self.title = title
        self.instruction = instruction
        self.question = question
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.difficulty = difficulty
        self.category = category
        self.exampleSentence = exampleSentence
        self.context = context
    }

    static func == (lhs: GrammarExercise, rhs: GrammarExercise) -> Bool {
        lhs.id == rhs.id
    }

    // 使用 CommonDifficulty 而不是重复定义
    // typealias Difficulty = CommonDifficulty
}

// 示例数据
extension GrammarExercise {
    static let sampleExercises: [GrammarExercise] = [
        GrammarExercise(
            id: UUID(),
            title: "动词时态",
            instruction: "选择正确的动词形式完成句子",
            question: "She ___ to school every day.",
            options: ["go", "goes", "going", "went"],
            correctAnswer: "goes",
            explanation: "当主语是第三人称单数时，动词需要加 -s 或 -es。",
            difficulty: .easy,
            category: "Verb Tenses",
            exampleSentence: "She goes to school every day.",
            context: "在英语中，第三人称单数（he, she, it）的动词需要加上-s或-es。"
        ),
        GrammarExercise(
            id: UUID(),
            title: "条件句",
            instruction: "选择正确的条件句形式",
            question: "If it rains tomorrow, I ___ at home.",
            options: ["stay", "will stay", "stayed", "would stay"],
            correctAnswer: "will stay",
            explanation: "这是第一类条件句，表示可能发生的未来情况。",
            difficulty: .medium,
            category: "Conditionals",
            exampleSentence: "If it rains tomorrow, I will stay at home.",
            context: "第一类条件句用于表达可能发生的未来情况，if从句用现在时，主句用将来时。"
        ),
        GrammarExercise(
            id: UUID(),
            title: "被动语态",
            instruction: "将句子改写为被动语态",
            question: "They built this house in 1990.",
            options: [
                "This house was built in 1990.",
                "This house is built in 1990.",
                "This house has been built in 1990.",
                "This house had been built in 1990."
            ],
            correctAnswer: "This house was built in 1990.",
            explanation: "过去时态的被动语态使用 was/were + 过去分词。",
            difficulty: .hard,
            category: "Passive Voice",
            exampleSentence: "This house was built in 1990.",
            context: "被动语态用于强调动作的接受者而不是执行者。过去时态的被动语态使用was/were + 过去分词。"
        )
    ]
}