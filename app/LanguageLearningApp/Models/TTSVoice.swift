import Foundation

/// TTS声音模型
struct TTSVoice: Identifiable, Hashable {
    /// 声音ID
    let id: String
    
    /// 声音名称
    let name: String
    
    /// 语言代码
    let languageCode: String
    
    /// 获取本地化的语言名称
    var localizedLanguageName: String {
        let locale = Locale(identifier: languageCode)
        return locale.localizedString(forLanguageCode: languageCode) ?? languageCode
    }
    
    /// 哈希值
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    /// 相等性比较
    static func == (lhs: TTSVoice, rhs: TTSVoice) -> Bool {
        return lhs.id == rhs.id
    }
}
