import Foundation

public struct UserAchievement: Identifiable, Codable {
    public let id: UUID
    public let userId: UUID
    public let achievementId: UUID
    public let achievementType: Achievement.AchievementType
    public var progress: Int
    public var isUnlocked: Bool
    public var rewardClaimed: Bool
    public var unlockedDate: Date?
    public let reward: Int
    public var isCompleted: Bool {
        isUnlocked && rewardClaimed
    }
    public var dateCompleted: Date? {
        isCompleted ? unlockedDate : nil
    }

    public init(
        id: UUID = UUID(),
        userId: UUID,
        achievementId: UUID,
        achievementType: Achievement.AchievementType,
        progress: Int = 0,
        isUnlocked: Bool = false,
        rewardClaimed: Bool = false,
        unlockedDate: Date? = nil,
        reward: Int
    ) {
        self.id = id
        self.userId = userId
        self.achievementId = achievementId
        self.achievementType = achievementType
        self.progress = progress
        self.isUnlocked = isUnlocked
        self.rewardClaimed = rewardClaimed
        self.unlockedDate = unlockedDate
        self.reward = reward
    }

    public static func create(from achievement: Achievement, userId: UUID, progress: Int = 0) -> UserAchievement {
        UserAchievement(
            userId: userId,
            achievementId: achievement.id,
            achievementType: achievement.type,
            progress: progress,
            reward: achievement.reward
        )
    }

    public static var sampleUserAchievements: [UserAchievement] = [
        UserAchievement(
            id: UUID(),
            userId: UUID(),
            achievementId: Achievement.sampleAchievements[0].id,
            achievementType: .streak,
            progress: 2,
            isUnlocked: false,
            rewardClaimed: false,
            reward: 100
        ),
        UserAchievement(
            id: UUID(),
            userId: UUID(),
            achievementId: Achievement.sampleAchievements[1].id,
            achievementType: .vocabulary,
            progress: 50,
            isUnlocked: false,
            rewardClaimed: false,
            reward: 200
        ),
        UserAchievement(
            id: UUID(),
            userId: UUID(),
            achievementId: Achievement.sampleAchievements[2].id,
            achievementType: .listening,
            progress: 30,
            isUnlocked: false,
            rewardClaimed: false,
            reward: 150
        ),
        UserAchievement(
            id: UUID(),
            userId: UUID(),
            achievementId: Achievement.sampleAchievements[3].id,
            achievementType: .speaking,
            progress: 20,
            isUnlocked: false,
            rewardClaimed: false,
            reward: 150
        ),
        UserAchievement(
            id: UUID(),
            userId: UUID(),
            achievementId: Achievement.sampleAchievements[4].id,
            achievementType: .lessons,
            progress: 5,
            isUnlocked: false,
            rewardClaimed: false,
            reward: 300
        ),
        UserAchievement(
            id: UUID(),
            userId: UUID(),
            achievementId: Achievement.sampleAchievements[5].id,
            achievementType: .points,
            progress: 500,
            isUnlocked: false,
            rewardClaimed: false,
            reward: 500
        ),
        UserAchievement(
            id: UUID(),
            userId: UUID(),
            achievementId: Achievement.sampleAchievements[6].id,
            achievementType: .challenges,
            progress: 3,
            isUnlocked: false,
            rewardClaimed: false,
            reward: 250
        ),
        UserAchievement(
            id: UUID(),
            userId: UUID(),
            achievementId: Achievement.sampleAchievements[7].id,
            achievementType: .social,
            progress: 5,
            isUnlocked: false,
            rewardClaimed: false,
            reward: 200
        )
    ]
}