import Foundation

struct Exercise: Identifiable, Codable {
    let id: UUID
    let type: ExerciseType
    let title: String?
    let description: String?
    let content: String?
    let question: String
    let instruction: String?
    let options: [String]
    let correctAnswer: String
    let explanation: String?
    let points: Int
    let audioURL: String?
    let imageURL: String?
    let tags: [String]
    let category: String?
    let difficulty: String?
    let authorId: UUID?
    let isPublished: Bool
    let createdAt: Date?
    let updatedAt: Date?

    // 向后兼容的属性
    var targetPhrase: String? {
        return content
    }

    var exampleSentence: String? {
        return explanation
    }

    enum ExerciseType: String, Codable {
        case multipleChoice = "multiple_choice"
        case fillInBlank = "fill_in_blank"
        case matching = "matching"
        case trueFalse = "true_false"
        case openEnded = "open_ended"
        case speaking = "speaking"
        case listening = "listening"
        case writing = "writing"
        case reading = "reading"
        case vocabulary = "vocabulary"
        case grammar = "grammar"

        // 向后兼容的中文显示名称
        var displayName: String {
            switch self {
            case .multipleChoice:
                return "选择题"
            case .fillInBlank:
                return "填空题"
            case .matching:
                return "匹配题"
            case .trueFalse:
                return "判断题"
            case .openEnded:
                return "开放题"
            case .speaking:
                return "口语"
            case .listening:
                return "听力"
            case .writing:
                return "写作"
            case .reading:
                return "阅读"
            case .vocabulary:
                return "词汇"
            case .grammar:
                return "语法"
            }
        }

        // 向后兼容的中文值初始化
        init?(chineseValue: String) {
            switch chineseValue {
            case "选择题":
                self = .multipleChoice
            case "填空题":
                self = .fillInBlank
            case "翻译":
                self = .openEnded
            case "听力":
                self = .listening
            case "口语":
                self = .speaking
            case "写作":
                self = .writing
            default:
                return nil
            }
        }
    }

    enum CodingKeys: String, CodingKey {
        case id, type, title, description, content, question, instruction
        case options, correctAnswer, explanation, points, audioURL, imageURL
        case tags, category, difficulty, authorId, isPublished, createdAt, updatedAt
    }

    init(
        id: UUID = UUID(),
        type: ExerciseType,
        title: String? = nil,
        description: String? = nil,
        content: String? = nil,
        question: String,
        instruction: String? = nil,
        options: [String] = [],
        correctAnswer: String,
        explanation: String? = nil,
        points: Int = 10,
        audioURL: String? = nil,
        imageURL: String? = nil,
        tags: [String] = [],
        category: String? = nil,
        difficulty: String? = nil,
        authorId: UUID? = nil,
        isPublished: Bool = true,
        createdAt: Date? = nil,
        updatedAt: Date? = nil
    ) {
        self.id = id
        self.type = type
        self.title = title
        self.description = description
        self.content = content
        self.question = question
        self.instruction = instruction
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.points = points
        self.audioURL = audioURL
        self.imageURL = imageURL
        self.tags = tags
        self.category = category
        self.difficulty = difficulty
        self.authorId = authorId
        self.isPublished = isPublished
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // 向后兼容的初始化方法
    init(
        id: UUID = UUID(),
        type: ExerciseType,
        question: String,
        instruction: String? = nil,
        options: [String] = [],
        correctAnswer: String,
        explanation: String? = nil,
        exampleSentence: String? = nil,
        audioURL: String? = nil,
        targetPhrase: String? = nil,
        imageURL: String? = nil
    ) {
        self.id = id
        self.type = type
        self.title = nil
        self.description = nil
        self.content = targetPhrase
        self.question = question
        self.instruction = instruction
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation ?? exampleSentence
        self.points = 10
        self.audioURL = audioURL
        self.imageURL = imageURL
        self.tags = []
        self.category = nil
        self.difficulty = nil
        self.authorId = nil
        self.isPublished = true
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}