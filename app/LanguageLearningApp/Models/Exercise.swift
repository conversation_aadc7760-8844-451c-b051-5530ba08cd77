import Foundation

struct Exercise: Identifiable, Codable {
    let id: UUID
    let type: ExerciseType
    let question: String
    let instruction: String?
    let options: [String]
    let correctAnswer: String
    let explanation: String?
    let exampleSentence: String?
    let audioURL: String?
    let targetPhrase: String?
    let imageURL: String?
    
    enum ExerciseType: String, Codable {
        case multipleChoice = "选择题"
        case fillInTheBlank = "填空题"
        case translation = "翻译"
        case listening = "听力"
        case speaking = "口语"
        case writing = "写作"
    }
    
    init(
        id: UUID = UUID(),
        type: ExerciseType,
        question: String,
        instruction: String? = nil,
        options: [String] = [],
        correctAnswer: String,
        explanation: String? = nil,
        exampleSentence: String? = nil,
        audioURL: String? = nil,
        targetPhrase: String? = nil,
        imageURL: String? = nil
    ) {
        self.id = id
        self.type = type
        self.question = question
        self.instruction = instruction
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.exampleSentence = exampleSentence
        self.audioURL = audioURL
        self.targetPhrase = targetPhrase
        self.imageURL = imageURL
    }
} 