import Foundation

/// 模拟数据提供者
class MockDataProvider {
    static let shared = MockDataProvider()
    
    private init() {}
    
    /// 获取模拟数据
    func getMockData(for endpoint: APIEndpoint) -> Data? {
        // 根据不同的 API 端点返回不同的模拟数据
        switch endpoint {
        case .lessons:
            return mockLessons()
        case .achievements:
            return mockAchievements()
        case .practiceHistory:
            return mockPracticeHistory()
        case .userProfile:
            return mockUserProfile()
        default:
            return nil
        }
    }
    
    // MARK: - 模拟数据生成
    
    private func mockLessons() -> Data? {
        let lessons: [[String: Any]] = [
            [
                "id": "mock-1",
                "title": "Mock Lesson 1",
                "description": "This is a mock lesson for testing",
                "category": "vocabulary",
                "level": "beginner",
                "difficulty": "easy",
                "duration": 15,
                "points": 10,
                "tags": ["mock", "test", "vocabulary"]
            ],
            [
                "id": "mock-2",
                "title": "Mock Lesson 2",
                "description": "Another mock lesson for testing",
                "category": "grammar",
                "level": "intermediate",
                "difficulty": "medium",
                "duration": 20,
                "points": 15,
                "tags": ["mock", "test", "grammar"]
            ],
            [
                "id": "mock-3",
                "title": "Mock Lesson 3",
                "description": "Advanced mock lesson for testing",
                "category": "speaking",
                "level": "advanced",
                "difficulty": "hard",
                "duration": 30,
                "points": 25,
                "tags": ["mock", "test", "speaking"]
            ]
        ]
        
        return try? JSONSerialization.data(withJSONObject: lessons)
    }
    
    private func mockAchievements() -> Data? {
        let achievements: [[String: Any]] = [
            [
                "id": UUID().uuidString,
                "title": "Mock Achievement 1",
                "description": "Complete your first mock test",
                "type": "lessons",
                "icon": "star.fill",
                "color": "#4CAF50",
                "requirement": 1,
                "reward": 50,
                "progress": 0,
                "isUnlocked": false
            ],
            [
                "id": UUID().uuidString,
                "title": "Mock Achievement 2",
                "description": "Complete 5 mock tests",
                "type": "lessons",
                "icon": "star.fill",
                "color": "#2196F3",
                "requirement": 5,
                "reward": 100,
                "progress": 0,
                "isUnlocked": false
            ]
        ]
        
        return try? JSONSerialization.data(withJSONObject: achievements)
    }
    
    private func mockPracticeHistory() -> Data? {
        let now = Date()
        let calendar = Calendar.current
        
        let practices: [[String: Any]] = [
            [
                "id": UUID().uuidString,
                "type": "vocabulary",
                "title": "Mock Vocabulary Practice",
                "date": ISO8601DateFormatter().string(from: now),
                "duration": 15,
                "score": 85
            ],
            [
                "id": UUID().uuidString,
                "type": "grammar",
                "title": "Mock Grammar Practice",
                "date": ISO8601DateFormatter().string(from: calendar.date(byAdding: .day, value: -1, to: now)!),
                "duration": 20,
                "score": 75
            ],
            [
                "id": UUID().uuidString,
                "type": "listening",
                "title": "Mock Listening Practice",
                "date": ISO8601DateFormatter().string(from: calendar.date(byAdding: .day, value: -2, to: now)!),
                "duration": 10,
                "score": 90
            ]
        ]
        
        return try? JSONSerialization.data(withJSONObject: practices)
    }
    
    private func mockUserProfile() -> Data? {
        let user: [String: Any] = [
            "id": UUID().uuidString,
            "username": "mockuser",
            "email": "<EMAIL>",
            "points": 500,
            "currentStreak": 7,
            "longestStreak": 14,
            "totalLessonsCompleted": 25,
            "vocabularyCount": 150,
            "listeningExerciseCount": 30,
            "speakingExerciseCount": 20,
            "registrationDate": ISO8601DateFormatter().string(from: Date().addingTimeInterval(-30 * 24 * 60 * 60))
        ]
        
        return try? JSONSerialization.data(withJSONObject: user)
    }
}
