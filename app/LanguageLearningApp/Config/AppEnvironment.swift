import Foundation
import SwiftUI

/// 环境配置
public enum AppEnvironment: String, CaseIterable {
    case development = "Development"
    case staging = "Staging"
    case production = "Production"

    /// 当前环境
    public static var current: AppEnvironment {
        get {
            let storedValue = UserDefaults.standard.string(forKey: "app_environment")
            return AppEnvironment(rawValue: storedValue ?? "") ?? .development
        }
        set {
            UserDefaults.standard.set(newValue.rawValue, forKey: "app_environment")
            // 通知环境变更
            NotificationCenter.default.post(name: .environmentChanged, object: nil)
        }
    }

    /// 基础 API URL
    public var baseURL: URL {
        switch self {
        case .development:
            // Use localhost or a real server IP address instead of the .local domain
            return URL(string: "http://localhost:8080/api/v1")!
        case .staging:
            return URL(string: "https://staging-api.languagelearningapp.com/api/v1")!
        case .production:
            return URL(string: "https://api.languagelearningapp.com/api/v1")!
        }
    }

    /// API 超时时间（秒）
    public var apiTimeout: TimeInterval {
        switch self {
        case .development:
            return 60  // 开发环境给更长的超时时间方便调试
        case .staging:
            return 45
        case .production:
            return 30
        }
    }

    /// 是否启用详细日志
    public var enableVerboseLogging: Bool {
        switch self {
        case .development, .staging:
            return true
        case .production:
            return false
        }
    }

    /// 是否显示开发者工具
    public var showDeveloperTools: Bool {
        switch self {
        case .development, .staging:
            return true
        case .production:
            return false
        }
    }

    /// 是否使用模拟数据
    public var useMockData: Bool {
        switch self {
        case .development:
            return UserDefaults.standard.bool(forKey: "use_mock_data")
        case .staging, .production:
            return false
        }
    }

    /// 环境标识颜色
    public var indicatorColor: String {
        switch self {
        case .development:
            return "#FF5733"  // 红色
        case .staging:
            return "#FFC300"  // 黄色
        case .production:
            return "#4CAF50"  // 绿色
        }
    }

    /// Localization key for the environment name
    public var localizationKey: String {
        switch self {
        case .development:
            return "env_development"
        case .staging:
            return "env_staging"
        case .production:
            return "env_production"
        }
    }
}

// 环境变更通知
extension Notification.Name {
    public static let environmentChanged = Notification.Name("environmentChanged")
} 