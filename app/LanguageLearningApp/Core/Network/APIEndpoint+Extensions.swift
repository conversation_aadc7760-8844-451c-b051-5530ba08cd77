import Foundation

// MARK: - API Endpoint Extensions for New Modules

extension APIEndpoint {

    // MARK: - Lesson Endpoints

    /// 课程相关的 API 端点
    public enum Lessons {
        /// 获取课程列表
        public static let list = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/lessons"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 获取课程详情
        public static func detail(id: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/lessons/\(id.uuidString)"),
                method: "GET",
                headers: defaultHeaders,
                bodyData: nil
            )
        }

        /// 获取课程进度
        public static let progress = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/lessons/progress"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 更新课程进度
        public static func updateProgress(id: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/lessons/\(id.uuidString)/progress"),
                method: "PUT",
                headers: defaultHeaders,
                bodyData: nil
            )
        }

        /// 收藏课程
        public static let favorites = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/lessons/favorites"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 切换收藏状态
        public static func toggleFavorite(id: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/lessons/\(id.uuidString)/favorite"),
                method: "POST",
                headers: defaultHeaders,
                bodyData: nil
            )
        }
    }

    // MARK: - Vocabulary Endpoints

    /// 词汇相关的 API 端点
    public enum Vocabulary {
        /// 获取词汇列表
        public static let words = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/vocabulary/words"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 获取词汇详情
        public static func wordDetail(id: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/vocabulary/words/\(id.uuidString)"),
                method: "GET",
                headers: defaultHeaders,
                bodyData: nil
            )
        }

        /// 获取词汇分类
        public static let categories = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/vocabulary/categories"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 获取词汇学习进度
        public static let progress = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/vocabulary/progress"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 更新词汇学习进度
        public static func updateProgress(wordId: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/vocabulary/words/\(wordId.uuidString)/progress"),
                method: "PUT",
                headers: defaultHeaders,
                bodyData: nil
            )
        }

        /// 获取已学习词汇
        public static let learnedWords = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/vocabulary/learned"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 标记词汇为已学习
        public static func markLearned(wordId: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/vocabulary/words/\(wordId.uuidString)/learned"),
                method: "POST",
                headers: defaultHeaders,
                bodyData: nil
            )
        }

        /// 获取收藏词汇
        public static let favoriteWords = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/vocabulary/favorites"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 切换词汇收藏状态
        public static func toggleFavorite(wordId: UUID, isFavorite: Bool) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/vocabulary/words/\(wordId.uuidString)/favorite"),
                method: isFavorite ? "POST" : "DELETE",
                headers: defaultHeaders,
                bodyData: nil
            )
        }
    }

    // MARK: - Evaluation Endpoints

    /// 评估相关的 API 端点
    public enum Evaluations {
        /// 获取评估列表
        public static let list = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/evaluations"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 开始评估
        public static let start = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/evaluations/start"),
            method: "POST",
            headers: defaultHeaders,
            bodyData: nil
        )

        /// 提交评估答案
        public static func submitAnswer(evaluationId: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/evaluations/\(evaluationId.uuidString)/answer"),
                method: "POST",
                headers: defaultHeaders,
                bodyData: nil
            )
        }

        /// 完成评估
        public static func complete(evaluationId: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/evaluations/\(evaluationId.uuidString)/complete"),
                method: "POST",
                headers: defaultHeaders,
                bodyData: nil
            )
        }

        /// 获取评估结果
        public static func result(evaluationId: UUID) -> APIEndpoint {
            return APIEndpoint.custom(
                url: baseURL.appendingPathComponent("/evaluations/\(evaluationId.uuidString)/result"),
                method: "GET",
                headers: defaultHeaders,
                bodyData: nil
            )
        }

        /// 获取评估历史
        public static let history = APIEndpoint.custom(
            url: baseURL.appendingPathComponent("/evaluations/history"),
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )
    }

    // MARK: - Helper Properties

    /// 默认请求头
    private static var defaultHeaders: [String: String] {
        var headers = [
            "Content-Type": "application/json",
            "Accept": "application/json"
        ]

        // 添加认证令牌（如果存在）
        if let token = UserDefaults.standard.string(forKey: "authToken") {
            headers["Authorization"] = "Bearer \(token)"
        }

        // 添加用户ID（如果存在）
        if let userId = UserDefaults.standard.string(forKey: "currentUserId") {
            headers["User-ID"] = userId
        }

        return headers
    }

    /// 创建带认证的端点
    public static func authenticated(url: URL, method: String, body: Data? = nil) -> APIEndpoint {
        return APIEndpoint.custom(
            url: url,
            method: method,
            headers: defaultHeaders,
            bodyData: body
        )
    }

    /// 创建带查询参数的端点
    public static func withQuery(baseURL: URL, path: String, queryItems: [URLQueryItem]) -> APIEndpoint {
        var components = URLComponents(url: baseURL.appendingPathComponent(path), resolvingAgainstBaseURL: false)!
        components.queryItems = queryItems

        return APIEndpoint.custom(
            url: components.url!,
            method: "GET",
            headers: defaultHeaders,
            bodyData: nil
        )
    }
}

// MARK: - Query Parameter Helpers

extension APIEndpoint {
    /// 词汇查询参数构建器
    public struct VocabularyQuery {
        public static func words(category: String? = nil, difficulty: String? = nil, limit: Int? = nil) -> [URLQueryItem] {
            var items: [URLQueryItem] = []

            if let category = category {
                items.append(URLQueryItem(name: "category", value: category))
            }

            if let difficulty = difficulty {
                items.append(URLQueryItem(name: "difficulty", value: difficulty))
            }

            if let limit = limit {
                items.append(URLQueryItem(name: "limit", value: String(limit)))
            }

            return items
        }
    }

    /// 课程查询参数构建器
    public struct LessonQuery {
        public static func lessons(category: String? = nil, difficulty: String? = nil, completed: Bool? = nil) -> [URLQueryItem] {
            var items: [URLQueryItem] = []

            if let category = category {
                items.append(URLQueryItem(name: "category", value: category))
            }

            if let difficulty = difficulty {
                items.append(URLQueryItem(name: "difficulty", value: difficulty))
            }

            if let completed = completed {
                items.append(URLQueryItem(name: "completed", value: String(completed)))
            }

            return items
        }
    }

    /// 评估查询参数构建器
    public struct EvaluationQuery {
        public static func evaluations(type: String? = nil, status: String? = nil) -> [URLQueryItem] {
            var items: [URLQueryItem] = []

            if let type = type {
                items.append(URLQueryItem(name: "type", value: type))
            }

            if let status = status {
                items.append(URLQueryItem(name: "status", value: status))
            }

            return items
        }
    }
}
