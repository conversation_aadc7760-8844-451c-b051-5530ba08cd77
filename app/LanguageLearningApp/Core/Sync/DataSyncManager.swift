import Foundation
import Combine
import Network

/// 数据同步管理器，处理离线数据的同步
public class DataSyncManager: ObservableObject {
    // MARK: - 单例
    public static let shared = DataSyncManager()
    
    // MARK: - Published Properties
    @Published public private(set) var isSyncing = false
    @Published public private(set) var syncProgress: Double = 0.0
    @Published public private(set) var lastSyncDate: Date?
    @Published public private(set) var pendingSyncCount = 0
    @Published public var error: Error?
    
    // MARK: - Private Properties
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")
    private var isNetworkAvailable = false
    private var cancellables = Set<AnyCancellable>()
    
    // 同步任务队列
    private var syncTasks: [SyncTask] = []
    private let syncQueue = DispatchQueue(label: "DataSync", qos: .background)
    
    // MARK: - Initialization
    private init() {
        setupNetworkMonitoring()
        loadPendingSyncTasks()
    }
    
    // MARK: - Public Methods
    
    /// 开始同步所有待同步数据
    public func syncAll() async {
        guard isNetworkAvailable else {
            print("Network not available, skipping sync")
            return
        }
        
        await MainActor.run {
            isSyncing = true
            syncProgress = 0.0
            error = nil
        }
        
        do {
            // 同步课程数据
            await syncLessonData()
            
            // 同步词汇数据
            await syncVocabularyData()
            
            // 同步评估数据
            await syncEvaluationData()
            
            // 同步练习数据
            await syncPracticeData()
            
            await MainActor.run {
                lastSyncDate = Date()
                UserDefaults.standard.set(lastSyncDate, forKey: "lastSyncDate")
                print("Data sync completed successfully")
            }
            
        } catch {
            await MainActor.run {
                self.error = error
                print("Data sync failed: \(error)")
            }
        }
        
        await MainActor.run {
            isSyncing = false
            syncProgress = 1.0
        }
    }
    
    /// 添加同步任务
    public func addSyncTask(_ task: SyncTask) {
        syncQueue.async {
            self.syncTasks.append(task)
            self.savePendingSyncTasks()
            
            DispatchQueue.main.async {
                self.pendingSyncCount = self.syncTasks.count
            }
        }
        
        // 如果网络可用，立即尝试同步
        if isNetworkAvailable {
            Task {
                await syncPendingTasks()
            }
        }
    }
    
    /// 同步待处理任务
    public func syncPendingTasks() async {
        guard isNetworkAvailable && !isSyncing else { return }
        
        await MainActor.run {
            isSyncing = true
        }
        
        let tasksToSync = syncTasks
        var completedTasks: [SyncTask] = []
        
        for (index, task) in tasksToSync.enumerated() {
            do {
                try await executeSync(task: task)
                completedTasks.append(task)
                
                await MainActor.run {
                    syncProgress = Double(index + 1) / Double(tasksToSync.count)
                }
            } catch {
                print("Failed to sync task \(task.id): \(error)")
                // 继续处理其他任务
            }
        }
        
        // 移除已完成的任务
        syncQueue.async {
            self.syncTasks.removeAll { task in
                completedTasks.contains { $0.id == task.id }
            }
            self.savePendingSyncTasks()
            
            DispatchQueue.main.async {
                self.pendingSyncCount = self.syncTasks.count
                self.isSyncing = false
            }
        }
    }
    
    /// 强制同步（忽略网络状态）
    public func forceSyncAll() async {
        await syncAll()
    }
    
    /// 清除所有待同步任务
    public func clearPendingTasks() {
        syncQueue.async {
            self.syncTasks.removeAll()
            self.savePendingSyncTasks()
            
            DispatchQueue.main.async {
                self.pendingSyncCount = 0
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isNetworkAvailable = path.status == .satisfied
                
                // 网络恢复时自动同步
                if path.status == .satisfied {
                    Task {
                        await self?.syncPendingTasks()
                    }
                }
            }
        }
        networkMonitor.start(queue: networkQueue)
    }
    
    private func loadPendingSyncTasks() {
        guard let data = UserDefaults.standard.data(forKey: "pendingSyncTasks") else {
            return
        }
        
        do {
            syncTasks = try JSONDecoder().decode([SyncTask].self, from: data)
            pendingSyncCount = syncTasks.count
            print("Loaded \(syncTasks.count) pending sync tasks")
        } catch {
            print("Failed to load pending sync tasks: \(error)")
        }
        
        // 加载最后同步时间
        lastSyncDate = UserDefaults.standard.object(forKey: "lastSyncDate") as? Date
    }
    
    private func savePendingSyncTasks() {
        do {
            let data = try JSONEncoder().encode(syncTasks)
            UserDefaults.standard.set(data, forKey: "pendingSyncTasks")
        } catch {
            print("Failed to save pending sync tasks: \(error)")
        }
    }
    
    private func executeSync(task: SyncTask) async throws {
        switch task.type {
        case .lessonProgress:
            try await syncLessonProgress(task: task)
        case .lessonFavorite:
            try await syncLessonFavorite(task: task)
        case .wordProgress:
            try await syncWordProgress(task: task)
        case .wordLearned:
            try await syncWordLearned(task: task)
        case .wordFavorite:
            try await syncWordFavorite(task: task)
        case .practiceSession:
            try await syncPracticeSession(task: task)
        case .evaluationResult:
            try await syncEvaluationResult(task: task)
        }
    }
    
    // MARK: - Specific Sync Methods
    
    private func syncLessonData() async {
        await updateProgress(0.1)
        // 实现课程数据同步逻辑
        print("Syncing lesson data...")
    }
    
    private func syncVocabularyData() async {
        await updateProgress(0.4)
        // 实现词汇数据同步逻辑
        print("Syncing vocabulary data...")
    }
    
    private func syncEvaluationData() async {
        await updateProgress(0.7)
        // 实现评估数据同步逻辑
        print("Syncing evaluation data...")
    }
    
    private func syncPracticeData() async {
        await updateProgress(0.9)
        // 实现练习数据同步逻辑
        print("Syncing practice data...")
    }
    
    private func syncLessonProgress(task: SyncTask) async throws {
        // 实现课程进度同步
        print("Syncing lesson progress: \(task.id)")
    }
    
    private func syncLessonFavorite(task: SyncTask) async throws {
        // 实现课程收藏同步
        print("Syncing lesson favorite: \(task.id)")
    }
    
    private func syncWordProgress(task: SyncTask) async throws {
        // 实现词汇进度同步
        print("Syncing word progress: \(task.id)")
    }
    
    private func syncWordLearned(task: SyncTask) async throws {
        // 实现词汇学习状态同步
        print("Syncing word learned: \(task.id)")
    }
    
    private func syncWordFavorite(task: SyncTask) async throws {
        // 实现词汇收藏同步
        print("Syncing word favorite: \(task.id)")
    }
    
    private func syncPracticeSession(task: SyncTask) async throws {
        // 实现练习会话同步
        print("Syncing practice session: \(task.id)")
    }
    
    private func syncEvaluationResult(task: SyncTask) async throws {
        // 实现评估结果同步
        print("Syncing evaluation result: \(task.id)")
    }
    
    @MainActor
    private func updateProgress(_ progress: Double) {
        syncProgress = progress
    }
}

// MARK: - Sync Task Model

public struct SyncTask: Codable, Identifiable {
    public let id: UUID
    public let type: SyncTaskType
    public let data: Data
    public let createdAt: Date
    public let priority: SyncPriority
    
    public init(id: UUID = UUID(), type: SyncTaskType, data: Data, priority: SyncPriority = .normal) {
        self.id = id
        self.type = type
        self.data = data
        self.createdAt = Date()
        self.priority = priority
    }
}

public enum SyncTaskType: String, Codable, CaseIterable {
    case lessonProgress = "lesson_progress"
    case lessonFavorite = "lesson_favorite"
    case wordProgress = "word_progress"
    case wordLearned = "word_learned"
    case wordFavorite = "word_favorite"
    case practiceSession = "practice_session"
    case evaluationResult = "evaluation_result"
}

public enum SyncPriority: Int, Codable, CaseIterable {
    case low = 0
    case normal = 1
    case high = 2
    case critical = 3
}

// MARK: - Sync Status

public enum SyncStatus {
    case idle
    case syncing
    case completed
    case failed(Error)
    
    public var isActive: Bool {
        switch self {
        case .syncing:
            return true
        default:
            return false
        }
    }
}
