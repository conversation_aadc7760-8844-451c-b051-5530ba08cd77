import SwiftUI

// MARK: - 统一的UI组件库
// 这个文件包含了应用中常用的统一UI组件，确保设计一致性

// MARK: - 统一的加载指示器
struct UnifiedLoadingView: View {
    let message: String
    let size: LoadingSize

    enum LoadingSize {
        case small, medium, large

        var progressSize: CGFloat {
            switch self {
            case .small: return 20
            case .medium: return 32
            case .large: return 48
            }
        }

        var fontSize: Font {
            switch self {
            case .small: return AppTheme.Typography.caption
            case .medium: return AppTheme.Typography.body
            case .large: return AppTheme.Typography.headline
            }
        }
    }

    init(message: String? = nil, size: LoadingSize = .medium) {
        self.message = message ?? LocalizationManager.shared.localizedString(LocalizationKey.loading_default)
        self.size = size
    }

    var body: some View {
        VStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.Colors.primary))
                .scaleEffect(size.progressSize / 20)

            Text(message)
                .font(size.fontSize)
                .foregroundColor(AppTheme.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.Colors.background.opacity(0.8))
    }
}

// MARK: - 统一的空状态视图
struct UnifiedEmptyStateView: View {
    let icon: String
    let title: String
    let message: String
    let actionTitle: String?
    let action: (() -> Void)?

    init(
        icon: String,
        title: String,
        message: String,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.icon = icon
        self.title = title
        self.message = message
        self.actionTitle = actionTitle
        self.action = action
    }

    // 便利初始化方法，使用本地化默认值
    init(
        icon: String = "tray",
        title: String? = nil,
        message: String? = nil,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        let localizationManager = LocalizationManager.shared
        self.icon = icon
        self.title = title ?? localizationManager.localizedString(LocalizationKey.empty_state_default)
        self.message = message ?? localizationManager.localizedString(LocalizationKey.no_data_available)
        self.actionTitle = actionTitle
        self.action = action
    }

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: icon)
                .font(.system(size: 48, weight: .light))
                .foregroundColor(AppTheme.Colors.textSecondary)

            VStack(spacing: 8) {
                Text(title)
                    .font(AppTheme.Typography.title3)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .multilineTextAlignment(.center)

                Text(message)
                    .font(AppTheme.Typography.body)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }

            if let actionTitle = actionTitle, let action = action {
                StyledButton(
                    title: actionTitle,
                    action: action,
                    isPrimary: true
                )
                .frame(maxWidth: 200)
            }
        }
        .padding(.horizontal, 32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - 统一的列表项
struct UnifiedListItem<Content: View>: View {
    let content: Content
    let action: (() -> Void)?
    let showChevron: Bool

    init(
        showChevron: Bool = true,
        action: (() -> Void)? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.action = action
        self.showChevron = showChevron
    }

    var body: some View {
        Button(action: action ?? {}) {
            HStack(spacing: 12) {
                content

                if showChevron && action != nil {
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(Color.clear)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(action == nil)
    }
}

// MARK: - 统一的标签
struct UnifiedTag: View {
    let text: String
    let style: TagStyle

    enum TagStyle {
        case primary, secondary, success, warning, error

        var backgroundColor: Color {
            switch self {
            case .primary: return AppTheme.Colors.primary.opacity(0.1)
            case .secondary: return AppTheme.Colors.textSecondary.opacity(0.1)
            case .success: return Color.green.opacity(0.1)
            case .warning: return Color.orange.opacity(0.1)
            case .error: return AppTheme.Colors.error.opacity(0.1)
            }
        }

        var textColor: Color {
            switch self {
            case .primary: return AppTheme.Colors.primary
            case .secondary: return AppTheme.Colors.textSecondary
            case .success: return Color.green
            case .warning: return Color.orange
            case .error: return AppTheme.Colors.error
            }
        }
    }

    init(_ text: String, style: TagStyle = .secondary) {
        self.text = text
        self.style = style
    }

    var body: some View {
        Text(text)
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(style.textColor)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(style.backgroundColor)
            )
    }
}

// MARK: - 统一的进度条
struct UnifiedProgressBar: View {
    let progress: Double
    let height: CGFloat
    let showPercentage: Bool

    init(progress: Double, height: CGFloat = 8, showPercentage: Bool = false) {
        self.progress = max(0, min(1, progress))
        self.height = height
        self.showPercentage = showPercentage
    }

    var body: some View {
        VStack(spacing: 4) {
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: height / 2)
                        .fill(AppTheme.Colors.textSecondary.opacity(0.2))
                        .frame(height: height)

                    RoundedRectangle(cornerRadius: height / 2)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    AppTheme.Colors.primary,
                                    AppTheme.Colors.accent3
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * progress, height: height)
                        .animation(.easeInOut(duration: 0.3), value: progress)
                }
            }
            .frame(height: height)

            if showPercentage {
                HStack {
                    Spacer()
                    Text("\(Int(progress * 100))%")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
            }
        }
    }
}

// MARK: - 统一的分割线
struct UnifiedDivider: View {
    let thickness: CGFloat
    let color: Color

    init(thickness: CGFloat = 1, color: Color = AppTheme.Colors.textSecondary.opacity(0.2)) {
        self.thickness = thickness
        self.color = color
    }

    var body: some View {
        Rectangle()
            .fill(color)
            .frame(height: thickness)
    }
}

// MARK: - 统一的头像视图
struct UnifiedAvatarView: View {
    let imageURL: URL?
    let name: String
    let size: CGFloat

    init(imageURL: URL? = nil, name: String, size: CGFloat = 40) {
        self.imageURL = imageURL
        self.name = name
        self.size = size
    }

    var body: some View {
        Group {
            if let imageURL = imageURL {
                AsyncImage(url: imageURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    fallbackView
                }
            } else {
                fallbackView
            }
        }
        .frame(width: size, height: size)
        .clipShape(Circle())
    }

    private var fallbackView: some View {
        ZStack {
            Circle()
                .fill(AppTheme.Colors.primary.opacity(0.2))

            Text(String(name.prefix(1)).uppercased())
                .font(.system(size: size * 0.4, weight: .semibold))
                .foregroundColor(AppTheme.Colors.primary)
        }
    }
}
