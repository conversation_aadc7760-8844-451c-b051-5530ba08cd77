import SwiftUI

// MARK: - Language Course Card
struct LanguageCourseCard: View {
    let title: String
    let language: String
    let progress: Double
    let flagEmoji: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Text(flagEmoji)
                        .font(.system(size: 40))

                    VStack(alignment: .leading, spacing: 4) {
                        Text(language)
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(title)
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(AppTheme.Colors.textTertiary)
                }

                // Progress bar
                VStack(alignment: .leading, spacing: 8) {
                    StyledProgressBar(progress: progress)

                    HStack {
                        Text("\(Int(progress * 100))% 完成")
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Spacer()

                        Text("继续学习")
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.primary)
                    }
                }
            }
            .padding(16)
            .background(AppTheme.Colors.card)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Vocabulary Card
struct VocabularyCard: View {
    let word: String
    let translation: String
    let pronunciation: String
    let partOfSpeech: String
    let isFavorite: Bool
    let onFavoriteToggle: () -> Void
    let onPlayPronunciation: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(alignment: .top) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(word)
                        .font(AppTheme.Typography.title3)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    HStack(spacing: 8) {
                        Text(partOfSpeech)
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textTertiary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppTheme.Colors.background)
                            .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)

                        Text(pronunciation)
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .italic()
                    }
                }

                Spacer()

                Button(action: onFavoriteToggle) {
                    Image(systemName: isFavorite ? "star.fill" : "star")
                        .font(.system(size: 20))
                        .foregroundColor(isFavorite ? AppTheme.Colors.accent2 : AppTheme.Colors.textTertiary)
                }
            }

            Text(translation)
                .font(AppTheme.Typography.body)
                .foregroundColor(AppTheme.Colors.textPrimary)

            Button(action: onPlayPronunciation) {
                HStack {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.system(size: 14))

                    Text("发音")
                        .font(AppTheme.Typography.footnote)
                }
                .foregroundColor(AppTheme.Colors.primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(AppTheme.Colors.background)
                .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(16)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

// MARK: - Styled Achievement Card
struct StyledAchievementCard: View {
    let title: String
    let description: String
    let icon: String
    let isUnlocked: Bool
    let progress: Int
    let requirement: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                ZStack {
                    Circle()
                        .fill(isUnlocked ? AppTheme.Colors.primary : AppTheme.Colors.background)
                        .frame(width: 50, height: 50)
                        .shadow(color: isUnlocked ? AppTheme.Colors.primary.opacity(0.5) : Color.clear, radius: 8)

                    Image(systemName: icon)
                        .font(.system(size: AppTheme.Dimensions.iconSizeMedium - 4, weight: .semibold))
                        .foregroundColor(isUnlocked ? AppTheme.Colors.textPrimary : AppTheme.Colors.textTertiary)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    Text(description)
                        .font(AppTheme.Typography.footnote)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .lineLimit(2)
                }

                Spacer()

                if isUnlocked {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(AppTheme.Colors.accent3)
                }
            }

            if !isUnlocked {
                VStack(alignment: .leading, spacing: 8) {
                    // Progress bar with integrated text
                    StyledProgressBar(
                        progress: Double(progress) / Double(requirement),
                        height: 12,
                        showProgressText: true,
                        currentValue: progress,
                        maxValue: requirement
                    )
                    .padding(.horizontal, 2) // 添加一点水平内边距，确保对齐
                }
            }
        }
        .padding(16)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}
