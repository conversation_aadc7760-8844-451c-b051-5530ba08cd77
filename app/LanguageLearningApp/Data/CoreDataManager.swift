import CoreData
import Foundation

/// Core Data 管理器
public class CoreDataManager {
    /// 单例实例
    public static let shared = CoreDataManager()

    /// 持久化容器
    private let persistentContainer: NSPersistentContainer

    /// 主上下文
    public var viewContext: NSManagedObjectContext {
        persistentContainer.viewContext
    }

    /// 私有初始化方法
    private init() {
        persistentContainer = NSPersistentContainer(name: "LanguageLearningApp")
        persistentContainer.loadPersistentStores { description, error in
            if let error = error {
                fatalError("无法加载 Core Data 存储: \(error)")
            }
        }

        // 配置自动合并策略
        viewContext.automaticallyMergesChangesFromParent = true
        viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
    }

    /// 创建新的后台上下文
    /// - Returns: 后台上下文
    public func newBackgroundContext() -> NSManagedObjectContext {
        let context = persistentContainer.newBackgroundContext()
        context.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        return context
    }

    /// 保存上下文
    /// - Parameter context: 要保存的上下文
    public func saveContext(_ context: NSManagedObjectContext) {
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("保存上下文失败: \(error)")
            }
        }
    }

    /// 在后台上下文中执行操作
    /// - Parameter block: 要执行的操作
    public func performBackgroundTask(_ block: @escaping (NSManagedObjectContext) -> Void) {
        let context = newBackgroundContext()
        context.perform {
            block(context)
            self.saveContext(context)
        }
    }

    /// 清除所有数据
    public func clearAllData() throws {
        let context = persistentContainer.viewContext

        // 获取所有实体名称
        let entityNames = persistentContainer.managedObjectModel.entities.compactMap { $0.name }

        for entityName in entityNames {
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
            let deleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)

            try context.execute(deleteRequest)
        }

        try context.save()
    }

    // MARK: - 离线功能支持

    /// 获取缓存大小
    public func getCacheSize() -> Int64 {
        guard let storeURL = persistentContainer.persistentStoreDescriptions.first?.url else {
            return 0
        }

        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: storeURL.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            print("Failed to get cache size: \(error)")
            return 0
        }
    }

    /// 获取指定分类的缓存大小
    public func getCacheSize(for category: String) -> Int64 {
        let context = persistentContainer.viewContext
        let entityName = entityNameForCategory(category)

        let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)

        do {
            let count = try context.count(for: fetchRequest)
            // 估算每个对象的平均大小
            let estimatedSizePerObject: Int64 = 1024 // 1KB per object
            return Int64(count) * estimatedSizePerObject
        } catch {
            print("Failed to get cache size for category \(category): \(error)")
            return 0
        }
    }

    /// 获取缓存项目数量
    public func getCacheItemCount() -> Int {
        let context = persistentContainer.viewContext
        let entityNames = persistentContainer.managedObjectModel.entities.compactMap { $0.name }

        var totalCount = 0
        for entityName in entityNames {
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
            do {
                let count = try context.count(for: fetchRequest)
                totalCount += count
            } catch {
                print("Failed to count items for entity \(entityName): \(error)")
            }
        }

        return totalCount
    }

    /// 获取待同步数据数量
    public func getPendingSyncCount() -> Int {
        // 这里需要根据实际的同步状态字段来实现
        // 假设有一个 syncStatus 字段标记同步状态
        return 0 // 暂时返回0，需要根据实际数据模型实现
    }

    /// 获取待同步数据
    public func getPendingSyncData() async throws -> [Any] {
        // 这里需要根据实际的数据模型来实现
        // 返回所有需要同步的数据
        return [] // 暂时返回空数组，需要根据实际数据模型实现
    }

    /// 清除缓存
    public func clearCache() async throws {
        let context = persistentContainer.viewContext

        // 清除过期的缓存数据
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()

        // 这里需要根据实际的数据模型来实现清除逻辑
        // 例如删除超过7天的缓存数据

        try context.save()
    }

    /// 根据分类获取实体名称
    private func entityNameForCategory(_ category: String) -> String {
        switch category {
        case "lessons": return "LessonEntity"
        case "vocabulary": return "VocabularyEntity"
        case "evaluations": return "EvaluationEntity"
        case "achievements": return "AchievementEntity"
        case "user_data": return "UserEntity"
        default: return "GenericEntity"
        }
    }
}