import Foundation

// MARK: - 通用难度级别
/// 通用难度级别枚举，用于所有练习类型
public enum CommonDifficulty: String, Codable, CaseIterable, Sendable {
    case easy = "easy"
    case medium = "medium"
    case hard = "hard"

    /// 显示名称
    public var displayName: String {
        switch self {
        case .easy:
            return "简单"
        case .medium:
            return "中等"
        case .hard:
            return "困难"
        }
    }

    /// 英文显示名称
    public var englishDisplayName: String {
        rawValue.capitalized
    }

    /// 难度分数（用于排序和比较）
    public var score: Int {
        switch self {
        case .easy:
            return 1
        case .medium:
            return 2
        case .hard:
            return 3
        }
    }
}

// MARK: - 通用练习类型
/// 通用练习类型枚举，用于统一不同模块的练习类型
public enum CommonExerciseType: String, Codable, CaseIterable, Sendable {
    case vocabulary = "vocabulary"
    case grammar = "grammar"
    case listening = "listening"
    case speaking = "speaking"
    case reading = "reading"
    case writing = "writing"
    case pronunciation = "pronunciation"
    case comprehensive = "comprehensive"
    case multipleChoice = "multiple-choice"
    case fillInTheBlank = "fill-in-blank"
    case translation = "translation"
    case matching = "matching"
    case ordering = "ordering"

    /// 显示名称
    public var displayName: String {
        switch self {
        case .vocabulary:
            return "词汇"
        case .grammar:
            return "语法"
        case .listening:
            return "听力"
        case .speaking:
            return "口语"
        case .reading:
            return "阅读"
        case .writing:
            return "写作"
        case .pronunciation:
            return "发音"
        case .comprehensive:
            return "综合"
        case .multipleChoice:
            return "选择题"
        case .fillInTheBlank:
            return "填空题"
        case .translation:
            return "翻译"
        case .matching:
            return "配对题"
        case .ordering:
            return "排序题"
        }
    }

    /// 图标名称
    public var iconName: String {
        switch self {
        case .vocabulary:
            return "textformat.abc"
        case .grammar:
            return "text.book.closed"
        case .listening:
            return "ear"
        case .speaking:
            return "mic"
        case .reading:
            return "book"
        case .writing:
            return "pencil"
        case .pronunciation:
            return "waveform"
        case .comprehensive:
            return "square.stack.3d.up"
        case .multipleChoice:
            return "list.bullet.circle"
        case .fillInTheBlank:
            return "text.cursor"
        case .translation:
            return "globe"
        case .matching:
            return "link"
        case .ordering:
            return "arrow.up.arrow.down"
        }
    }
}

// MARK: - 通用状态枚举
/// 通用加载状态
public enum LoadingState<T>: Equatable, Sendable where T: Equatable, T: Sendable {
    case idle
    case loading
    case loaded(T)
    case error(String)

    public static func == (lhs: LoadingState<T>, rhs: LoadingState<T>) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle):
            return true
        case (.loading, .loading):
            return true
        case (.loaded(let lhsData), .loaded(let rhsData)):
            return lhsData == rhsData
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError == rhsError
        default:
            return false
        }
    }
}

/// 通用操作状态
public enum OperationState: Equatable, Sendable {
    case idle
    case inProgress
    case success
    case failure(String)
}

// MARK: - 向后兼容的类型别名
/// 为了向后兼容，提供类型别名
/// 注意：Difficulty 已在 LessonTypes.swift 中定义为 LessonDifficulty
/// 这里不重复定义以避免冲突
public typealias ExerciseType = CommonExerciseType
