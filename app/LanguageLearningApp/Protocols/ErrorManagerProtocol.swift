import Foundation
import Combine

/// 错误管理器协议，定义错误处理相关功能
protocol ErrorManagerProtocol: ObservableObject {
    /// 当前错误
    var currentError: AppError? { get }
    
    /// 是否显示错误
    var showError: Bool { get set }
    
    /// 错误日志
    var errorLog: [ErrorLogEntry] { get }
    
    /// 显示错误
    /// - Parameter error: 应用错误
    func showError(_ error: AppError)
    
    /// 显示错误消息
    /// - Parameters:
    ///   - message: 错误消息
    ///   - severity: 错误严重程度
    func showError(message: String, severity: ErrorSeverity)
    
    /// 清除当前错误
    func clearError()
    
    /// 记录错误
    /// - Parameter error: 应用错误
    func logError(_ error: AppError)
    
    /// 获取错误日志
    /// - Returns: 错误日志条目数组
    func getErrorLog() -> [ErrorLogEntry]
    
    /// 清除错误日志
    func clearErrorLog()
    
    /// 导出错误日志
    /// - Returns: 错误日志数据
    func exportErrorLog() -> Data?
}
