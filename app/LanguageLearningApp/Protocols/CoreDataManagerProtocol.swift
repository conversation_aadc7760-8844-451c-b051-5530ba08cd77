import Foundation
import CoreData

/// Core Data 管理器协议
protocol CoreDataManagerProtocol {
    /// 主上下文
    var viewContext: NSManagedObjectContext { get }

    /// 创建新的后台上下文
    func newBackgroundContext() -> NSManagedObjectContext

    /// 保存上下文
    func saveContext(_ context: NSManagedObjectContext)

    /// 在后台上下文中执行操作
    func performBackgroundTask(_ block: @escaping (NSManagedObjectContext) -> Void)

    /// 清除所有数据
    func clearAllData() throws

    // MARK: - 离线功能支持

    /// 获取缓存大小
    func getCacheSize() -> Int64

    /// 获取指定分类的缓存大小
    func getCacheSize(for category: String) -> Int64

    /// 获取缓存项目数量
    func getCacheItemCount() -> Int

    /// 获取待同步数据数量
    func getPendingSyncCount() -> Int

    /// 获取待同步数据
    func getPendingSyncData() async throws -> [Any]

    /// 清除缓存
    func clearCache() async throws
}

/// 使 CoreDataManager 遵循协议
extension CoreDataManager: CoreDataManagerProtocol {
    // 所有方法已在 CoreDataManager 中实现
}
