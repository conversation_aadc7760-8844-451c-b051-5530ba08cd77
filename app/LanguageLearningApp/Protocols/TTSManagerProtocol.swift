import Foundation
import Combine

/// 文本转语音管理器协议，定义TTS相关功能
@MainActor
protocol TTSManagerProtocol: ObservableObject {
    /// 可用的TTS引擎
    var availableEngines: [TTSEngine] { get }
    
    /// 当前TTS引擎
    var currentEngine: TTSEngine { get }
    
    /// 设置当前TTS引擎
    /// - Parameter engineId: 引擎ID
    func setCurrentEngine(engineId: String)
    
    /// 播放样本
    /// - Parameters:
    ///   - text: 要播放的文本
    ///   - languageCode: 语言代码
    ///   - voiceIdentifier: 声音标识符（可选）
    ///   - completion: 完成回调，返回错误（如果有）
    func playSample(text: String, languageCode: String, voiceIdentifier: String?, completion: @escaping @Sendable (Error?) -> Void)
    
    /// 播放样本（简化版本）
    /// - Parameters:
    ///   - text: 要播放的文本
    ///   - languageCode: 语言代码
    ///   - completion: 完成回调，返回错误（如果有）
    func playSample(text: String, languageCode: String, completion: @escaping @Sendable (Error?) -> Void)
    
    /// 停止播放样本
    func stopSample()
    
    /// 获取可用的声音
    /// - Parameter languageCode: 语言代码
    /// - Returns: 可用声音数组
    func getAvailableVoices(for languageCode: String) async -> [TTSVoice]
    
    /// 获取可用的语言
    /// - Returns: 可用语言代码数组
    func getAvailableLanguages() async -> [String]
    
    /// 初始化TTS引擎
    func initializeEngines()
    
    /// 保存TTS设置
    func saveSettings()
    
    /// 加载TTS设置
    func loadSettings()
}
