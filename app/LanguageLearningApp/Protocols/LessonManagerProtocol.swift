import Foundation
import Combine

/// 课程管理器协议，定义课程管理相关功能
protocol LessonManagerProtocol: ObservableObject {
    /// 所有课程
    var lessons: [Lesson] { get }

    /// 课程分类
    var categories: [LessonCategoryModel] { get }

    /// 课程进度
    var progress: [String: LessonProgress] { get }

    /// 已完成的课程
    var completedLessons: [Lesson] { get }

    /// 收藏的课程ID
    var favoriteLessons: Set<String> { get }

    /// 搜索文本
    var searchText: String { get set }

    /// 过滤后的课程
    var filteredLessons: [Lesson] { get }

    /// 加载课程进度
    func loadProgress()

    /// 获取已完成的课程
    /// - Returns: 已完成的课程数组
    func getCompletedLessons() -> [Lesson]

    /// 获取进行中的课程
    /// - Returns: 进行中的课程数组
    func getInProgressLessons() -> [Lesson]

    /// 获取课程进度
    /// - Parameter lessonId: 课程ID
    /// - Returns: 课程进度（如果存在）
    func getProgress(for lessonId: String) -> LessonProgress?

    /// 完成课程
    /// - Parameter lesson: 课程
    func completeLesson(_ lesson: Lesson)

    /// 完成练习
    /// - Parameters:
    ///   - lessonId: 课程ID
    ///   - exerciseId: 练习ID
    ///   - isCorrect: 是否正确
    func completeExercise(lessonId: String, exerciseId: String, isCorrect: Bool)

    /// 保存进度
    func saveProgress()

    /// 添加课程
    /// - Parameter lesson: 课程
    func addLesson(_ lesson: Lesson)

    /// 更新课程
    /// - Parameter lesson: 课程
    func updateLesson(_ lesson: Lesson)

    /// 删除课程
    /// - Parameter lesson: 课程
    func deleteLesson(_ lesson: Lesson)

    /// 过滤课程
    /// - Parameter searchText: 搜索文本
    func filterLessons(searchText: String)

    /// 切换收藏状态
    /// - Parameter lessonId: 课程ID
    func toggleFavorite(lessonId: String)

    /// 检查是否收藏
    /// - Parameter lessonId: 课程ID
    /// - Returns: 是否收藏
    func isFavorite(lessonId: String) -> Bool

    /// 获取推荐课程
    /// - Returns: 推荐课程数组
    func getRecommendedLessons() -> [Lesson]

    /// 从API加载课程
    /// - Parameter completion: 完成回调
    func loadLessonsFromAPI(completion: (() -> Void)?) async

    /// 从API加载收藏课程
    func loadFavoriteLessonsFromAPI() async

    /// 更新课程进度到API
    /// - Parameters:
    ///   - lessonId: 课程ID
    ///   - progress: 进度（0-1）
    ///   - completed: 是否完成
    func updateLessonProgressToAPI(lessonId: String, progress: Double, completed: Bool) async

    /// 更新收藏状态到API
    /// - Parameters:
    ///   - lessonId: 课程ID
    ///   - isFavorite: 是否收藏
    func updateFavoriteStatusToAPI(lessonId: String, isFavorite: Bool) async
}
