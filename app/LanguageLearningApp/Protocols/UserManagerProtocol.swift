import Foundation
import Combine

/// 用户管理器协议，定义用户管理相关功能
public protocol UserManagerProtocol: ObservableObject {
    /// 当前用户
    var currentUser: User? { get }
    var authToken: String? { get }

    /// 是否已登录
    var isLoggedIn: Bool { get }

    /// 登录
    /// - Parameters:
    ///   - username: 用户名
    ///   - password: 密码
    /// - Returns: 异步操作结果
    func login(username: String, password: String) async throws

    /// 注册
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 电子邮件
    ///   - password: 密码
    /// - Returns: 异步操作结果
    func register(username: String, email: String, password: String) async throws

    /// 登出
    func logout()

    /// 更新用户资料
    /// - Parameters:
    ///   - name: 用户名
    ///   - email: 电子邮件
    ///   - avatar: 头像数据
    /// - Returns: 异步操作结果
    func updateProfile(name: String?, email: String?, avatar: Data?) async throws

    /// 更新用户设置
    /// - Parameter settings: 用户设置
    /// - Returns: 异步操作结果
    func updateSettings(settings: UserSettings) async throws

    /// 重置密码
    /// - Parameter email: 电子邮件
    /// - Returns: 异步操作结果
    func resetPassword(email: String) async throws

    /// 更改密码
    /// - Parameters:
    ///   - oldPassword: 旧密码
    ///   - newPassword: 新密码
    /// - Returns: 异步操作结果
    func changePassword(oldPassword: String, newPassword: String) async throws

    /// 删除账户
    /// - Returns: 异步操作结果
    func deleteAccount() async throws

    /// 检查每日连续登录
    func checkDailyStreak()

    /// 更新用户词汇量
    /// - Parameter count: 词汇量
    func updateUserVocabularyCount(_ count: Int)

    /// 更新用户听力练习次数
    /// - Parameter count: 听力练习次数
    func updateUserListeningExerciseCount(_ count: Int)

    /// 更新用户口语练习次数
    /// - Parameter count: 口语练习次数
    func updateUserSpeakingExerciseCount(_ count: Int)

    /// 更新用户积分
    /// - Parameter points: 积分
    func updateUserPoints(_ points: Int) async

    /// 更新用户完成的挑战数量
    /// - Parameter count: 完成的挑战数量
    func updateUserCompletedChallenges(_ count: Int) async

    /// 更新用户帮助的用户数量
    /// - Parameter count: 帮助的用户数量
    func updateUserHelpedUsers(_ count: Int) async

    /// 加载用户资料
    /// - Returns: 异步操作结果
    func loadUserProfileFromAPI() async

    /// 加载用户统计信息
    /// - Returns: 异步操作结果
    func loadUserStats() async

    /// 加载用户设置
    /// - Returns: 异步操作结果
    func loadUserSettings() async
}
