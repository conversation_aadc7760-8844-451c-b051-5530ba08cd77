import Foundation
import Combine

/// 词汇管理器协议，定义词汇管理相关功能
public protocol VocabularyManagerProtocol: ObservableObject {
    /// 所有词汇
    var words: [Word] { get }

    /// 词汇分类
    var categories: [VocabularyCategory] { get }

    /// 词汇进度
    var wordProgress: [UUID: WordProgress] { get }

    /// 已学习的词汇
    var learnedWords: [Word] { get }

    /// 收藏的词汇
    var favoriteWords: [Word] { get }

    /// 搜索文本
    var searchText: String { get set }

    /// 过滤后的词汇
    var filteredWords: [Word] { get }

    /// 当前选择的分类
    var selectedCategory: VocabularyCategory? { get set }

    /// 当前选择的难度
    var selectedDifficulty: String? { get set }

    /// 添加词汇
    /// - Parameter word: 词汇
    func addWord(_ word: Word)

    /// 更新词汇
    /// - Parameter word: 词汇
    func updateWord(_ word: Word)

    /// 删除词汇
    /// - Parameter word: 词汇
    func deleteWord(_ word: Word)

    /// 获取词汇进度
    /// - Parameter wordId: 词汇ID
    /// - Returns: 词汇进度（如果存在）
    func getWordProgress(for wordId: UUID) -> WordProgress?

    /// 更新词汇进度
    /// - Parameter progress: 词汇进度
    func updateWordProgress(_ progress: WordProgress)

    /// 标记词汇为已学习
    /// - Parameter wordId: 词汇ID
    func markWordAsLearned(wordId: UUID)

    /// 切换收藏状态
    /// - Parameter wordId: 词汇ID
    func toggleFavorite(wordId: UUID)

    /// 检查是否收藏
    /// - Parameter wordId: 词汇ID
    /// - Returns: 是否收藏
    func isFavorite(wordId: UUID) -> Bool

    /// 过滤词汇
    /// - Parameter searchText: 搜索文本
    func filterWords(searchText: String)

    /// 按分类过滤
    /// - Parameter category: 分类
    func filterByCategory(_ category: VocabularyCategory?)

    /// 按难度过滤
    /// - Parameter difficulty: 难度
    func filterByDifficulty(_ difficulty: String?) async

    /// 获取推荐词汇
    /// - Returns: 推荐词汇数组
    func getRecommendedWords() -> [Word]

    /// 获取今日词汇
    /// - Returns: 今日词汇数组
    func getTodayWords() -> [Word]

    /// 从API加载词汇
    /// - Parameter completion: 完成回调
    func loadWordsFromAPI(completion: (() -> Void)?) async

    /// 从API加载分类
    /// - Parameter completion: 完成回调
    func loadCategoriesFromAPI(completion: (() -> Void)?) async

    /// 从API加载词汇进度
    /// - Parameter completion: 完成回调
    func loadWordProgressFromAPI(completion: (() -> Void)?) async

    /// 同步词汇进度到API
    /// - Parameter progress: 词汇进度
    func syncWordProgressToAPI(_ progress: WordProgress) async

    /// 同步收藏状态到API
    /// - Parameters:
    ///   - wordId: 词汇ID
    ///   - isFavorite: 是否收藏
    func syncFavoriteStatusToAPI(wordId: UUID, isFavorite: Bool) async
}
