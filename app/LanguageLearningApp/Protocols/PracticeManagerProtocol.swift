import Foundation
import Combine

// 使用 PracticeSession.PracticeType 而不是重复定义
// 这样可以避免类型冲突并保持一致性

/// 练习管理器协议，定义练习管理相关功能
public protocol PracticeManagerProtocol: ObservableObject {
    /// 最近的练习
    var recentPractices: [PracticeSession] { get }

    /// 每日连续学习天数
    var dailyStreak: Int { get }

    /// 总练习时间
    var totalPracticeTime: TimeInterval { get }

    /// 平均分数
    var averageScore: Double { get }

    /// 添加练习
    /// - Parameter practice: 练习会话
    func addPractice(_ practice: PracticeSession)

    /// 更新练习
    /// - Parameter practice: 练习会话
    func updatePractice(_ practice: PracticeSession)

    /// 删除练习
    /// - Parameter practice: 练习会话
    func deletePractice(_ practice: PracticeSession)

    /// 更新统计数据
    func updateStatistics()

    /// 获取推荐练习
    /// - Returns: 推荐练习数组
    func getRecommendedPractice() -> [PracticeSession]

    /// 从API加载练习历史
    /// - Parameter completion: 完成回调
    func loadPracticeHistoryFromAPI(completion: (() -> Void)?) async

    /// 从API加载推荐练习
    /// - Returns: 推荐练习数组
    func loadRecommendedPracticeFromAPI() async -> [PracticeSession]

    /// 保存练习会话到API
    /// - Parameters:
    ///   - type: 练习类型
    ///   - duration: 持续时间（秒）
    ///   - score: 分数
    func savePracticeSessionToAPI(type: PracticeSession.PracticeType, duration: TimeInterval, score: Int) async
}
