import SwiftUI

struct DailyPracticeView: View {
    @StateObject private var viewModel = DailyPracticeViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @EnvironmentObject private var errorManager: ErrorManager
    @State private var showingSettings = false
    @State private var navigateToCard: PracticeCardModel?
    @State private var showingCardDetails = false
    @State private var selectedCardForDetails: PracticeCardModel?

    var body: some View {
        NavigationStack {
            StyledContainer {
                VStack(spacing: 24) {
                    // Header
                    // VStack(spacing: 8) {
                    //     Text(localizationManager.localizedString(LocalizationKey.daily_practice))
                    //         .font(AppTheme.Typography.title1)
                    //         .foregroundColor(AppTheme.Colors.textPrimary)

                    //     Text(localizationManager.localizedString(LocalizationKey.swipe_instruction))
                    //         .font(AppTheme.Typography.subheadline)
                    //         .foregroundColor(AppTheme.Colors.textSecondary)
                    // }
                    // .padding(.top, 16)

                    // Card stack
                    ZStack {
                        // Show empty state if no cards
                        if viewModel.cards.isEmpty {
                            emptyStateView
                        } else if viewModel.currentCardIndex >= viewModel.cards.count {
                            // Show completed state if all cards processed
                            completedStateView
                        } else {
                            // Show card stack
                            cardStackView
                        }

                        // Loading overlay
                        if viewModel.isLoading {
                            UnifiedLoadingView(
                                message: localizationManager.localizedString(LocalizationKey.loading),
                                size: .large
                            )
                        }
                    }
                    .frame(maxHeight: .infinity)

                    // Action buttons
                    // if !viewModel.cards.isEmpty && viewModel.currentCardIndex < viewModel.cards.count {
                    //     HStack(spacing: 40) {
                    //         // Skip button
                    //         Button(action: {
                    //             viewModel.skipCurrentCard()
                    //         }) {
                    //             Image(systemName: "xmark.circle.fill")
                    //                 .font(.system(size: 60))
                    //                 .foregroundColor(.red)
                    //         }

                    //         // Confirm button
                    //         Button(action: {
                    //             viewModel.confirmCurrentCard()
                    //         }) {
                    //             Image(systemName: "checkmark.circle.fill")
                    //                 .font(.system(size: 60))
                    //                 .foregroundColor(.green)
                    //         }
                    //     }
                    //     .padding(.bottom, 24)
                    // }

                    // Stats
                    // HStack(spacing: 24) {
                    //     StatView(
                    //         title: localizationManager.localizedString(LocalizationKey.confirmed),
                    //         value: "\(viewModel.confirmedCards.count)",
                    //         icon: "checkmark.circle",
                    //         color: .green
                    //     )

                    //     StatView(
                    //         title: localizationManager.localizedString(LocalizationKey.skipped),
                    //         value: "\(viewModel.skippedCards.count)",
                    //         icon: "xmark.circle",
                    //         color: .red
                    //     )
                    // }
                    // .padding(.horizontal)
                }
                // .navigationTitle(localizationManager.localizedString(LocalizationKey.daily_practice))
                // .navigationBarTitleDisplayMode(.inline)
                // .toolbar {
                //     ToolbarItem(placement: .navigationBarTrailing) {
                //         Button(action: {
                //             showingSettings = true
                //         }) {
                //             Image(systemName: "gear")
                //                 .foregroundColor(AppTheme.Colors.textPrimary)
                //         }
                //     }
                // }
                .sheet(isPresented: $showingSettings) {
                    SettingsView()
                        .environmentObject(errorManager)
                }
                .sheet(isPresented: $showingCardDetails) {
                    if let card = selectedCardForDetails {
                        CardDetailsView(card: card)
                    }
                }
                .refreshable {
                    // Set refreshing state to true
                    viewModel.isRefreshing = true

                    do {
                        // Try to load cards from API
                        await viewModel.refreshCards()
                    } catch {
                        // Ensure refreshing state is reset even if there's an unexpected error
                        viewModel.isRefreshing = false
                    }

                    // Final safety check to ensure refresh state is always reset
                    if viewModel.isRefreshing {
                        viewModel.isRefreshing = false
                    }
                }
                .onChange(of: viewModel.isRefreshing) { newValue in
                    print("Daily Practice refresh state changed to: \(newValue)")
                }
                .navigationDestination(item: $navigateToCard) { card in
                    // Navigate to the appropriate view based on the card type
                    switch card.content {
                    case .word:
                        WordLearningView()
                    case .listening:
                        ListeningPracticeView()
                    case .speaking:
                        SpeakingPracticeView()
                    case .grammar:
                        // Use a simple view that doesn't require a path parameter
                        Text("Grammar Practice")
                            .font(.title)
                            .padding()
                    case .exercise(let exercise):
                        // Create a sample lesson for the exercise
                        LessonExerciseView(lesson: Lesson(
                            id: "daily-practice-\(exercise.id)",
                            title: exercise.question,
                            description: exercise.instruction ?? "Complete this exercise to improve your skills.",
                            category: .vocabulary,
                            level: .beginner,
                            difficulty: .easy,
                            duration: 10,
                            points: 50,
                            tags: ["practice", "daily"]
                        ))
                    }
                }
            }
        }
    }

    // MARK: - Card Stack View

    private var cardStackView: some View {
        ZStack {
            // Show only the current card and the next card (if available)
            ForEach(viewModel.cards.indices.reversed(), id: \.self) { index in
                if index >= viewModel.currentCardIndex && index <= viewModel.currentCardIndex + 1 {
                    if index == viewModel.currentCardIndex {
                        // Current card
                        SwipeCardView(
                            content: {
                                PracticeCardContainerView(card: viewModel.cards[index])
                            },
                            onSwipeRight: {
                                viewModel.confirmCurrentCard()
                            },
                            onSwipeLeft: {
                                viewModel.skipCurrentCard()
                            }
                        )
                        .transition(.asymmetric(insertion: .opacity, removal: .opacity))
                    } else {
                        // Next card (shown behind current card)
                        PracticeCardContainerView(card: viewModel.cards[index])
                            .frame(maxWidth: .infinity)
                            .scaleEffect(0.9)
                            .offset(y: 10)
                    }
                }
            }
        }
    }

    // MARK: - Empty State View

    private var emptyStateView: some View {
        UnifiedEmptyStateView(
            icon: "tray",
            title: localizationManager.localizedString(LocalizationKey.no_practice_cards),
            message: localizationManager.localizedString(LocalizationKey.pull_to_refresh)
        )
        .padding()
    }

    // MARK: - Completed State View

    private var completedStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.circle")
                .font(.system(size: 60))
                .foregroundColor(AppTheme.Colors.primary)

            Text(localizationManager.localizedString(LocalizationKey.all_done))
                .font(AppTheme.Typography.title3)
                .foregroundColor(AppTheme.Colors.textPrimary)

            Text(localizationManager.localizedString(LocalizationKey.completed_all_cards))
                .font(AppTheme.Typography.body)
                .foregroundColor(AppTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)

            Button(action: {
                Task {
                    await viewModel.refreshCards()
                }
            }) {
                Text(localizationManager.localizedString(LocalizationKey.refresh_cards))
                    .font(AppTheme.Typography.body)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(AppTheme.Colors.primary)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
            }
            .padding(.top, 8)
        }
        .padding()
    }
}
// MARK: - Stat View

struct StatView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: AppTheme.Dimensions.paddingSmall) {
            // Icon with background
            ZStack {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 40, height: 40)

                Image(systemName: icon)
                    .font(.system(size: AppTheme.Dimensions.iconSizeMedium))
                    .foregroundColor(color)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(AppTheme.Typography.caption1)
                    .foregroundColor(AppTheme.Colors.textSecondary)

                Text(value)
                    .font(AppTheme.Typography.title3)
                    .foregroundColor(AppTheme.Colors.textPrimary)
            }

            Spacer()
        }
        .padding(AppTheme.Dimensions.paddingMedium)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

// MARK: - Preview

struct DailyPracticeView_Previews: PreviewProvider {
    static var previews: some View {
        DailyPracticeView()
            .environmentObject(ErrorManager.shared)
    }
}
