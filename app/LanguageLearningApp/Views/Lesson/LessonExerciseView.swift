import SwiftUI
import AVFoundation

struct LessonExerciseView: View {
    let lesson: Lesson
    @Environment(\.dismiss) private var dismiss
    @StateObject private var lessonManager = LessonManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @StateObject private var audioPlayer = AudioPlayer()

    @State private var currentExerciseIndex = 0
    @State private var userAnswer = ""
    @State private var showingFeedback = false
    @State private var isCorrect = false
    @State private var showingCompletion = false
    @State private var isRecording = false
    @State private var recordedAudioURL: URL?

    // 示例练习数据
    private let exercises = [
        Exercise(
            id: UUID(),
            type: .multipleChoice,
            question: "选择正确的单词含义",
            instruction: "请选择 'Apple' 的正确中文翻译",
            options: ["苹果", "香蕉", "橙子", "葡萄"],
            correctAnswer: "苹果",
            explanation: "Apple 在中文中的意思是苹果。",
            exampleSentence: "I eat an apple every day."
        ),
        Exercise(
            id: UUID(),
            type: .fillInTheBlank,
            question: "完成句子",
            instruction: "根据句意填写正确的动词形式",
            options: ["go", "goes", "going", "went"],
            correctAnswer: "go",
            explanation: "当主语是 I 时，动词使用原形。",
            exampleSentence: "I ___ to school every day."
        ),
        Exercise(
            id: UUID(),
            type: .translation,
            question: "翻译练习",
            instruction: "将以下句子翻译成中文",
            options: [],
            correctAnswer: "你好，你好吗？",
            explanation: "这是最基本的问候语翻译。",
            exampleSentence: "Hello, how are you?"
        ),
        Exercise(
            id: UUID(),
            type: .listening,
            question: "听力理解",
            instruction: "听录音并选择正确的描述",
            options: ["正在吃饭", "正在睡觉", "正在工作", "正在学习"],
            correctAnswer: "正在工作",
            explanation: "录音中提到 'I am working now'。",
            audioURL: "listening_sample_1"
        ),
        Exercise(
            id: UUID(),
            type: .speaking,
            question: "口语练习",
            instruction: "跟读以下句子",
            options: [],
            correctAnswer: "Nice to meet you",
            explanation: "这是常用的寒暄用语。",
            audioURL: "speaking_sample_1",
            targetPhrase: "Nice to meet you"
        ),
        Exercise(
            id: UUID(),
            type: .writing,
            question: "写作练习",
            instruction: "根据提示写一段话",
            options: [],
            correctAnswer: "I like learning Chinese because it is interesting.",
            explanation: "这是一个简单的个人观点表达。",
            exampleSentence: "Topic: Why do you learn Chinese?"
        )
    ]

    var body: some View {
        StyledContainer {
            VStack(spacing: 20) {
                // 顶部信息栏
                HStack {
                    // 课程类别图标
                    ZStack {
                        Circle()
                            .fill(categoryColor)
                            .frame(width: 40, height: 40)
                            .shadow(color: categoryColor.opacity(0.3), radius: 4, x: 0, y: 2)

                        Image(systemName: categoryIcon)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                    }

                    VStack(alignment: .leading, spacing: 2) {
                        Text(lesson.title)
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text("Exercise \(currentExerciseIndex + 1) of \(exercises.count)")
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }

                    Spacer()
                }
                .padding(.horizontal)

                // 进度指示器
                VStack(spacing: 4) {
                    StyledProgressBar(progress: Double(currentExerciseIndex) / Double(exercises.count - 1))

                    HStack {
                        Text("\(Int((Double(currentExerciseIndex) / Double(exercises.count - 1)) * 100))% completed")
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Spacer()

                        HStack(spacing: 4) {
                            Image(systemName: "star.fill")
                                .font(.system(size: 12))
                                .foregroundColor(AppTheme.Colors.accent2)

                            Text("+\(exercises[currentExerciseIndex].type == .writing ? 10 : 5) points")
                                .font(AppTheme.Typography.caption1)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }
                    }
                }
                .padding(.horizontal)

                // 练习内容
                ScrollView {
                    VStack(spacing: 24) {
                        // 问题卡片
                        StyledCard {
                            VStack(alignment: .leading, spacing: 16) {
                                // 练习类型标签
                                HStack {
                                    Text(exercises[currentExerciseIndex].type.rawValue.capitalized)
                                        .font(AppTheme.Typography.caption1)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(exerciseTypeColor)
                                        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)

                                    Spacer()
                                }

                                // 问题标题
                                Text(exercises[currentExerciseIndex].question)
                                    .font(AppTheme.Typography.title3)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                // 指导说明
                                if let instruction = exercises[currentExerciseIndex].instruction {
                                    Text(instruction)
                                        .font(AppTheme.Typography.body)
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                }

                                // 示例句子
                                if let example = exercises[currentExerciseIndex].exampleSentence {
                                    Text(example)
                                        .font(AppTheme.Typography.body)
                                        .foregroundColor(AppTheme.Colors.textPrimary)
                                        .padding(12)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .background(AppTheme.Colors.background)
                                        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                                }
                            }
                            .padding(16)
                        }

                        // 练习类型特定的视图
                        VStack(alignment: .leading, spacing: 16) {
                            StyledSectionHeader(title: "Your Answer")

                            switch exercises[currentExerciseIndex].type {
                            case .multipleChoice:
                                EnhancedMultipleChoiceView(
                                    options: exercises[currentExerciseIndex].options,
                                    onSelect: checkAnswer
                                )

                            case .fillInTheBlank:
                                EnhancedFillInBlankView(
                                    options: exercises[currentExerciseIndex].options,
                                    userAnswer: $userAnswer,
                                    onSubmit: { checkAnswer(userAnswer) }
                                )

                            case .translation:
                                EnhancedTranslationView(
                                    userAnswer: $userAnswer,
                                    onSubmit: { checkAnswer(userAnswer) }
                                )

                            case .listening:
                                EnhancedListeningView(
                                    audioURL: exercises[currentExerciseIndex].audioURL ?? "",
                                    options: exercises[currentExerciseIndex].options,
                                    audioPlayer: audioPlayer,
                                    onSelect: checkAnswer
                                )

                            case .speaking:
                                EnhancedSpeakingView(
                                    audioURL: exercises[currentExerciseIndex].audioURL ?? "",
                                    targetPhrase: exercises[currentExerciseIndex].targetPhrase ?? "",
                                    isRecording: $isRecording,
                                    recordedAudioURL: $recordedAudioURL,
                                    audioPlayer: audioPlayer,
                                    onSubmit: { checkAnswer("") }
                                )

                            case .writing:
                                EnhancedWritingView(
                                    userAnswer: $userAnswer,
                                    onSubmit: { checkAnswer(userAnswer) }
                                )
                            }
                        }

                        // 导航按钮
                        if !showingFeedback {
                            HStack(spacing: 16) {
                                if currentExerciseIndex > 0 {
                                    Button(action: {
                                        currentExerciseIndex -= 1
                                        userAnswer = ""
                                        recordedAudioURL = nil
                                    }) {
                                        HStack(spacing: 8) {
                                            Image(systemName: "chevron.left")
                                                .font(.system(size: 14, weight: .semibold))
                                            Text("Previous")
                                                .font(AppTheme.Typography.subheadline)
                                        }
                                        .foregroundColor(AppTheme.Colors.textPrimary)
                                        .padding(.vertical, 12)
                                        .padding(.horizontal, 16)
                                        .background(AppTheme.Colors.card)
                                        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                                        )
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                } else {
                                    Spacer()
                                }

                                Spacer()

                                // Skip button
                                Button(action: {
                                    if currentExerciseIndex < exercises.count - 1 {
                                        currentExerciseIndex += 1
                                        userAnswer = ""
                                        recordedAudioURL = nil
                                    } else {
                                        showingCompletion = true
                                    }
                                }) {
                                    Text("Skip")
                                        .font(AppTheme.Typography.subheadline)
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                        .padding(.vertical, 12)
                                        .padding(.horizontal, 16)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                            .padding(.horizontal)
                        }
                    }
                    .padding(.bottom, 20)
                }

                // 反馈视图
                if showingFeedback {
                    EnhancedFeedbackView(
                        isCorrect: isCorrect,
                        explanation: exercises[currentExerciseIndex].explanation ?? "",
                        onContinue: {
                            showingFeedback = false
                            if currentExerciseIndex < exercises.count - 1 {
                                currentExerciseIndex += 1
                                userAnswer = ""
                                recordedAudioURL = nil
                            } else {
                                showingCompletion = true
                            }
                        }
                    )
                }
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { dismiss() }) {
                    HStack {
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(AppTheme.Colors.textPrimary)
                }
            }
        }
        .alert("课程完成", isPresented: $showingCompletion) {
            Button("确定") {
                lessonManager.completeLesson(lesson)
                dismiss()
            }
        } message: {
            Text("恭喜你完成了所有练习！")
        }
    }

    private func checkAnswer(_ answer: String) {
        isCorrect = answer == exercises[currentExerciseIndex].correctAnswer
        showingFeedback = true
    }

    // 辅助属性
    private var categoryColor: Color {
        switch lesson.category {
        case .vocabulary: return AppTheme.Colors.primary
        case .grammar: return AppTheme.Colors.secondary
        case .listening: return AppTheme.Colors.accent1
        case .speaking: return AppTheme.Colors.accent3
        case .reading: return AppTheme.Colors.accent2
        case .writing: return AppTheme.Colors.writing
        case .uncategorized: return AppTheme.Colors.uncategorized
        }
    }

    private var categoryIcon: String {
        switch lesson.category {
        case .vocabulary: return "textformat.abc"
        case .grammar: return "text.book.closed"
        case .listening: return "ear"
        case .speaking: return "mic"
        case .reading: return "book"
        case .writing: return "pencil"
        case .uncategorized: return "questionmark.circle"
        }
    }

    private var exerciseTypeColor: Color {
        switch exercises[currentExerciseIndex].type {
        case .multipleChoice: return AppTheme.Colors.primary
        case .fillInTheBlank: return AppTheme.Colors.secondary
        case .translation: return AppTheme.Colors.accent1
        case .listening: return AppTheme.Colors.accent3
        case .speaking: return AppTheme.Colors.writing
        case .writing: return AppTheme.Colors.accent2
        }
    }
}

// 增强型子视图组件
struct EnhancedMultipleChoiceView: View {
    let options: [String]
    let onSelect: (String) -> Void
    @State private var selectedOption: String? = nil

    var body: some View {
        VStack(spacing: 12) {
            ForEach(options, id: \.self) { option in
                Button(action: {
                    selectedOption = option
                    onSelect(option)
                }) {
                    HStack {
                        Text(option)
                            .font(AppTheme.Typography.body)
                            .foregroundColor(selectedOption == option ? AppTheme.Colors.primary : AppTheme.Colors.textPrimary)
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        if selectedOption == option {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(AppTheme.Colors.primary)
                        }
                    }
                    .background(selectedOption == option ? AppTheme.Colors.primary.opacity(0.1) : AppTheme.Colors.card)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .stroke(selectedOption == option ? AppTheme.Colors.primary : Color.white.opacity(0.1), lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

struct EnhancedFillInBlankView: View {
    let options: [String]
    @Binding var userAnswer: String
    let onSubmit: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            TextField("输入答案", text: $userAnswer)
                .font(AppTheme.Typography.body)
                .padding()
                .background(AppTheme.Colors.card)
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )

            if !options.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(options, id: \.self) { option in
                            Button(action: { userAnswer = option }) {
                                Text(option)
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 8)
                                    .background(AppTheme.Colors.background)
                                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }

            Button(action: onSubmit) {
                HStack {
                    Text("提交")
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .shadow(color: AppTheme.Colors.primary.opacity(0.3), radius: 5, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

struct EnhancedTranslationView: View {
    @Binding var userAnswer: String
    let onSubmit: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            TextEditor(text: $userAnswer)
                .font(AppTheme.Typography.body)
                .padding(12)
                .frame(height: 120)
                .background(AppTheme.Colors.card)
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )

            Button(action: onSubmit) {
                HStack {
                    Text("提交")
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .shadow(color: AppTheme.Colors.primary.opacity(0.3), radius: 5, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

struct EnhancedListeningView: View {
    let audioURL: String
    let options: [String]
    @ObservedObject var audioPlayer: AudioPlayer
    let onSelect: (String) -> Void
    @State private var isPlaying = false

    var body: some View {
        VStack(spacing: 20) {
            // 音频播放按钮
            Button(action: {
                isPlaying.toggle()
                audioPlayer.playAudio(url: audioURL)
            }) {
                ZStack {
                    Circle()
                        .fill(AppTheme.Colors.accent1)
                        .frame(width: 80, height: 80)
                        .shadow(color: AppTheme.Colors.accent1.opacity(0.3), radius: 10, x: 0, y: 5)

                    Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                        .font(.system(size: 30))
                        .foregroundColor(.white)
                }
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.vertical, 10)

            // 选项
            EnhancedMultipleChoiceView(options: options, onSelect: onSelect)
        }
    }
}

struct EnhancedSpeakingView: View {
    let audioURL: String
    let targetPhrase: String
    @Binding var isRecording: Bool
    @Binding var recordedAudioURL: URL?
    @ObservedObject var audioPlayer: AudioPlayer
    let onSubmit: () -> Void
    @State private var isPlaying = false

    var body: some View {
        VStack(spacing: 20) {
            // 目标短语
            StyledCard {
                VStack(spacing: 12) {
                    Text("Repeat after me")
                        .font(AppTheme.Typography.caption1)
                        .foregroundColor(AppTheme.Colors.textSecondary)

                    Text(targetPhrase)
                        .font(AppTheme.Typography.title3)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .multilineTextAlignment(.center)
                        .padding(.vertical, 8)
                }
                .padding()
            }

            HStack(spacing: 30) {
                // 播放示例按钮
                VStack {
                    Button(action: {
                        isPlaying.toggle()
                        audioPlayer.playAudio(url: audioURL)
                    }) {
                        ZStack {
                            Circle()
                                .fill(AppTheme.Colors.accent1)
                                .frame(width: 60, height: 60)
                                .shadow(color: AppTheme.Colors.accent1.opacity(0.3), radius: 8, x: 0, y: 4)

                            Image(systemName: "play.fill")
                                .font(.system(size: 24))
                                .foregroundColor(.white)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())

                    Text("Listen")
                        .font(AppTheme.Typography.caption1)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .padding(.top, 8)
                }

                // 录音按钮
                VStack {
                    Button(action: {
                        isRecording.toggle()
                        // TODO: 实现录音功能
                    }) {
                        ZStack {
                            Circle()
                                .fill(isRecording ? AppTheme.Colors.error : AppTheme.Colors.accent3)
                                .frame(width: 60, height: 60)
                                .shadow(color: (isRecording ? AppTheme.Colors.error : AppTheme.Colors.accent3).opacity(0.3), radius: 8, x: 0, y: 4)

                            Image(systemName: isRecording ? "stop.fill" : "mic.fill")
                                .font(.system(size: 24))
                                .foregroundColor(.white)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())

                    Text(isRecording ? "Recording..." : "Record")
                        .font(AppTheme.Typography.caption1)
                        .foregroundColor(isRecording ? AppTheme.Colors.error : AppTheme.Colors.textSecondary)
                        .padding(.top, 8)
                }
            }
            .padding(.vertical, 20)

            if recordedAudioURL != nil {
                Button(action: {
                    // TODO: 播放录音
                }) {
                    HStack {
                        Image(systemName: "play.circle")
                            .font(.system(size: 16))
                        Text("Play Recording")
                            .font(AppTheme.Typography.subheadline)
                    }
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .padding(.vertical, 10)
                    .padding(.horizontal, 16)
                    .background(AppTheme.Colors.card)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }

            Button(action: onSubmit) {
                HStack {
                    Text("提交")
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .shadow(color: AppTheme.Colors.primary.opacity(0.3), radius: 5, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.top, 10)
        }
    }
}

struct EnhancedWritingView: View {
    @Binding var userAnswer: String
    let onSubmit: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            TextEditor(text: $userAnswer)
                .font(AppTheme.Typography.body)
                .padding(12)
                .frame(height: 200)
                .background(AppTheme.Colors.card)
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )

            Button(action: onSubmit) {
                HStack {
                    Text("提交")
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .shadow(color: AppTheme.Colors.primary.opacity(0.3), radius: 5, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

struct EnhancedFeedbackView: View {
    let isCorrect: Bool
    let explanation: String
    let onContinue: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            // 结果图标
            ZStack {
                Circle()
                    .fill(isCorrect ? AppTheme.Colors.success.opacity(0.1) : AppTheme.Colors.error.opacity(0.1))
                    .frame(width: 80, height: 80)

                Image(systemName: isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(isCorrect ? AppTheme.Colors.success : AppTheme.Colors.error)
            }

            // 结果文本
            Text(isCorrect ? "回答正确！" : "回答错误")
                .font(AppTheme.Typography.title3)
                .foregroundColor(isCorrect ? AppTheme.Colors.success : AppTheme.Colors.error)

            // 解释
            StyledCard {
                VStack(alignment: .leading, spacing: 12) {
                    HStack(spacing: 12) {
                        Image(systemName: "lightbulb.fill")
                            .font(.system(size: 20))
                            .foregroundColor(AppTheme.Colors.accent2)

                        Text("解释")
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }

                    Text(explanation)
                        .font(AppTheme.Typography.body)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .padding(16)
            }

            // 继续按钮
            Button(action: onContinue) {
                HStack {
                    Text("继续")
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .shadow(color: AppTheme.Colors.primary.opacity(0.3), radius: 5, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.top, 10)
        }
        .padding(20)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        .padding()
    }
}

class AudioPlayer: ObservableObject {
    private var player: AVAudioPlayer?

    func playAudio(url: String) {
        // TODO: 实现音频播放
        print("Playing audio: \(url)")
    }
}

#Preview {
    NavigationView {
        LessonExerciseView(lesson: Lesson(
            id: "preview",
            title: "示例课程",
            description: "这是一个示例课程的详细描述。",
            category: .vocabulary,
            level: .beginner,
            difficulty: .easy,
            duration: 30,
            points: 100,
            tags: ["基础", "词汇", "入门"]
        ))
    }
}