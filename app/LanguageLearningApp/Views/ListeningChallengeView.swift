import SwiftUI

struct ListeningChallengeView: View {
    @StateObject private var viewModel = ListeningViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @EnvironmentObject private var errorManager: ErrorManager
    @State private var showingSettings = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 挑战进度
            VStack(spacing: 10) {
                Text("今日挑战")
                    .font(.title)
                    .bold()
                
                Text("完成 5 个听力练习")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                ProgressView(value: viewModel.progress)
                    .padding()
            }
            .padding()
            
            // 当前练习
            if let exercise = viewModel.currentExercise {
                ScrollView {
                    VStack(spacing: 20) {
                        // 音频控制
                        Button(action: {
                            viewModel.playAudio()
                        }) {
                            Image(systemName: viewModel.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                                .font(.system(size: 60))
                                .foregroundColor(.blue)
                        }
                        .padding()
                        
                        // 问题和选项
                        if viewModel.currentQuestionIndex < exercise.questions.count {
                            let question = exercise.questions[viewModel.currentQuestionIndex]
                            
                            Text(question.question)
                                .font(.headline)
                                .padding()
                            
                            ForEach(0..<question.options.count, id: \.self) { index in
                                Button(action: {
                                    viewModel.selectAnswer(index)
                                }) {
                                    Text(question.options[index])
                                        .frame(maxWidth: .infinity)
                                        .padding()
                                        .background(
                                            viewModel.selectedAnswer == index ?
                                            Color.blue.opacity(0.2) :
                                            Color.gray.opacity(0.1)
                                        )
                                        .cornerRadius(10)
                                }
                                .disabled(viewModel.isAnswerSubmitted)
                            }
                            
                            if viewModel.isAnswerSubmitted {
                                Text(viewModel.isAnswerCorrect ? "回答正确！" : "回答错误")
                                    .foregroundColor(viewModel.isAnswerCorrect ? .green : .red)
                                    .padding(.top)
                            }
                        }
                    }
                    .padding()
                }
            } else {
                Text("今日挑战已完成")
                    .font(.headline)
                    .foregroundColor(.secondary)
            }
        }
        .navigationTitle("听力挑战")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingSettings.toggle() }) {
                    Image(systemName: "gear")
                }
            }
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
                .environmentObject(errorManager)
        }
    }
}

#Preview {
    NavigationView {
        ListeningChallengeView()
            .environmentObject(ErrorManager.shared)
    }
} 