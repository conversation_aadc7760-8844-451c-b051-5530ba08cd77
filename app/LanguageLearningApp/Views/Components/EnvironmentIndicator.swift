import SwiftUI
import Foundation

/// 环境指示器视图
struct EnvironmentIndicator: View {
    @State private var currentEnvironment = AppEnvironment.current
    @State private var showEnvironmentSettings = false

    // 监听环境变更通知
    private let environmentPublisher = NotificationCenter.default.publisher(for: .environmentChanged)

    var body: some View {
        Group {
            // 仅在非生产环境显示
            if currentEnvironment != .production {
                Button(action: {
                    showEnvironmentSettings = true
                }) {
                    HStack(spacing: 4) {
                        Circle()
                            .fill(environmentColor)
                            .frame(width: 8, height: 8)

                        Text(currentEnvironment.rawValue)
                            .font(AppTheme.Typography.caption2)
                            .foregroundColor(environmentColor)
                    }
                    .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                    .padding(.vertical, 4)
                    .background(environmentColor.opacity(0.1))
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                }
                .sheet(isPresented: $showEnvironmentSettings) {
                    EnvironmentSettingsView()
                }
                .onReceive(environmentPublisher) { _ in
                    // 更新当前环境
                    currentEnvironment = AppEnvironment.current
                }
            }
        }
    }

    private var environmentColor: Color {
        switch currentEnvironment {
        case .development:
            return AppTheme.Colors.environmentDevelopment
        case .staging:
            return AppTheme.Colors.environmentStaging
        case .production:
            return AppTheme.Colors.environmentProduction
        }
    }
}
