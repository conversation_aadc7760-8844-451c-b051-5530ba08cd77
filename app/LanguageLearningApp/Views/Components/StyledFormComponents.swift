import SwiftUI

// Import AppTheme

// MARK: - Styled Form Row
struct StyledFormRow<Content: View>: View {
    let content: Content

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        content
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(Color.clear)
    }
}

// MARK: - Styled Form Section
struct StyledFormSection<Content: View>: View {
    let title: String
    let content: Content

    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingSmall + 4) {
            if !title.isEmpty {
                Text(title)
                    .font(AppTheme.Typography.headline.weight(.bold))
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .padding(.leading, AppTheme.Dimensions.paddingMedium)
                    .padding(.top, AppTheme.Dimensions.paddingSmall)
            }

            VStack(spacing: 1) {
                content
            }
            .background(
                // Glass morphism effect
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge - 4)
                    .fill(AppTheme.Colors.card)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge - 4)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.2),
                                        Color.white.opacity(0.05)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1.5
                            )
                    )
            )
            .cornerRadius(AppTheme.Dimensions.cornerRadiusLarge - 4)
            .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
        }
    }
}
