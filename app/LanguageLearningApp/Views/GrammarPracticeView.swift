import SwiftUI

struct GrammarPracticeView: View {
    @Binding var path: NavigationPath
    @StateObject private var viewModel = GrammarViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        StyledContainer {
            if let exercise = viewModel.currentExercise {
                GrammarExerciseContentView(exercise: exercise, viewModel: viewModel, localizationManager: localizationManager)
            } else {
                EmptyStateView(localizationManager: localizationManager)
            }
        }
        .navigationTitle(localizationManager.localizedString(LocalizationKey.grammarExercise))
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { dismiss() }) {
                    HStack {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text(localizationManager.localizedString(LocalizationKey.practice))
                            .font(AppTheme.Typography.subheadline)
                    }
                    .foregroundColor(AppTheme.Colors.textPrimary)
                }
            }
        }
        .animation(.easeInOut, value: viewModel.showExplanation)
    }
}

// MARK: - Subviews

// Empty state view
private struct EmptyStateView: View {
    let localizationManager: LocalizationManager

    var body: some View {
        UnifiedEmptyStateView(
            icon: "book.closed",
            title: localizationManager.localizedString(LocalizationKey.no_exercise),
            message: localizationManager.localizedString(LocalizationKey.check_back_later)
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

// Exercise content view
private struct GrammarExerciseContentView: View {
    let exercise: GrammarExercise
    @ObservedObject var viewModel: GrammarViewModel
    let localizationManager: LocalizationManager

    var body: some View {
        VStack(spacing: 24) {
            // Header and Progress
            VStack(spacing: 16) {
                Text(exercise.title)
                    .font(AppTheme.Typography.title2)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                VStack(spacing: 8) {
                    HStack {
                        Text(String(format: localizationManager.localizedString(LocalizationKey.exercise_progress), Int(viewModel.progress * 100)))
                            .font(AppTheme.Typography.footnote)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Spacer()

                        // Fix for the currentExerciseIndex issue
                        let currentIndex = viewModel.exercises.firstIndex(where: { $0.id == exercise.id }) ?? 0
                        Text("\(currentIndex + 1)/\(viewModel.exercises.count)")
                            .font(AppTheme.Typography.footnote)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }

                    StyledProgressBar(progress: viewModel.progress)
                }
            }

            // Exercise Content
            ScrollView {
                VStack(spacing: 24) {
                    // Question
                    StyledCard {
                        VStack(alignment: .leading, spacing: 16) {
                            Text(exercise.question)
                                .font(AppTheme.Typography.body)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .fixedSize(horizontal: false, vertical: true)

                            if let context = exercise.context, !context.isEmpty {
                                Text(context)
                                    .font(AppTheme.Typography.callout)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                                    .padding(.vertical, 8)
                                    .padding(.horizontal, 12)
                                    .background(AppTheme.Colors.background)
                                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                            }
                        }
                        .padding(16)
                    }

                    // Options
                    VStack(spacing: 12) {
                        ForEach(exercise.options.indices, id: \.self) { index in
                            OptionButton(index: index, option: exercise.options[index], viewModel: viewModel)
                        }
                    }

                    // Explanation (shown after answering)
                    if viewModel.hasAnswered {
                        ExplanationView(exercise: exercise, viewModel: viewModel, localizationManager: localizationManager)
                    }

                    Spacer(minLength: 40)

                    // Continue Button
                    if viewModel.hasAnswered {
                        ContinueButtonView(viewModel: viewModel, localizationManager: localizationManager)
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

// Option button
private struct OptionButton: View {
    let index: Int
    let option: String
    @ObservedObject var viewModel: GrammarViewModel

    var body: some View {
        Button(action: {
            if !viewModel.hasAnswered {
                viewModel.checkAnswer(selectedIndex: index)
            }
        }) {
            HStack {
                Text(option)
                    .font(AppTheme.Typography.body)
                    .foregroundColor(optionTextColor)
                    .multilineTextAlignment(.leading)

                Spacer()

                if viewModel.hasAnswered {
                    Image(systemName: optionIcon)
                        .foregroundColor(optionIconColor)
                }
            }
            .padding(16)
            .background(optionBackground)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(optionBorder, lineWidth: 1)
            )
        }
        .disabled(viewModel.hasAnswered)
    }

    // Computed properties for styling
    private var optionTextColor: Color {
        if !viewModel.hasAnswered {
            return AppTheme.Colors.textPrimary
        }

        if index == viewModel.selectedAnswerIndex {
            return viewModel.isAnswerCorrect ? AppTheme.Colors.success : AppTheme.Colors.error
        } else if index == viewModel.correctAnswerIndex {
            return AppTheme.Colors.success
        }

        return AppTheme.Colors.textPrimary
    }

    private var optionIconColor: Color {
        if index == viewModel.selectedAnswerIndex {
            return viewModel.isAnswerCorrect ? AppTheme.Colors.success : AppTheme.Colors.error
        } else if index == viewModel.correctAnswerIndex {
            return AppTheme.Colors.success
        }

        return AppTheme.Colors.textTertiary
    }

    private var optionIcon: String {
        if index == viewModel.selectedAnswerIndex {
            return viewModel.isAnswerCorrect ? "checkmark.circle.fill" : "xmark.circle.fill"
        } else if index == viewModel.correctAnswerIndex {
            return "checkmark.circle.fill"
        }

        return "circle"
    }

    private var optionBackground: Color {
        if !viewModel.hasAnswered {
            return AppTheme.Colors.card
        }

        if index == viewModel.selectedAnswerIndex {
            return viewModel.isAnswerCorrect ?
                AppTheme.Colors.success.opacity(0.1) :
                AppTheme.Colors.error.opacity(0.1)
        } else if index == viewModel.correctAnswerIndex {
            return AppTheme.Colors.success.opacity(0.1)
        }

        return AppTheme.Colors.card
    }

    private var optionBorder: Color {
        if !viewModel.hasAnswered {
            return Color.white.opacity(0.1)
        }

        if index == viewModel.selectedAnswerIndex {
            return viewModel.isAnswerCorrect ?
                AppTheme.Colors.success.opacity(0.3) :
                AppTheme.Colors.error.opacity(0.3)
        } else if index == viewModel.correctAnswerIndex {
            return AppTheme.Colors.success.opacity(0.3)
        }

        return Color.white.opacity(0.1)
    }
}

// Explanation view
private struct ExplanationView: View {
    let exercise: GrammarExercise
    @ObservedObject var viewModel: GrammarViewModel
    let localizationManager: LocalizationManager

    var body: some View {
        VStack {
            if viewModel.showExplanation {
                StyledCard {
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Text(localizationManager.localizedString(LocalizationKey.explanation))
                                .font(AppTheme.Typography.headline)
                                .foregroundColor(AppTheme.Colors.textPrimary)

                            Spacer()

                            Button(action: {
                                withAnimation {
                                    viewModel.showExplanation = false
                                }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(AppTheme.Colors.textTertiary)
                            }
                        }

                        Text(exercise.explanation)
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .padding(16)
                }
            } else {
                Button(action: {
                    withAnimation {
                        viewModel.showExplanation = true
                    }
                }) {
                    HStack {
                        Image(systemName: "info.circle")
                        Text(localizationManager.localizedString(LocalizationKey.explanation))
                    }
                    .font(AppTheme.Typography.subheadline)
                    .foregroundColor(AppTheme.Colors.primary)
                    .padding(.vertical, 8)
                }
            }
        }
    }
}

// Continue button view
private struct ContinueButtonView: View {
    @ObservedObject var viewModel: GrammarViewModel
    let localizationManager: LocalizationManager

    var body: some View {
        Button(action: {
            viewModel.nextExercise()
        }) {
            HStack {
                Text(localizationManager.localizedString(LocalizationKey.next_exercise))
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(.white)

                Image(systemName: "arrow.right")
                    .font(.system(size: 16, weight: .semibold))
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 10, x: 0, y: 5)
        }
    }
}

#Preview {
    NavigationView {
        GrammarPracticeView(path: .constant(NavigationPath()))
    }
}
