import SwiftUI
import AVFoundation

struct ListeningView: View {
    @StateObject private var viewModel = ListeningViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @EnvironmentObject private var lessonManager: LessonManager
    @EnvironmentObject private var errorManager: ErrorManager

    var body: some View {
        VStack(spacing: 20) {
            if let exercise = viewModel.currentExercise {
                ExerciseHeader(exercise: exercise, progress: viewModel.progress)
                AudioControls(viewModel: viewModel)
                TranscriptView(exercise: exercise, showTranscript: viewModel.showTranscript)
                QuestionView(exercise: exercise, viewModel: viewModel)
                NavigationControls(viewModel: viewModel, exercise: exercise)
            } else {
                UnifiedEmptyStateView(
                    icon: "headphones",
                    title: localizationManager.localizedString(LocalizationKey.no_exercise),
                    message: "No listening exercises available at the moment"
                )
            }
        }
        .padding()
        .navigationTitle(localizationManager.localizedString(LocalizationKey.listeningExercise))
    }
}

// MARK: - Subviews
struct ExerciseHeader: View {
    let exercise: ListeningExercise
    let progress: Double
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(spacing: 10) {
            Text(exercise.title)
                .font(.title)
                .bold()

            Text(String(format: localizationManager.localizedString(LocalizationKey.exercise_progress), Int(progress * 100)))
                .font(.subheadline)
                .foregroundColor(.secondary)

            ProgressView(value: progress)
                .padding()
        }
    }
}

struct AudioControls: View {
    @ObservedObject var viewModel: ListeningViewModel
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        HStack(spacing: 20) {
            Button(action: {
                viewModel.playAudio()
            }) {
                Image(systemName: viewModel.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.blue)
            }

            Button(action: viewModel.toggleTranscript) {
                Text(viewModel.showTranscript ?
                    localizationManager.localizedString(LocalizationKey.hideTranscript) :
                    localizationManager.localizedString(LocalizationKey.showTranscript))
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
        }
    }
}

struct TranscriptView: View {
    let exercise: ListeningExercise
    let showTranscript: Bool
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        if showTranscript {
            ScrollView {
                Text(exercise.transcript)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
            }
            .frame(maxHeight: 200)
        }
    }
}

struct QuestionView: View {
    let exercise: ListeningExercise
    @ObservedObject var viewModel: ListeningViewModel
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        if viewModel.currentQuestionIndex < exercise.questions.count {
            let question = exercise.questions[viewModel.currentQuestionIndex]

            VStack(alignment: .leading, spacing: 15) {
                Text(question.question)
                    .font(.headline)

                ForEach(0..<question.options.count, id: \.self) { index in
                    Button(action: {
                        viewModel.selectAnswer(index)
                    }) {
                        Text(question.options[index])
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(
                                viewModel.selectedAnswer == index ?
                                Color.blue.opacity(0.2) :
                                Color.gray.opacity(0.1)
                            )
                            .cornerRadius(10)
                    }
                    .disabled(viewModel.isAnswerSubmitted)
                }

                if viewModel.isAnswerSubmitted {
                    Text(viewModel.isAnswerCorrect ?
                        localizationManager.localizedString(LocalizationKey.correct_answer) :
                        localizationManager.localizedString(LocalizationKey.incorrect_answer))
                        .foregroundColor(viewModel.isAnswerCorrect ? .green : .red)
                        .padding(.top)
                }
            }
            .padding()
        }
    }
}

struct NavigationControls: View {
    @ObservedObject var viewModel: ListeningViewModel
    let exercise: ListeningExercise
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        HStack {
            Button(action: {
                viewModel.stopAudio()
                viewModel.currentQuestionIndex = max(0, viewModel.currentQuestionIndex - 1)
            }) {
                HStack {
                    Image(systemName: "chevron.left")
                    Text(localizationManager.localizedString(LocalizationKey.previous))
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(10)
            }
            .disabled(viewModel.currentQuestionIndex == 0)

            Spacer()

            Button(action: {
                viewModel.stopAudio()
                viewModel.currentQuestionIndex = min(exercise.questions.count - 1, viewModel.currentQuestionIndex + 1)
            }) {
                HStack {
                    Text(localizationManager.localizedString(LocalizationKey.next))
                    Image(systemName: "chevron.right")
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(10)
            }
            .disabled(viewModel.currentQuestionIndex == exercise.questions.count - 1)
        }
        .padding()
    }
}

#Preview {
    NavigationView {
        ListeningView()
            .environmentObject(LessonManager.shared)
            .environmentObject(ErrorManager.shared)
    }
}