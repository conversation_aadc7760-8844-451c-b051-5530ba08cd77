import SwiftUI

/// 离线设置视图
struct OfflineSettingsView: View {
    @StateObject private var offlineManager = OfflineDataManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var showingClearCacheAlert = false
    @State private var cacheStatistics: OfflineCacheStatistics?
    
    var body: some View {
        NavigationView {
            List {
                // 离线模式开关
                offlineModeSection
                
                // 同步状态
                syncStatusSection
                
                // 缓存信息
                cacheInfoSection
                
                // 缓存管理
                cacheManagementSection
            }
            .navigationTitle(localizationManager.localizedString(LocalizationKey.offline_mode))
            .onAppear {
                loadCacheStatistics()
            }
            .refreshable {
                loadCacheStatistics()
            }
        }
    }
    
    // MARK: - Sections
    
    private var offlineModeSection: some View {
        Section {
            HStack {
                Image(systemName: offlineManager.isOfflineMode ? "wifi.slash" : "wifi")
                    .foregroundColor(offlineManager.isOfflineMode ? .orange : .green)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(localizationManager.localizedString(LocalizationKey.offline_mode))
                        .font(.headline)
                    
                    Text(offlineManager.isOfflineMode ? 
                         localizationManager.localizedString(LocalizationKey.offline_data_available) :
                         localizationManager.localizedString("online_mode"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { offlineManager.isOfflineMode },
                    set: { newValue in
                        if newValue {
                            offlineManager.enableOfflineMode()
                        } else {
                            offlineManager.disableOfflineMode()
                        }
                    }
                ))
            }
            .padding(.vertical, 4)
        } header: {
            Text(localizationManager.localizedString("offline_settings"))
        }
    }
    
    private var syncStatusSection: some View {
        Section {
            // 同步状态
            HStack {
                Image(systemName: "arrow.triangle.2.circlepath")
                    .foregroundColor(offlineManager.isSyncing ? .blue : .gray)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(offlineManager.isSyncing ? 
                         localizationManager.localizedString(LocalizationKey.sync_in_progress) :
                         localizationManager.localizedString("sync_status"))
                        .font(.headline)
                    
                    if let lastSync = offlineManager.lastSyncTime {
                        Text(localizationManager.localizedString(LocalizationKey.offline_last_sync, arguments: formatDate(lastSync)))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Text(localizationManager.localizedString("never_synced"))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if offlineManager.isSyncing {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            .padding(.vertical, 4)
            
            // 待同步项目
            if offlineManager.pendingSyncCount > 0 {
                HStack {
                    Image(systemName: "clock.arrow.circlepath")
                        .foregroundColor(.orange)
                    
                    Text(localizationManager.localizedString(LocalizationKey.offline_sync_pending, arguments: offlineManager.pendingSyncCount))
                        .font(.subheadline)
                    
                    Spacer()
                }
                .padding(.vertical, 4)
            }
            
            // 同步按钮
            Button(action: {
                Task {
                    try? await offlineManager.syncToServer()
                    try? await offlineManager.syncFromServer()
                    loadCacheStatistics()
                }
            }) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                    Text(localizationManager.localizedString("sync_now"))
                }
            }
            .disabled(offlineManager.isSyncing)
            
        } header: {
            Text(localizationManager.localizedString("sync_status"))
        }
    }
    
    private var cacheInfoSection: some View {
        Section {
            if let stats = cacheStatistics {
                // 总缓存大小
                HStack {
                    Image(systemName: "internaldrive")
                        .foregroundColor(.blue)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(localizationManager.localizedString(LocalizationKey.offline_cache_size, arguments: formatBytes(stats.totalSize)))
                            .font(.headline)
                        
                        Text(localizationManager.localizedString("total_cache_items", arguments: stats.itemCount))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                .padding(.vertical, 4)
                
                // 分类缓存大小
                ForEach(Array(stats.categories.keys.sorted()), id: \.self) { category in
                    HStack {
                        Image(systemName: iconForCategory(category))
                            .foregroundColor(colorForCategory(category))
                        
                        Text(localizationManager.localizedString(category))
                            .font(.subheadline)
                        
                        Spacer()
                        
                        Text(formatBytes(stats.categories[category] ?? 0))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 2)
                }
            } else {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text(localizationManager.localizedString("loading_cache_info"))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 8)
            }
        } header: {
            Text(localizationManager.localizedString("cache_information"))
        }
    }
    
    private var cacheManagementSection: some View {
        Section {
            // 清除缓存按钮
            Button(action: {
                showingClearCacheAlert = true
            }) {
                HStack {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                    Text(localizationManager.localizedString("clear_cache"))
                        .foregroundColor(.red)
                }
            }
            .alert(localizationManager.localizedString("confirm_clear_cache"), isPresented: $showingClearCacheAlert) {
                Button(localizationManager.localizedString(LocalizationKey.cancel), role: .cancel) { }
                Button(localizationManager.localizedString("clear"), role: .destructive) {
                    Task {
                        try? await offlineManager.clearCache()
                        loadCacheStatistics()
                    }
                }
            } message: {
                Text(localizationManager.localizedString("clear_cache_warning"))
            }
            
        } header: {
            Text(localizationManager.localizedString("cache_management"))
        } footer: {
            Text(localizationManager.localizedString("cache_management_footer"))
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Helper Methods
    
    private func loadCacheStatistics() {
        cacheStatistics = offlineManager.getCacheStatistics()
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    private func iconForCategory(_ category: String) -> String {
        switch category {
        case "lessons": return "book"
        case "vocabulary": return "textformat.abc"
        case "evaluations": return "checkmark.circle"
        case "achievements": return "trophy"
        case "user_data": return "person"
        default: return "folder"
        }
    }
    
    private func colorForCategory(_ category: String) -> Color {
        switch category {
        case "lessons": return .blue
        case "vocabulary": return .green
        case "evaluations": return .orange
        case "achievements": return .yellow
        case "user_data": return .purple
        default: return .gray
        }
    }
}

// MARK: - Preview
struct OfflineSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        OfflineSettingsView()
    }
}
