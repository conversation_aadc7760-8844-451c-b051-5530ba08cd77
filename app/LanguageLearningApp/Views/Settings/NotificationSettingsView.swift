import SwiftUI
import UserNotifications

struct NotificationSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var userManager: UserManager
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var dailyReminder = true
    @State private var weeklyProgress = true
    @State private var newContent = true
    @State private var reminderTime = Date()
    @State private var isSaving = false
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            StyledContainer {
                VStack(spacing: 24) {
                    // Header with icon
                    VStack(spacing: 16) {
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppTheme.Colors.accent2,
                                            AppTheme.Colors.secondary
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 80, height: 80)
                                .shadow(color: AppTheme.Colors.accent2.opacity(0.5), radius: 10, x: 0, y: 5)

                            Image(systemName: "bell.fill")
                                .font(.system(size: 30))
                                .foregroundColor(.white)
                        }

                        Text(localizationManager.localizedString(LocalizationKey.notification_settings))
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(localizationManager.localizedString("manage_notification_preferences"))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, 10)

                    // Notification Types Section
                    StyledSectionHeader(title: localizationManager.localizedString("notification_types"))

                    StyledCard {
                        VStack(spacing: 0) {
                            StyledToggle(
                                title: localizationManager.localizedString(LocalizationKey.daily_reminder),
                                isOn: $dailyReminder,
                                icon: "calendar.badge.clock"
                            )
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            StyledToggle(
                                title: localizationManager.localizedString(LocalizationKey.weekly_progress_notification),
                                isOn: $weeklyProgress,
                                icon: "chart.bar.fill"
                            )
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            StyledToggle(
                                title: localizationManager.localizedString(LocalizationKey.new_content_notification),
                                isOn: $newContent,
                                icon: "sparkles"
                            )
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                        }
                    }

                    // Reminder Time Section
                    if dailyReminder {
                        StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.reminder_time))

                        StyledCard {
                            VStack(alignment: .leading, spacing: 12) {
                                Text(localizationManager.localizedString(LocalizationKey.daily_reminder_time))
                                    .font(AppTheme.Typography.callout)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                DatePicker("", selection: $reminderTime, displayedComponents: .hourAndMinute)
                                    .datePickerStyle(WheelDatePickerStyle())
                                    .labelsHidden()
                                    .accentColor(AppTheme.Colors.primary)
                                    .colorScheme(.dark)
                                    .frame(maxWidth: .infinity)
                            }
                            .padding(16)
                        }
                    }

                    // Notification Permissions Section
                    StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.notification_permissions))

                    StyledCard {
                        Button(action: {
                            // 打开系统通知设置
                            if let url = URL(string: UIApplication.openSettingsURLString) {
                                UIApplication.shared.open(url)
                            }
                        }) {
                            HStack {
                                Image(systemName: "switch.2")
                                    .font(.system(size: 18))
                                    .foregroundColor(AppTheme.Colors.accent1)
                                    .frame(width: 24, height: 24)

                                Text(localizationManager.localizedString(LocalizationKey.manage_notification_permissions))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                Spacer()

                                Image(systemName: "arrow.up.right.square")
                                    .font(.system(size: 16))
                                    .foregroundColor(AppTheme.Colors.textTertiary)
                            }
                            .padding(16)
                        }
                    }

                    Spacer(minLength: 30)

                    // Save Button
                    StyledButton(
                        title: localizationManager.localizedString("save_settings"),
                        action: {
                            saveSettings()
                        },
                        icon: "checkmark",
                        isLoading: isSaving,
                        isDisabled: isSaving
                    )
                    .padding(.horizontal, 20)
                }
            }
            .navigationTitle(localizationManager.localizedString(LocalizationKey.notification_settings))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(localizationManager.localizedString(LocalizationKey.notification_settings))
                        .font(AppTheme.Typography.title3)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { dismiss() }) {
                        Text(localizationManager.localizedString(LocalizationKey.done))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                }
            }
            .onAppear {
                loadSettings()
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    dismissButton: .default(Text(localizationManager.localizedString(LocalizationKey.ok))) {
                        if alertTitle == localizationManager.localizedString("settings_saved") {
                            dismiss()
                        }
                    }
                )
            }
        }
    }

    private func loadSettings() {
        // 从 UserDefaults 加载设置
        let defaults = UserDefaults.standard
        dailyReminder = defaults.bool(forKey: "notification_daily_reminder")
        weeklyProgress = defaults.bool(forKey: "notification_weekly_progress")
        newContent = defaults.bool(forKey: "notification_new_content")

        if let savedTime = defaults.object(forKey: "notification_reminder_time") as? Date {
            reminderTime = savedTime
        } else {
            // 默认设置为晚上8点
            var components = DateComponents()
            components.hour = 20
            components.minute = 0
            if let defaultTime = Calendar.current.date(from: components) {
                reminderTime = defaultTime
            }
        }
    }

    private func saveSettings() {
        // 保存设置到 UserDefaults
        let defaults = UserDefaults.standard
        defaults.set(dailyReminder, forKey: "notification_daily_reminder")
        defaults.set(weeklyProgress, forKey: "notification_weekly_progress")
        defaults.set(newContent, forKey: "notification_new_content")
        defaults.set(reminderTime, forKey: "notification_reminder_time")

        // 如果启用了每日提醒，设置通知
        if dailyReminder {
            scheduleNotification()
        } else {
            // 取消通知
            UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        }

        // 同步设置到服务器
        isSaving = true

        Task {
            do {
                // 创建用户设置对象
                var settings = UserSettings()
                if let user = userManager.currentUser {
                    settings = user.settings ?? UserSettings()
                }

                // 更新通知设置
                settings.notificationsEnabled = dailyReminder

                // 调用API更新设置
                try await userManager.updateSettings(settings: settings)

                DispatchQueue.main.async {
                    self.isSaving = false
                    // 成功时不显示提示，直接关闭页面
                    dismiss()
                }
            } catch {
                DispatchQueue.main.async {
                    self.isSaving = false
                    self.alertTitle = localizationManager.localizedString("settings_error")
                    self.alertMessage = error.localizedDescription
                    self.showAlert = true
                }
            }
        }
    }

    private func scheduleNotification() {
        // 请求通知权限
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                // 创建通知内容
                let content = UNMutableNotificationContent()
                content.title = localizationManager.localizedString("learning_reminder")
                content.body = localizationManager.localizedString("learning_reminder_message")
                content.sound = UNNotificationSound.default

                // 提取小时和分钟
                let calendar = Calendar.current
                let hour = calendar.component(.hour, from: reminderTime)
                let minute = calendar.component(.minute, from: reminderTime)

                // 创建触发器
                var dateComponents = DateComponents()
                dateComponents.hour = hour
                dateComponents.minute = minute
                let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)

                // 创建请求
                let request = UNNotificationRequest(identifier: "dailyReminder", content: content, trigger: trigger)

                // 添加请求
                UNUserNotificationCenter.current().add(request) { error in
                    if let error = error {
                        print("通知设置失败: \(error)")
                    }
                }
            }
        }
    }
}

#Preview {
    NotificationSettingsView()
}
