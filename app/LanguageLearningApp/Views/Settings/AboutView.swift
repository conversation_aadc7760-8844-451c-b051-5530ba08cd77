import SwiftUI

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.openURL) private var openURL
    @StateObject private var localizationManager = LocalizationManager.shared

    let appVersion = "1.0.0"
    let buildNumber = "100"

    var body: some View {
        NavigationView {
            StyledContainer {
                VStack(spacing: 24) {
                    // App Info
                    VStack(spacing: 16) {
                        LogoView()

                        Text(localizationManager.localizedString("app_name_title"))
                            .font(AppTheme.Typography.title1)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(
                            String(
                                format: localizationManager.localizedString("version_format"),
                                appVersion, buildNumber)
                        )
                        .font(AppTheme.Typography.subheadline)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                    }
                    .padding(.bottom, 10)

                    // Developer Info
                    StyledSectionHeader(title: localizationManager.localizedString("developer"))

                    StyledCard {
                        VStack(spacing: 0) {
                            HStack {
                                Text(localizationManager.localizedString("development_team"))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                Spacer()

                                Text(localizationManager.localizedString("app_name_title"))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }
                            .padding(16)

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            Button(action: {
                                openURL(URL(string: "mailto:<EMAIL>")!)
                            }) {
                                HStack {
                                    Image(systemName: "envelope.fill")
                                        .font(.system(size: 18))
                                        .foregroundColor(AppTheme.Colors.accent3)
                                        .frame(width: 24, height: 24)

                                    Text(localizationManager.localizedString("contact_us"))
                                        .font(AppTheme.Typography.body)
                                        .foregroundColor(AppTheme.Colors.textPrimary)

                                    Spacer()

                                    Image(systemName: "arrow.up.right")
                                        .font(.system(size: 16))
                                        .foregroundColor(AppTheme.Colors.textTertiary)
                                }
                                .padding(16)
                            }

                            // 移除 TestErrorView 引用，因为文件已被删除
                        }
                    }

                    // Legal Info
                    StyledSectionHeader(
                        title: localizationManager.localizedString("legal_information"))

                    StyledCard {
                        VStack(spacing: 0) {
                            Button(action: {
                                openURL(URL(string: "https://www.languagelearningapp.com/terms")!)
                            }) {
                                HStack {
                                    Image(systemName: "doc.text.fill")
                                        .font(.system(size: 18))
                                        .foregroundColor(AppTheme.Colors.accent1)
                                        .frame(width: 24, height: 24)

                                    Text(localizationManager.localizedString("terms_of_service"))
                                        .font(AppTheme.Typography.body)
                                        .foregroundColor(AppTheme.Colors.textPrimary)

                                    Spacer()

                                    Image(systemName: "arrow.up.right.square")
                                        .font(.system(size: 16))
                                        .foregroundColor(AppTheme.Colors.textTertiary)
                                }
                                .padding(16)
                            }

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            Button(action: {
                                openURL(URL(string: "https://www.languagelearningapp.com/privacy")!)
                            }) {
                                HStack {
                                    Image(systemName: "hand.raised.fill")
                                        .font(.system(size: 18))
                                        .foregroundColor(AppTheme.Colors.accent1)
                                        .frame(width: 24, height: 24)

                                    Text(localizationManager.localizedString("privacy_policy"))
                                        .font(AppTheme.Typography.body)
                                        .foregroundColor(AppTheme.Colors.textPrimary)

                                    Spacer()

                                    Image(systemName: "arrow.up.right.square")
                                        .font(.system(size: 16))
                                        .foregroundColor(AppTheme.Colors.textTertiary)
                                }
                                .padding(16)
                            }
                        }
                    }

                    // Third-party Libraries
                    StyledSectionHeader(
                        title: localizationManager.localizedString("third_party_libraries"))

                    StyledCard {
                        NavigationLink(destination: ThirdPartyLibrariesView()) {
                            HStack {
                                Image(systemName: "cube.box.fill")
                                    .font(.system(size: 18))
                                    .foregroundColor(AppTheme.Colors.primary)
                                    .frame(width: 24, height: 24)

                                Text(localizationManager.localizedString("open_source_components"))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                Spacer()

                                Image(systemName: "chevron.right")
                                    .font(.system(size: 14))
                                    .foregroundColor(AppTheme.Colors.textTertiary)
                            }
                            .padding(16)
                        }
                    }

                    // Copyright Info
                    Text(
                        String(
                            format: localizationManager.localizedString("copyright_notice"),
                            Calendar.current.component(.year, from: Date()))
                    )
                    .font(AppTheme.Typography.footnote)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.top, 20)

                    Spacer(minLength: 30)
                }
            }
            .navigationTitle(localizationManager.localizedString("about_title"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { dismiss() }) {
                        Text(localizationManager.localizedString(LocalizationKey.done))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                }
            }
        }
    }
}

struct ThirdPartyLibrariesView: View {
    @StateObject private var localizationManager = LocalizationManager.shared

    let libraries = [
        ("SwiftUI", "Apple Inc.", "https://developer.apple.com/xcode/swiftui/"),
        ("Combine", "Apple Inc.", "https://developer.apple.com/documentation/combine"),
        ("Swift Algorithms", "Apple Inc.", "https://github.com/apple/swift-algorithms"),
        ("Swift Collections", "Apple Inc.", "https://github.com/apple/swift-collections"),
    ]

    var body: some View {
        StyledContainer {
            VStack(spacing: 16) {
                ForEach(libraries.indices, id: \.self) { index in
                    let library = libraries[index]

                    StyledCard {
                        VStack(alignment: .leading, spacing: 12) {
                            Text(library.0)
                                .font(AppTheme.Typography.headline)
                                .foregroundColor(AppTheme.Colors.textPrimary)

                            Text(
                                String(
                                    format: localizationManager.localizedString("developer"),
                                    library.1)
                            )
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                            if let url = URL(string: library.2) {
                                Link(destination: url) {
                                    HStack {
                                        Text(localizationManager.localizedString("view_project"))
                                            .font(AppTheme.Typography.footnote)

                                        Image(systemName: "arrow.up.right")
                                            .font(.system(size: 12))
                                    }
                                    .foregroundColor(AppTheme.Colors.accent3)
                                }
                            }
                        }
                        .padding(16)
                    }
                }
            }
        }
        .navigationTitle(localizationManager.localizedString("third_party_libraries"))
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    AboutView()
}
