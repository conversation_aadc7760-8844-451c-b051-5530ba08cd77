import SwiftUI
import Speech
import Foundation

struct SpeakingView: View {
    @StateObject private var viewModel = SpeakingViewModel()
    @StateObject private var ttsManager = TTSManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @EnvironmentObject private var errorManager: ErrorManager

    var body: some View {
        StyledContainer {
            VStack(spacing: 24) {
                if let exercise = viewModel.currentExercise {
                    // Header and Progress
                    VStack(spacing: 16) {
                        Text(exercise.title)
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        VStack(spacing: 8) {
                            HStack {
                                Text(String(format: localizationManager.localizedString(LocalizationKey.exercise_progress), Int(viewModel.progress * 100)))
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Spacer()

                                Text("\((viewModel.exercises.firstIndex(where: { $0.id == viewModel.currentExercise?.id }) ?? 0) + 1)/\(viewModel.exercises.count)")
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }

                            StyledProgressBar(progress: viewModel.progress)
                        }
                    }

                    // Exercise Prompt Section
                    VStack(alignment: .leading, spacing: 12) {
                        StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.exercisePrompt))

                        StyledCard {
                            Text(exercise.prompt)
                                .font(AppTheme.Typography.body)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .padding(16)
                        }
                    }

                    // Target Phrase Section
                    VStack(alignment: .leading, spacing: 12) {
                        StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.targetPhrase))

                        StyledCard {
                            VStack(spacing: 16) {
                                Text(exercise.targetPhrase)
                                    .font(AppTheme.Typography.headline)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal, 16)
                                    .padding(.top, 16)

                                // Play Button
                                Button(action: {
                                    if viewModel.isPlaying {
                                        ttsManager.stopSample()
                                        viewModel.isPlaying = false
                                    } else {
                                        ttsManager.playSample(text: exercise.targetPhrase, languageCode: viewModel.currentLanguage) { error in
                                            if let error = error {
                                                errorManager.showError(.customError(error.localizedDescription))
                                                viewModel.isPlaying = false
                                            } else {
                                                viewModel.isPlaying = true
                                            }
                                        }
                                    }
                                }) {
                                    HStack(spacing: 12) {
                                        Image(systemName: viewModel.isPlaying ? "stop.circle.fill" : "play.circle.fill")
                                            .font(.system(size: 24))

                                        Text(viewModel.isPlaying ?
                                            localizationManager.localizedString(LocalizationKey.stop) :
                                            localizationManager.localizedString(LocalizationKey.play))
                                            .font(AppTheme.Typography.subheadline)
                                    }
                                    .foregroundColor(viewModel.isPlaying ? AppTheme.Colors.error : AppTheme.Colors.primary)
                                    .padding(.vertical, 10)
                                    .padding(.horizontal, 20)
                                    .background(
                                        (viewModel.isPlaying ? AppTheme.Colors.error : AppTheme.Colors.primary)
                                            .opacity(0.1)
                                    )
                                    .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .padding(.bottom, 16)
                            }
                        }
                    }

                    // Recording Section
                    VStack(alignment: .leading, spacing: 12) {
                        StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.yourAnswer))

                        VStack(spacing: 20) {
                            // Recording Button
                            Button(action: {
                                if viewModel.isRecording {
                                    viewModel.stopRecording()
                                } else {
                                    viewModel.startRecording()
                                }
                            }) {
                                ZStack {
                                    Circle()
                                        .fill(viewModel.isRecording ? AppTheme.Colors.error.opacity(0.1) : AppTheme.Colors.primary.opacity(0.1))
                                        .frame(width: 120, height: 120)

                                    Circle()
                                        .stroke(viewModel.isRecording ? AppTheme.Colors.error : AppTheme.Colors.primary, lineWidth: 2)
                                        .frame(width: 120, height: 120)

                                    VStack(spacing: 8) {
                                        Image(systemName: viewModel.isRecording ? "stop.circle.fill" : "mic.circle.fill")
                                            .font(.system(size: 40))
                                            .foregroundColor(viewModel.isRecording ? AppTheme.Colors.error : AppTheme.Colors.primary)

                                        Text(viewModel.isRecording ?
                                            localizationManager.localizedString(LocalizationKey.stopRecording) :
                                            localizationManager.localizedString(LocalizationKey.startRecording))
                                            .font(AppTheme.Typography.caption1)
                                            .foregroundColor(viewModel.isRecording ? AppTheme.Colors.error : AppTheme.Colors.primary)
                                    }
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                            .frame(maxWidth: .infinity)

                            // Recording Indicator
                            if viewModel.isRecording {
                                HStack(spacing: 8) {
                                    Circle()
                                        .fill(AppTheme.Colors.error)
                                        .frame(width: 8, height: 8)

                                    Text("Recording...")
                                        .font(AppTheme.Typography.footnote)
                                        .foregroundColor(AppTheme.Colors.error)
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal, 16)
                                .background(AppTheme.Colors.error.opacity(0.1))
                                .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                            }

                            // Transcribed Text
                            if !viewModel.transcribedText.isEmpty {
                                StyledCard {
                                    VStack(alignment: .leading, spacing: 12) {
                                        Text("Transcribed Text:")
                                            .font(AppTheme.Typography.footnote)
                                            .foregroundColor(AppTheme.Colors.textSecondary)

                                        Text(viewModel.transcribedText)
                                            .font(AppTheme.Typography.body)
                                            .foregroundColor(AppTheme.Colors.textPrimary)
                                            .padding(12)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .background(AppTheme.Colors.background)
                                            .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                                    }
                                    .padding(16)
                                }
                                .transition(.opacity)
                            }
                        }
                    }

                    // Feedback Section
                    if viewModel.showFeedback {
                        VStack(alignment: .leading, spacing: 12) {
                            StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.feedback))

                            StyledCard {
                                VStack(alignment: .leading, spacing: 12) {
                                    HStack(spacing: 12) {
                                        Image(systemName: "waveform.circle.fill")
                                            .font(.system(size: 20))
                                            .foregroundColor(AppTheme.Colors.accent1)

                                        Text("Pronunciation Feedback")
                                            .font(AppTheme.Typography.headline)
                                            .foregroundColor(AppTheme.Colors.textPrimary)
                                    }

                                    Text(viewModel.feedback)
                                        .font(AppTheme.Typography.body)
                                        .foregroundColor(AppTheme.Colors.textPrimary)
                                }
                                .padding(16)
                            }
                        }
                        .transition(.opacity)
                    }

                    Spacer(minLength: 20)

                    // Next Button
                    Button(action: viewModel.nextExercise) {
                        HStack {
                            Text(localizationManager.localizedString(LocalizationKey.next))
                                .font(AppTheme.Typography.headline)
                                .foregroundColor(.white)

                            Image(systemName: "arrow.right")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                        .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 10, x: 0, y: 5)
                    }
                    .padding(.horizontal, 20)
                } else {
                    // Empty State
                    VStack(spacing: 20) {
                        Image(systemName: "mic.slash")
                            .font(.system(size: 50))
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Text(localizationManager.localizedString(LocalizationKey.no_exercise))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Text("Try again later or check other practice types")
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textTertiary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 40)
                }
            }
        }
        .navigationTitle(localizationManager.localizedString(LocalizationKey.speakingExercise))
        .animation(.easeInOut, value: viewModel.isRecording)
        .animation(.easeInOut, value: viewModel.transcribedText)
        .animation(.easeInOut, value: viewModel.showFeedback)
        .animation(.easeInOut, value: viewModel.isPlaying)
    }
}

#Preview {
    NavigationView {
        SpeakingView()
            .environmentObject(ErrorManager.shared)
    }
}