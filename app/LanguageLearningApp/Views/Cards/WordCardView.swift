import SwiftUI
import Foundation
import AVFoundation

// Helper class to handle AVSpeechSynthesizerDelegate callbacks
class SpeechDelegateHandler: NSObject, AVSpeechSynthesizerDelegate {
    @Binding var isPlayingPronunciation: Bool

    init(isPlayingPronunciation: Binding<Bool>) {
        _isPlayingPronunciation = isPlayingPronunciation
    }

    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isPlayingPronunciation = false
        }
    }

    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isPlayingPronunciation = false
        }
    }
}

/// 单词卡片内容视图
struct WordCardView: View {
    let card: PracticeCardModel
    let word: Word
    @State private var isShowingTranslation: Bool = false
    @State private var isShowingExample: Bool = false
    @State private var animateGradient: Bool = false
    @State private var isPlayingPronunciation: Bool = false
    @State private var speechSynthesizer = AVSpeechSynthesizer()
    @State private var speechDelegate: SpeechDelegateHandler?

    var body: some View {
        VStack(spacing: AppTheme.Dimensions.paddingMedium) {
            // Enhanced Word and translation with modern card design
            VStack(spacing: AppTheme.Dimensions.spacingLarge) {
                // Word header with part of speech
                HStack {
                    Text(word.partOfSpeech)
                        .font(AppTheme.Typography.caption1)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                        .padding(.vertical, 6)
                        .background(AppTheme.Colors.backgroundSecondary)
                        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)

                    Spacer()

                    // Difficulty indicator
                    HStack(spacing: 4) {
                        ForEach(0..<3) { index in
                            Circle()
                                .fill(index < word.difficulty ? AppTheme.Colors.accent2 : AppTheme.Colors.backgroundSecondary)
                                .frame(width: 8, height: 8)
                        }
                    }
                    .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                    .padding(.vertical, 6)
                    .background(AppTheme.Colors.backgroundSecondary)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                }
                .padding(.horizontal, 4)

                // Word with pronunciation button
                HStack {
                    Spacer()

                    VStack(spacing: AppTheme.Dimensions.spacingMedium) {
                        Text(word.text)
                            .font(AppTheme.Typography.largeTitle)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .multilineTextAlignment(.center)

                        // Pronunciation button with animation
                        Button(action: {
                            playTTS(text: word.text, languageCode: word.languageCode ?? "en-US")
                        }) {
                            HStack(spacing: AppTheme.Dimensions.spacingSmall) {
                                Image(systemName: isPlayingPronunciation ? "pause.circle.fill" : "play.circle.fill")
                                    .font(.system(size: AppTheme.Dimensions.iconSizeMedium))

                                Text(word.pronunciation)
                                    .font(AppTheme.Typography.subheadline)
                                    .italic()
                            }
                            .foregroundColor(card.color)
                            .padding(.vertical, AppTheme.Dimensions.paddingSmall)
                            .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
                            .background(card.color.opacity(0.1))
                            .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }

                    Spacer()
                }

                // Translation with enhanced animation
                Button(action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                        isShowingTranslation.toggle()
                    }
                }) {
                    VStack(spacing: AppTheme.Dimensions.spacingSmall) {
                        if isShowingTranslation {
                            Text("Translation")
                                .font(AppTheme.Typography.caption1)
                                .foregroundColor(AppTheme.Colors.textTertiary)
                        }

                        Text(isShowingTranslation ? word.translation : "Tap to reveal translation")
                            .font(isShowingTranslation ? AppTheme.Typography.title2 : AppTheme.Typography.subheadline)
                            .foregroundColor(isShowingTranslation ? card.color : AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.vertical, isShowingTranslation ? AppTheme.Dimensions.paddingMedium : AppTheme.Dimensions.paddingSmall)
                            .padding(.horizontal, AppTheme.Dimensions.paddingLarge)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                    .fill(isShowingTranslation ? card.color.opacity(0.1) : AppTheme.Colors.backgroundSecondary)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                            .stroke(
                                                isShowingTranslation ?
                                                    card.color.opacity(0.3) :
                                                    AppTheme.Colors.textPrimary.opacity(0.1),
                                                lineWidth: isShowingTranslation ? 1.5 : 1
                                            )
                                    )
                            )
                            .scaleEffect(isShowingTranslation ? 1.02 : 1.0)
                    }
                }
                .buttonStyle(PlainButtonStyle())

                // Example toggle button
                Button(action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                        isShowingExample.toggle()
                    }
                }) {
                    HStack(spacing: AppTheme.Dimensions.spacingSmall) {
                        Image(systemName: isShowingExample ? "text.quote.rtl" : "text.quote")
                            .font(.system(size: AppTheme.Dimensions.iconSizeSmall))

                        Text(isShowingExample ? "Hide Example" : "Show Example")
                            .font(AppTheme.Typography.subheadline)
                    }
                    .padding(.vertical, AppTheme.Dimensions.paddingSmall)
                    .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
                    .foregroundColor(isShowingExample ? card.color : AppTheme.Colors.textSecondary)
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .fill(isShowingExample ? card.color.opacity(0.1) : AppTheme.Colors.backgroundSecondary)
                            .overlay(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                    .stroke(
                                        isShowingExample ? card.color.opacity(0.3) : AppTheme.Colors.textPrimary.opacity(0.1),
                                        lineWidth: 1
                                    )
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())

                // Example sentence with highlight (if showing)
                if isShowingExample {
                    VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingMedium) {
                        HStack {
                            Image(systemName: "text.quote")
                                .font(.system(size: AppTheme.Dimensions.iconSizeSmall))
                                .foregroundColor(card.color)

                            Text("Example Sentence")
                                .font(AppTheme.Typography.headline)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }

                        Text(highlightedExample(sentence: word.exampleSentence, word: word.text))
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .lineSpacing(4)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .padding(AppTheme.Dimensions.paddingLarge)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .fill(AppTheme.Colors.backgroundSecondary)
                            .overlay(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                    .stroke(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                AppTheme.Colors.textPrimary.opacity(0.2),
                                                AppTheme.Colors.textPrimary.opacity(0.05)
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 1
                                    )
                            )
                    )
                }

                // Progress indicator
                if word.isLearned {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(AppTheme.Colors.success)

                        Text("Word mastered")
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Spacer()

                        if let lastReviewed = word.lastReviewed {
                            Text("Last reviewed: \(formatDate(lastReviewed))")
                                .font(AppTheme.Typography.caption2)
                                .foregroundColor(AppTheme.Colors.textTertiary)
                        }
                    }
                    .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                    .padding(.vertical, AppTheme.Dimensions.paddingSmall)
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                            .fill(AppTheme.Colors.success.opacity(0.1))
                    )
                } else {
                    // Learning progress indicator
                    VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingSmall) {
                        HStack {
                            Text("Learning Progress")
                                .font(AppTheme.Typography.caption1)
                                .foregroundColor(AppTheme.Colors.textSecondary)

                            Spacer()

                            Text("New Word")
                                .font(AppTheme.Typography.caption2.bold())
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                                .padding(.vertical, 2)
                                .background(
                                    Capsule()
                                        .fill(card.color)
                                )
                        }

                        // Progress bar
                        GeometryReader { geometry in
                            ZStack(alignment: .leading) {
                                // Background
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .fill(AppTheme.Colors.backgroundSecondary)
                                    .frame(height: 8)

                                // Progress (random value for demonstration)
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [card.color, card.color.opacity(0.7)]),
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .frame(width: max(0, min(geometry.size.width * 0.3, geometry.size.width)), height: 8)
                            }
                        }
                        .frame(height: 8)
                    }
                }
            }
            .padding(AppTheme.Dimensions.paddingLarge)
            .background(AppTheme.Colors.backgroundSecondary)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                AppTheme.Colors.textPrimary.opacity(0.2),
                                AppTheme.Colors.textPrimary.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(color: AppTheme.Shadows.medium.color, radius: AppTheme.Shadows.medium.radius, x: AppTheme.Shadows.medium.x, y: AppTheme.Shadows.medium.y)
        }
        .onAppear {
            withAnimation(Animation.linear(duration: 2.0).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
            // Initialize delegate handler
            self.speechDelegate = SpeechDelegateHandler(isPlayingPronunciation: $isPlayingPronunciation)
            self.speechSynthesizer.delegate = self.speechDelegate
        }
    }

    private func playTTS(text: String, languageCode: String) {
        if speechSynthesizer.isSpeaking {
            speechSynthesizer.stopSpeaking(at: .immediate)
            // isPlayingPronunciation will be set to false by the delegate's didCancel or didFinish
            // return // Optionally return if you want toggle behavior (tap to play, tap to stop)
        }
        
        guard !text.isEmpty else {
            print("Text to speak is empty.")
            return
        }

        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to set up audio session: \(error.localizedDescription)")
            return
        }

        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: languageCode)
        // Adjust rate and pitch if desired
        // utterance.rate = AVSpeechUtteranceDefaultSpeechRate
        // utterance.pitchMultiplier = 1.0

        speechSynthesizer.speak(utterance)
        isPlayingPronunciation = true // Set immediately, will be reset by delegate
    }

    // Helper function to get icon for part of speech
    private func partOfSpeechIcon(_ partOfSpeech: String) -> String {
        switch partOfSpeech.lowercased() {
        case "noun":
            return "person.fill"
        case "verb":
            return "arrow.right"
        case "adjective":
            return "text.badge.checkmark"
        case "adverb":
            return "arrow.up.right"
        case "pronoun":
            return "person.crop.circle"
        case "preposition":
            return "arrow.up.and.down"
        case "conjunction":
            return "link"
        case "interjection":
            return "exclamationmark.bubble"
        default:
            return "textformat.abc"
        }
    }

    // Helper function to format date
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: date)
    }

    // Helper function to highlight word in example sentence
    private func highlightedExample(sentence: String, word: String) -> AttributedString {
        var attributedString = AttributedString(sentence)

        if let range = attributedString.range(of: word, options: [.caseInsensitive]) {
            attributedString[range].foregroundColor = card.color
            attributedString[range].font = AppTheme.Typography.body.bold()
        }

        return attributedString
    }
}
