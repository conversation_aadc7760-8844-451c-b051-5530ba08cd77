import SwiftUI

/// 卡片内容视图 - 根据卡片类型显示不同的内容
struct CardContentView: View {
    let card: PracticeCardModel
    @State private var animateGradient: Bool = false

    var body: some View {
        VStack(spacing: AppTheme.Dimensions.paddingMedium) {
            // ALWAYS SHOW PREVIEW CONTENT
            cardPreviewContent
        }
        .onAppear {
            withAnimation(Animation.linear(duration: 2.0).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
        }
    }

    // 卡片预览内容
    @ViewBuilder
    private var cardPreviewContent: some View {
        switch card.content {
        case .word(let word):
            WordCardView(card: card, word: word)
        case .listening(let exercise):
            ListeningCardView(card: card, exercise: exercise)
        case .speaking(let exercise):
            SpeakingCardView(card: card, exercise: exercise)
        case .grammar(let exercise):
            GrammarCardView(card: card, exercise: exercise)
        case .exercise(let exercise):
            ExerciseCardView(card: card, exercise: exercise)
        }
    }
}
