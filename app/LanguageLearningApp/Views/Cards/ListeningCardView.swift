import SwiftUI
import AVFoundation

/// 听力卡片内容视图
struct ListeningCardView: View {
    let card: PracticeCardModel
    let exercise: ListeningExercise
    @State private var animateGradient: Bool = false
    @StateObject private var viewModel = ListeningViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(spacing: AppTheme.Dimensions.paddingMedium) {
            VStack(spacing: AppTheme.Dimensions.paddingMedium) {
                // Enhanced audio player card
                ZStack {
                    // Background with gradient
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    card.color.opacity(0.15),
                                    card.color.opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            card.color.opacity(0.3),
                                            card.color.opacity(0.1)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1.5
                                )
                        )

                    VStack(spacing: 16) {
                        // Title with category badge
                        HStack {
                            Text(exercise.title)
                                .font(AppTheme.Typography.title2.bold())
                                .foregroundColor(AppTheme.Colors.textPrimary)

                            Spacer()

                            Text(exercise.category)
                                .font(AppTheme.Typography.caption2.bold())
                                .foregroundColor(card.color)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(card.color.opacity(0.15))
                                        .overlay(
                                            Capsule()
                                                .stroke(card.color.opacity(0.3), lineWidth: 1)
                                        )
                                )
                        }

                        // Audio waveform visualization
                        HStack(spacing: 3) {
                            ForEach(0..<20, id: \.self) { index in
                                RoundedRectangle(cornerRadius: 1.5)
                                    .fill(card.color.opacity(0.7))
                                    .frame(width: 3, height: CGFloat(4 + index % 8 * 3))
                                    .opacity(viewModel.isPlaying ?
                                        (animateGradient ? 0.3 + Double(index % 5) * 0.15 : 0.7 - Double(index % 5) * 0.15) :
                                        0.4)
                                    .animation(
                                        viewModel.isPlaying ?
                                            Animation.easeInOut(duration: 0.8)
                                                .repeatForever()
                                                .delay(Double(index) * 0.05) :
                                            Animation.easeOut(duration: 0.3),
                                        value: animateGradient
                                    )
                            }
                        }
                        .frame(height: 30)
                        .padding(.vertical, 8)

                        // Audio controls
                        HStack(spacing: 20) {
                            // Rewind button
                            Button(action: {
                                // Rewind audio - restart from beginning
                                viewModel.stopAudio()
                                // Small delay before playing again
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    viewModel.setCurrentExercise(exercise)
                                    viewModel.playAudio()
                                }
                            }) {
                                Image(systemName: "backward.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                                    .frame(width: 44, height: 44)
                                    .background(
                                        Circle()
                                            .fill(AppTheme.Colors.background)
                                            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())

                            // Play button
                            Button(action: {
                                // Set the current exercise and play audio
                                viewModel.setCurrentExercise(exercise)
                                viewModel.playAudio()
                            }) {
                                ZStack {
                                    // Outer glow
                                    Circle()
                                        .fill(card.color.opacity(0.2))
                                        .frame(width: 70, height: 70)
                                        .blur(radius: 4)

                                    // Main circle
                                    Circle()
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [card.color, card.color.opacity(0.8)]),
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                        .frame(width: 60, height: 60)
                                        .shadow(color: card.color.opacity(0.4), radius: 8, x: 0, y: 4)

                                    // Play/Pause icon
                                    Image(systemName: viewModel.isPlaying ? "pause.fill" : "play.fill")
                                        .font(.system(size: AppTheme.Dimensions.iconSizeMedium))
                                        .foregroundColor(.white)
                                }
                            }
                            .buttonStyle(PlainButtonStyle())

                            // Forward button
                            Button(action: {
                                // Toggle transcript visibility
                                viewModel.toggleTranscript()
                            }) {
                                Image(systemName: viewModel.showTranscript ? "text.badge.minus" : "text.badge.plus")
                                    .font(.system(size: 20))
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                                    .frame(width: 44, height: 44)
                                    .background(
                                        Circle()
                                            .fill(AppTheme.Colors.background)
                                            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }

                        // Transcript preview (truncated or full based on toggle)
                        if !exercise.transcript.isEmpty {
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Image(systemName: "text.bubble")
                                        .font(.system(size: 12))
                                        .foregroundColor(card.color)

                                    Text(viewModel.showTranscript ?
                                         localizationManager.localizedString(LocalizationKey.showTranscript) :
                                         "Transcript Preview")
                                        .font(AppTheme.Typography.caption1.bold())
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                }

                                Text(viewModel.showTranscript ?
                                     exercise.transcript :
                                     exercise.transcript.prefix(100) + (exercise.transcript.count > 100 ? "..." : ""))
                                    .font(AppTheme.Typography.callout)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                                    .italic()
                                    .lineSpacing(4)
                                    .padding(AppTheme.Dimensions.paddingSmall)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .background(
                                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                            .fill(AppTheme.Colors.background.opacity(0.5))
                                    )
                            }
                        }
                    }
                    .padding(AppTheme.Dimensions.paddingMedium)
                }

                // Questions preview
                VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingMedium) {
                    // Header with count
                    HStack {
                        HStack(spacing: 6) {
                            Image(systemName: "questionmark.circle.fill")
                                .font(.system(size: 16))
                                .foregroundColor(card.color)

                            Text("Questions")
                                .font(AppTheme.Typography.headline)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                        }

                        Spacer()

                        // Question count badge
                        Text("\(exercise.questions.count) questions")
                            .font(AppTheme.Typography.caption2.bold())
                            .foregroundColor(card.color)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(card.color.opacity(0.15))
                                    .overlay(
                                        Capsule()
                                            .stroke(card.color.opacity(0.3), lineWidth: 1)
                                    )
                            )
                    }

                    Divider()
                        .background(AppTheme.Colors.textTertiary.opacity(0.3))

                    // Sample question
                    if !exercise.questions.isEmpty {
                        VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingSmall) {
                            Text(exercise.questions[0].question)
                                .font(AppTheme.Typography.body.bold())
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .padding(AppTheme.Dimensions.paddingSmall)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(
                                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                        .fill(AppTheme.Colors.background)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                .stroke(card.color.opacity(0.2), lineWidth: 1)
                                        )
                                )

                            // Options grid
                            if exercise.questions[0].options.count >= 2 {
                                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 8) {
                                    ForEach(exercise.questions[0].options.indices.prefix(4), id: \.self) { index in
                                        Text(exercise.questions[0].options[index])
                                            .font(AppTheme.Typography.subheadline)
                                            .foregroundColor(AppTheme.Colors.textPrimary)
                                            .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                                            .padding(.vertical, 8)
                                            .frame(maxWidth: .infinity, alignment: .center)
                                            .background(
                                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                    .fill(index == exercise.questions[0].correctAnswer ? card.color.opacity(0.1) : AppTheme.Colors.background)
                                                    .overlay(
                                                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                            .stroke(index == exercise.questions[0].correctAnswer ? card.color : AppTheme.Colors.textTertiary.opacity(0.3),
                                                                    lineWidth: index == exercise.questions[0].correctAnswer ? 1.5 : 1)
                                                    )
                                            )
                                    }
                                }

                                if exercise.questions[0].options.count > 4 {
                                    Text("+ \(exercise.questions[0].options.count - 4) more options")
                                        .font(AppTheme.Typography.caption1)
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                        .padding(.top, 4)
                                }
                            }
                        }
                    }

                    // Difficulty indicator
                    HStack {
                        // Difficulty level
                        HStack(spacing: 4) {
                            Text("Difficulty:")
                                .font(AppTheme.Typography.caption1)
                                .foregroundColor(AppTheme.Colors.textSecondary)

                            Text(exercise.difficulty.rawValue.capitalized)
                                .font(AppTheme.Typography.caption1.bold())
                                .foregroundColor(difficultyColor(exercise.difficulty))
                        }

                        Spacer()

                        // Practice count (placeholder)
                        HStack(spacing: 4) {
                            Image(systemName: "headphones")
                                .font(.system(size: 12))
                                .foregroundColor(card.color)

                            Text("Listening Exercise")
                                .font(AppTheme.Typography.caption1)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }
                    }
                    .padding(.top, 4)
                }
                .padding(AppTheme.Dimensions.paddingMedium)
                .background(AppTheme.Colors.backgroundSecondary)
                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.white.opacity(0.2),
                                    Color.white.opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
            }
        }
        .onAppear {
            // Set the current exercise when the view appears
            viewModel.setCurrentExercise(exercise)

            withAnimation(Animation.linear(duration: 2.0).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
        }
        .onDisappear {
            // Stop audio when view disappears
            viewModel.stopAudio()
        }
    }

    // Helper function to get color based on difficulty
    private func difficultyColor(_ difficulty: CommonDifficulty) -> Color {
        switch difficulty {
        case .easy:
            return .green
        case .medium:
            return .orange
        case .hard:
            return .red
        }
    }
}
