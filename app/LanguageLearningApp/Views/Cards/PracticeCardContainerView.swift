import SwiftUI

/// 练习卡片容器视图 - 包含卡片内容和交互元素
struct PracticeCardContainerView: View {
    let card: PracticeCardModel
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var animateGradient: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingMedium) {
            // 卡片内容
            ScrollView {
                CardContentView(card: card)
                    .frame(maxWidth: .infinity)
            }

            // 卡片底部
            HStack {
                // 练习次数徽章
                HStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 10))
                        .foregroundColor(card.color)

                    // 这个理想情况下应该来自模型中的真实计数
                    Text("5 practices")
                        .font(AppTheme.Typography.caption2)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(card.color.opacity(0.1))
                )

                Spacer()

                // 动画滑动提示
                HStack(spacing: 6) {
                    Text(localizationManager.localizedString(LocalizationKey.swipe_to_practice))
                        .font(AppTheme.Typography.caption1)
                        .foregroundColor(AppTheme.Colors.textSecondary)

                    // 动画箭头
                    Image(systemName: "hand.tap.fill")
                        .font(.system(size: AppTheme.Dimensions.iconSizeSmall))
                        .foregroundColor(card.color)
                        .offset(x: animateGradient ? 2 : -2)
                        .animation(
                            Animation.easeInOut(duration: 1.0)
                                .repeatForever(autoreverses: true),
                            value: animateGradient
                        )
                }
            }
        }
        .padding(AppTheme.Dimensions.paddingLarge)
        .background(
            ZStack {
                // 基础卡片背景
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                    .fill(AppTheme.Colors.card)

                // 微妙的图案叠加
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                card.color.opacity(0.05),
                                Color.clear
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                // 顶部高光
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.3),
                                Color.white.opacity(0.1),
                                Color.clear
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1.5
                    )
            }
        )
        .cornerRadius(AppTheme.Dimensions.cornerRadiusLarge)
        // 增强阴影，多层次增加深度感
        .shadow(color: Color.black.opacity(0.08), radius: 2, x: 0, y: 1)
        .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
        .shadow(color: card.color.opacity(0.1), radius: 15, x: 0, y: 8)
        .frame(maxHeight: .infinity)
        .padding(.bottom, 60)
    }
}
