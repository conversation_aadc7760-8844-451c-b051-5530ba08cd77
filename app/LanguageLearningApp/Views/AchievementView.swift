import SwiftUI
import Combine

struct AchievementView: View {
    @ObservedObject var achievementManager = AchievementManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @EnvironmentObject private var errorManager: ErrorManager
    @State private var selectedType: Achievement.AchievementType?
    @State private var isRefreshing = false

    var body: some View {
        NavigationView {
            StyledContainer {
                VStack(spacing: 24) {
                    // 成就类型选择器
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 16) {
                            ForEach(Achievement.AchievementType.allCases, id: \.self) { type in
                                AchievementTypeButton(
                                    type: type,
                                    isSelected: selectedType == type,
                                    action: { selectedType = type }
                                )
                            }
                        }
                        .padding(.horizontal, 4)
                    }
                    .padding(.top, 8)

                    // All Achievements text is now shown directly in the view
                    VStack(alignment: .leading, spacing: 4) {
                        Text(localizationManager.localizedString(LocalizationKey.all_achievements_title))
                            .font(.title3)
                            .fontWeight(.bold)
                            .padding(.horizontal, 4)

                        Text(String(format: localizationManager.localizedString(LocalizationKey.achievements_count_format), filteredAchievements.count))
                            .font(.footnote)
                            .foregroundColor(.gray)
                            .padding(.horizontal, 4)
                    }
                    .padding(.top, 8)

                    // 成就列表
                    LazyVStack(spacing: 16) {
                        ForEach(filteredAchievements) { achievement in
                            AchievementCardView(achievement: achievement)
                        }
                    }
                }
            }
            .refreshable {
                // 设置刷新状态为 true
                isRefreshing = true

                do {
                    // 尝试从 API 加载成就数据
                    await achievementManager.loadAchievementsFromAPI(completion: {
                        // 完成时（成功或错误）重置刷新状态
                        isRefreshing = false
                    })

                    // 加载用户成就数据
                    await achievementManager.loadUserAchievementsFromAPI(completion: nil)
                } catch {
                    // 确保在发生意外错误时也重置刷新状态
                    isRefreshing = false
                }

                // 最终安全检查，确保刷新状态始终被重置
                if isRefreshing {
                    isRefreshing = false
                }
            }
            .onChange(of: isRefreshing) { newValue in
                print("Achievement refresh state changed to: \(newValue)")
            }
        }
    }

    private var filteredAchievements: [Achievement] {
        if let type = selectedType {
            return achievementManager.achievements.filter { $0.type == type }
        }
        return achievementManager.achievements
    }
}

struct AchievementTypeButton: View {
    let type: Achievement.AchievementType
    let isSelected: Bool
    let action: () -> Void
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        Button(action: action) {
            VStack(spacing: 10) {
                // Icon with gradient background
                ZStack {
                    Group {
                        if isSelected {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                        } else {
                            Circle()
                                .fill(AppTheme.Colors.card)
                        }
                    }
                    .frame(width: 50, height: 50)
                    .shadow(color: isSelected ? AppTheme.Colors.primary.opacity(0.5) : Color.clear, radius: 8)

                    Image(systemName: iconName)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(isSelected ? .white : AppTheme.Colors.textTertiary)
                }

                Text(localizationManager.localizedString(type.localizationKey))
                    .font(AppTheme.Typography.caption1)
                    .foregroundColor(isSelected ? AppTheme.Colors.textPrimary : AppTheme.Colors.textSecondary)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
            }
            .frame(width: 90, height: 90)
            .padding(.vertical, 8)
            .padding(.horizontal, 4)
            .background(isSelected ? AppTheme.Colors.card.opacity(0.6) : Color.clear)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        isSelected ?
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    AppTheme.Colors.primary.opacity(0.8),
                                    AppTheme.Colors.primary.opacity(0.2)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ) :
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.clear,
                                    Color.clear
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                        lineWidth: 1.5
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var iconName: String {
        switch type {
        case .streak: return "flame.fill"
        case .vocabulary: return "textformat.abc"
        case .listening: return "ear.fill"
        case .speaking: return "mic.fill"
        case .lessons: return "book.fill"
        case .points: return "star.fill"
        case .challenges: return "trophy.fill"
        case .social: return "person.2.fill"
        }
    }
}

struct AchievementView_Previews: PreviewProvider {
    static var previews: some View {
        AchievementView()
    }
}
