import SwiftUI

// MARK: - User Profile Header
struct UserProfileHeader: View {
    let username: String
    let streak: Int
    let onSettingsTap: () -> Void
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(spacing: 20) {
            // User avatar and info
            HStack(spacing: 20) {
                AvatarView(
                    initials: String(username.prefix(1).uppercased()),
                    size: 80
                )

                VStack(alignment: .leading, spacing: 6) {
                    Text(username)
                        .font(AppTheme.Typography.title2)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    HStack(spacing: 8) {
                        Image(systemName: "flame.fill")
                            .foregroundColor(AppTheme.Colors.accent2)

                        Text(
                            String(
                                format: localizationManager.localizedString(LocalizationKey.streak),
                                streak)
                        )
                        .font(AppTheme.Typography.subheadline)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                    }
                }

                Spacer()

                But<PERSON>(action: onSettingsTap) {
                    Image(systemName: "gearshape.fill")
                        .font(.system(size: 22))
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .frame(width: 44, height: 44)
                        .background(AppTheme.Colors.card)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                        )
                }
            }
            .padding(.horizontal, 4)
        }
    }
}

// MARK: - Stat Card
struct StatCard: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 16) {
                // Icon with gradient background
                ZStack {
                    Circle()
                        .fill(AppTheme.Colors.card)
                        .frame(width: 48, height: 48)
                        .overlay(
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.white.opacity(0.2),
                                            Color.white.opacity(0.05),
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )

                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundStyle(AppTheme.Colors.primaryGradient)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(AppTheme.Typography.footnote)
                        .foregroundColor(AppTheme.Colors.textSecondary)

                    Text(value)
                        .font(AppTheme.Typography.title3)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                }
            }
        }
        .padding(16)
        .frame(maxWidth: .infinity)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05),
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
    }
}

// MARK: - Achievement Summary Row
struct AchievementSummaryRow: View {
    let achievement: Achievement
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        HStack(spacing: 16) {
            // Icon with gradient background
            ZStack {
                Circle()
                    .fill(achievement.isUnlocked ? AppTheme.Colors.primary : AppTheme.Colors.card)
                    .frame(width: 40, height: 40)
                    .shadow(
                        color: achievement.isUnlocked
                            ? AppTheme.Colors.primary.opacity(0.5) : Color.clear, radius: 5)

                Image(systemName: achievement.icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(
                        achievement.isUnlocked ? .white : AppTheme.Colors.textSecondary)
            }

            // Achievement details
            VStack(alignment: .leading, spacing: 4) {
                Text(localizationManager.localizedString(achievement.titleKey))
                    .font(AppTheme.Typography.callout)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .lineLimit(1)

                if !achievement.isUnlocked {
                    HStack(spacing: 8) {
                        StyledProgressBar(
                            progress: Double(achievement.progress)
                                / Double(achievement.requirement),
                            height: 6
                        )
                        .frame(width: 80)

                        Text("\(achievement.progress)/\(achievement.requirement)")
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }
                }
            }

            Spacer()

            // Completion indicator
            if achievement.isUnlocked {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 18))
                    .foregroundColor(AppTheme.Colors.accent3)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 16)
    }
}

// MARK: - Main Profile View
struct ProfileView: View {
    @ObservedObject var userManager = UserManager.shared
    @ObservedObject var achievementManager = AchievementManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @EnvironmentObject private var errorManager: ErrorManager
    @State private var showingLogoutAlert = false
    @State private var showingSettings = false
    @State private var navigateToAchievementView = false

    private var recentAchievements: [Achievement] {
        Array(achievementManager.achievements.prefix(3))
    }

    private var streakText: String {
        if let user = UserManager.shared.currentUser {
            return "\(user.stats?.streakDays ?? 0) days"
        }
        return "0 days"
    }

    var body: some View {
        ZStack {
            StyledContainer {
                VStack(spacing: 24) {
                    // User Profile Header
                    UserProfileHeader(
                        username: userManager.currentUser?.username ?? "",
                        streak: userManager.currentUser?.stats?.streakDays ?? 0,
                        onSettingsTap: { showingSettings = true }
                    )

                    // Learning Statistics
                    StyledSectionHeader(
                        title: localizationManager.localizedString(LocalizationKey.statistics)
                    )

                    HStack(spacing: 16) {
                        StatCard(
                            title: localizationManager.localizedString(LocalizationKey.vocabulary),
                            value: "\(userManager.currentUser?.stats?.vocabularyCount ?? 0)",
                            icon: "textformat.abc"
                        )

                        StatCard(
                            title: localizationManager.localizedString(LocalizationKey.exercises),
                            value:
                                "\((userManager.currentUser?.stats?.listeningExerciseCount ?? 0) + (userManager.currentUser?.stats?.speakingExerciseCount ?? 0))",
                            icon: "headphones"
                        )
                    }

                    // Achievements
                    StyledSectionHeader(
                        title: localizationManager.localizedString(LocalizationKey.achievements),
                        action: {
                            // 导航到成就视图
                            navigateToAchievementView = true
                        }
                    )

                    StyledCard {
                        VStack(spacing: 0) {
                            ForEach(recentAchievements) { achievement in
                                AchievementSummaryRow(achievement: achievement)

                                if achievement.id != recentAchievements.last?.id {
                                    Divider()
                                        .background(Color.white.opacity(0.1))
                                        .padding(.horizontal)
                                }
                            }
                        }
                    }

                    Spacer(minLength: 30)

                    // Logout Button
                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.logout),
                        action: {
                            showingLogoutAlert = true
                        },
                        icon: "rectangle.portrait.and.arrow.right",
                        isPrimary: false,
                        isDestructive: true
                    )
                    .padding(.horizontal, 20)
                }
            }
        }
        // .navigationTitle(localizationManager.localizedString(LocalizationKey.profile))
        .navigationBarTitleDisplayMode(.inline)
        .background(
            Group { // Wrap hidden NavigationLinks in a Group
                NavigationLink(
                    destination: AchievementView(),
                    isActive: $navigateToAchievementView,
                    label: { EmptyView() }
                )
                .hidden()
            }
        )
        .sheet(isPresented: $showingSettings) {
            SettingsView()
                .environmentObject(errorManager)
        }
        .alert(isPresented: $showingLogoutAlert) {
            Alert(
                title: Text(localizationManager.localizedString(LocalizationKey.logoutConfirm)),
                message: Text(localizationManager.localizedString(LocalizationKey.logoutMessage)),
                primaryButton: .destructive(
                    Text(localizationManager.localizedString(LocalizationKey.logout))
                ) {
                    Task {
                        await userManager.logout()
                    }
                },
                secondaryButton: .cancel(
                    Text(localizationManager.localizedString(LocalizationKey.cancel)))
            )
        }
    }
}
