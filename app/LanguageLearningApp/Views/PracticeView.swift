import SwiftUI
import Foundation

struct PracticeView: View {
    @StateObject private var lessonManager: LessonManager = LessonManager.shared
    @StateObject private var practiceManager: PracticeManager = PracticeManager.shared
    @StateObject private var errorManager: ErrorManager = ErrorManager.shared
    @State private var selectedTab = 0
    @State private var path = NavigationPath()
    @State private var isRefreshing = false

    var body: some View {
        NavigationStack(path: $path) {
            StyledContainer {
                ScrollView {
                    VStack(spacing: 24) {
                        PracticeTypesSection()
                        RecommendedPracticeSection(lessonManager: lessonManager)
                        PracticeHistorySection(practiceManager: practiceManager)
                    }
                }
                .refreshable {
                    await refreshPracticeHistory()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .onChange(of: isRefreshing) { newValue in
                print("Practice refresh state changed to: \(newValue)")
            }
        }
    }

    private func refreshPracticeHistory() async {
        isRefreshing = true
        do {
            await practiceManager.loadPracticeHistoryFromAPI(completion: {
                isRefreshing = false
            })
        } catch {
            isRefreshing = false
        }
        if isRefreshing {
            isRefreshing = false
        }
    }
}

// MARK: - Practice Types Section
private struct PracticeTypesSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            StyledSectionHeader(title: "Practice Types")

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                PracticeTypeNavigationLink(
                    destination: WordLearningView(),
                    title: "Word Learning",
                    icon: "textformat.abc",
                    color: AppTheme.Colors.primary
                )

                PracticeTypeNavigationLink(
                    destination: ListeningPracticeView(),
                    title: "Listening Practice",
                    icon: "ear",
                    color: AppTheme.Colors.accent1
                )

                PracticeTypeNavigationLink(
                    destination: SpeakingPracticeView(),
                    title: "Speaking Practice",
                    icon: "mic",
                    color: AppTheme.Colors.secondary
                )

                PracticeTypeNavigationLink(
                    destination: GrammarPracticeView(path: .constant(NavigationPath())),
                    title: "Grammar Practice",
                    icon: "text.book.closed",
                    color: AppTheme.Colors.accent3
                )
            }
        }
    }
}

// MARK: - Practice Type Navigation Link
private struct PracticeTypeNavigationLink<Destination: View>: View {
    let destination: Destination
    let title: String
    let icon: String
    let color: Color

    var body: some View {
        NavigationLink(destination: destination) {
            PracticeTypeCard(
                title: title,
                icon: icon,
                color: color
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Recommended Practice Section
private struct RecommendedPracticeSection: View {
    let lessonManager: LessonManager

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            StyledSectionHeader(
                title: "Recommended Practice",
                subtitle: "Based on your learning progress"
            )

            if lessonManager.getRecommendedLessons().isEmpty {
                UnifiedEmptyStateView(
                    icon: "star",
                    title: "No recommendations yet",
                    message: "Complete more lessons to get personalized recommendations"
                )
            } else {
                VStack(spacing: 16) {
                    ForEach(lessonManager.getRecommendedLessons()) { lesson in
                        NavigationLink(destination: EnhancedLessonDetailView(lesson: lesson)) {
                            RecommendedPracticeCard(lesson: lesson)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
    }
}

// MARK: - Practice History Section
private struct PracticeHistorySection: View {
    let practiceManager: PracticeManager

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            StyledSectionHeader(
                title: "Practice History",
                subtitle: "Your recent practice sessions",
                action: {
                    // View all practice history
                },
                actionTitle: "View All"
            )

            if practiceManager.recentPractices.isEmpty {
                EmptyStateView(
                    icon: "clock",
                    title: "No practice history",
                    message: "Start practicing to see your history here"
                )
            } else {
                VStack(spacing: 16) {
                    ForEach(practiceManager.recentPractices) { practiceSession in
                        PracticeHistoryCard(practiceSession: practiceSession)
                    }
                }
            }
        }
    }
}

// MARK: - Empty State View
private struct EmptyStateView: View {
    let icon: String
    let title: String
    let message: String

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: icon)
                .font(.system(size: 50))
                .foregroundColor(AppTheme.Colors.textSecondary)

            Text(title)
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textSecondary)

            Text(message)
                .font(AppTheme.Typography.subheadline)
                .foregroundColor(AppTheme.Colors.textTertiary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 30)
    }
}

struct PracticeTypeCard: View {
    let title: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(color)
                    .frame(width: 60, height: 60)
                    .shadow(color: color.opacity(0.5), radius: 8, x: 0, y: 4)

                Image(systemName: icon)
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(.white)
            }

            Text(title)
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textPrimary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 20)
        .padding(.horizontal, 12)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

struct RecommendedPracticeCard: View {
    let lesson: Lesson

    var body: some View {
        HStack(spacing: 16) {
            // Category icon
            ZStack {
                Circle()
                    .fill(categoryColor)
                    .frame(width: 50, height: 50)

                Image(systemName: categoryIcon)
                    .font(.system(size: 20))
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: 6) {
                Text(lesson.title)
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                Text(lesson.description)
                    .font(AppTheme.Typography.footnote)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                    .lineLimit(2)

                HStack(spacing: 12) {
                    // Duration
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.system(size: 12))
                        Text("\(lesson.duration) min")
                    }
                    .font(AppTheme.Typography.caption1)
                    .foregroundColor(AppTheme.Colors.textSecondary)

                    // Points
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.system(size: 12))
                        Text("\(lesson.points) points")
                    }
                    .font(AppTheme.Typography.caption1)
                    .foregroundColor(AppTheme.Colors.accent2)
                }
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(AppTheme.Colors.textTertiary)
        }
        .padding(16)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }

    private var categoryColor: Color {
        switch lesson.category {
        case .vocabulary: return AppTheme.Colors.primary
        case .grammar: return AppTheme.Colors.secondary
        case .listening: return AppTheme.Colors.accent1
        case .speaking: return AppTheme.Colors.accent3
        case .reading: return AppTheme.Colors.accent2
        case .writing: return AppTheme.Colors.writing
        case .uncategorized: return AppTheme.Colors.uncategorized
        }
    }

    private var categoryIcon: String {
        switch lesson.category {
        case .vocabulary: return "textformat.abc"
        case .grammar: return "text.book.closed"
        case .listening: return "ear"
        case .speaking: return "mic"
        case .reading: return "book"
        case .writing: return "pencil"
        case .uncategorized: return "questionmark.circle"
        }
    }
}

struct PracticeHistoryCard: View {
    let practiceSession: PracticeSession

    init(practiceSession: PracticeSession) {
        self.practiceSession = practiceSession
    }

    var body: some View {
        HStack(spacing: 16) {
            // Practice type icon
            ZStack {
                Circle()
                    .fill(typeColor)
                    .frame(width: 50, height: 50)

                Image(systemName: typeIcon)
                    .font(.system(size: 20))
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: 6) {
                Text(practiceSession.type.displayName)
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                Text(practiceSession.endTime, format: .dateTime.day().month().year().hour().minute())
                    .font(AppTheme.Typography.footnote)
                    .foregroundColor(AppTheme.Colors.textSecondary)

                HStack(spacing: 12) {
                    // Score
                    HStack(spacing: 4) {
                        Image(systemName: "chart.bar.fill")
                            .font(.system(size: 12))
                        Text("Score: \(practiceSession.score)%")
                    }
                    .font(AppTheme.Typography.caption1)
                    .foregroundColor(scoreColor)

                    // Duration
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.system(size: 12))
                        Text("\(Int(practiceSession.duration / 60)) min")
                    }
                    .font(AppTheme.Typography.caption1)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                }
            }

            Spacer()

            // Score indicator
            ZStack {
                Circle()
                    .stroke(scoreColor.opacity(0.3), lineWidth: 3)
                    .frame(width: 40, height: 40)

                Circle()
                    .trim(from: 0, to: CGFloat(practiceSession.score) / 100)
                    .stroke(scoreColor, lineWidth: 3)
                    .frame(width: 40, height: 40)
                    .rotationEffect(.degrees(-90))

                Text("\(practiceSession.score)")
                    .font(AppTheme.Typography.caption1.bold())
                    .foregroundColor(scoreColor)
            }
        }
        .padding(16)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }

    private var typeColor: Color {
        switch practiceSession.type {
        case .vocabulary:
            return AppTheme.Colors.primary
        case .grammar:
            return AppTheme.Colors.secondary
        case .listening:
            return AppTheme.Colors.accent1
        case .speaking:
            return AppTheme.Colors.accent3
        case .reading:
            return AppTheme.Colors.accent2
        case .writing:
            return AppTheme.Colors.writing
        case .pronunciation:
            return AppTheme.Colors.accent3 // 使用与口语相同的颜色
        case .comprehensive:
            return AppTheme.Colors.primary // 使用主色调
        }
    }

    private var typeIcon: String {
        switch practiceSession.type {
        case .vocabulary:
            return "textformat.abc"
        case .grammar:
            return "text.book.closed"
        case .listening:
            return "ear"
        case .speaking:
            return "mic"
        case .reading:
            return "book"
        case .writing:
            return "pencil"
        case .pronunciation:
            return "waveform" // 发音练习使用波形图标
        case .comprehensive:
            return "square.stack.3d.up" // 综合练习使用堆叠图标
        }
    }

    private var scoreColor: Color {
        if practiceSession.score >= 80 {
            return AppTheme.Colors.success
        } else if practiceSession.score >= 60 {
            return AppTheme.Colors.warning
        } else {
            return AppTheme.Colors.error
        }
    }
}

struct DeveloperToolCard: View {
    let title: String
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 16) {
            // Icon
            ZStack {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 50, height: 50)

                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(color)
            }

            Text(title)
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textPrimary)

            Spacer()

            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(AppTheme.Colors.textTertiary)
        }
        .padding(16)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

#Preview {
    PracticeView()
}