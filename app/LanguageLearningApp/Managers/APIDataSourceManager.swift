import Foundation
import Combine

/// API 数据源管理器
/// 统一管理所有 API 调用和 Mock 数据的切换
public class APIDataSourceManager: ObservableObject {
    public static let shared = APIDataSourceManager()

    // MARK: - Published Properties

    /// 是否正在使用 Mock 数据
    @Published public var isUsingMockData: Bool = false

    /// API 连接状态
    @Published public var apiConnectionStatus: APIConnectionStatus = .unknown

    /// 最后一次 API 调用时间
    @Published public var lastAPICallTime: Date?

    // MARK: - Private Properties

    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    private init() {
        // 监听环境配置变化
        NotificationCenter.default.publisher(for: .environmentChanged)
            .sink { [weak self] _ in
                self?.updateMockDataStatus()
            }
            .store(in: &cancellables)

        // 初始化状态
        updateMockDataStatus()

        // 定期检查 API 连接状态
        startAPIHealthCheck()
    }

    // MARK: - Public Methods

    /// 获取词汇数据
    public func getWords(category: String? = nil, difficulty: String? = nil) async throws -> [Word] {
        // 暂时只返回 Mock 数据
        return Word.sampleWords
    }

    /// 获取成就数据
    public func getAchievements() async throws -> [Achievement] {
        // 暂时只返回 Mock 数据
        return Achievement.sampleAchievements
    }

    /// 获取用户成就数据
    public func getUserAchievements() async throws -> [UserAchievement] {
        // 暂时只返回 Mock 数据
        return UserAchievement.sampleUserAchievements
    }

    /// 获取语法练习
    func getGrammarExercises() async throws -> [GrammarExercise] {
        // 暂时只返回 Mock 数据
        return GrammarExercise.sampleExercises
    }

    /// 获取听力练习
    func getListeningExercises() async throws -> [ListeningExercise] {
        // 暂时只返回 Mock 数据
        return ListeningExercise.sampleExercises
    }

    /// 获取口语练习
    func getSpeakingExercises() async throws -> [SpeakingExercise] {
        // 暂时只返回 Mock 数据
        return SpeakingExercise.sampleExercises
    }

    /// 获取推荐练习
    func getRecommendedPractice() async throws -> [PracticeCardModel] {
        // 暂时只返回 Mock 数据
        return PracticeCardModel.samples
    }

    /// 获取练习历史
    public func getPracticeHistory() async throws -> [PracticeSession] {
        // 返回一些示例练习历史
        return []
    }

    /// 获取用户统计信息
    public func getUserStats() async throws -> UserStats {
        // 暂时只返回 Mock 数据
        return UserStats.sample
    }

    /// 提交练习结果
    func submitPracticeResult(endpoint: APIEndpoint) async throws -> APIResponse {
        // 模拟成功响应
        return APIResponse(isCorrect: true, accuracy: 0.9, message: "Mock submission successful", score: 100, points: 10)
    }

    // MARK: - Private Methods

    /// 检查是否应该使用 Mock 数据
    private func shouldUseMockData() -> Bool {
        return AppEnvironment.current.useMockData
    }

    /// 更新 Mock 数据状态
    private func updateMockDataStatus() {
        DispatchQueue.main.async {
            self.isUsingMockData = self.shouldUseMockData()
        }
    }

    /// 开始 API 健康检查
    private func startAPIHealthCheck() {
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await self?.checkAPIHealth()
                }
            }
            .store(in: &cancellables)
    }

    /// 检查 API 健康状态
    private func checkAPIHealth() async {
        DispatchQueue.main.async {
            if self.shouldUseMockData() {
                self.apiConnectionStatus = .mockData
            } else {
                // 暂时设置为已连接状态
                self.apiConnectionStatus = .connected
                self.lastAPICallTime = Date()
            }
        }
    }

    /// 强制刷新 API 连接状态
    public func refreshAPIStatus() async {
        await checkAPIHealth()
    }
}

// MARK: - Supporting Types

/// API 连接状态
public enum APIConnectionStatus {
    case unknown
    case connected
    case disconnected
    case mockData

    public var displayName: String {
        switch self {
        case .unknown:
            return "未知"
        case .connected:
            return "已连接"
        case .disconnected:
            return "已断开"
        case .mockData:
            return "Mock 数据"
        }
    }

    public var color: String {
        switch self {
        case .unknown:
            return "#808080"
        case .connected:
            return "#4CAF50"
        case .disconnected:
            return "#F44336"
        case .mockData:
            return "#FF9800"
        }
    }
}


