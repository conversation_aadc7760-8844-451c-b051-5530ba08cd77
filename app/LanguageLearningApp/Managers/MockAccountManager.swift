import Foundation

class MockAccountManager {
    static let shared = MockAccountManager()

    struct MockAccount {
        let username: String
        let password: String
        let role: String
    }

    let mockAccounts: [MockAccount] = [
        MockAccount(username: "demo", password: "demo123", role: "student"),
        <PERSON><PERSON><PERSON><PERSON>unt(username: "student", password: "student123", role: "student"),
        <PERSON><PERSON><PERSON><PERSON>unt(username: "teacher", password: "teacher123", role: "teacher"),
        <PERSON><PERSON><PERSON><PERSON>unt(username: "admin", password: "admin123", role: "admin"),
        <PERSON><PERSON>Account(username: "beginner", password: "beginner123", role: "beginner"),
        Mo<PERSON><PERSON>ccount(username: "advanced", password: "advanced123", role: "advanced")
    ]

    private init() {}

    func getMockAccount(username: String) -> MockAccount? {
        return mockAccounts.first { $0.username == username }
    }

    func validateMockAccount(username: String, password: String) -> <PERSON><PERSON> {
        guard let account = getMockAccount(username: username) else { return false }
        return account.password == password
    }
}