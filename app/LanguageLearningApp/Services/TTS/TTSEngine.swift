import Foundation
import AVFoundation // For AVSpeechSynthesisVoiceGender

// Information about an available TTS voice
struct VoiceInfo: Identifiable, Hashable, Sendable {
    let id: String // Engine-specific voice identifier
    let name: String
    let languageCode: String // BCP-47 language code (e.g., "en-US", "zh-CN")
    let quality: AVSpeechSynthesisVoiceQuality.RawValue? // Optional: .default, .enhanced, .premium
    let gender: AVSpeechSynthesisVoiceGender.RawValue? // Optional: .male, .female, .unspecified

    // Initializer for system voices
    init(id: String, name: String, languageCode: String, quality: AVSpeechSynthesisVoiceQuality? = nil, gender: AVSpeechSynthesisVoiceGender? = nil) {
        self.id = id
        self.name = name
        self.languageCode = languageCode
        self.quality = quality?.rawValue
        self.gender = gender?.rawValue
    }
    
    // Add other initializers if needed for other TTS engines that might have different voice metadata
}

// Protocol for Text-to-Speech engines
protocol TTSEngine: Sendable {
    var engineId: String { get } // Unique identifier for the engine (e.g., "system", "acapela")
    var engineName: String { get } // User-friendly name (e.g., "System Default", "Acapela Voices")
    var isAvailable: Bool { get async } // Is the engine configured and ready to use? (e.g., SDK initialized, API key set)

    func speak(text: String, languageCode: String, voiceIdentifier: String?, completion: @escaping @Sendable (Error?) -> Void) async
    
    // Get available voices - can be extensive, so might be good to filter by language
    func getAvailableVoices(forLanguageCode: String?) async -> [VoiceInfo]
    
    // Optional: Method to stop any ongoing speech
    func stopSpeaking() async
    
    // Optional: Method to handle delegate callbacks if needed for playback state
    // func setDelegate(_ delegate: TTSEngineDelegate) 
}

// Optional: Delegate protocol for more fine-grained playback control or status updates
// protocol TTSEngineDelegate: AnyObject {
//     func didStartSpeaking(engine: TTSEngine)
//     func didFinishSpeaking(engine: TTSEngine, successfully: Bool)
//     func didPauseSpeaking(engine: TTSEngine)
//     func didContinueSpeaking(engine: TTSEngine)
//     func didCancelSpeaking(engine: TTSEngine)
// }

// Add a default implementation for stopSpeaking if not all engines need it.
extension TTSEngine {
    func stopSpeaking() async {
        // Default: do nothing. Engines that support stopping should override this.
        print("\(engineName) does not support programmatic stopping or it's not implemented.")
    }
} 