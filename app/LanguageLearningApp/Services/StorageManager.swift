import Foundation
import SwiftUI

/// 负责应用数据的本地存储和读取
public class StorageManager: StorageManagerProtocol {
    public static let shared = StorageManager()

    private let userDefaults = UserDefaults.standard

    // MARK: - 键值常量
    private struct Keys {
        static let currentUser = "currentUser"
        static let isLoggedIn = "isLoggedIn"
        static let lessonProgress = "lessonProgress"
        static let achievements = "achievements"
        static let userAchievements = "userAchievements"
        static let appSettings = "appSettings"
        static let lastLoginDate = "lastLoginDate"
        static let lessons = "lessons"
        static let practices = "practices"
    }

    private init() {}

    // MARK: - 用户数据

    /// 保存当前用户信息
    public func saveCurrentUser(_ user: User) {
        do {
            let data = try JSONEncoder().encode(user)
            userDefaults.set(data, forKey: Keys.currentUser)
            userDefaults.synchronize()
        } catch {
            print("Error saving user: \(error.localizedDescription)")
        }
    }

    /// 读取当前用户信息
    public func loadCurrentUser() -> User? {
        guard let data = userDefaults.data(forKey: Keys.currentUser) else {
            return nil
        }

        do {
            let user = try JSONDecoder().decode(User.self, from: data)
            return user
        } catch {
            print("Error loading user: \(error.localizedDescription)")
            return nil
        }
    }

    /// 保存登录状态
    public func saveLoginState(_ isLoggedIn: Bool) {
        userDefaults.set(isLoggedIn, forKey: Keys.isLoggedIn)

        if isLoggedIn {
            userDefaults.set(Date(), forKey: Keys.lastLoginDate)
        }

        userDefaults.synchronize()
    }

    /// 读取登录状态
    public func loadLoginState() -> Bool {
        return userDefaults.bool(forKey: Keys.isLoggedIn)
    }

    /// 获取上次登录日期
    public func getLastLoginDate() -> Date? {
        return userDefaults.object(forKey: Keys.lastLoginDate) as? Date
    }

    // MARK: - 课程进度

    /// 保存课程进度
    public func saveLessonProgress(_ progress: [String: LessonProgress]) {
        do {
            let data = try JSONEncoder().encode(progress)
            userDefaults.set(data, forKey: Keys.lessonProgress)
            userDefaults.synchronize()
        } catch {
            print("Error saving lesson progress: \(error.localizedDescription)")
        }
    }

    /// 读取课程进度
    public func loadLessonProgress() -> [String: LessonProgress] {
        guard let data = userDefaults.data(forKey: Keys.lessonProgress) else {
            return [:]
        }
        do {
            let progress = try JSONDecoder().decode([String: LessonProgress].self, from: data)
            return progress
        } catch {
            print("Error loading lesson progress: \(error.localizedDescription)")
            return [:]
        }
    }

    /// 保存收藏课程
    public func saveFavoriteLessons(_ favorites: [String]) {
        do {
            let data = try JSONEncoder().encode(favorites)
            userDefaults.set(data, forKey: "favoriteLessons")
            userDefaults.synchronize()
        } catch {
            print("Error saving favorite lessons: \(error.localizedDescription)")
        }
    }

    /// 加载收藏课程
    public func loadFavoriteLessons() -> [String] {
        guard let data = userDefaults.data(forKey: "favoriteLessons") else {
            return []
        }

        do {
            let favorites = try JSONDecoder().decode([String].self, from: data)
            return favorites
        } catch {
            print("Error loading favorite lessons: \(error.localizedDescription)")
            return []
        }
    }

    // MARK: - 成就

    /// 保存成就列表
    public func saveAchievements(_ achievements: [Achievement]) {
        do {
            let data = try JSONEncoder().encode(achievements)
            userDefaults.set(data, forKey: Keys.achievements)
            userDefaults.synchronize()
        } catch {
            print("Error saving achievements: \(error.localizedDescription)")
        }
    }

    /// 读取成就列表
    public func loadAchievements() -> [Achievement]? {
        guard let data = userDefaults.data(forKey: Keys.achievements) else {
            return nil
        }

        do {
            let achievements = try JSONDecoder().decode([Achievement].self, from: data)
            return achievements
        } catch {
            print("Error loading achievements: \(error.localizedDescription)")
            return nil
        }
    }

    /// 保存用户成就
    public func saveUserAchievements(_ userAchievements: [UserAchievement]) {
        do {
            let data = try JSONEncoder().encode(userAchievements)
            userDefaults.set(data, forKey: Keys.userAchievements)
            userDefaults.synchronize()
        } catch {
            print("Error saving user achievements: \(error.localizedDescription)")
        }
    }

    /// 读取用户成就
    public func loadUserAchievements() -> [UserAchievement]? {
        guard let data = userDefaults.data(forKey: Keys.userAchievements) else {
            return nil
        }

        do {
            let userAchievements = try JSONDecoder().decode([UserAchievement].self, from: data)
            return userAchievements
        } catch {
            print("Error loading user achievements: \(error.localizedDescription)")
            return nil
        }
    }

    // MARK: - 应用设置

    /// 保存应用设置 (Dictionary 版本, 仅供内部使用)
    private func saveAppSettings(_ settings: [String: Any]) {
        userDefaults.set(settings, forKey: Keys.appSettings)
        userDefaults.synchronize()
    }

    /// 读取应用设置 (Dictionary 版本, 仅供内部使用)
    private func loadAppSettingsDictionary() -> [String: Any]? {
        return userDefaults.dictionary(forKey: Keys.appSettings)
    }

    /// 保存应用设置 (UserSettings版本)
    public func saveAppSettings(_ settings: UserSettings) {
        do {
            let data = try JSONEncoder().encode(settings)
            userDefaults.set(data, forKey: "appSettingsObject")
            userDefaults.synchronize()
        } catch {
            print("Error saving app settings: \(error.localizedDescription)")
        }
    }

    /// 加载应用设置 (UserSettings版本)
    public func loadAppSettings() -> UserSettings? {
        guard let data = userDefaults.data(forKey: "appSettingsObject") else {
            return nil
        }
        do {
            let settings = try JSONDecoder().decode(UserSettings.self, from: data)
            return settings
        } catch {
            print("Error loading app settings: \(error.localizedDescription)")
            return nil
        }
    }

    /// 保存TTS设置
    public func saveTTSSettings(_ settings: TTSSettings) {
        do {
            let data = try JSONEncoder().encode(settings)
            userDefaults.set(data, forKey: "ttsSettings")
            userDefaults.synchronize()
        } catch {
            print("Error saving TTS settings: \(error.localizedDescription)")
        }
    }

    /// 加载TTS设置
    public func loadTTSSettings() -> TTSSettings? {
        guard let data = userDefaults.data(forKey: "ttsSettings") else {
            return nil
        }
        do {
            let settings = try JSONDecoder().decode(TTSSettings.self, from: data)
            return settings
        } catch {
            print("Error loading TTS settings: \(error.localizedDescription)")
            return nil
        }
    }

    // MARK: - 课程数据

    /// 保存课程列表
    public func saveLessons(_ lessons: [Lesson]) {
        do {
            let data = try JSONEncoder().encode(lessons)
            userDefaults.set(data, forKey: Keys.lessons)
            userDefaults.synchronize()
        } catch {
            print("Error saving lessons: \(error.localizedDescription)")
        }
    }

    /// 读取课程列表
    public func loadLessons() -> [Lesson] {
        guard let data = userDefaults.data(forKey: Keys.lessons) else {
            return []
        }
        do {
            let lessons = try JSONDecoder().decode([Lesson].self, from: data)
            return lessons
        } catch {
            print("Error loading lessons: \(error.localizedDescription)")
            return []
        }
    }

    // MARK: - Practice Storage

    /// 保存练习记录（已弃用，使用 savePracticeSessions 代替）
    @available(*, deprecated, message: "Use savePracticeSessions instead")
    func savePractices(_ sessions: [PracticeSession]) {
        savePracticeSessions(sessions)
    }

    /// 读取练习记录（已弃用，使用 loadPracticeSessions 代替）
    @available(*, deprecated, message: "Use loadPracticeSessions instead")
    func loadPractices() -> [PracticeSession] {
        return loadPracticeSessions()
    }

    /// 保存练习会话
    public func savePracticeSessions(_ sessions: [PracticeSession]) {
        do {
            let data = try JSONEncoder().encode(sessions)
            userDefaults.set(data, forKey: Keys.practices)
            userDefaults.synchronize()
        } catch {
            print("Error saving practice sessions: \(error.localizedDescription)")
        }
    }

    /// 加载练习会话
    public func loadPracticeSessions() -> [PracticeSession] {
        guard let data = userDefaults.data(forKey: Keys.practices) else {
            return []
        }
        do {
            let sessions = try JSONDecoder().decode([PracticeSession].self, from: data)
            return sessions
        } catch {
            print("Error loading practice sessions: \(error.localizedDescription)")
            return []
        }
    }

    /// 清除练习记录
    func clearPractices() {
        userDefaults.removeObject(forKey: Keys.practices)
        userDefaults.synchronize()
    }

    // MARK: - 清除数据

    /// 清除所有数据
    public func clearAllData() {
        let domain = Bundle.main.bundleIdentifier!
        userDefaults.removePersistentDomain(forName: domain)
        userDefaults.synchronize()
    }

    /// 清除特定类型的数据
    public func clearData(ofType type: StorageDataType) {
        switch type {
        case .user:
            userDefaults.removeObject(forKey: Keys.currentUser)
            userDefaults.removeObject(forKey: Keys.isLoggedIn)
            userDefaults.removeObject(forKey: Keys.lastLoginDate)
        case .lessons:
            userDefaults.removeObject(forKey: Keys.lessons)
            userDefaults.removeObject(forKey: Keys.lessonProgress)
        case .achievements:
            userDefaults.removeObject(forKey: Keys.achievements)
            userDefaults.removeObject(forKey: Keys.userAchievements)
        case .settings:
            userDefaults.removeObject(forKey: Keys.appSettings)
            userDefaults.removeObject(forKey: "appSettingsObject")
            userDefaults.removeObject(forKey: "ttsSettings")
        case .practices:
            userDefaults.removeObject(forKey: Keys.practices)
        }
        userDefaults.synchronize()
    }

    // MARK: - 离线功能支持

    /// 获取缓存大小
    public func getCacheSize() -> Int64 {
        var totalSize: Int64 = 0

        // 计算 UserDefaults 中存储的数据大小
        let keys = [Keys.currentUser, Keys.lessonProgress, Keys.achievements,
                   Keys.userAchievements, Keys.lessons, Keys.practices]

        for key in keys {
            if let data = userDefaults.data(forKey: key) {
                totalSize += Int64(data.count)
            }
        }

        return totalSize
    }

    /// 清除缓存
    public func clearCache() async throws {
        // 清除过期的缓存数据
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()

        // 这里可以实现更精细的缓存清理逻辑
        // 例如只清除过期的课程数据、练习记录等

        // 暂时不实现具体逻辑，避免误删重要数据
    }

    /// 保存词汇数据
    public func saveVocabulary(_ vocabulary: [Word]) async throws {
        do {
            let data = try JSONEncoder().encode(vocabulary)
            userDefaults.set(data, forKey: "vocabulary")
            userDefaults.synchronize()
        } catch {
            throw error
        }
    }

    /// 保存评估数据
    public func saveEvaluations(_ evaluations: [Evaluation]) async throws {
        do {
            let data = try JSONEncoder().encode(evaluations)
            userDefaults.set(data, forKey: "evaluations")
            userDefaults.synchronize()
        } catch {
            throw error
        }
    }

    /// 保存用户数据
    public func saveUserData(_ userData: User) async throws {
        saveCurrentUser(userData)
    }
}
