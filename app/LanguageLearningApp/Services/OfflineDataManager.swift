import Foundation
import Combine
import CoreData

/// 离线数据管理器协议
protocol OfflineDataManagerProtocol {
    /// 是否处于离线模式
    var isOfflineMode: Bool { get }

    /// 缓存大小（字节）
    var cacheSize: Int64 { get }

    /// 最后同步时间
    var lastSyncTime: Date? { get }

    /// 待同步的数据数量
    var pendingSyncCount: Int { get }

    /// 开始离线模式
    func enableOfflineMode()

    /// 结束离线模式
    func disableOfflineMode()

    /// 同步数据到服务器
    func syncToServer() async throws

    /// 从服务器同步数据
    func syncFromServer() async throws

    /// 清除缓存
    func clearCache() async throws

    /// 获取缓存统计信息
    func getCacheStatistics() -> OfflineCacheStatistics
}

/// 离线缓存统计信息
struct OfflineCacheStatistics {
    let totalSize: Int64
    let itemCount: Int
    let lastSyncTime: Date?
    let pendingSyncCount: Int
    let categories: [String: Int64] // 分类缓存大小
}

/// 离线数据管理器
@MainActor
class OfflineDataManager: ObservableObject, OfflineDataManagerProtocol {
    static let shared = OfflineDataManager()

    // MARK: - Published Properties
    @Published var isOfflineMode: Bool = false
    @Published var isSyncing: Bool = false
    @Published var syncProgress: Double = 0.0
    @Published var lastSyncError: Error?

    // MARK: - Private Properties
    private let userDefaults = UserDefaults.standard
    private let localizationManager = LocalizationManager.shared
    private let errorManager = ErrorManager.shared
    private var cancellables = Set<AnyCancellable>()

    // 依赖注入
    private let networkService: NetworkServiceProtocol
    private let storageManager: StorageManagerProtocol
    private let coreDataManager: CoreDataManagerProtocol

    // 缓存限制
    private let maxCacheSize: Int64 = 500 * 1024 * 1024 // 500MB
    private let maxCacheAge: TimeInterval = 7 * 24 * 60 * 60 // 7天

    // MARK: - Computed Properties
    var cacheSize: Int64 {
        return calculateCacheSize()
    }

    var lastSyncTime: Date? {
        return userDefaults.object(forKey: "LastSyncTime") as? Date
    }

    var pendingSyncCount: Int {
        return getPendingSyncCount()
    }

    // MARK: - Initialization
    private init(
        networkService: NetworkServiceProtocol = NetworkService.shared,
        storageManager: StorageManagerProtocol = StorageManager.shared,
        coreDataManager: CoreDataManagerProtocol = CoreDataManager.shared
    ) {
        self.networkService = networkService
        self.storageManager = storageManager
        self.coreDataManager = coreDataManager

        setupNetworkMonitoring()
        loadOfflineSettings()
    }

    // MARK: - Setup
    private func setupNetworkMonitoring() {
        // 监听网络状态变化
        NotificationCenter.default.publisher(for: .networkStatusChanged)
            .sink { [weak self] notification in
                if let isConnected = notification.userInfo?["isConnected"] as? Bool {
                    self?.handleNetworkStatusChange(isConnected: isConnected)
                }
            }
            .store(in: &cancellables)
    }

    private func loadOfflineSettings() {
        isOfflineMode = userDefaults.bool(forKey: "OfflineModeEnabled")
    }

    // MARK: - Public Methods
    func enableOfflineMode() {
        isOfflineMode = true
        userDefaults.set(true, forKey: "OfflineModeEnabled")

        // 通知用户进入离线模式
        let message = localizationManager.localizedString(LocalizationKey.offline_mode)
        errorManager.showError(message: message, severity: .info)

        // 发送通知
        NotificationCenter.default.post(name: .offlineModeEnabled, object: nil)
    }

    func disableOfflineMode() {
        isOfflineMode = false
        userDefaults.set(false, forKey: "OfflineModeEnabled")

        // 发送通知
        NotificationCenter.default.post(name: .offlineModeDisabled, object: nil)

        // 尝试同步数据
        Task {
            try? await syncToServer()
        }
    }

    func syncToServer() async throws {
        guard !isSyncing else { return }

        isSyncing = true
        syncProgress = 0.0

        do {
            // 获取待同步的数据
            let pendingData = try await getPendingSyncData()

            // 分批同步数据
            let batchSize = 10
            let totalBatches = (pendingData.count + batchSize - 1) / batchSize

            for (index, batch) in pendingData.chunked(into: batchSize).enumerated() {
                try await syncBatchToServer(batch)
                syncProgress = Double(index + 1) / Double(totalBatches)
            }

            // 更新最后同步时间
            userDefaults.set(Date(), forKey: "LastSyncTime")

            // 显示成功消息
            let message = localizationManager.localizedString(LocalizationKey.success_data_synced)
            errorManager.showError(message: message, severity: .info)

        } catch {
            lastSyncError = error
            let message = localizationManager.localizedString(LocalizationKey.sync_failed)
            errorManager.showError(message: message, severity: .error)
            isSyncing = false
            syncProgress = 0.0
            throw error
        }

        isSyncing = false
        syncProgress = 0.0
    }

    func syncFromServer() async throws {
        guard !isSyncing else { return }

        isSyncing = true
        syncProgress = 0.0

        do {
            // 同步各种类型的数据
            try await syncLessonsFromServer()
            syncProgress = 0.2

            try await syncVocabularyFromServer()
            syncProgress = 0.4

            try await syncEvaluationsFromServer()
            syncProgress = 0.6

            try await syncAchievementsFromServer()
            syncProgress = 0.8

            try await syncUserDataFromServer()
            syncProgress = 1.0

            // 更新最后同步时间
            userDefaults.set(Date(), forKey: "LastSyncTime")

        } catch {
            lastSyncError = error
            isSyncing = false
            syncProgress = 0.0
            throw error
        }

        isSyncing = false
        syncProgress = 0.0
    }

    func clearCache() async throws {
        do {
            // 清除各种缓存
            try await coreDataManager.clearCache()
            try await storageManager.clearCache()

            // 清除用户偏好中的缓存设置
            userDefaults.removeObject(forKey: "LastSyncTime")

            let message = localizationManager.localizedString(LocalizationKey.cache_cleared)
            errorManager.showError(message: message, severity: .info)

        } catch {
            let message = localizationManager.localizedString(LocalizationKey.error_cache_operation)
            errorManager.showError(message: message, severity: .error)
            throw error
        }
    }

    func getCacheStatistics() -> OfflineCacheStatistics {
        let categories = [
            "lessons": calculateCategoryCacheSize("lessons"),
            "vocabulary": calculateCategoryCacheSize("vocabulary"),
            "evaluations": calculateCategoryCacheSize("evaluations"),
            "achievements": calculateCategoryCacheSize("achievements"),
            "user_data": calculateCategoryCacheSize("user_data")
        ]

        return OfflineCacheStatistics(
            totalSize: cacheSize,
            itemCount: getTotalCacheItemCount(),
            lastSyncTime: lastSyncTime,
            pendingSyncCount: pendingSyncCount,
            categories: categories
        )
    }
}

// MARK: - Private Methods
private extension OfflineDataManager {
    func handleNetworkStatusChange(isConnected: Bool) {
        if isConnected && !isOfflineMode {
            // 网络恢复，尝试同步
            Task {
                try? await syncToServer()
            }
        } else if !isConnected {
            // 网络断开，自动进入离线模式
            enableOfflineMode()
        }
    }

    func calculateCacheSize() -> Int64 {
        // 计算总缓存大小
        var totalSize: Int64 = 0

        // Core Data 缓存大小
        totalSize += coreDataManager.getCacheSize()

        // 文件缓存大小
        totalSize += storageManager.getCacheSize()

        return totalSize
    }

    func calculateCategoryCacheSize(_ category: String) -> Int64 {
        // 根据分类计算缓存大小
        return coreDataManager.getCacheSize(for: category)
    }

    func getTotalCacheItemCount() -> Int {
        return coreDataManager.getCacheItemCount()
    }

    func getPendingSyncCount() -> Int {
        return coreDataManager.getPendingSyncCount()
    }

    func getPendingSyncData() async throws -> [Any] {
        return try await coreDataManager.getPendingSyncData()
    }

    func syncBatchToServer(_ batch: [Any]) async throws {
        // 实现批量同步逻辑
        for data in batch {
            // 这里需要根据实际的网络服务API来实现
            // try await networkService.syncData(data)
            print("Syncing data: \(data)")
        }
    }
}

// MARK: - Sync Methods
private extension OfflineDataManager {
    func syncLessonsFromServer() async throws {
        // 同步课程数据
        // 这里需要根据实际的API端点来实现
        // let lessons: [Lesson] = try await networkService.request(.lessons)
        // storageManager.saveLessons(lessons)
        print("Syncing lessons from server")
    }

    func syncVocabularyFromServer() async throws {
        // 同步词汇数据
        // let vocabulary: [Vocabulary] = try await networkService.request(.vocabulary)
        // try await storageManager.saveVocabulary(vocabulary)
        print("Syncing vocabulary from server")
    }

    func syncEvaluationsFromServer() async throws {
        // 同步评估数据
        // let evaluations: [Evaluation] = try await networkService.request(.evaluations)
        // try await storageManager.saveEvaluations(evaluations)
        print("Syncing evaluations from server")
    }

    func syncAchievementsFromServer() async throws {
        // 同步成就数据
        // let achievements: [Achievement] = try await networkService.request(.achievements)
        // storageManager.saveAchievements(achievements)
        print("Syncing achievements from server")
    }

    func syncUserDataFromServer() async throws {
        // 同步用户数据
        // let userData: User = try await networkService.request(.userProfile)
        // try await storageManager.saveUserData(userData)
        print("Syncing user data from server")
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let offlineModeEnabled = Notification.Name("offlineModeEnabled")
    static let offlineModeDisabled = Notification.Name("offlineModeDisabled")
    static let networkStatusChanged = Notification.Name("networkStatusChanged")
    static let syncProgressUpdated = Notification.Name("syncProgressUpdated")
}

// MARK: - Helper Extensions
extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}

// MARK: - Syncable Data Protocol
protocol SyncableData {
    var id: UUID { get }
    var lastModified: Date { get }
    var syncStatus: OfflineSyncStatus { get set }
}

enum OfflineSyncStatus {
    case synced
    case pending
    case failed
}
