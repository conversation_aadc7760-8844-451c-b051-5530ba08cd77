import Foundation
import Combine

/// 仓库错误类型
public enum RepositoryError: Error, Equatable {
    case localDataSourceError(Error)
    case remoteDataSourceError(Error)
    case entityNotFound
    case saveFailed
    case deleteFailed
    case unknown
    case authenticationRequired
    case operationNotSupported(String)
    
    public static func == (lhs: RepositoryError, rhs: RepositoryError) -> Bool {
        switch (lhs, rhs) {
        case (.entityNotFound, .entityNotFound),
             (.saveFailed, .saveFailed),
             (.deleteFailed, .deleteFailed),
             (.unknown, .unknown),
             (.authenticationRequired, .authenticationRequired):
            return true
        case (.localDataSourceError(let lhsError), .localDataSourceError(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        case (.remoteDataSourceError(let lhsError), .remoteDataSourceError(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        case (.operationNotSupported(let lhsMsg), .operationNotSupported(let rhsMsg)):
            return lhsMsg == rhsMsg
        default:
            return false
        }
    }
    
    var localizedDescription: String {
        switch self {
        case .localDataSourceError(let error):
            return "本地数据源错误: \(error.localizedDescription)"
        case .remoteDataSourceError(let error):
            return "远程数据源错误: \(error.localizedDescription)"
        case .entityNotFound:
            return "实体未找到"
        case .saveFailed:
            return "保存失败"
        case .deleteFailed:
            return "删除失败"
        case .unknown:
            return "未知错误"
        case .authenticationRequired:
            return "Authentication is required for this operation."
        case .operationNotSupported(let message):
            return "Operation not supported: \(message)"
        }
    }
}

/// 基础仓库实现
open class BaseRepository<T, ID: Hashable, LocalDS: LocalDataSourceProtocol, RemoteDS: RemoteDataSourceProtocol>: RepositoryProtocol 
    where LocalDS.T == T, LocalDS.ID == ID, RemoteDS.T == T, RemoteDS.ID == ID {
    
    // 本地数据源
    internal let localDataSource: LocalDS
    
    // 远程数据源
    internal let remoteDataSource: RemoteDS
    
    // 是否应该首先尝试从远程获取数据
    private let shouldFetchRemoteFirst: Bool
    
    /// 初始化方法
    /// - Parameters:
    ///   - localDataSource: 本地数据源
    ///   - remoteDataSource: 远程数据源
    ///   - shouldFetchRemoteFirst: 是否应该首先尝试从远程获取数据
    public init(localDataSource: LocalDS, remoteDataSource: RemoteDS, shouldFetchRemoteFirst: Bool = true) {
        self.localDataSource = localDataSource
        self.remoteDataSource = remoteDataSource
        self.shouldFetchRemoteFirst = shouldFetchRemoteFirst
    }
    
    /// 获取所有实体
    /// - Returns: 包含所有实体的发布者
    open func getAll() -> AnyPublisher<[T], Error> {
        if shouldFetchRemoteFirst {
            return getRemoteAll()
                .flatMap { [weak self] remoteEntities -> AnyPublisher<[T], Error> in
                    guard let self = self else {
                        return Fail<[T], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                    }
                    
                    // Remote succeeded, save to local and return remote entities
                    return self.saveAllToLocal(remoteEntities).map { _ in remoteEntities }.eraseToAnyPublisher()
                }
                .catch { [weak self] error -> AnyPublisher<[T], Error> in
                    guard let self = self else {
                        return Fail<[T], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                    }
                    
                    // Remote failed, return local entities
                    return self.getLocalAll()
                }
                .eraseToAnyPublisher()
        } else {
            return getLocalAll()
                .catch { [weak self] error -> AnyPublisher<[T], Error> in
                    guard let self = self else {
                        return Fail<[T], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                    }
                    
                    // 如果本地获取失败，尝试从远程获取
                    return self.getRemoteAll()
                        .flatMap { remoteEntities -> AnyPublisher<[T], Error> in
                            // 保存远程数据到本地
                            return self.saveAllToLocal(remoteEntities)
                        }
                        .eraseToAnyPublisher()
                }
                .eraseToAnyPublisher()
        }
    }
    
    /// 根据ID获取单个实体
    /// - Parameter id: 实体标识符
    /// - Returns: 包含实体的发布者
    open func getById(_ id: ID) -> AnyPublisher<T?, Error> {
        if shouldFetchRemoteFirst {
            return getRemoteById(id)
                .flatMap { [weak self] remoteEntity -> AnyPublisher<T?, Error> in
                    guard let self = self else {
                        return Fail<T?, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                    }
                    
                    // Remote succeeded, save to local and return remote entity (as optional)
                    return self.saveToLocal(remoteEntity).map { $0 as T? }.eraseToAnyPublisher()
                }
                .catch { [weak self] error -> AnyPublisher<T?, Error> in
                    guard let self = self else {
                        return Fail<T?, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                    }
                    
                    // Remote failed, return local entity
                    return self.getLocalById(id)
                }
                .eraseToAnyPublisher()
        } else {
            return getLocalById(id)
                .catch { [weak self] error -> AnyPublisher<T?, Error> in
                    guard let self = self else {
                        return Fail<T?, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                    }
                    
                    // 如果本地获取失败，尝试从远程获取
                    return self.getRemoteById(id)
                        .flatMap { remoteEntity -> AnyPublisher<T?, Error> in
                            // 保存远程数据到本地
                            return self.saveToLocal(remoteEntity)
                                .map { Optional($0) }
                                .eraseToAnyPublisher()
                        }
                        .eraseToAnyPublisher()
                }
                .eraseToAnyPublisher()
        }
    }
    
    /// 保存单个实体
    /// - Parameter entity: 要保存的实体
    /// - Returns: 包含保存后实体的发布者
    open func save(_ entity: T) -> AnyPublisher<T, Error> {
        return remoteDataSource.save(entity)
            .flatMap { [weak self] savedEntity -> AnyPublisher<T, Error> in
                guard let self = self else {
                    return Fail<T, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                }
                
                // 保存到本地
                return self.saveToLocal(savedEntity)
            }
            .catch { [weak self] error -> AnyPublisher<T, Error> in
                guard let self = self else {
                    return Fail<T, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                }
                
                // 如果远程保存失败，只保存到本地
                return self.saveToLocal(entity)
            }
            .eraseToAnyPublisher()
    }
    
    /// 保存多个实体
    /// - Parameter entities: 要保存的实体列表
    /// - Returns: 包含保存后实体列表的发布者
    open func saveAll(_ entities: [T]) -> AnyPublisher<[T], Error> {
        return remoteDataSource.saveAll(entities)
            .flatMap { [weak self] savedEntities -> AnyPublisher<[T], Error> in
                guard let self = self else {
                    return Fail<[T], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                }
                
                // 保存到本地
                return self.saveAllToLocal(savedEntities)
            }
            .catch { [weak self] error -> AnyPublisher<[T], Error> in
                guard let self = self else {
                    return Fail<[T], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher()
                }
                
                // 如果远程保存失败，只保存到本地
                return self.saveAllToLocal(entities)
            }
            .eraseToAnyPublisher()
    }
    
    /// 删除单个实体
    /// - Parameter id: 要删除的实体标识符
    /// - Returns: 包含删除成功标志的发布者
    open func delete(_ id: ID) -> AnyPublisher<Bool, Error> {
        return remoteDataSource.delete(id)
            .flatMap { [weak self] success -> AnyPublisher<Bool, Error> in
                guard let self = self, success else {
                    return Just(false).setFailureType(to: Error.self).eraseToAnyPublisher()
                }
                
                // 从本地删除
                return self.deleteFromLocal(id)
            }
            .eraseToAnyPublisher()
    }
    
    // MARK: - 异步方法
    
    /// 异步获取所有实体
    /// - Returns: 实体列表
    open func getAllAsync() async throws -> [T] {
        if shouldFetchRemoteFirst {
            do {
                // 尝试从远程获取
                let remoteEntities = try await remoteDataSource.getAllAsync()
                
                // 保存到本地
                _ = try localDataSource.saveAll(remoteEntities)
                
                return remoteEntities
            } catch {
                // 如果远程获取失败，尝试从本地获取
                return try localDataSource.getAll()
            }
        } else {
            do {
                // 尝试从本地获取
                return try localDataSource.getAll()
            } catch {
                // 如果本地获取失败，尝试从远程获取
                let remoteEntities = try await remoteDataSource.getAllAsync()
                
                // 保存到本地
                _ = try localDataSource.saveAll(remoteEntities)
                
                return remoteEntities
            }
        }
    }
    
    /// 异步根据ID获取单个实体
    /// - Parameter id: 实体标识符
    /// - Returns: 实体（如存在）
    open func getByIdAsync(_ id: ID) async throws -> T? {
        if shouldFetchRemoteFirst {
            do {
                // 尝试从远程获取
                let remoteEntity = try await remoteDataSource.getByIdAsync(id)
                
                // 保存到本地
                _ = try localDataSource.save(remoteEntity)
                
                return remoteEntity
            } catch {
                // 如果远程获取失败，尝试从本地获取
                return try localDataSource.getById(id)
            }
        } else {
            // 尝试从本地获取
            if let localEntity = try localDataSource.getById(id) {
                return localEntity
            }
            
            // 如果本地获取失败，尝试从远程获取
            do {
                let remoteEntity = try await remoteDataSource.getByIdAsync(id)
                
                // 保存到本地
                _ = try localDataSource.save(remoteEntity)
                
                return remoteEntity
            } catch {
                return nil
            }
        }
    }
    
    /// 异步保存单个实体
    /// - Parameter entity: 要保存的实体
    /// - Returns: 保存后的实体
    open func saveAsync(_ entity: T) async throws -> T {
        do {
            // 尝试保存到远程
            let remoteEntity = try await remoteDataSource.saveAsync(entity)
            
            // 保存到本地
            _ = try localDataSource.save(remoteEntity)
            
            return remoteEntity
        } catch {
            // 如果远程保存失败，只保存到本地
            return try localDataSource.save(entity)
        }
    }
    
    /// 异步保存多个实体
    /// - Parameter entities: 要保存的实体列表
    /// - Returns: 保存后的实体列表
    open func saveAllAsync(_ entities: [T]) async throws -> [T] {
        do {
            // 尝试保存到远程
            let remoteEntities = try await remoteDataSource.saveAllAsync(entities)
            
            // 保存到本地
            _ = try localDataSource.saveAll(remoteEntities)
            
            return remoteEntities
        } catch {
            // 如果远程保存失败，只保存到本地
            return try localDataSource.saveAll(entities)
        }
    }
    
    /// 异步删除单个实体
    /// - Parameter id: 要删除的实体标识符
    /// - Returns: 删除是否成功
    open func deleteAsync(_ id: ID) async throws -> Bool {
        do {
            // 尝试从远程删除
            let success = try await remoteDataSource.deleteAsync(id)
            
            if success {
                // 如果远程删除成功，也从本地删除
                return try localDataSource.delete(id)
            }
            
            return false
        } catch {
            // 如果远程删除失败，仍然尝试从本地删除
            return try localDataSource.delete(id)
        }
    }
    
    // MARK: - 私有辅助方法
    
    /// 从本地获取所有实体
    /// - Returns: 包含本地实体的发布者
    private func getLocalAll() -> AnyPublisher<[T], Error> {
        return Future<[T], Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.unknown))
                return
            }
            
            do {
                let entities = try self.localDataSource.getAll()
                promise(.success(entities))
            } catch {
                promise(.failure(RepositoryError.localDataSourceError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 从远程获取所有实体
    /// - Returns: 包含远程实体的发布者
    private func getRemoteAll() -> AnyPublisher<[T], Error> {
        return remoteDataSource.getAll()
            .mapError { RepositoryError.remoteDataSourceError($0) }
            .eraseToAnyPublisher()
    }
    
    /// 从本地通过ID获取实体
    /// - Parameter id: 实体标识符
    /// - Returns: 包含本地实体的发布者
    private func getLocalById(_ id: ID) -> AnyPublisher<T?, Error> {
        return Future<T?, Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.unknown))
                return
            }
            
            do {
                let entity = try self.localDataSource.getById(id)
                promise(.success(entity))
            } catch {
                promise(.failure(RepositoryError.localDataSourceError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 从远程通过ID获取实体
    /// - Parameter id: 实体标识符
    /// - Returns: 包含远程实体的发布者
    private func getRemoteById(_ id: ID) -> AnyPublisher<T, Error> {
        return remoteDataSource.getById(id)
            .mapError { RepositoryError.remoteDataSourceError($0) }
            .eraseToAnyPublisher()
    }
    
    /// 保存实体到本地
    /// - Parameter entity: 要保存的实体
    /// - Returns: 包含保存后实体的发布者
    private func saveToLocal(_ entity: T) -> AnyPublisher<T, Error> {
        return Future<T, Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.unknown))
                return
            }
            
            do {
                let savedEntity = try self.localDataSource.save(entity)
                promise(.success(savedEntity))
            } catch {
                promise(.failure(RepositoryError.localDataSourceError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 保存多个实体到本地
    /// - Parameter entities: 要保存的实体列表
    /// - Returns: 包含保存后实体列表的发布者
    private func saveAllToLocal(_ entities: [T]) -> AnyPublisher<[T], Error> {
        return Future<[T], Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.unknown))
                return
            }
            
            do {
                let savedEntities = try self.localDataSource.saveAll(entities)
                promise(.success(savedEntities))
            } catch {
                promise(.failure(RepositoryError.localDataSourceError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 从本地删除实体
    /// - Parameter id: 要删除的实体标识符
    /// - Returns: 包含删除成功标志的发布者
    private func deleteFromLocal(_ id: ID) -> AnyPublisher<Bool, Error> {
        return Future<Bool, Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.unknown))
                return
            }
            
            do {
                let success = try self.localDataSource.delete(id)
                promise(.success(success))
            } catch {
                promise(.failure(RepositoryError.localDataSourceError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
}
