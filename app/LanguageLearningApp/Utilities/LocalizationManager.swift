import Foundation

class LocalizationManager: ObservableObject, LocalizationManagerProtocol {
    static let shared = LocalizationManager()

    @Published var currentLanguage: String {
        didSet {
            UserDefaults.standard.set(currentLanguage, forKey: "AppLanguage")
            updateLanguage()
            NotificationCenter.default.post(name: .languageDidChange, object: nil)
        }
    }

    let supportedLanguages = [
        ("en", "English"),
        ("zh-<PERSON>", "简体中文"),
        ("zh-Hant", "繁體中文"),
        ("yue", "粤语")
    ]

    private var bundle: Bundle?

    private init() {
        // 从 UserDefaults 获取保存的语言设置，如果没有则使用系统语言
        if let savedLanguage = UserDefaults.standard.string(forKey: "AppLanguage") {
            currentLanguage = savedLanguage
        } else {
            if #available(macOS 13.0, iOS 16.0, *) {
                currentLanguage = Locale.current.language.languageCode?.identifier ?? "en"
            } else {
                // Fallback on earlier versions
                currentLanguage = Locale.current.languageCode ?? "en"
            }
        }

        // 初始化 bundle
        updateLanguage()
    }

    private func updateLanguage() {
        // 更新 Bundle 的语言设置
        if let languageBundle = Bundle.main.path(forResource: currentLanguage, ofType: "lproj"),
           let bundle = Bundle(path: languageBundle) {
            self.bundle = bundle
            UserDefaults.standard.set([currentLanguage], forKey: "AppleLanguages")
            UserDefaults.standard.synchronize()
        } else {
            self.bundle = Bundle.main
        }
    }

    func localizedString(_ key: String) -> String {
        return bundle?.localizedString(forKey: key, value: nil, table: nil) ?? key
    }

    func getCurrentLanguageName() -> String {
        return supportedLanguages.first { $0.0 == currentLanguage }?.1 ?? "English"
    }

    // MARK: - LocalizationManagerProtocol Implementation

    var availableLanguages: [String] {
        return supportedLanguages.map { $0.0 }
    }

    var languageDisplayNames: [String: String] {
        return Dictionary(uniqueKeysWithValues: supportedLanguages)
    }

    func setLanguage(_ languageCode: String) {
        currentLanguage = languageCode
    }

    func localizedString(_ key: String, arguments: CVarArg...) -> String {
        let format = localizedString(key)
        return String(format: format, arguments: arguments)
    }

    func getLanguageDisplayName(for languageCode: String) -> String {
        return languageDisplayNames[languageCode] ?? languageCode
    }

    func isLanguageSupported(_ languageCode: String) -> Bool {
        return availableLanguages.contains(languageCode)
    }

    func getSystemPreferredLanguage() -> String {
        if #available(macOS 13.0, iOS 16.0, *) {
            return Locale.current.language.languageCode?.identifier ?? "en"
        } else {
            return Locale.current.languageCode ?? "en"
        }
    }

    func saveLanguageSettings() {
        UserDefaults.standard.set(currentLanguage, forKey: "AppLanguage")
        UserDefaults.standard.synchronize()
    }

    func loadLanguageSettings() {
        if let savedLanguage = UserDefaults.standard.string(forKey: "AppLanguage") {
            currentLanguage = savedLanguage
        }
    }

    func resetToSystemLanguage() {
        currentLanguage = getSystemPreferredLanguage()
    }
}

extension Notification.Name {
    static let languageDidChange = Notification.Name("languageDidChange")
}

// 本地化字符串键
enum LocalizationKey {
    // 通用
    static let ok = "ok"
    static let cancel = "cancel"
    static let next = "next"
    static let previous = "previous"
    static let submit = "submit"
    static let play = "play"
    static let pause = "pause"
    static let stop = "stop"
    static let listen = "listen"
    static let done = "done"
    static let settings = "settings"
    static let language = "language"
    static let confirm = "confirm"
    static let back = "back"
    static let save = "save"
    static let delete = "delete"
    static let edit = "edit"
    static let loading = "loading"
    static let error = "error"
    static let success = "success"

    // 导航
    static let home = "home"
    static let lessons = "lessons"
    static let practice = "practice"
    static let profile = "profile"
    static let achievements = "achievements"

    // 个人中心
    static let streak = "streak"
    static let vocabulary = "vocabulary"
    static let listeningExercises = "listening_exercises"
    static let speakingExercises = "speaking_exercises"
    static let logout = "logout"
    static let logoutConfirm = "logout_confirm"
    static let logoutMessage = "logout_message"
    static let progress = "progress"
    static let profile_edit = "profile_edit"
    static let profile_save = "profile_save"
    static let profile_cancel = "profile_cancel"
    static let profile_delete = "profile_delete"
    static let profile_delete_confirm = "profile_delete_confirm"
    static let profile_delete_message = "profile_delete_message"
    static let profile_delete_success = "profile_delete_success"
    static let profile_delete_failed = "profile_delete_failed"
    static let statistics = "statistics"

    // TTS 设置
    static let tts_settings_title = "tts_settings_title"
    static let tts_settings_subtitle = "tts_settings_subtitle"

    // 练习类型
    static let practice_types = "practice_types"
    static let daily_practice = "daily_practice"
    static let swipe_instruction = "swipe_instruction"
    static let confirmed = "confirmed"
    static let skipped = "skipped"
    static let no_practice_cards = "no_practice_cards"
    static let pull_to_refresh = "pull_to_refresh"
    static let all_done = "all_done"
    static let completed_all_cards = "completed_all_cards"
    static let refresh_cards = "refresh_cards"
    static let swipe_to_practice = "swipe_to_practice"
    static let word_learning = "word_learning"
    static let listening_practice = "listening_practice"
    static let speaking_practice = "speaking_practice"
    static let recommended_practice = "recommended_practice"
    static let daily_words = "daily_words"
    static let listening_challenge = "listening_challenge"
    static let developer_tools = "developer_tools"
    static let error_test = "error_test"

    // 听力练习
    static let listeningExercise = "listening_exercise"
    static let showTranscript = "show_transcript"
    static let hideTranscript = "hide_transcript"
    static let exercisePrompt = "exercise_prompt"
    static let targetPhrase = "target_phrase"
    static let yourAnswer = "your_answer"
    static let feedback = "feedback"
    static let startListening = "start_listening"
    static let stopListening = "stop_listening"
    static let no_exercise = "no_exercise"
    static let check_back_later = "check_back_later"
    static let exercise_progress = "exercise_progress"
    static let correct_answer = "correct_answer"
    static let incorrect_answer = "incorrect_answer"

    // 口语练习
    static let speakingExercise = "speaking_exercise"
    static let startRecording = "start_recording"
    static let stopRecording = "stop_recording"
    static let recording = "recording"
    static let pronunciationAccuracy = "pronunciation_accuracy"
    static let speakNow = "speak_now"
    static let processing = "processing"
    static let recording_permission_required = "recording_permission_required"
    static let recording_permission_denied = "recording_permission_denied"
    static let recording_error = "recording_error"
    static let recording_success = "recording_success"
    static let recording_failed = "recording_failed"
    static let recording_processing = "recording_processing"
    static let recording_complete = "recording_complete"
    static let example_sentence = "example_sentence"
    static let great_job = "great_job"
    static let keep_practicing = "keep_practicing"

    // 设置
    static let notifications = "notifications"
    static let enableNotifications = "enable_notifications"
    static let learningGoals = "learning_goals"
    static let dailyGoal = "daily_goal"
    static let appearance = "appearance"
    static let darkMode = "dark_mode"
    static let testError = "test_error"
    static let settings_reset = "settings_reset"
    static let settings_reset_confirm = "settings_reset_confirm"
    static let settings_reset_success = "settings_reset_success"
    static let settings_reset_failed = "settings_reset_failed"

    // 账户设置
    static let account = "account"
    static let account_settings = "account_settings"
    static let account_settings_subtitle = "account_settings_subtitle"
    static let personal_info = "personal_info"
    static let username_title = "username_title"
    static let username_placeholder = "username_placeholder"
    static let email_title = "email_title"
    static let email_placeholder = "email_placeholder"
    static let update_success = "update_success"
    static let update_success_message = "update_success_message"
    static let update_failed = "update_failed"
    static let delete_account = "delete_account"
    static let delete_failed = "delete_failed"
    static let change_password = "change_password"
    static let change_password_subtitle = "change_password_subtitle"
    static let current_password = "current_password"
    static let new_password = "new_password"
    static let confirm_new_password = "confirm_new_password"
    static let password_changed = "password_changed"
    static let password_change_failed = "password_change_failed"
    static let security = "security"
    static let password_requirements = "password_requirements"
    static let min_8_characters = "min_8_characters"
    static let passwords_match = "passwords_match"
    static let update_password = "update_password"
    static let passwords_not_match = "passwords_not_match"
    static let passwords_not_match_message = "passwords_not_match_message"
    static let password_too_short = "password_too_short"
    static let password_min_chars_message = "password_min_chars_message"
    static let password_changed_message = "password_changed_message"

    // 通知设置
    static let notification_settings = "notification_settings"
    static let daily_reminder = "daily_reminder"
    static let daily_reminder_time = "daily_reminder_time"
    static let weekly_progress_notification = "weekly_progress_notification"
    static let new_content_notification = "new_content_notification"
    static let reminder_time = "reminder_time"
    static let notification_permissions = "notification_permissions"
    static let notification_types = "notification_types"
    static let manage_notification_permissions = "manage_notification_permissions"
    static let save_settings = "save_settings"
    static let notification_settings_saved = "notification_settings_saved"
    static let settings_saved = "settings_saved"
    static let language_settings_saved = "language_settings_saved"
    static let settings_error = "settings_error"

    // 环境设置
    static let environment_settings = "environment_settings"
    static let current_environment = "current_environment"
    static let environment = "environment"
    static let current_selection = "current_selection"
    static let apply_settings = "apply_settings"
    static let switch_environment = "switch_environment"
    static let switch_environment_message = "switch_environment_message"
    static let close = "close"

    // 关于页面
    static let about = "about"
    static let third_party_libraries = "third_party_libraries"
    static let developer = "developer"
    static let view_project = "view_project"

    // 应用设置
    static let app_settings = "app_settings"
    static let change_app_language = "change_app_language"
    static let manage_notification_preferences = "manage_notification_preferences"
    static let about_support = "about_support"
    static let app_information_credits = "app_information_credits"
    static let help_support = "help_support"
    static let get_help_app = "get_help_app"
    static let version = "version"

    // 语言设置
    static let language_preference_subtitle = "language_preference_subtitle"
    static let language_settings_info_title = "language_settings_info_title"
    static let language_settings_info_description = "language_settings_info_description"

    // 统一组件
    static let loading_default = "loading_default"
    static let empty_state_default = "empty_state_default"
    static let try_again = "try_again"
    static let refresh = "refresh"
    static let no_data_available = "no_data_available"
    static let data_loading = "data_loading"
    static let operation_in_progress = "operation_in_progress"

    // 批量操作
    static let batch_operation_success = "batch_operation_success"
    static let batch_operation_failed = "batch_operation_failed"
    static let batch_operation_partial = "batch_operation_partial"
    static let items_processed = "items_processed"
    static let items_failed = "items_failed"
    static let batch_add_favorites = "batch_add_favorites"
    static let batch_remove_favorites = "batch_remove_favorites"
    static let batch_delete_items = "batch_delete_items"
    static let batch_sync_data = "batch_sync_data"

    // 智能推荐
    static let personalized_recommendations = "personalized_recommendations"
    static let recommended_for_you = "recommended_for_you"
    static let learning_path_suggestions = "learning_path_suggestions"
    static let next_steps = "next_steps"
    static let difficulty_match = "difficulty_match"
    static let based_on_progress = "based_on_progress"
    static let smart_suggestions = "smart_suggestions"

    // 学习分析
    static let learning_analytics = "learning_analytics"
    static let completion_rate = "completion_rate"
    static let study_time = "study_time"
    static let progress_overview = "progress_overview"
    static let category_breakdown = "category_breakdown"
    static let performance_trends = "performance_trends"
    static let learning_insights = "learning_insights"

    // Manager 错误消息
    static let error_loading_evaluations = "error_loading_evaluations"
    static let error_starting_evaluation = "error_starting_evaluation"
    static let error_submitting_answer = "error_submitting_answer"
    static let error_completing_evaluation = "error_completing_evaluation"
    static let error_loading_evaluation_history = "error_loading_evaluation_history"
    static let error_updating_user_points = "error_updating_user_points"
    static let error_updating_user_stats = "error_updating_user_stats"
    static let error_batch_operation = "error_batch_operation"
    static let error_sync_data = "error_sync_data"
    static let error_cache_operation = "error_cache_operation"

    // 成功消息
    static let success_evaluation_completed = "success_evaluation_completed"
    static let success_answer_submitted = "success_answer_submitted"
    static let success_data_synced = "success_data_synced"
    static let success_batch_operation = "success_batch_operation"
    static let success_cache_cleared = "success_cache_cleared"

    // 评估相关
    static let evaluation_not_found = "evaluation_not_found"
    static let evaluation_already_completed = "evaluation_already_completed"
    static let evaluation_in_progress = "evaluation_in_progress"
    static let evaluation_time_remaining = "evaluation_time_remaining"
    static let evaluation_questions_remaining = "evaluation_questions_remaining"

    // 离线功能
    static let offline_mode = "offline_mode"
    static let offline_data_available = "offline_data_available"
    static let offline_sync_pending = "offline_sync_pending"
    static let offline_cache_size = "offline_cache_size"
    static let offline_last_sync = "offline_last_sync"
    static let sync_in_progress = "sync_in_progress"
    static let sync_completed = "sync_completed"
    static let sync_failed = "sync_failed"
    static let cache_cleared = "cache_cleared"
    static let cache_size_limit_reached = "cache_size_limit_reached"

    // 错误消息
    static let errorNetwork = "error_network"
    static let errorServer = "error_server"
    static let errorTimeout = "error_timeout"
    static let errorNoInternet = "error_no_internet"
    static let errorAPI = "error_api"
    static let errorAudio = "error_audio"
    static let errorSpeech = "error_speech"
    static let errorMicrophone = "error_microphone"
    static let errorPermission = "error_permission"
    static let errorUnknown = "error_unknown"
    static let errorData = "error_data"
    static let errorDataSave = "error_data_save"
    static let errorDataNotFound = "error_data_not_found"
    static let errorInvalidData = "error_invalid_data"
    static let errorAuth = "error_auth"
    static let errorUserNotFound = "error_user_not_found"
    static let errorInvalidCredentials = "error_invalid_credentials"
    static let errorRegistration = "error_registration"
    static let errorAudioRecording = "error_audio_recording"
    static let errorNotImplemented = "error_not_implemented"
    static let errorCancelled = "error_cancelled"

    // API 特定错误
    static let errorInvalidResponse = "error_invalid_response"
    static let errorDecoding = "error_decoding"
    static let errorUnauthorized = "error_unauthorized"
    static let errorNotFound = "error_not_found"
    static let errorBadRequest = "error_bad_request"
    static let errorForbidden = "error_forbidden"
    static let errorConflict = "error_conflict"
    static let errorTooManyRequests = "error_too_many_requests"
    static let errorInternalServer = "error_internal_server"
    static let errorBadGateway = "error_bad_gateway"
    static let errorServiceUnavailable = "error_service_unavailable"
    static let errorGatewayTimeout = "error_gateway_timeout"

    // 业务逻辑错误
    static let errorInvalidInput = "error_invalid_input"
    static let errorOperationNotAllowed = "error_operation_not_allowed"
    static let errorResourceNotFound = "error_resource_not_found"
    static let errorValidation = "error_validation"
    static let errorBusinessRule = "error_business_rule"

    // 学习相关错误
    static let errorLessonNotAvailable = "error_lesson_not_available"
    static let errorPracticeExpired = "error_practice_expired"
    static let errorEvaluationNotFound = "error_evaluation_not_found"
    static let errorProgressSync = "error_progress_sync"
    static let errorAchievementUnlock = "error_achievement_unlock"

    // 存储错误
    static let errorCoreData = "error_core_data"
    static let errorFileSystem = "error_file_system"
    static let errorCache = "error_cache"

    // 权限错误
    static let errorFeatureNotAvailable = "error_feature_not_available"

    // 错误类型
    static let error_type_network = "error_type_network"
    static let error_type_data = "error_type_data"
    static let error_type_user = "error_type_user"
    static let error_type_audio = "error_type_audio"
    static let error_type_system = "error_type_system"

    // 错误操作
    static let error_copied = "error_copied"
    static let error_copy = "error_copy"
    static let error_close = "error_close"

    // 错误测试
    static let error_test_title = "error_test_title"
    static let test_network_error = "test_network_error"
    static let test_data_error = "test_data_error"
    static let test_audio_error = "test_audio_error"
    static let test_auth_error = "test_auth_error"
    static let test_unknown_error = "test_unknown_error"
    static let clear_error = "clear_error"

    // 课程相关
    static let lessonDetail = "lesson_detail"
    static let startLesson = "start_lesson"
    static let completeLesson = "complete_lesson"
    static let lessonProgress = "lesson_progress"
    static let nextLesson = "next_lesson"
    static let previousLesson = "previous_lesson"
    static let recommended_lessons = "recommended_lessons"
    static let duration_minutes = "duration_minutes"
    static let points = "points"
    static let exercises = "exercises"
    static let lessonCompleted = "lesson_completed"
    static let lessonCompletionMessage = "lesson_completion_message"
    static let all_categories = "all_categories"
    static let search_lessons = "search_lessons"
    static let category = "category"

    // 推荐原因
    static let recommendation_level_match = "recommendation_level_match"
    static let recommendation_difficulty_match = "recommendation_difficulty_match"
    static let recommendation_category_match = "recommendation_category_match"
    static let recommendation_progress = "recommendation_progress"
    static let recommendation_duration = "recommendation_duration"

    // 单词学习
    static let newWords = "new_words"
    static let reviewWords = "review_words"
    static let masteredWords = "mastered_words"
    static let addWord = "add_word"
    static let removeWord = "remove_word"
    static let wordMeaning = "word_meaning"
    static let wordExample = "word_example"
    static let no_words = "no_words"
    static let show_translation = "show_translation"
    static let hide_translation = "hide_translation"
    static let show_example = "show_example"
    static let hide_example = "hide_example"
    static let translate_word = "translate_word"
    static let enter_translation = "enter_translation"
    static let practice_complete = "practice_complete"
    static let score = "score"
    static let word_practice = "word_practice"
    static let exit = "exit"

    // 学习进度
    static let daily_goal_title = "daily_goal_title"
    static let daily_goal_description = "daily_goal_description"
    static let learning_progress = "learning_progress"
    static let weekly_study_time = "weekly_study_time"

    // 登录和注册
    static let app_name = "app_name"
    static let username = "username"
    static let password = "password"
    static let confirm_password = "confirm_password"
    static let email = "email"
    static let login = "login"
    static let register = "register"
    static let enter_username_password = "enter_username_password"
    static let fill_all_fields = "fill_all_fields"
    static let login_success = "login_success"
    static let login_failed = "login_failed"
    static let register_success = "register_success"
    static let register_failed = "register_failed"
    static let invalid_email = "invalid_email"
    static let username_taken = "username_taken"
    static let email_taken = "email_taken"
    static let remember_me = "remember_me"
    static let forgot_password = "forgot_password"
    static let no_account = "no_account"
    static let login_with = "login_with"
    static let enter_email = "enter_email"
    static let reset_password = "reset_password"
    static let reset_password_instruction = "reset_password_instruction"
    static let send_reset_link = "send_reset_link"
    static let reset_link_sent = "reset_link_sent"
    static let back_to_login = "back_to_login"
    static let welcome_message = "welcome_message"
    static let create_account_message = "create_account_message"
    static let password_placeholder = "password_placeholder"
    static let confirm_password_placeholder = "confirm_password_placeholder"

    // 成就
    static let achievement_streak = "achievement_streak"
    static let achievement_vocabulary = "achievement_vocabulary"
    static let achievement_listening = "achievement_listening"
    static let achievement_speaking = "achievement_speaking"
    static let achievement_lessons = "achievement_lessons"
    static let achievement_points = "achievement_points"
    static let achievement_challenges = "achievement_challenges"
    static let achievement_social = "achievement_social"
    static let reward_points = "reward_points"
    static let achievement_unlocked = "achievement_unlocked"
    static let achievement_locked = "achievement_locked"
    static let achievement_progress = "achievement_progress"
    static let achievement_reward = "achievement_reward"
    static let all_achievements = "all_achievements"
    static let items = "items"

    // 练习相关
    static let exercise_title = "exercise_title"
    static let exercise_instruction = "exercise_instruction"
    static let enter_answer = "enter_answer"
    static let example = "example"
    static let submit_answer = "submit_answer"
    static let answer_correct = "answer_correct"
    static let answer_incorrect = "answer_incorrect"
    static let explanation = "explanation"
    static let play_audio = "play_audio"
    static let pause_audio = "pause_audio"
    static let stop_audio = "stop_audio"
    static let next_exercise = "next_exercise"
    static let grammarExercise = "grammar_exercise"

    // 课程编辑
    static let lesson_title = "lesson_title"
    static let lesson_description = "lesson_description"
    static let lesson_category = "lesson_category"
    static let lesson_duration = "lesson_duration"
    static let lesson_points = "lesson_points"
    static let lesson_level = "lesson_level"
    static let lesson_difficulty = "lesson_difficulty"
    static let lesson_tags = "lesson_tags"
    static let lesson_tags_help = "lesson_tags_help"
    static let lesson_basic_info = "lesson_basic_info"
    static let lesson_details = "lesson_details"
    static let lesson_new = "lesson_new"
    static let lesson_edit = "lesson_edit"

    // Achievements (New keys start here)
    static let ach_streak_3_days_title = "ach_streak_3_days_title"
    static let ach_streak_3_days_desc = "ach_streak_3_days_desc"
    static let ach_vocab_master_title = "ach_vocab_master_title"
    static let ach_vocab_master_desc = "ach_vocab_master_desc"
    static let ach_listening_pro_title = "ach_listening_pro_title"
    static let ach_listening_pro_desc = "ach_listening_pro_desc"
    static let ach_speaking_expert_title = "ach_speaking_expert_title"
    static let ach_speaking_expert_desc = "ach_speaking_expert_desc"
    static let ach_lesson_completer_title = "ach_lesson_completer_title"
    static let ach_lesson_completer_desc = "ach_lesson_completer_desc"
    static let ach_points_collector_title = "ach_points_collector_title"
    static let ach_points_collector_desc = "ach_points_collector_desc"
    static let ach_challenger_title = "ach_challenger_title"
    static let ach_challenger_desc = "ach_challenger_desc"
    static let ach_social_butterfly_title = "ach_social_butterfly_title"
    static let ach_social_butterfly_desc = "ach_social_butterfly_desc"

    // AchievementView
    static let all_achievements_title = "all_achievements_title"
    static let achievements_count_format = "achievements_count_format" // E.g., "%d Achievements"

    // SettingsView
    static let change_app_language_subtitle = "change_app_language_subtitle"
    static let manage_notification_preferences_subtitle = "manage_notification_preferences_subtitle"
    static let app_information_credits_subtitle = "app_information_credits_subtitle"
    static let get_help_app_subtitle = "get_help_app_subtitle"
    static let version_label = "version_label"
    static let about_support_section_title = "about_support_section_title"

    // AppEnvironment
    static let env_development = "env_development"
    static let env_staging = "env_staging"
    static let env_production = "env_production"

    // EnvironmentSettingsView
    static let environment_settings_title = "environment_settings_title"
    static let environment_settings_subtitle = "environment_settings_subtitle"
    static let current_environment_section_title = "current_environment_section_title"
    static let environment_picker_label = "environment_picker_label"
    static let current_selection_format = "current_selection_format" // E.g., "Current: %@"
    static let development_options_section_title = "development_options_section_title"
    static let use_mock_data_toggle = "use_mock_data_toggle"
    static let environment_info_section_title = "environment_info_section_title"
    static let api_base_url_label = "api_base_url_label"
    static let api_timeout_label = "api_timeout_label"
    static let seconds_format = "seconds_format" // E.g., "%d seconds"
    static let verbose_logging_label = "verbose_logging_label"
    static let enabled_status = "enabled_status"
    static let disabled_status = "disabled_status"
    static let developer_tools_label = "developer_tools_label"
    static let shown_status = "shown_status"
    static let hidden_status = "hidden_status"
    static let apply_settings_button = "apply_settings_button"
    static let close_button = "close_button" // Potentially reuse 'close' if appropriate
    static let switch_environment_alert_title = "switch_environment_alert_title"
    static let switch_environment_alert_message = "switch_environment_alert_message"
    static let confirm_button = "confirm_button" // Potentially reuse 'confirm'
    static let cancel_button = "cancel_button"   // Potentially reuse 'cancel'

    // AboutView & ThirdPartyLibrariesView (confirming ones used)
    static let app_name_title = "app_name_title" // Used in AboutView
    static let version_format = "version_format" // Used in AboutView, e.g. "Version %@ (%@)"
    static let development_team = "development_team" // Used in AboutView
    static let contact_us = "contact_us" // Used in AboutView
    static let legal_information = "legal_information" // Used in AboutView
    static let terms_of_service = "terms_of_service" // Used in AboutView
    static let privacy_policy = "privacy_policy" // Used in AboutView
    static let open_source_components = "open_source_components" // Used in AboutView
    static let copyright_notice = "copyright_notice" // Used in AboutView, e.g. "© %d MyApp"
    static let about_title = "about_title" // Navigation title for AboutView

    // ProfileView

    // 练习相关
    static let continue_learning = "continue_learning"
    static let today_practice = "today_practice"
    static let weekly_average = "weekly_average"
    static let skill_analysis = "skill_analysis"
    static let grammar = "grammar"
    static let listening = "listening"
    static let speaking = "speaking"
    static let reading = "reading"
    static let writing = "writing"
    static let mixed = "mixed"

    // 练习相关
    static let start_learning = "start_learning"
    static let practice_description = "practice_description"
    static let completed_exercises = "completed_exercises"
    static let strongest_skill = "strongest_skill"
    static let weakest_skill = "weakest_skill"

    // 练习相关
    static let start_evaluation = "start_evaluation"
    static let minutes = "minutes"
    static let view_detailed_stats = "view_detailed_stats"
    static let no_skill_data = "no_skill_data"

    // 练习相关
    static let start_personalized_learning = "start_personalized_learning"
    static let practice_again = "practice_again"
    static let start_practice = "start_practice"
    static let practice_completed = "practice_completed"
    static let practice_completed_message = "practice_completed_message"

    // 评估相关
    static let language_assessment = "language_assessment"
    static let language_level_assessment = "language_level_assessment"
    static let assessment_description = "assessment_description"
    static let assessment_duration = "assessment_duration"
    static let assessment_questions = "assessment_questions"
    static let start_new_assessment = "start_new_assessment"
    static let recent_assessments = "recent_assessments"
    static let view_all = "view_all"
    static let assessment_benefits = "assessment_benefits"
    static let accurate_assessment = "accurate_assessment"
    static let accurate_assessment_desc = "accurate_assessment_desc"
    static let personalized_learning = "personalized_learning"
    static let personalized_learning_desc = "personalized_learning_desc"
    static let progress_tracking = "progress_tracking"
    static let progress_tracking_desc = "progress_tracking_desc"
    static let professional_standards = "professional_standards"
    static let professional_standards_desc = "professional_standards_desc"

    // MARK: - 评估相关
    static let assessment_history = "assessment_history"
    static let no_assessment_history = "no_assessment_history"
    static let no_assessment_history_desc = "no_assessment_history_desc"
    static let error_loading_question = "error_loading_question"
    static let error_loading_assessment = "error_loading_assessment"
    static let confirm_exit = "confirm_exit"
    static let confirm_exit_message = "confirm_exit_message"
    static let continue_assessment = "continue_assessment"
    static let question = "question"
    static let unsupported_question_type = "unsupported_question_type"
    static let enter_your_answer = "enter_your_answer"

    // 个性化学习相关
    static let startPersonalizedLearning = "start_personalized_learning"
    static let personalizedLearningDescription = "personalized_learning_description"
    static let startingLearning = "starting_learning"
    static let learningStarted = "learning_started"
    static let evaluationPrompt = "evaluation_prompt"
    static let startLearning = "start_learning"

    // 主题设置
    static let theme_settings = "theme_settings"
    static let theme_settings_subtitle = "theme_settings_subtitle"
    static let light_theme = "light_theme"
    static let dark_theme = "dark_theme"
    static let system_theme = "system_theme"
    static let theme_changed = "theme_changed"
    static let theme_changed_message = "theme_changed_message"
}
