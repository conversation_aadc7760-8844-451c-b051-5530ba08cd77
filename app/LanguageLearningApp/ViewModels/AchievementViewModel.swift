import Foundation
import Combine

@MainActor
class AchievementViewModel: ObservableObject {
    @Published var userAchievements: [UserAchievement] = []
    @Published var isLoading = false

    private let apiDataSource = APIDataSourceManager.shared
    @Published var error: Error?

    // 服务和管理器
    private let networkService: NetworkServiceProtocol
    private let userManager: UserManager

    private var cancellables = Set<AnyCancellable>()

    init(
        networkService: NetworkServiceProtocol? = nil,
        userManager: UserManager? = nil
    ) {
        print("🏆 [AchievementViewModel] 初始化开始")

        // 尝试从容器解析依赖项，如果失败则使用默认值
        if let networkService = networkService {
            self.networkService = networkService
        } else {
            do {
                self.networkService = try DependencyContainer.shared.tryResolve(NetworkServiceProtocol.self)
                print("🏆 [AchievementViewModel] 成功从容器解析 NetworkService")
            } catch {
                print("🏆 [AchievementViewModel] 无法从容器解析 NetworkService，使用默认实例: \(error)")
                self.networkService = NetworkService.shared
            }
        }

        if let userManager = userManager {
            self.userManager = userManager
        } else {
            do {
                self.userManager = try DependencyContainer.shared.tryResolve(UserManager.self)
                print("🏆 [AchievementViewModel] 成功从容器解析 UserManager")
            } catch {
                print("🏆 [AchievementViewModel] 无法从容器解析 UserManager，使用默认实例: \(error)")
                self.userManager = UserManager.shared
            }
        }

        loadUserAchievements()
        setupNotifications()

        // 异步加载API数据
        Task {
            await loadUserAchievementsFromAPI()
        }
    }

    private func loadUserAchievements() {
        isLoading = true

        Task {
            do {
                // 使用 API 数据源管理器获取用户成就数据
                let achievements = try await apiDataSource.getUserAchievements()
                DispatchQueue.main.async {
                    self.userAchievements = achievements
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.error = error
                    self.isLoading = false

                    // 如果 API 调用失败且在开发环境，加载示例数据作为备选
                    if AppEnvironment.current.useMockData {
                        self.userAchievements = UserAchievement.sampleUserAchievements
                    }
                }
            }
        }
    }

    private func setupNotifications() {
        NotificationCenter.default.publisher(for: .streakUpdated)
            .sink { [weak self] _ in
                self?.updateStreakAchievements()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: .exerciseCompleted)
            .sink { [weak self] notification in
                if let userInfo = notification.userInfo,
                   let isCorrect = userInfo["isCorrect"] as? Bool {
                    self?.updateExerciseAchievements(isCorrect: isCorrect)
                }
            }
            .store(in: &cancellables)
    }

    private func updateStreakAchievements() {
        guard let user = userManager.currentUser,
              let stats = user.stats else { return }

        for (index, achievement) in userAchievements.enumerated() where achievement.achievementType == .streak {
            userAchievements[index].progress = stats.streakDays
            if stats.streakDays >= Achievement.sampleAchievements.first(where: { $0.type == .streak })?.requirement ?? 0 {
                userAchievements[index].isUnlocked = true
                userAchievements[index].unlockedDate = Date()
            }
        }
    }

    private func updateExerciseAchievements(isCorrect: Bool) {
        guard let user = userManager.currentUser,
              let stats = user.stats else { return }

        for (index, achievement) in userAchievements.enumerated() {
            switch achievement.achievementType {
            case .vocabulary:
                userAchievements[index].progress = stats.vocabularyCount
                if stats.vocabularyCount >= Achievement.sampleAchievements.first(where: { $0.type == .vocabulary })?.requirement ?? 0 {
                    userAchievements[index].isUnlocked = true
                    userAchievements[index].unlockedDate = Date()
                }
            case .listening:
                userAchievements[index].progress = stats.listeningExerciseCount
                if stats.listeningExerciseCount >= Achievement.sampleAchievements.first(where: { $0.type == .listening })?.requirement ?? 0 {
                    userAchievements[index].isUnlocked = true
                    userAchievements[index].unlockedDate = Date()
                }
            case .speaking:
                userAchievements[index].progress = stats.speakingExerciseCount
                if stats.speakingExerciseCount >= Achievement.sampleAchievements.first(where: { $0.type == .speaking })?.requirement ?? 0 {
                    userAchievements[index].isUnlocked = true
                    userAchievements[index].unlockedDate = Date()
                }
            default:
                break
            }
        }
    }

    func claimReward(for achievement: UserAchievement) {
        guard let index = userAchievements.firstIndex(where: { $0.id == achievement.id }),
              achievement.isUnlocked && !achievement.rewardClaimed else { return }

        userAchievements[index].rewardClaimed = true
        Task {
            await userManager.updateUserPoints(achievement.reward)
        }
    }

    var unlockedAchievements: [UserAchievement] {
        userAchievements.filter { $0.isUnlocked }
    }

    var inProgressAchievements: [UserAchievement] {
        userAchievements.filter { !$0.isUnlocked }
    }

    func getAchievementProgress(for type: Achievement.AchievementType) -> Double {
        let typeAchievements = userAchievements.filter { $0.achievementType == type }
        guard !typeAchievements.isEmpty else { return 0 }

        let totalProgress = typeAchievements.reduce(0) { $0 + $1.progress }
        let totalRequirement = Achievement.sampleAchievements
            .filter { $0.type == type }
            .reduce(0) { $0 + $1.requirement }

        return Double(totalProgress) / Double(totalRequirement)
    }

    func getUnclaimedRewards() -> Int {
        userAchievements.filter { $0.isUnlocked && !$0.rewardClaimed }
            .reduce(0) { $0 + $1.reward }
    }

    // MARK: - 网络加载用户成就
    func loadUserAchievementsFromAPI() async {
        isLoading = true
        do {
            let apiUserAchievements: [UserAchievement] = try await networkService.request(.userAchievements)
            DispatchQueue.main.async {
                self.userAchievements = apiUserAchievements
                self.isLoading = false
            }
        } catch {
            print("Failed to load user achievements from API: \(error)")
            DispatchQueue.main.async {
                self.isLoading = false
                self.error = error
            }
        }
    }

    // 领取成就奖励
    func claimRewardFromAPI(for achievement: UserAchievement) async {
        guard achievement.isUnlocked && !achievement.rewardClaimed else { return }

        isLoading = true
        do {
            // 调用API领取奖励
            let updatedAchievement: UserAchievement = try await networkService.request(
                .claimAchievementReward(achievementID: achievement.achievementId)
            )

            DispatchQueue.main.async {
                // 更新本地成就列表
                if let index = self.userAchievements.firstIndex(where: { $0.id == achievement.id }) {
                    self.userAchievements[index] = updatedAchievement
                }

                // 更新用户积分
                if let user = self.userManager.currentUser,
                   let stats = user.stats {
                    Task {
                        await self.userManager.updateUserPoints(stats.points + achievement.reward)
                    }
                }

                self.isLoading = false
            }
        } catch {
            print("Failed to claim achievement reward: \(error)")
            DispatchQueue.main.async {
                self.isLoading = false
                self.error = error
            }
        }
    }
}