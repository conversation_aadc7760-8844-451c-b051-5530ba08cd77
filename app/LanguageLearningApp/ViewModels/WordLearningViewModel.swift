import Foundation
import Combine
import SwiftUI

@MainActor
class WordLearningViewModel: ObservableObject {
    @Published var words: [Word] = []
    @Published var currentWordIndex: Int = 0
    @Published var currentWord: Word?
    @Published var progress: Double = 0.0
    @Published var isShowingTranslation: Bool = false
    @Published var isShowingExample: Bool = false

    private var cancellables = Set<AnyCancellable>()
    private let networkService: NetworkServiceProtocol
    private let userManager: any UserManagerProtocol
    private let errorManager: ErrorManager
    private let ttsManager: TTSManager

    init(
        networkService: NetworkServiceProtocol = NetworkService.shared,
        userManager: (any UserManagerProtocol)? = nil,
        errorManager: ErrorManager = ErrorManager.shared,
        ttsManager: TTSManager? = nil
    ) {
        self.networkService = networkService
        self.userManager = userManager ?? UserManager.shared
        self.errorManager = errorManager
        self.ttsManager = ttsManager ?? TTSManager.shared

        $words.combineLatest($currentWordIndex)
            .map { words, index -> Word? in
                guard !words.isEmpty, index >= 0, index < words.count else {
                    return nil
                }
                return words[index]
            }
            .assign(to: &$currentWord)

        $words.combineLatest($currentWordIndex)
            .map { words, index -> Double in
                guard !words.isEmpty, words.count > 0 else { return 0.0 }
                return Double(index + 1) / Double(words.count)
            }
            .assign(to: &$progress)
    }

    func loadWords() {
        // Placeholder: Actual implementation would fetch words
        // self.words = ...
    }

    func nextWord() {
        if !words.isEmpty {
            currentWordIndex = (currentWordIndex + 1) % words.count
        }
    }

    func previousWord() {
        if !words.isEmpty {
            currentWordIndex = (currentWordIndex - 1 + words.count) % words.count
        }
    }

    func toggleTranslation() {
        isShowingTranslation.toggle()
    }

    func toggleExample() {
        isShowingExample.toggle()
    }

    func markWordAsKnown() {
        // Placeholder
    }

    func markWordAsDifficult() {
        // Placeholder
    }

    func playCurrentWordPronunciation() {
        guard let wordToPlay = currentWord else { return }
        ttsManager.playSample(text: wordToPlay.text, languageCode: wordToPlay.languageCode) { error in
            if let error = error {
                print("TTS Error: \(error.localizedDescription)")
            }
        }
    }
}