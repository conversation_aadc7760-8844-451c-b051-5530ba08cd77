import Foundation
import SwiftUI

/// API端点枚举
public enum APIEndpoint: APIEndpointProtocol {
    /// 基础URL
    public static var baseURL: URL {
        return AppEnvironment.current.baseURL
    }

    // MARK: - 练习相关
    case grammarExercises
    case submitGrammarAnswer(exerciseID: UUID, answer: String)
    case speakingExercises
    case submitSpeakingAnswer(exerciseID: UUID, recordingURL: URL)
    case listeningExercises
    case submitListeningAnswer(exerciseID: UUID, answerIndex: Int)
    case wordExercises(category: String?, difficulty: String?)
    case submitWordAnswer(wordID: UUID, isCorrect: Bool)

    // MARK: - 课程相关
    case lessons
    case lessonDetail(id: UUID)
    case lessonProgress(id: UUID)
    case updateLessonProgress(id: UUID, progress: Double, completed: Bool)
    case favoriteLessons
    case favoriteLesson(id: UUID)
    case unfavoriteLesson(id: UUID)

    // MARK: - 用户相关
    case login(username: String, password: String)
    case register(username: String, email: String, password: String)
    case logout
    case userProfile
    case updateUserProfile(name: String?, email: String?, avatar: Data?)
    case resetPassword(email: String)
    case changePassword(oldPassword: String, newPassword: String)
    case deleteAccount

    // MARK: - 成就相关
    case achievements
    case userAchievements
    case claimAchievementReward(achievementID: UUID)

    // MARK: - 词汇相关
    case words(category: String?, difficulty: String?)
    case wordDetail(id: UUID)
    case markWordAsLearned(id: UUID)
    case userWordList

    // MARK: - 练习历史和推荐
    case practiceHistory
    case recommendedPractice
    case savePracticeSession(type: String, duration: Int, score: Int)
    case dailyPracticeStats
    case weeklyPracticeStats

    // MARK: - 统计和分析
    case userStats
    case learningStreak
    case progressReport(period: String)

    // MARK: - 设置相关
    case userSettings
    case updateUserSettings(settings: [String: Any])

    // MARK: - 个性化学习相关
    case initiatePersonalizedLearning
    case personalizedLearningStatus
    case currentLearningPath
    case nextExercise(pathID: UUID)
    case completeExercise(pathID: UUID, lessonID: String)

    // MARK: - 评估相关
    case evaluations
    case createEvaluation
    case evaluationDetail(id: UUID)
    case startEvaluation(id: UUID)
    case submitEvaluationAnswer(id: UUID, questionID: UUID, answer: String)
    case completeEvaluation(id: UUID)
    case evaluationResults(id: UUID)
    case userEvaluationHistory

    // MARK: - 自定义请求
    case custom(url: URL, method: String, headers: [String: String], bodyData: Data?)

    /// 获取完整URL
    public var url: URL {
        switch self {
        // 练习相关
        case .grammarExercises:
            return Self.baseURL.appendingPathComponent("/grammar/exercises")
        case .submitGrammarAnswer(let id, _):
            return Self.baseURL.appendingPathComponent("/grammar/exercises/\(id)/submit")
        case .speakingExercises:
            return Self.baseURL.appendingPathComponent("/speaking/exercises")
        case .submitSpeakingAnswer(let id, _):
            return Self.baseURL.appendingPathComponent("/speaking/exercises/\(id)/submit")
        case .listeningExercises:
            return Self.baseURL.appendingPathComponent("/listening/exercises")
        case .submitListeningAnswer(let id, _):
            return Self.baseURL.appendingPathComponent("/listening/exercises/\(id)/submit")
        case .wordExercises(let category, let difficulty):
            var url = Self.baseURL.appendingPathComponent("/word/exercises")
            var queryItems = [URLQueryItem]()
            if let category = category {
                queryItems.append(URLQueryItem(name: "category", value: category))
            }
            if let difficulty = difficulty {
                queryItems.append(URLQueryItem(name: "difficulty", value: difficulty))
            }
            if !queryItems.isEmpty, var components = URLComponents(url: url, resolvingAgainstBaseURL: true) {
                components.queryItems = queryItems
                if let urlWithQuery = components.url {
                    url = urlWithQuery
                }
            }
            return url
        case .submitWordAnswer(let id, _):
            return Self.baseURL.appendingPathComponent("/word/exercises/\(id)/submit")

        // 课程相关
        case .lessons:
            return Self.baseURL.appendingPathComponent("/lessons")
        case .lessonDetail(let id):
            return Self.baseURL.appendingPathComponent("/lessons/\(id)")
        case .lessonProgress(let id):
            return Self.baseURL.appendingPathComponent("/lessons/\(id)/progress")
        case .updateLessonProgress(let id, _, _):
            return Self.baseURL.appendingPathComponent("/lessons/\(id)/progress")
        case .favoriteLessons:
            return Self.baseURL.appendingPathComponent("/lessons/favorites")
        case .favoriteLesson(let id):
            return Self.baseURL.appendingPathComponent("/lessons/\(id)/favorite")
        case .unfavoriteLesson(let id):
            return Self.baseURL.appendingPathComponent("/lessons/\(id)/unfavorite")

        // 用户相关
        case .login:
            return Self.baseURL.appendingPathComponent("/auth/login")
        case .register:
            return Self.baseURL.appendingPathComponent("/auth/register")
        case .logout:
            return Self.baseURL.appendingPathComponent("/auth/logout")
        case .userProfile:
            return Self.baseURL.appendingPathComponent("/user/profile")
        case .updateUserProfile:
            return Self.baseURL.appendingPathComponent("/user/profile")
        case .resetPassword:
            return Self.baseURL.appendingPathComponent("/auth/reset-password")
        case .changePassword:
            return Self.baseURL.appendingPathComponent("/auth/change-password")
        case .deleteAccount:
            return Self.baseURL.appendingPathComponent("/user/account")

        // 成就相关
        case .achievements:
            return Self.baseURL.appendingPathComponent("/achievements")
        case .userAchievements:
            return Self.baseURL.appendingPathComponent("/user/achievements")
        case .claimAchievementReward(let id):
            return Self.baseURL.appendingPathComponent("/achievements/\(id)/claim")

        // 词汇相关
        case .words(let category, let difficulty):
            var url = Self.baseURL.appendingPathComponent("/words")
            var queryItems = [URLQueryItem]()
            if let category = category {
                queryItems.append(URLQueryItem(name: "category", value: category))
            }
            if let difficulty = difficulty {
                queryItems.append(URLQueryItem(name: "difficulty", value: difficulty))
            }
            if !queryItems.isEmpty, var components = URLComponents(url: url, resolvingAgainstBaseURL: true) {
                components.queryItems = queryItems
                if let urlWithQuery = components.url {
                    url = urlWithQuery
                }
            }
            return url
        case .wordDetail(let id):
            return Self.baseURL.appendingPathComponent("/words/\(id)")
        case .markWordAsLearned(let id):
            return Self.baseURL.appendingPathComponent("/words/\(id)/learned")
        case .userWordList:
            return Self.baseURL.appendingPathComponent("/user/words")

        // 练习历史和推荐
        case .practiceHistory:
            return Self.baseURL.appendingPathComponent("/practice/history")
        case .recommendedPractice:
            return Self.baseURL.appendingPathComponent("/practice/recommended")
        case .savePracticeSession:
            return Self.baseURL.appendingPathComponent("/practice/session")
        case .dailyPracticeStats:
            return Self.baseURL.appendingPathComponent("/practice/stats/daily")
        case .weeklyPracticeStats:
            return Self.baseURL.appendingPathComponent("/practice/stats/weekly")

        // 统计和分析
        case .userStats:
            return Self.baseURL.appendingPathComponent("/user/stats")
        case .learningStreak:
            return Self.baseURL.appendingPathComponent("/user/streak")
        case .progressReport(let period):
            return Self.baseURL.appendingPathComponent("/user/progress/\(period)")

        // 设置相关
        case .userSettings:
            return Self.baseURL.appendingPathComponent("/user/settings")
        case .updateUserSettings:
            return Self.baseURL.appendingPathComponent("/user/settings")

        // 个性化学习相关
        case .initiatePersonalizedLearning:
            return Self.baseURL.appendingPathComponent("/personalized-learning/initiate")
        case .personalizedLearningStatus:
            return Self.baseURL.appendingPathComponent("/personalized-learning/status")
        case .currentLearningPath:
            return Self.baseURL.appendingPathComponent("/personalized-learning/current-path")
        case .nextExercise(let pathID):
            return Self.baseURL.appendingPathComponent("/learning-paths/\(pathID)/next-exercise")
        case .completeExercise(let pathID, let lessonID):
            return Self.baseURL.appendingPathComponent("/learning-paths/\(pathID)/complete-exercise/\(lessonID)")

        // 评估相关
        case .evaluations:
            return Self.baseURL.appendingPathComponent("/evaluations")
        case .createEvaluation:
            return Self.baseURL.appendingPathComponent("/evaluations")
        case .evaluationDetail(let id):
            return Self.baseURL.appendingPathComponent("/evaluations/\(id)")
        case .startEvaluation(let id):
            return Self.baseURL.appendingPathComponent("/evaluations/\(id)/start")
        case .submitEvaluationAnswer(let id, _, _):
            return Self.baseURL.appendingPathComponent("/evaluations/\(id)/answer")
        case .completeEvaluation(let id):
            return Self.baseURL.appendingPathComponent("/evaluations/\(id)/complete")
        case .evaluationResults(let id):
            return Self.baseURL.appendingPathComponent("/evaluations/\(id)/results")
        case .userEvaluationHistory:
            return Self.baseURL.appendingPathComponent("/user/evaluations")

        // 自定义请求
        case .custom(let url, _, _, _):
            return url
        }
    }

    /// 获取HTTP方法
    public var method: String {
        switch self {
        // GET 请求
        case .grammarExercises, .speakingExercises, .listeningExercises, .wordExercises,
             .lessons, .lessonDetail, .lessonProgress, .favoriteLessons,
             .userProfile, .achievements, .userAchievements,
             .words, .wordDetail, .userWordList,
             .practiceHistory, .recommendedPractice, .dailyPracticeStats, .weeklyPracticeStats,
             .userStats, .learningStreak, .progressReport,
             .userSettings, .personalizedLearningStatus, .currentLearningPath, .nextExercise,
             .evaluations, .evaluationDetail, .evaluationResults, .userEvaluationHistory:
            return "GET"

        // POST 请求
        case .submitGrammarAnswer, .submitSpeakingAnswer, .submitListeningAnswer, .submitWordAnswer,
             .login, .register, .resetPassword,
             .savePracticeSession, .initiatePersonalizedLearning, .completeExercise,
             .createEvaluation, .startEvaluation, .submitEvaluationAnswer, .completeEvaluation,
             .favoriteLesson, .claimAchievementReward:
            return "POST"

        // PUT 请求
        case .updateLessonProgress, .updateUserProfile, .changePassword, .updateUserSettings,
             .markWordAsLearned:
            return "PUT"

        // DELETE 请求
        case .logout, .deleteAccount, .unfavoriteLesson:
            return "DELETE"

        // 自定义请求
        case .custom(_, let method, _, _):
            return method
        }
    }

    /// 获取请求头
    public var headers: [String: String] {
        var headers = ["Content-Type": "application/json"]

        // 添加认证头
        switch self {
        case .login, .register, .resetPassword:
            // 这些端点不需要认证
            break
        case .custom(_, _, let customHeaders, _):
            // 使用自定义头
            return customHeaders
        default:
            // 其他所有端点需要认证
            if let token = UserDefaults.standard.string(forKey: "authToken") {
                headers["Authorization"] = "Bearer \(token)"
            }
        }

        return headers
    }

    /// 获取请求体
    public var body: () -> Data? {
        switch self {
        case .submitGrammarAnswer(_, let answer):
            return { try? JSONSerialization.data(withJSONObject: ["answer": answer]) }

        case .submitSpeakingAnswer(_, let recordingURL):
            return { try? JSONSerialization.data(withJSONObject: ["recordingPath": recordingURL.absoluteString]) }

        case .submitListeningAnswer(_, let answerIndex):
            return { try? JSONSerialization.data(withJSONObject: ["answerIndex": answerIndex]) }

        case .submitWordAnswer(_, let isCorrect):
            return { try? JSONSerialization.data(withJSONObject: ["isCorrect": isCorrect]) }

        case .updateLessonProgress(_, let progress, let completed):
            return { try? JSONSerialization.data(withJSONObject: ["progress": progress, "completed": completed]) }

        case .favoriteLesson:
            return { "{}".data(using: .utf8) }

        case .login(let username, let password):
            return { try? JSONSerialization.data(withJSONObject: ["username": username, "password": password]) }

        case .register(let username, let email, let password):
            return { try? JSONSerialization.data(withJSONObject: ["username": username, "email": email, "password": password]) }

        case .updateUserProfile(let name, let email, let avatar):
            return {
                var userData: [String: Any] = [:]
                if let name = name { userData["name"] = name }
                if let email = email { userData["email"] = email }
                if let avatar = avatar { userData["avatar"] = avatar.base64EncodedString() }
                return try? JSONSerialization.data(withJSONObject: userData)
            }

        case .resetPassword(let email):
            return { try? JSONSerialization.data(withJSONObject: ["email": email]) }

        case .changePassword(let oldPassword, let newPassword):
            return { try? JSONSerialization.data(withJSONObject: ["oldPassword": oldPassword, "newPassword": newPassword]) }

        case .savePracticeSession(let type, let duration, let score):
            return { try? JSONSerialization.data(withJSONObject: ["type": type, "duration": duration, "score": score]) }

        case .updateUserSettings(let settings):
            return { try? JSONSerialization.data(withJSONObject: settings) }

        case .initiatePersonalizedLearning:
            return { try? JSONSerialization.data(withJSONObject: [:]) }

        case .completeExercise(_, let lessonID):
            return { try? JSONSerialization.data(withJSONObject: ["lessonId": lessonID]) }

        case .createEvaluation:
            return {
                let evaluationData: [String: Any] = [
                    "type": "placement",
                    "Title": "中文水平评估",
                    "Duration": 30,
                    "PassingScore": 60,
                    "Sections": [
                        [
                            "title": "词汇测试",
                            "skill": "vocabulary",
                            "weight": 30,
                            "questions": [
                                [
                                    "type": "multiple-choice",
                                    "content": "选择\"苹果\"的正确翻译",
                                    "options": ["Apple", "Orange", "Banana", "Pear"],
                                    "correctAnswer": "Apple",
                                    "points": 10
                                ],
                                [
                                    "type": "multiple-choice",
                                    "content": "选择\"你好\"的正确翻译",
                                    "options": ["Hello", "Goodbye", "Thank you", "Sorry"],
                                    "correctAnswer": "Hello",
                                    "points": 10
                                ]
                            ]
                        ],
                        [
                            "title": "语法测试",
                            "skill": "grammar",
                            "weight": 30,
                            "questions": [
                                [
                                    "type": "multiple-choice",
                                    "content": "选择正确的句子",
                                    "options": ["我是学生", "我学生是", "是我学生", "学生我是"],
                                    "correctAnswer": "我是学生",
                                    "points": 10
                                ]
                            ]
                        ]
                    ]
                ]
                return try? JSONSerialization.data(withJSONObject: evaluationData)
            }

        case .startEvaluation:
            return { try? JSONSerialization.data(withJSONObject: [:]) }

        case .submitEvaluationAnswer(_, let questionID, let answer):
            return { try? JSONSerialization.data(withJSONObject: ["questionId": questionID.uuidString, "answer": answer]) }

        case .completeEvaluation:
            return { try? JSONSerialization.data(withJSONObject: [:]) }

        case .custom(_, _, _, let bodyData):
            return { bodyData }

        default:
            return { nil }
        }
    }
}
