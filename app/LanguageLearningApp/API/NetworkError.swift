import Foundation

/// 统一的网络错误类型
public enum NetworkError: Error {
    case invalidURL
    case requestFailed(Error)
    case invalidResponse
    case decodingFailed(Error)
    case unauthorized
    case serverError(statusCode: Int, message: String?)
    case noInternetConnection
    case timeout
    case cancelled
    case unknown
    
    /// 提供友好的错误描述
    public var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .requestFailed(let error):
            return "请求失败: \(error.localizedDescription)"
        case .invalidResponse:
            return "无效的响应"
        case .decodingFailed(let error):
            return "数据解析失败: \(error.localizedDescription)"
        case .unauthorized:
            return "未授权，请重新登录"
        case .serverError(let statusCode, let message):
            if let message = message {
                return "服务器错误(\(statusCode)): \(message)"
            }
            return "服务器错误: 状态码 \(statusCode)"
        case .noInternetConnection:
            return "无网络连接"
        case .timeout:
            return "请求超时"
        case .cancelled:
            return "请求已取消"
        case .unknown:
            return "未知错误"
        }
    }
    
    /// 从NSError转换为NetworkError
    public static func fromNSError(_ error: NSError) -> NetworkError {
        switch error.code {
        case NSURLErrorNotConnectedToInternet:
            return .noInternetConnection
        case NSURLErrorTimedOut:
            return .timeout
        case NSURLErrorCancelled:
            return .cancelled
        default:
            return .requestFailed(error)
        }
    }
    
    /// 从HTTP状态码创建NetworkError
    public static func fromHTTPResponse(statusCode: Int, data: Data?) -> NetworkError {
        switch statusCode {
        case 401:
            return .unauthorized
        case 400...499:
            var message: String? = nil
            if let data = data, let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = json["message"] as? String {
                message = errorMessage
            }
            return .serverError(statusCode: statusCode, message: message)
        case 500...599:
            var message: String? = nil
            if let data = data, let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = json["message"] as? String {
                message = errorMessage
            }
            return .serverError(statusCode: statusCode, message: message)
        default:
            return .unknown
        }
    }
}

/// 用于服务器返回的错误消息
public struct APIErrorResponse: Decodable {
    let message: String
    let code: String?
    let details: String?
}

/// 扩展Error类型提供更好的错误处理
extension Error {
    /// 将任何错误转换为NetworkError
    var asNetworkError: NetworkError {
        if let networkError = self as? NetworkError {
            return networkError
        }
        
        if let nsError = self as NSError? {
            return NetworkError.fromNSError(nsError)
        }
        
        return .requestFailed(self)
    }
} 