import Foundation
import Combine

/// API端点协议
public protocol APIEndpointProtocol {
    var url: URL { get }
    var method: String { get }
    var headers: [String: String] { get }
    var body: () -> Data? { get }
}

/// 网络协议
public protocol NetworkProtocol {
    /// 检查网络连接状态
    /// - Returns: 是否有网络连接
    func isNetworkAvailable() -> Bool

    /// 发送请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - method: HTTP方法
    ///   - headers: HTTP头
    ///   - body: 请求体
    /// - Returns: 响应数据
    func request(url: URL, method: String, headers: [String: String], body: Data?) async throws -> Data

    /// 取消所有请求
    func cancelAllRequests()
}

/// API客户端协议
public protocol APIClientProtocol {
    /// 检查网络连接状态
    var isNetworkAvailable: Bool { get }

    /// 发送请求
    /// - Parameter endpoint: API端点
    /// - Returns: 包含响应数据的发布者
    func request(endpoint: APIEndpointProtocol) -> AnyPublisher<Data, Error>

    /// 发送GET请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - headers: 请求头
    /// - Returns: 响应数据
    func get(_ url: URL, headers: [String: String]) async throws -> Data

    /// 发送POST请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - headers: 请求头
    ///   - body: 请求体
    /// - Returns: 响应数据
    func post(_ url: URL, headers: [String: String], body: Data?) async throws -> Data

    /// 发送DELETE请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - headers: 请求头
    ///   - body: 请求体
    /// - Returns: 响应数据
    func delete(_ url: URL, headers: [String: String], body: Data?) async throws -> Data

    /// 取消所有请求
    func cancelAllRequests()
}