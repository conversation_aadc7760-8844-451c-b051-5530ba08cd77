import Foundation

/// 通用 API 响应包装器，用于解析标准 API 响应格式
struct APIResponseWrapper<T: Decodable>: Decodable {
    /// 操作是否成功
    let success: Bool

    /// 成功消息
    let message: String?

    /// 响应数据
    let data: T?

    /// 错误消息
    let error: String?

    /// 解码键
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case data
        case error
    }

    /// 自定义初始化方法，处理可能的空数据和不同类型的数据
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        success = try container.decode(Bool.self, forKey: .success)
        message = try container.decodeIfPresent(String.self, forKey: .message)
        error = try container.decodeIfPresent(String.self, forKey: .error)

        // 只有在成功时才尝试解析数据
        if success {
            // 尝试解析数据，处理不同的数据类型
            do {
                // 直接尝试解析为目标类型
                data = try container.decodeIfPresent(T.self, forKey: .data)
            } catch {
                // 如果直接解析失败，获取原始数据并尝试特殊处理
                if let dataContainer = try? container.nestedContainer(keyedBy: DynamicCodingKeys.self, forKey: .data) {
                    // 处理嵌套对象
                    let decoder = try container.superDecoder(forKey: .data)
                    data = try? T(from: decoder)
                } else if var dataArray = try? container.nestedUnkeyedContainer(forKey: .data) {
                    // 处理数组
                    let decoder = try container.superDecoder(forKey: .data)
                    data = try? T(from: decoder)
                } else {
                    // 无法解析
                    data = nil
                    print("无法解析 data 字段: \(error)")
                }
            }
        } else {
            data = nil
        }
    }
}

/// 用于动态解码的键
struct DynamicCodingKeys: CodingKey {
    var stringValue: String
    var intValue: Int?

    init?(stringValue: String) {
        self.stringValue = stringValue
        self.intValue = nil
    }

    init?(intValue: Int) {
        self.stringValue = "\(intValue)"
        self.intValue = intValue
    }
} 