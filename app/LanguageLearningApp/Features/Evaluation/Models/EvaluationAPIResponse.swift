import Foundation

/// 评估API响应模型，用于解析API返回的评估数据
struct EvaluationAPIResponse: Codable {
    /// 操作是否成功
    let success: Bool
    /// 消息
    let message: String
    /// 评估数据
    let data: EvaluationAPIData?

    enum CodingKeys: String, CodingKey {
        case success
        case message
        case data
    }
}

/// 评估API数据模型，用于解析API返回的评估数据
struct EvaluationAPIData: Codable {
    /// 评估ID
    let id: String
    /// 用户ID
    let userId: String
    /// 评估类型
    let type: String
    /// 评估标题
    let title: String
    /// 评估描述
    let description: String
    /// 通过分数
    let passingScore: Int
    /// 评估时长（分钟）
    let duration: Int
    /// 问题总数
    let totalQuestions: Int
    /// 评估部分
    let sections: [APIEvaluationSection]
    /// 是否已开始
    let isStarted: Bool?
    /// 开始时间
    let startedAt: String?
    /// 是否已完成 (可能是Int或Bool)
    private let _isCompleted: Any
    /// 完成时间
    let completedAt: String?
    /// 创建时间
    let createdAt: String
    /// 更新时间
    let updatedAt: String

    /// 获取isCompleted的值（转换为Int）
    var isCompleted: Int {
        if let intValue = _isCompleted as? Int {
            return intValue
        } else if let boolValue = _isCompleted as? Bool {
            return boolValue ? 1 : 0
        } else if let stringValue = _isCompleted as? String {
            return stringValue == "true" || stringValue == "1" ? 1 : 0
        }
        return 0
    }

    enum CodingKeys: String, CodingKey {
        case id
        case userId
        case type
        case title
        case description
        case passingScore
        case duration
        case totalQuestions
        case sections
        case isStarted
        case startedAt
        case _isCompleted = "isCompleted"
        case completedAt
        case createdAt
        case updatedAt
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        userId = try container.decode(String.self, forKey: .userId)
        type = try container.decode(String.self, forKey: .type)
        title = try container.decode(String.self, forKey: .title)
        description = try container.decode(String.self, forKey: .description)
        passingScore = try container.decode(Int.self, forKey: .passingScore)
        duration = try container.decode(Int.self, forKey: .duration)
        totalQuestions = try container.decode(Int.self, forKey: .totalQuestions)
        sections = try container.decode([APIEvaluationSection].self, forKey: .sections)
        isStarted = try container.decodeIfPresent(Bool.self, forKey: .isStarted)
        startedAt = try container.decodeIfPresent(String.self, forKey: .startedAt)
        completedAt = try container.decodeIfPresent(String.self, forKey: .completedAt)
        createdAt = try container.decode(String.self, forKey: .createdAt)
        updatedAt = try container.decode(String.self, forKey: .updatedAt)

        // 尝试不同类型的解码
        if let intValue = try? container.decode(Int.self, forKey: ._isCompleted) {
            _isCompleted = intValue
            print("isCompleted解码为Int: \(intValue)")
        } else if let boolValue = try? container.decode(Bool.self, forKey: ._isCompleted) {
            _isCompleted = boolValue
            print("isCompleted解码为Bool: \(boolValue)")
        } else if let stringValue = try? container.decode(String.self, forKey: ._isCompleted) {
            _isCompleted = stringValue
            print("isCompleted解码为String: \(stringValue)")
        } else {
            _isCompleted = 0
            print("isCompleted无法解码，使用默认值0")
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(userId, forKey: .userId)
        try container.encode(type, forKey: .type)
        try container.encode(title, forKey: .title)
        try container.encode(description, forKey: .description)
        try container.encode(passingScore, forKey: .passingScore)
        try container.encode(duration, forKey: .duration)
        try container.encode(totalQuestions, forKey: .totalQuestions)
        try container.encode(sections, forKey: .sections)
        try container.encodeIfPresent(isStarted, forKey: .isStarted)
        try container.encodeIfPresent(startedAt, forKey: .startedAt)
        try container.encodeIfPresent(completedAt, forKey: .completedAt)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)

        // 编码isCompleted字段
        // 将isCompleted属性转换为Int进行编码
        try container.encode(isCompleted, forKey: ._isCompleted)
    }

    /// 转换为应用内Evaluation模型
    func toEvaluation() -> Evaluation {
        // 解析日期
        let dateFormatter = ISO8601DateFormatter()
        let createdAtDate = dateFormatter.date(from: createdAt) ?? Date()
        let updatedAtDate = dateFormatter.date(from: updatedAt) ?? Date()
        let startedAtDate = startedAt != nil ? dateFormatter.date(from: startedAt!) : nil
        let completedAtDate = completedAt != nil ? dateFormatter.date(from: completedAt!) : nil

        // 转换部分
        let convertedSections = sections.map { section -> EvaluationSection in
            // 创建问题列表
            let questions = section.questions?.map { question -> EvaluationQuestion in
                return EvaluationQuestion(
                    id: UUID(),
                    sectionID: UUID(),
                    type: QuestionType(rawValue: question.type ?? "multiple-choice") ?? .multipleChoice,
                    content: question.content ?? "未知问题",
                    options: question.options,
                    correctAnswer: question.correctAnswer ?? "",
                    userAnswer: nil,
                    isCorrect: nil,
                    points: question.points ?? 5,
                    explanation: nil,
                    audioURL: nil,
                    imageURL: nil
                )
            } ?? []

            // 创建部分
            return EvaluationSection(
                id: UUID(),
                evaluationID: UUID(uuidString: section.evaluationId ?? "") ?? UUID(),
                title: section.title ?? "未知部分",
                skill: section.skill ?? "unknown",
                weight: section.weight ?? 1,
                score: nil,
                questions: questions
            )
        }

        // 创建Evaluation对象
        return Evaluation(
            id: UUID(uuidString: id) ?? UUID(),
            userID: UUID(uuidString: userId) ?? UUID(),
            type: EvaluationType(rawValue: type) ?? .placement,
            category: .vocabulary,  // 默认使用词汇类别
            status: isCompleted == 1 ? .completed : (isStarted == true ? .inProgress : .available),  // 使用正确的状态枚举值
            title: title,
            description: description,
            passingScore: passingScore,
            duration: duration,
            totalQuestions: totalQuestions,
            sections: convertedSections,
            isStarted: startedAtDate != nil,
            isCompleted: isCompleted == 1,
            createdAt: createdAtDate,
            updatedAt: updatedAtDate
        )
    }
}
