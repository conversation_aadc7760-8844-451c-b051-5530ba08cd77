import Foundation

// 扩展 EvaluationQuestion 以提供更好的兼容性
extension EvaluationQuestion {
    /// 将 EvaluationQuestion 转换为字典格式，以便与 ExerciseDataProcessor 兼容
    func toDictionary() -> [String: Any] {
        var dict: [String: Any] = [
            "id": id.uuidString,
            "content": content,
            "type": type.rawValue
        ]

        if let options = options {
            dict["options"] = options
        }

        // correctAnswer is non-optional, so we can directly add it
        dict["correctAnswer"] = correctAnswer

        if let userAnswer = userAnswer {
            dict["userAnswer"] = userAnswer
        }

        if let isCorrect = isCorrect {
            dict["isCorrect"] = isCorrect
        }

        if let explanation = explanation {
            dict["explanation"] = explanation
        }

        if let audioURL = audioURL {
            dict["audioURL"] = audioURL.absoluteString
        }

        if let imageURL = imageURL {
            dict["imageURL"] = imageURL.absoluteString
        }

        return dict
    }
}
