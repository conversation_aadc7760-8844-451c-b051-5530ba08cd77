import Foundation


/// 课程推荐模型
struct LessonRecommendation: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let difficulty: String
    let estimatedTimeMinutes: Int
}

/// 练习推荐模型
struct PracticeRecommendation: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: String
    let focusArea: String
    let estimatedTimeMinutes: Int
}

/// 资源推荐模型
struct ResourceRecommendation: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: String
    let url: String
}

/// 个性化学习路径模型
struct PersonalizedLearningPath: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let evaluationId: UUID
    let targetLevel: String
    let currentLevel: String
    let estimatedTimeToComplete: Int
    let recommendedDailyStudyTime: Int
    let recommendedDaysPerWeek: Int
    let learningObjectives: [String]
    let learningTips: [String]
    let recommendedLessons: [LessonRecommendation]
    let recommendedPractices: [PracticeRecommendation]
    let recommendedResources: [ResourceRecommendation]
    let status: LearningPathStatus
    let createdAt: Date
    let updatedAt: Date
}
