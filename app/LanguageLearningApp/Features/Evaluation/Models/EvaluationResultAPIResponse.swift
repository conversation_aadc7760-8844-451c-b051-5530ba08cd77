import Foundation

/// API评估结果响应模型，用于解析API返回的评估结果数据
struct EvaluationResultAPIResponse: Codable {
    /// 操作是否成功
    let success: Bool
    /// 消息
    let message: String
    /// 评估结果数据
    let data: EvaluationResultAPIData
    
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case data
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 处理success字段，可能是Bool或Int
        if let boolValue = try? container.decode(Bool.self, forKey: .success) {
            success = boolValue
        } else if let intValue = try? container.decode(Int.self, forKey: .success) {
            success = intValue == 1
        } else {
            success = false
        }
        
        message = try container.decode(String.self, forKey: .message)
        data = try container.decode(EvaluationResultAPIData.self, forKey: .data)
    }
}

/// API评估结果数据模型
struct EvaluationResultAPIData: Codable {
    /// 结果ID
    let id: String
    /// 评估ID
    let evaluationId: String
    /// 用户ID
    let userId: String
    /// 总分
    let overallScore: Int
    /// 通过分数
    let passingScore: Int
    /// 是否通过
    let isPassed: Bool
    /// 反馈
    let feedback: String
    /// 建议
    let recommendations: [String]
    /// 部分得分
    let sectionScores: [EvaluationSectionScoreAPI]
    /// 完成时间
    let completedAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case evaluationId
        case userId
        case overallScore
        case passingScore
        case isPassed
        case feedback
        case recommendations
        case sectionScores
        case completedAt
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(String.self, forKey: .id)
        evaluationId = try container.decode(String.self, forKey: .evaluationId)
        userId = try container.decode(String.self, forKey: .userId)
        overallScore = try container.decode(Int.self, forKey: .overallScore)
        passingScore = try container.decode(Int.self, forKey: .passingScore)
        
        // 处理isPassed字段，可能是Bool或Int
        if let boolValue = try? container.decode(Bool.self, forKey: .isPassed) {
            isPassed = boolValue
        } else if let intValue = try? container.decode(Int.self, forKey: .isPassed) {
            isPassed = intValue == 1
        } else {
            isPassed = false
        }
        
        feedback = try container.decode(String.self, forKey: .feedback)
        recommendations = try container.decode([String].self, forKey: .recommendations)
        sectionScores = try container.decode([EvaluationSectionScoreAPI].self, forKey: .sectionScores)
        completedAt = try container.decode(String.self, forKey: .completedAt)
    }
    
    /// 转换为应用内EvaluationResult模型
    func toEvaluationResult() -> EvaluationResult {
        // 解析日期
        let dateFormatter = ISO8601DateFormatter()
        let completedAtDate = dateFormatter.date(from: completedAt) ?? Date()
        
        // 转换部分得分
        let convertedSectionScores = sectionScores.map { apiScore -> EvaluationSectionScore in
            return EvaluationSectionScore(
                skill: apiScore.skill,
                score: apiScore.score,
                maxScore: 100, // 假设最高分为100
                sectionTitle: apiScore.sectionTitle,
                weaknesses: apiScore.weaknesses
            )
        }
        
        // 创建EvaluationResult对象
        return EvaluationResult(
            id: UUID(uuidString: id) ?? UUID(),
            evaluationID: UUID(uuidString: evaluationId) ?? UUID(),
            userID: UUID(uuidString: userId) ?? UUID(),
            overallScore: overallScore,
            maxScore: 100, // 假设最高分为100
            isPassed: isPassed,
            level: isPassed ? "通过" : "未通过", // 简单的级别判断
            feedback: feedback,
            recommendations: recommendations,
            sectionScores: convertedSectionScores,
            learningPath: nil,
            completedAt: completedAtDate,
            createdAt: completedAtDate // 使用完成时间作为创建时间
        )
    }
}

/// API评估部分得分模型
struct EvaluationSectionScoreAPI: Codable {
    /// 技能名称
    let skill: String
    /// 部分标题
    let sectionTitle: String
    /// 得分
    let score: Int
    /// 弱点列表
    let weaknesses: [String]
    
    enum CodingKeys: String, CodingKey {
        case skill
        case sectionTitle
        case score
        case weaknesses
    }
}
