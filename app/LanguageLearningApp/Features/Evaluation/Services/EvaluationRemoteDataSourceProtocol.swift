import Foundation
import Combine

public protocol EvaluationRemoteDataSourceProtocol {
    typealias T = Evaluation
    typealias ID = UUID

    // Combine-based methods
    func getAll(authToken: String) -> AnyPublisher<[Evaluation], Error>
    func getById(_ id: ID, authToken: String) -> AnyPublisher<Evaluation, Error>
    func save(_ entity: Evaluation, authToken: String) -> AnyPublisher<Evaluation, Error>
    func saveAll(_ entities: [Evaluation], authToken: String) -> AnyPublisher<[Evaluation], Error>
    func delete(_ id: ID, authToken: String) -> AnyPublisher<Bool, Error>

    func startEvaluation(evaluationId: UUID, authToken: String) -> AnyPublisher<Evaluation, Error>
    func submitAnswer(evaluationId: UUID, questionId: String, answer: EvaluationAnswer, authToken: String) -> AnyPublisher<Evaluation, Error>
    func completeEvaluation(evaluationId: UUID, authToken: String) -> AnyPublisher<EvaluationResult, Error>
    func getUserEvaluationHistory(userId: String, authToken: String) -> AnyPublisher<[EvaluationResult], Error>
    func getEvaluationResult(evaluationId: UUID, authToken: String) -> AnyPublisher<EvaluationResult, Error>

    // MARK: - Async versions
    func getAllAsync(authToken: String) async throws -> [Evaluation]
    func getByIdAsync(_ id: ID, authToken: String) async throws -> Evaluation
    func saveAsync(_ entity: Evaluation, authToken: String) async throws -> Evaluation
    func saveAllAsync(_ entities: [Evaluation], authToken: String) async throws -> [Evaluation]
    func deleteAsync(_ id: ID, authToken: String) async throws -> Bool

    func getAvailableEvaluationsAsync(userId: String, authToken: String) async throws -> [Evaluation]
    func startEvaluationAsync(evaluationId: UUID, authToken: String) async throws -> Evaluation
    func submitAnswerAsync(evaluationId: UUID, questionId: String, answer: EvaluationAnswer, userId: String, authToken: String) async throws -> Evaluation
    func completeEvaluationAsync(evaluationId: UUID, authToken: String) async throws -> EvaluationResult
    func getUserEvaluationHistoryAsync(userId: String, authToken: String) async throws -> [EvaluationResult]
    func getEvaluationResultAsync(evaluationId: UUID, authToken: String) async throws -> EvaluationResult
} 