import Foundation
import Combine

/// 评估本地数据源实现
public class EvaluationLocalDataSource: EvaluationLocalDataSourceProtocol {
    // 存储键
    private let evaluationStorageKey = "evaluations"
    private let resultStorageKey = "evaluation_results"
    
    // 单例实例
    public static let shared = EvaluationLocalDataSource()
    
    // 私有初始化方法，确保单例模式
    private init() {}
    
    /// 获取所有评估
    /// - Returns: 评估列表
    public func getAll() throws -> [Evaluation] {
        guard let data = UserDefaults.standard.data(forKey: evaluationStorageKey) else {
            return []
        }
        return try JSONDecoder().decode([Evaluation].self, from: data)
    }
    
    /// 根据ID获取评估
    /// - Parameter id: 评估ID
    /// - Returns: 评估（如存在）
    public func getById(_ id: UUID) throws -> Evaluation? {
        let evaluations = try getAll()
        return evaluations.first { $0.id == id }
    }
    
    /// 保存评估
    /// - Parameter entity: 要保存的评估
    /// - Returns: 保存后的评估
    public func save(_ entity: Evaluation) throws -> Evaluation {
        var evaluations = try getAll()
        evaluations.removeAll { $0.id == entity.id }
        evaluations.append(entity)
        let data = try JSONEncoder().encode(evaluations)
        UserDefaults.standard.set(data, forKey: evaluationStorageKey)
        return entity
    }
    
    /// 保存多个评估
    /// - Parameter entities: 要保存的评估列表
    /// - Returns: 保存后的评估列表
    public func saveAll(_ entities: [Evaluation]) throws -> [Evaluation] {
        var evaluations = try getAll()
        for entity in entities {
            evaluations.removeAll { $0.id == entity.id }
        }
        evaluations.append(contentsOf: entities)
        let data = try JSONEncoder().encode(evaluations)
        UserDefaults.standard.set(data, forKey: evaluationStorageKey)
        return entities
    }
    
    /// 删除评估
    /// - Parameter id: 要删除的评估ID
    /// - Returns: 删除是否成功
    public func delete(_ id: UUID) throws -> Bool {
        var evaluations = try getAll()
        let initialCount = evaluations.count
        evaluations.removeAll { $0.id == id }
        if evaluations.count < initialCount {
            let data = try JSONEncoder().encode(evaluations)
            UserDefaults.standard.set(data, forKey: evaluationStorageKey)
            return true
        }
        return false
    }
    
    /// 删除所有评估记录
    public func deleteAll() throws {
        UserDefaults.standard.removeObject(forKey: evaluationStorageKey)
    }
    
    /// 获取用户可用的评估 (currently returns all locally cached evaluations)
    public func getAvailableEvaluations(userId: String) throws -> [Evaluation] {
        // Current Evaluation model doesn't seem to be user-specific for local cache filtering
        // This might need refinement if local evaluations should be filtered by user.
        return try getAll()
    }
    
    /// 获取评估结果
    /// - Parameter evaluationId: 评估ID
    /// - Parameter userId: 用户ID
    /// - Returns: 评估结果（如存在）
    public func getEvaluationResult(evaluationId: UUID, userId: String) throws -> EvaluationResult? {
        guard let data = UserDefaults.standard.data(forKey: resultStorageKey) else {
            return nil
        }
        let results = try JSONDecoder().decode([EvaluationResult].self, from: data)
        // Assuming EvaluationResult has an `evaluationID` or similar property, and `userID` (String).
        // If EvaluationResult.id is the evaluationId, and it also contains userId:
        return results.first { $0.id == evaluationId && $0.userID.uuidString == userId } // Adapt if userID on EvaluationResult is String
        // If EvaluationResult has a specific `evaluationId` property:
        // return results.first { $0.evaluationId == evaluationId && $0.userID.uuidString == userId }
    }
    
    /// 保存评估结果
    /// - Parameter result: 要保存的评估结果
    /// - Parameter evaluationId: 评估ID
    /// - Parameter userId: 用户ID
    /// - Returns: 保存的评估结果
    public func saveEvaluationResult(result: EvaluationResult, evaluationId: UUID, userId: String) throws {
        // Parameters evaluationId and userId are for protocol conformance, actual data is in result object.
        var allResults: [EvaluationResult] = []        
        if let data = UserDefaults.standard.data(forKey: resultStorageKey) {
            allResults = try JSONDecoder().decode([EvaluationResult].self, from: data)
        }
        allResults.removeAll { $0.id == result.id } // Assuming result.id is unique for an evaluation attempt
        allResults.append(result)
        let data = try JSONEncoder().encode(allResults)
        UserDefaults.standard.set(data, forKey: resultStorageKey)
    }
    
    /// 获取用户评估历史
    /// - Parameter userId: 用户ID
    /// - Returns: 评估结果列表
    public func getUserEvaluationHistory(userId: String) throws -> [EvaluationResult] {
        guard let data = UserDefaults.standard.data(forKey: resultStorageKey) else {
            return []
        }
        let results = try JSONDecoder().decode([EvaluationResult].self, from: data)
        // Assuming EvaluationResult.userID is UUID, converting to String for comparison
        return results.filter { $0.userID.uuidString == userId }
    }
    
    /// 清除所有评估相关数据 (replaces clearAll with synchronous throwing version)
    public func clearAllEvaluationData() throws {
        UserDefaults.standard.removeObject(forKey: evaluationStorageKey)
        UserDefaults.standard.removeObject(forKey: resultStorageKey)
    }
} 