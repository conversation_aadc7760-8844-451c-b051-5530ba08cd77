import Foundation
import Combine

// Protocol for EvaluationLocalDataSource
public protocol EvaluationLocalDataSourceProtocol {
    // Generic CRUD (matching RepositoryProtocol needs if used directly)
    func getAll() throws -> [Evaluation]
    func getById(_ id: UUID) throws -> Evaluation?
    func save(_ entity: Evaluation) throws -> Evaluation
    func saveAll(_ entities: [Evaluation]) throws -> [Evaluation] // Or Void, depending on use
    func delete(_ id: UUID) throws -> Bool // Or Void
    func deleteAll() throws // Specific to EvaluationLocalDataSource

    // Evaluation-specific local operations (synchronous, throwing)
    func getAvailableEvaluations(userId: String) throws -> [Evaluation] // Assuming user ID might filter local cache
    func getEvaluationResult(evaluationId: UUID, userId: String) throws -> EvaluationResult?
    func saveEvaluationResult(result: EvaluationResult, evaluationId: UUID, userId: String) throws
    func getUserEvaluationHistory(userId: String) throws -> [EvaluationResult] // Or [EvaluationSummary]
    func clearAllEvaluationData() throws // More specific than just deleteAll which might be on T
} 