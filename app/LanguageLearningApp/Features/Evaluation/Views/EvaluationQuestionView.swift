import SwiftUI
import AVFoundation

/// 评估问题视图
struct EvaluationQuestionView: View {
    @ObservedObject var viewModel: EvaluationViewModel
    @State private var selectedOption: String?
    @State private var textInput: String = ""
    @State private var isRecording: Bool = false
    @State private var recordingURL: URL?
    @State private var audioPlayer: AVAudioPlayer?
    @State private var isPlaying: Bool = false
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部进度条
            ProgressView(value: viewModel.progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .padding(.horizontal)
            
            // 剩余时间
            if let remainingTime = viewModel.remainingTime {
                Text("剩余时间: \(formatTime(remainingTime))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 5)
            }
            
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 问题内容
                    if let question = viewModel.currentQuestion {
                        // 问题标题
                        Text("问题 \(viewModel.currentQuestionIndex + 1)/\(viewModel.totalQuestions)")
                            .font(.headline)
                            .foregroundColor(.secondary)
                            .padding(.top)
                        
                        // 问题内容
                        Text(question.content)
                            .font(.title3)
                            .fontWeight(.medium)
                            .padding(.vertical, 5)
                        
                        // 根据问题类型显示不同的回答界面
                        switch question.type {
                        case .multipleChoice, .singleChoice:
                            multipleChoiceView(question)
                        case .fillIn:
                            fillInView()
                        case .speaking:
                            speakingView()
                        case .listening:
                            listeningView(question)
                        }
                    } else {
                        // 加载中
                        ProgressView()
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding()
                    }
                    
                    Spacer()
                }
                .padding()
                .frame(maxWidth: .infinity)
            }
            
            // 底部提交按钮
            Button(action: submitAnswer) {
                Text("提交答案")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(isAnswerValid() ? Color.blue : Color.gray)
                    .cornerRadius(10)
            }
            .disabled(!isAnswerValid())
            .padding()
        }
        .navigationTitle("语言评估")
        .navigationBarTitleDisplayMode(.inline)
        .background(colorScheme == .dark ? Color.black : Color.white)
        .onDisappear {
            stopAudio()
        }
    }
    
    /// 多选题视图
    /// - Parameter question: 问题模型
    /// - Returns: 多选题视图
    private func multipleChoiceView(_ question: EvaluationQuestion) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            if let options = question.options {
                ForEach(options, id: \.self) { option in
                    Button(action: {
                        selectedOption = option
                    }) {
                        HStack {
                            Text(option)
                                .font(.body)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            if selectedOption == option {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(selectedOption == option ? Color.blue.opacity(0.1) : Color(UIColor.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    /// 填空题视图
    /// - Returns: 填空题视图
    private func fillInView() -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("请输入您的答案:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            TextField("在此输入", text: $textInput)
                .padding()
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)
                .autocapitalization(.none)
        }
    }
    
    /// 口语题视图
    /// - Returns: 口语题视图
    private func speakingView() -> some View {
        VStack(spacing: 20) {
            Text("请录制您的回答")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 录音按钮
            Button(action: toggleRecording) {
                VStack {
                    Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(isRecording ? .red : .blue)
                    
                    Text(isRecording ? "停止录音" : "开始录音")
                        .font(.caption)
                        .foregroundColor(isRecording ? .red : .blue)
                        .padding(.top, 5)
                }
                .padding()
                .background(
                    Circle()
                        .fill(Color(UIColor.systemGray6))
                        .frame(width: 120, height: 120)
                )
            }
            
            // 播放录音按钮（如果已录制）
            if recordingURL != nil {
                Button(action: togglePlayback) {
                    HStack {
                        Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                            .foregroundColor(.blue)
                        
                        Text(isPlaying ? "暂停" : "播放录音")
                            .foregroundColor(.blue)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.blue, lineWidth: 1)
                    )
                }
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(UIColor.systemGray6))
        .cornerRadius(10)
    }
    
    /// 听力题视图
    /// - Parameter question: 问题模型
    /// - Returns: 听力题视图
    private func listeningView(_ question: EvaluationQuestion) -> some View {
        VStack(spacing: 20) {
            Text("请听音频并选择正确答案")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 播放音频按钮
            Button(action: togglePlayback) {
                HStack {
                    Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                    
                    Text(isPlaying ? "暂停" : "播放音频")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.blue)
                .cornerRadius(10)
            }
            
            // 选项
            if let options = question.options {
                ForEach(options, id: \.self) { option in
                    Button(action: {
                        selectedOption = option
                    }) {
                        HStack {
                            Text(option)
                                .font(.body)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            if selectedOption == option {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(selectedOption == option ? Color.blue.opacity(0.1) : Color(UIColor.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    /// 切换录音状态
    private func toggleRecording() {
        isRecording.toggle()
        
        if isRecording {
            startRecording()
        } else {
            stopRecording()
        }
    }
    
    /// 开始录音
    private func startRecording() {
        let audioSession = AVAudioSession.sharedInstance()
        
        do {
            try audioSession.setCategory(.playAndRecord, mode: .default)
            try audioSession.setActive(true)
            
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let audioFilename = documentsPath.appendingPathComponent("recording-\(Date().timeIntervalSince1970).m4a")
            
            let settings = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100,
                AVNumberOfChannelsKey: 2,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
            ]
            
            // 这里需要实际实现录音逻辑
            // 简化处理，仅设置URL
            recordingURL = audioFilename
            
        } catch {
            print("录音失败: \(error.localizedDescription)")
        }
    }
    
    /// 停止录音
    private func stopRecording() {
        // 这里需要实际实现停止录音逻辑
    }
    
    /// 切换音频播放状态
    private func togglePlayback() {
        if isPlaying {
            stopAudio()
        } else {
            playAudio()
        }
    }
    
    /// 播放音频
    private func playAudio() {
        // 这里需要实际实现音频播放逻辑
        isPlaying = true
    }
    
    /// 停止音频
    private func stopAudio() {
        // 这里需要实际实现停止音频逻辑
        isPlaying = false
    }
    
    /// 检查答案是否有效
    /// - Returns: 是否有效
    private func isAnswerValid() -> Bool {
        guard let question = viewModel.currentQuestion else { return false }
        
        switch question.type {
        case .multipleChoice, .singleChoice:
            return selectedOption != nil
        case .fillIn:
            return !textInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        case .speaking:
            return recordingURL != nil
        case .listening:
            return selectedOption != nil
        }
    }
    
    /// 提交答案
    private func submitAnswer() {
        guard let question = viewModel.currentQuestion else { return }
        
        var answer = ""
        
        switch question.type {
        case .multipleChoice, .singleChoice:
            if let selectedOption = selectedOption {
                answer = selectedOption
            }
        case .fillIn:
            answer = textInput
        case .speaking:
            if let url = recordingURL {
                answer = url.absoluteString
            }
        case .listening:
            if let selectedOption = selectedOption {
                answer = selectedOption
            }
        }
        
        viewModel.submitAnswer(answer: answer)
        
        // 重置状态
        selectedOption = nil
        textInput = ""
        recordingURL = nil
        isRecording = false
        isPlaying = false
    }
    
    /// 格式化时间
    /// - Parameter seconds: 秒数
    /// - Returns: 格式化的时间字符串（mm:ss）
    private func formatTime(_ seconds: TimeInterval) -> String {
        let minutes = Int(seconds) / 60
        let seconds = Int(seconds) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

struct EvaluationQuestionView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EvaluationQuestionView(viewModel: EvaluationViewModel())
        }
    }
}
