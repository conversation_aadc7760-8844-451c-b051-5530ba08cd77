import SwiftUI
import Combine

/// 开始新评估视图
struct StartNewEvaluationView: View {
    @StateObject private var viewModel: StartNewEvaluationViewModel
    @Environment(\.dismiss) private var dismiss

    init(viewModel: StartNewEvaluationViewModel = StartNewEvaluationViewModel(
        evaluationManager: DependencyContainer.shared.resolve(EvaluationManager.self)
    )) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }

    var body: some View {
        // 打印视图状态，帮助调试
        let _ = print("视图状态: isLoading=\(viewModel.isLoading), showError=\(viewModel.showError)")

        ZStack {
            // 背景
            Color(UIColor.systemBackground)
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 20) {
                // 标题
                Text("开始新的语言评估")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 30)

                // 说明
                VStack(alignment: .leading, spacing: 15) {
                    Text("评估说明")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("通过完成这个评估，我们将能够：")
                        .font(.subheadline)

                    VStack(alignment: .leading, spacing: 10) {
                        bulletPoint("评估您当前的语言水平")
                        bulletPoint("创建个性化的学习路径")
                        bulletPoint("为您推荐适合的练习内容")
                        bulletPoint("追踪您的学习进度")
                    }
                    .padding(.leading)

                    Text("评估大约需要15-20分钟完成，包含听力、阅读、语法和词汇等多个部分。")
                        .font(.subheadline)
                        .padding(.top, 5)
                }
                .padding()
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(10)

                // 开始按钮
                Button(action: {
                    print("开始评估按钮被点击")
                    // 直接调用视图模型的方法，不使用alert
                    viewModel.startNewEvaluation()
                }) {
                    Text("开始评估")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(10)
                }
                .padding(.top, 20)
                .disabled(viewModel.isLoading)

                // 取消按钮
                Button(action: {
                    dismiss()
                }) {
                    Text("稍后再说")
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(10)
                }
                .disabled(viewModel.isLoading)

                Spacer()
            }
            .padding()

            // 加载指示器
            if viewModel.isLoading {
                ProgressView("正在准备评估...")
                    .progressViewStyle(CircularProgressViewStyle())
                    .padding()
                    .background(Color(UIColor.systemBackground))
                    .cornerRadius(10)
                    .shadow(radius: 5)
            }
        }
        .navigationTitle("语言评估")
        .navigationBarTitleDisplayMode(.inline)
        .background(
            NavigationLink(
                destination: EvaluationView(evaluationId: viewModel.currentEvaluationId),
                isActive: $viewModel.navigateToEvaluation,
                label: { EmptyView() }
            )
        )
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage)
        }
    }

    /// 创建项目符号点
    private func bulletPoint(_ text: String) -> some View {
        HStack(alignment: .top, spacing: 10) {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
                .font(.system(size: 16))

            Text(text)
                .font(.subheadline)
        }
    }
}

/// 开始新评估视图模型
class StartNewEvaluationViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var showError = false
    @Published var errorMessage = ""
    @Published var navigateToEvaluation = false
    @Published var currentEvaluationId: UUID?

    private let evaluationManager: EvaluationManager
    private var cancellables = Set<AnyCancellable>()

    init(evaluationManager: EvaluationManager) {
        self.evaluationManager = evaluationManager
        setupSubscriptions()
    }

    /// 设置订阅
    private func setupSubscriptions() {
        // 监听当前评估
        evaluationManager.$currentEvaluation
            .receive(on: DispatchQueue.main)
            .sink { [weak self] evaluation in
                if let evaluation = evaluation {
                    self?.currentEvaluationId = evaluation.id
                    self?.navigateToEvaluation = true
                    self?.isLoading = false
                }
            }
            .store(in: &cancellables)

        // 监听错误
        evaluationManager.$error
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                if let error = error {
                    self?.isLoading = false
                    self?.showError = true
                    self?.errorMessage = error.localizedDescription
                    print("创建评估失败: \(error.localizedDescription)")

                    // 如果是网络错误，创建一个离线评估ID
                    if error.localizedDescription.contains("网络") || error.localizedDescription.contains("连接") {
                        print("检测到网络错误，创建离线评估ID")
                        let offlineEvaluationId = UUID()
                        self?.currentEvaluationId = offlineEvaluationId

                        // 延迟1秒后导航到评估页面
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            print("准备导航到离线评估页面，评估ID: \(offlineEvaluationId)")
                            self?.navigateToEvaluation = true
                        }
                    }
                }
            }
            .store(in: &cancellables)

        // 监听加载状态
        evaluationManager.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                self?.isLoading = isLoading
            }
            .store(in: &cancellables)
    }

    /// 开始新评估
    func startNewEvaluation() {
        // 重置状态
        showError = false
        errorMessage = ""
        navigateToEvaluation = false
        currentEvaluationId = nil

        print("开始创建新评估...")

        // 使用评估管理器创建评估
        evaluationManager.createEvaluation { [weak self] evaluationId in
            DispatchQueue.main.async {
                if let evaluationId = evaluationId {
                    print("成功创建评估，ID: \(evaluationId)")
                    self?.currentEvaluationId = evaluationId
                    self?.navigateToEvaluation = true
                } else {
                    print("创建评估失败")
                    // 创建一个离线评估ID
                    let offlineEvaluationId = UUID()
                    self?.currentEvaluationId = offlineEvaluationId

                    // 延迟1秒后导航到评估页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        print("准备导航到离线评估页面，评估ID: \(offlineEvaluationId)")
                        self?.navigateToEvaluation = true
                    }
                }
            }
        }
    }
}

struct StartNewEvaluationView_Previews: PreviewProvider {
    static var previews: some View {
        StartNewEvaluationView()
    }
}
