import SwiftUI

/// 评估历史视图，显示用户的评估历史记录
struct EvaluationHistoryView: View {
    let history: [EvaluationResult]
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var selectedResult: EvaluationResult?
    @State private var showingDetail = false

    var body: some View {
        NavigationView {
            StyledContainer {
                Group {
                    if history.isEmpty {
                        emptyHistoryView
                    } else {
                        historyListView
                    }
                }
                .navigationBarTitle(localizationManager.localizedString(LocalizationKey.assessment_history), displayMode: .inline)
                .navigationBarItems(trailing: closeButton)
                .sheet(item: $selectedResult) { result in
                    EvaluationResultView(viewModel: EvaluationResultViewModel(resultId: result.id))
                }
            }
        }
    }

    // 关闭按钮
    private var closeButton: some View {
        Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.title3)
                .foregroundColor(AppTheme.Colors.textSecondary)
        }
    }

    // 空历史视图
    private var emptyHistoryView: some View {
        VStack(spacing: AppTheme.Dimensions.spacingLarge) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(AppTheme.Colors.textSecondary)

            Text(localizationManager.localizedString(LocalizationKey.no_assessment_history))
                .font(AppTheme.Typography.title2)
                .foregroundColor(AppTheme.Colors.textPrimary)

            Text(localizationManager.localizedString(LocalizationKey.no_assessment_history_desc))
                .font(AppTheme.Typography.body)
                .foregroundColor(AppTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .padding()
    }

    // 历史列表视图
    private var historyListView: some View {
        List {
            ForEach(history) { result in
                Button(action: {
                    selectedResult = result
                }) {
                    historyRow(for: result)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .listStyle(InsetGroupedListStyle())
    }

    // 历史行
    private func historyRow(for result: EvaluationResult) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingSmall) {
                Text(result.localizedLevelName())
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                Text(result.completedTimeDescription())
                    .font(AppTheme.Typography.caption)
                    .foregroundColor(AppTheme.Colors.textSecondary)
            }

            Spacer()

            ZStack {
                Circle()
                    .stroke(AppTheme.Colors.backgroundSecondary, lineWidth: 4)
                    .frame(width: 50, height: 50)

                Circle()
                    .trim(from: 0, to: CGFloat(result.scorePercentage / 100))
                    .stroke(scoreColor(for: result.overallPerformanceLevel), lineWidth: 4)
                    .frame(width: 50, height: 50)
                    .rotationEffect(.degrees(-90))

                Text("\(result.overallScore)")
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)
            }

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(AppTheme.Colors.textSecondary)
        }
        .padding(.vertical, AppTheme.Dimensions.spacingSmall)
    }

    // 根据表现级别返回颜色
    private func scoreColor(for level: PerformanceLevel) -> Color {
        switch level {
        case .excellent:
            return AppTheme.Colors.success
        case .good:
            return AppTheme.Colors.primary
        case .satisfactory:
            return AppTheme.Colors.warning
        case .needsImprovement:
            return AppTheme.Colors.error
        }
    }
}

struct EvaluationHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        let sectionScores = [
            EvaluationSectionScore(skill: "listening", score: 80, maxScore: 100),
            EvaluationSectionScore(skill: "reading", score: 75, maxScore: 100)
        ]

        let mockResults = [
            EvaluationResult(
                id: UUID(),
                evaluationID: UUID(),
                userID: UUID(),
                overallScore: 75,
                maxScore: 100,
                isPassed: true,
                level: "intermediate",
                feedback: "您的中文水平已达到中级水平",
                recommendations: ["加强听力练习"],
                sectionScores: sectionScores,
                learningPath: nil,
                completedAt: Date().addingTimeInterval(-7*24*60*60),
                createdAt: Date().addingTimeInterval(-7*24*60*60)
            ),
            EvaluationResult(
                id: UUID(),
                evaluationID: UUID(),
                userID: UUID(),
                overallScore: 85,
                maxScore: 100,
                isPassed: true,
                level: "upper_intermediate",
                feedback: "您的中文水平有了显著提高",
                recommendations: ["继续加强口语流利度"],
                sectionScores: sectionScores,
                learningPath: nil,
                completedAt: Date(),
                createdAt: Date()
            )
        ]

        return EvaluationHistoryView(history: mockResults)
    }
}
