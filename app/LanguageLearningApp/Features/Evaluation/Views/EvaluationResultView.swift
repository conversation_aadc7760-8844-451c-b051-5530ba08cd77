import SwiftUI

/// 评估结果视图
struct EvaluationResultView: View {
    @ObservedObject var viewModel: EvaluationResultViewModel
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.presentationMode) var presentationMode
    @State private var navigateToPractice = false

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 结果标题
                Text("评估结果")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(Color.primary)
                    .padding(.top)

                if case .loaded = viewModel.state, let result = viewModel.result {
                    // 结果摘要
                    resultSummaryView(result)

                    // 技能分析
                    skillAnalysisView(result)

                    // 强弱项分析
                    strengthWeaknessAnalysisView()

                    // 反馈和建议
                    feedbackView(result)

                    // 开始学习按钮
                    if case .initiatingLearning = viewModel.state {
                        ProgressView("正在初始化个性化学习...")
                            .frame(maxWidth: .infinity)
                            .padding()
                    } else if case .learningInitiated = viewModel.state {
                        NavigationLink(destination: DailyPracticeDashboardView()) {
                            Text("查看我的学习路径")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green)
                                .cornerRadius(10)
                        }
                        .padding(.vertical)
                    } else {
                        Button(action: {
                            // 启动个性化学习
                            viewModel.initiatePersonalizedLearning { success in
                                if success {
                                    // 可以在这里添加成功提示或其他操作
                                }
                            }
                        }) {
                            Text("开始个性化学习")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                        .padding(.vertical)
                    }
                } else if case .loading = viewModel.state {
                    // 加载中
                    UnifiedLoadingView(message: "Loading results...", size: .medium)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                } else {
                    // 错误信息 - 使用统一的错误处理
                    VStack {
                        Text("加载结果失败")
                            .font(.headline)
                            .foregroundColor(.red)
                            .padding()
                    }
                    .onAppear {
                        // 使用统一的错误管理器显示错误
                        if let errorMessage = viewModel.errorMessage {
                            ErrorManager.shared.showError(.customError(errorMessage))
                        }
                    }
                }
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(colorScheme == .dark ? Color.black : Color.white)
        }
        .navigationTitle("评估结果")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarItems(trailing: Button("完成") {
            presentationMode.wrappedValue.dismiss()
        })
        .onReceive(viewModel.$shouldNavigateToPractice) { shouldNavigate in
            if shouldNavigate {
                navigateToPractice = true
            }
        }
        .navigationDestination(isPresented: $navigateToPractice) {
            DailyPracticeDashboardView()
        }
        .withErrorHandling() // 添加统一错误处理
    }

    /// 结果摘要视图
    /// - Parameter result: 评估结果
    /// - Returns: 摘要视图
    private func resultSummaryView(_ result: EvaluationResult) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("总体水平")
                .font(.headline)
                .fontWeight(.semibold)

            HStack(spacing: 20) {
                // 总分
                VStack {
                    Text("\(result.overallScore)")
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(.blue)

                    Text("总分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)

                // 水平
                VStack {
                    Text(result.localizedLevelName())
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(result.isPassed ? .green : .orange)

                    Text("水平")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)

                // 通过状态
                VStack {
                    Image(systemName: result.isPassed ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                        .font(.system(size: 30))
                        .foregroundColor(result.isPassed ? .green : .orange)

                    Text(result.isPassed ? "通过" : "未通过")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 技能分析视图
    /// - Parameter result: 评估结果
    /// - Returns: 技能分析视图
    private func skillAnalysisView(_ result: EvaluationResult) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("技能分析")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 15) {
                ForEach(result.sectionScores, id: \.skill) { sectionScore in
                    skillScoreRow(
                        title: sectionScore.localizedSkillName(),
                        score: sectionScore.score,
                        maxScore: sectionScore.maxScore,
                        level: viewModel.skillLevelDescription(for: sectionScore.score)
                    )
                }
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 强弱项分析视图
    /// - Returns: 强弱项分析视图
    private func strengthWeaknessAnalysisView() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("强弱项分析")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 10) {
                // 强项
                if let strongest = viewModel.strongestSkill() {
                    VStack(alignment: .leading, spacing: 5) {
                        Text("最强项")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)

                        HStack {
                            Image(systemName: "star.fill")
                                .foregroundColor(.green)

                            Text(strongest.skill)
                                .font(.body)
                                .fontWeight(.medium)

                            Spacer()

                            Text("\(strongest.score) 分")
                                .font(.body)
                                .foregroundColor(.green)
                        }
                    }
                    .padding(.bottom, 5)
                }

                Divider()

                // 弱项
                if let weakest = viewModel.weakestSkill() {
                    VStack(alignment: .leading, spacing: 5) {
                        Text("需要提高")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)

                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)

                            Text(weakest.skill)
                                .font(.body)
                                .fontWeight(.medium)

                            Spacer()

                            Text("\(weakest.score) 分")
                                .font(.body)
                                .foregroundColor(.orange)
                        }
                    }
                }
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 技能分数行视图
    /// - Parameters:
    ///   - title: 技能标题
    ///   - score: 分数
    ///   - maxScore: 最高分
    ///   - level: 水平描述
    /// - Returns: 技能分数行视图
    private func skillScoreRow(title: String, score: Int, maxScore: Int, level: String) -> some View {
        VStack(alignment: .leading, spacing: 5) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                Text("\(score)/\(maxScore)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Text(level)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(scoreColor(score).opacity(0.2))
                    .foregroundColor(scoreColor(score))
                    .cornerRadius(5)
            }

            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    Rectangle()
                        .fill(Color(UIColor.systemGray5))
                        .frame(height: 8)
                        .cornerRadius(4)

                    // 进度
                    Rectangle()
                        .fill(scoreColor(score))
                        .frame(width: geometry.size.width * CGFloat(score) / CGFloat(maxScore), height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
        }
    }

    /// 反馈视图
    /// - Parameter result: 评估结果
    /// - Returns: 反馈视图
    private func feedbackView(_ result: EvaluationResult) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("反馈与建议")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 10) {
                // 反馈
                Text(result.feedback)
                    .font(.body)
                    .foregroundColor(.primary)

                Divider()

                // 建议
                Text("学习建议")
                    .font(.subheadline)
                    .fontWeight(.medium)

                ForEach(result.recommendations, id: \.self) { recommendation in
                    HStack(alignment: .top) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.system(size: 16))
                            .frame(width: 20, height: 20)

                        Text(recommendation)
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                    .padding(.vertical, 2)
                }
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 根据分数获取颜色
    /// - Parameter score: 分数
    /// - Returns: 对应的颜色
    private func scoreColor(_ score: Int) -> Color {
        if score >= 90 {
            return .green
        } else if score >= 70 {
            return .blue
        } else if score >= 50 {
            return .yellow
        } else if score >= 30 {
            return .orange
        } else {
            return .red
        }
    }
}

struct EvaluationResultView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EvaluationResultView(viewModel: EvaluationResultViewModel(resultId: UUID()))
        }
    }
}
