import Foundation
import Combine

/// 评估结果视图模型，处理评估结果展示逻辑
public class EvaluationResultViewModel: ObservableObject {
    private let evaluationManager: EvaluationManager
    private let personalizedLearningService: PersonalizedLearningServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // 结果状态
    public enum ResultState: Equatable {
        case loading
        case loaded
        case initiatingLearning
        case learningInitiated
        case error(String)

        public static func == (lhs: ResultState, rhs: ResultState) -> Bool {
            switch (lhs, rhs) {
            case (.loading, .loading), (.loaded, .loaded),
                 (.initiatingLearning, .initiatingLearning), (.learningInitiated, .learningInitiated):
                return true
            case (.error(let lhsError), .error(let rhsError)):
                return lhsError == rhsError
            default:
                return false
            }
        }
    }

    // 发布的属性
    @Published public var state: ResultState = .loading
    @Published public var result: EvaluationResult?
    @Published public var errorMessage: String?
    @Published private(set) var error: Error?
    private let evaluationId: UUID

    /// 初始化（使用评估ID）
    /// - Parameter resultId: 评估ID
    public init(resultId: UUID) {
        self.evaluationId = resultId
        self.evaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self)
        self.personalizedLearningService = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self)
        setupSubscriptions()
        loadResult(id: resultId)
    }

    /// 初始化（直接使用结果对象）
    /// - Parameter result: 评估结果
    public init(result: EvaluationResult) {
        self.evaluationId = result.id
        self.result = result
        self.state = .loaded
        self.evaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self)
        self.personalizedLearningService = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self)
        setupSubscriptions()
    }

    /// 设置订阅
    private func setupSubscriptions() {
        // 监听评估管理器的结果变化
        evaluationManager.$evaluationResult
            .receive(on: DispatchQueue.main)
            .sink { [weak self] result in
                if let result = result {
                    self?.result = result
                    self?.state = .loaded
                }
            }
            .store(in: &cancellables)

        evaluationManager.$error
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                if let error = error {
                    self?.state = .error(error.localizedDescription)
                    self?.errorMessage = error.localizedDescription
                }
            }
            .store(in: &cancellables)

        evaluationManager.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                if isLoading && self?.state != .initiatingLearning {
                    self?.state = .loading
                }
            }
            .store(in: &cancellables)
    }

    /// 加载结果
    /// - Parameter id: 评估ID
    public func loadResult(id: UUID) {
        state = .loading

        // 使用评估管理器加载结果
        evaluationManager.loadEvaluationResult(id: id)
    }

    /// 获取技能分数
    /// - Parameter skill: 技能名称
    /// - Returns: 技能分数
    public func scoreForSkill(_ skill: String) -> Int? {
        return result?.sectionScores.first { $0.skill == skill }?.score
    }

    /// 获取最强技能
    /// - Returns: 最强技能名称和分数
    public func strongestSkill() -> (skill: String, score: Int)? {
        guard let highestSection = result?.highestScoringSection else { return nil }
        return (highestSection.skill, highestSection.score)
    }

    /// 获取最弱技能
    /// - Returns: 最弱技能名称和分数
    public func weakestSkill() -> (skill: String, score: Int)? {
        guard let lowestSection = result?.lowestScoringSection else { return nil }
        return (lowestSection.skill, lowestSection.score)
    }

    /// 获取技能水平描述
    /// - Parameter score: 分数
    /// - Returns: 水平描述
    public func skillLevelDescription(for score: Int) -> String {
        if score >= 90 {
            return "高级"
        } else if score >= 70 {
            return "中高级"
        } else if score >= 50 {
            return "中级"
        } else if score >= 30 {
            return "初中级"
        } else {
            return "初级"
        }
    }

    /// 获取整体水平描述
    /// - Returns: 水平描述
    public func overallLevelDescription() -> String {
        guard let result = result else { return "未知" }
        return result.localizedLevelName()
    }

    /// 获取强项技能列表
    /// - Returns: 强项技能列表
    public func strengths() -> [EvaluationSectionScore] {
        return result?.strengths ?? []
    }

    /// 获取弱项技能列表
    /// - Returns: 弱项技能列表
    public func weaknesses() -> [EvaluationSectionScore] {
        return result?.weaknesses ?? []
    }

    /// 获取需要改进的技能
    /// - Returns: 需要改进的技能名称列表
    public func areasToImprove() -> [String] {
        return result?.areasToImprove ?? []
    }

    /// 获取评估完成时间描述
    /// - Returns: 完成时间描述
    public func completedTimeDescription() -> String {
        return result?.completedTimeDescription() ?? "未知"
    }

    /// 获取总体表现级别
    /// - Returns: 表现级别
    public func overallPerformanceLevel() -> PerformanceLevel? {
        return result?.overallPerformanceLevel
    }

    /// 获取总体得分百分比
    /// - Returns: 得分百分比
    public func scorePercentage() -> Double {
        return result?.scorePercentage ?? 0.0
    }

    /// 获取推荐学习建议
    /// - Returns: 学习建议列表
    public func recommendations() -> [String] {
        return result?.recommendations ?? []
    }

    // 添加导航状态
    @Published public var shouldNavigateToPractice: Bool = false
    @Published public var currentLearningPathId: UUID?

    /// 启动个性化学习
    /// - Parameter completion: 完成回调，返回是否成功
    func initiatePersonalizedLearning(completion: @escaping (Bool) -> Void) {
        state = .initiatingLearning

        Task {
            do {
                let pathId = try await personalizedLearningService.initiatePersonalizedLearning()
                await MainActor.run {
                    self.state = .learningInitiated
                    self.currentLearningPathId = pathId
                    // 获取当前学习路径
                    self.getCurrentLearningPath() // This will now also be async internally
                    completion(true)
                }
            } catch {
                await MainActor.run {
                    self.state = .error(error.localizedDescription)
                    self.errorMessage = error.localizedDescription
                    completion(false)
                }
            }
        }
    }

    /// 获取当前学习路径
    private func getCurrentLearningPath() {
        Task {
            do {
                // The service method returns PersonalizedLearningPath, we don't use it directly here
                // but confirm it was fetched successfully before navigating.
                _ = try await personalizedLearningService.getCurrentLearningPath()
                await MainActor.run {
                    // 设置导航标志，触发导航到练习页面
                    self.shouldNavigateToPractice = true
                }
            } catch {
                await MainActor.run {
                    self.state = .error(error.localizedDescription)
                    self.errorMessage = error.localizedDescription
                }
            }
        }
    }


}
