import Foundation
import Combine

/// 词汇仓库协议
public protocol VocabularyRepositoryProtocol {
    /// 获取词汇列表
    func getWords(category: String?, difficulty: String?) async throws -> [Word]

    /// 获取词汇详情
    func getWordDetail(id: UUID) async throws -> Word

    /// 获取词汇分类
    func getCategories() async throws -> [VocabularyCategory]

    /// 获取所有词汇进度
    func getAllWordProgress() async throws -> [WordProgress]

    /// 获取词汇进度
    func getWordProgress(id: UUID) async throws -> WordProgress

    /// 更新词汇进度
    func updateWordProgress(wordId: UUID, correct: Bool, timeSpent: TimeInterval) async throws -> WordProgress

    /// 获取已学习词汇
    func getLearnedWords() async throws -> [Word]

    /// 标记词汇为已学习
    func markWordAsLearned(wordId: UUID) async throws -> Bool

    /// 获取收藏词汇
    func getFavoriteWords() async throws -> [Word]

    /// 切换收藏状态
    func toggleFavoriteWord(id: UUID, isFavorite: Bool) async throws -> Bool

    /// Combine 支持
    func getWordsPublisher(category: String?, difficulty: String?) -> AnyPublisher<[Word], Error>
    func getWordDetailPublisher(id: UUID) -> AnyPublisher<Word, Error>
}

/// 词汇仓库实现
public class VocabularyRepository: VocabularyRepositoryProtocol {
    // MARK: - 单例
    public static let shared = VocabularyRepository()

    // MARK: - Private Properties
    private let remoteDataSource: VocabularyRemoteDataSourceProtocol
    private let localDataSource: VocabularyLocalDataSourceProtocol
    private let userManager: any UserManagerProtocol

    // MARK: - Initialization
    public init(
        remoteDataSource: VocabularyRemoteDataSourceProtocol = VocabularyRemoteDataSource(),
        localDataSource: VocabularyLocalDataSourceProtocol = VocabularyLocalDataSource(),
        userManager: (any UserManagerProtocol)? = nil
    ) {
        self.remoteDataSource = remoteDataSource
        self.localDataSource = localDataSource
        self.userManager = userManager ?? UserManager.shared
    }

    // MARK: - Public Methods

    public func getWords(category: String? = nil, difficulty: String? = nil) async throws -> [Word] {
        guard let authToken = getAuthToken() else { throw VocabularyRepositoryError.authenticationRequired }
        do {
            // 优先从远程获取
            let remoteWords = try await remoteDataSource.getWords(category: category, difficulty: difficulty, authToken: authToken)

            // 缓存到本地
            try await localDataSource.saveWords(remoteWords)

            return remoteWords
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getWords(category: category, difficulty: difficulty)
        }
    }

    public func getWordDetail(id: UUID) async throws -> Word {
        guard let authToken = getAuthToken() else { throw VocabularyRepositoryError.authenticationRequired }
        do {
            // 优先从远程获取
            let remoteWord = try await remoteDataSource.getWordDetail(id: id, authToken: authToken)

            // 缓存到本地
            try await localDataSource.saveWord(remoteWord)

            return remoteWord
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getWordDetail(id: id)
        }
    }

    public func getCategories() async throws -> [VocabularyCategory] {
        guard let authToken = getAuthToken() else { throw VocabularyRepositoryError.authenticationRequired }
        do {
            // 优先从远程获取
            let remoteCategories = try await remoteDataSource.getCategories(authToken: authToken)

            // 缓存到本地
            try await localDataSource.saveCategories(remoteCategories)

            return remoteCategories
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getCategories()
        }
    }

    public func getAllWordProgress() async throws -> [WordProgress] {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { throw VocabularyRepositoryError.authenticationRequired }
        do {
            // 优先从远程获取
            let remoteProgress = try await remoteDataSource.getAllWordProgress(authToken: authToken, userID: userID)

            // 缓存到本地
            try await localDataSource.saveAllWordProgress(remoteProgress)

            return remoteProgress
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getAllWordProgress()
        }
    }

    public func getWordProgress(id: UUID) async throws -> WordProgress {
        // This method might be called to check local cache first,
        // so auth token/userID are only needed if fetching from remote.
        do {
            return try await localDataSource.getWordProgress(id: id) // Try local first
        } catch {
            guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else {
                throw VocabularyRepositoryError.authenticationRequired
            }
            let remoteProgress = try await remoteDataSource.getWordProgress(id: id, authToken: authToken, userID: userID)
            try await localDataSource.saveWordProgress(remoteProgress)
            return remoteProgress
        }
    }

    public func updateWordProgress(wordId: UUID, correct: Bool, timeSpent: TimeInterval) async throws -> WordProgress {
        guard let authToken = getAuthToken(), let userID = getCurrentUserId(), let userIDString = getCurrentUserIdString() else {
            throw VocabularyRepositoryError.authenticationRequired
        }

        // 获取当前进度或创建新进度
        let currentProgress: WordProgress
        do {
            // Try to get progress using the version that might hit remote if local fails
            currentProgress = try await getWordProgress(id: wordId)
        } catch {
            // 如果没有进度记录，创建新的 (requires logged-in user)
            currentProgress = WordProgress(
                wordId: wordId,
                userId: userID // Use the guarded non-optional userID here
            )
        }

        // 更新进度
        let updatedProgress = currentProgress.updatedProgress(correct: correct, timeSpent: timeSpent)

        do {
            // 优先更新远程
            let remoteProgress = try await remoteDataSource.updateWordProgress(updatedProgress, authToken: authToken, userID: userIDString)

            // 同步到本地
            try await localDataSource.saveWordProgress(remoteProgress)

            return remoteProgress
        } catch {
            // 远程失败时保存到本地，标记为待同步
            print("Remote update failed, saving locally: \(error)")
            var localProgress = updatedProgress
            localProgress.needsSync = true
            try await localDataSource.saveWordProgress(localProgress)

            return localProgress
        }
    }

    public func getLearnedWords() async throws -> [Word] {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { throw VocabularyRepositoryError.authenticationRequired }
        do {
            // 优先从远程获取
            let remoteLearned = try await remoteDataSource.getLearnedWords(authToken: authToken, userID: userID)

            // 缓存到本地
            try await localDataSource.saveLearnedWords(remoteLearned)

            return remoteLearned
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getLearnedWords()
        }
    }

    public func markWordAsLearned(wordId: UUID) async throws -> Bool {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { throw VocabularyRepositoryError.authenticationRequired }
        do {
            // 优先从远程获取
            let success = try await remoteDataSource.markWordAsLearned(wordId: wordId, authToken: authToken, userID: userID)

            // 更新本地状态 (如果需要)
            // 注意：如果远程操作失败，本地可能不会立即反映"未学习"状态，
            // 这取决于具体需求。目前假设远程成功则本地也成功。
            if success {
                try await localDataSource.markWordAsLearned(wordId: wordId)
            }
            return success
        } catch {
            // 远程失败时，可以在本地标记为待同步
            print("Remote markWordAsLearned failed, marking for sync: \(error)")
            try await localDataSource.markLearnedForSync(wordId: wordId) // 假设有这个方法
            // 或者根据需求抛出错误
            throw error // Re-throw the original error or a custom one
        }
    }

    public func getFavoriteWords() async throws -> [Word] {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { throw VocabularyRepositoryError.authenticationRequired }
        do {
            // 优先从远程获取
            let remoteFavorites = try await remoteDataSource.getFavoriteWords(authToken: authToken, userID: userID)

            // 缓存到本地
            try await localDataSource.saveFavoriteWords(remoteFavorites)

            return remoteFavorites
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getFavoriteWords()
        }
    }

    public func toggleFavoriteWord(id: UUID, isFavorite: Bool) async throws -> Bool {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { throw VocabularyRepositoryError.authenticationRequired }
        do {
            // 优先更新远程
            let success = try await remoteDataSource.toggleFavoriteWord(id: id, isFavorite: isFavorite, authToken: authToken, userID: userID)

            // 更新本地状态
            if success {
                try await localDataSource.toggleFavoriteWord(id: id, isFavorite: isFavorite)
            }
            return success
        } catch {
            // 远程失败时，可以在本地标记为待同步
            print("Remote toggleFavoriteWord failed, marking for sync: \(error)")
            try await localDataSource.markFavoriteForSync(id: id, isFavorite: isFavorite) // 假设有这个方法
            // 或者根据需求抛出错误
            throw error // Re-throw the original error or a custom one
        }
    }

    // MARK: - Combine Support

    public func getWordsPublisher(category: String? = nil, difficulty: String? = nil) -> AnyPublisher<[Word], Error> {
        return Future { promise in
            Task {
                do {
                    let words = try await self.getWords(category: category, difficulty: difficulty)
                    promise(.success(words))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    public func getWordDetailPublisher(id: UUID) -> AnyPublisher<Word, Error> {
        return Future { promise in
            Task {
                do {
                    let word = try await self.getWordDetail(id: id)
                    promise(.success(word))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    // MARK: - Private Helper Methods

    private func getAuthToken() -> String? {
        return userManager.authToken
    }

    private func getCurrentUserId() -> UUID? {
        // User.id is already UUID, so just return it.
        return userManager.currentUser?.id
    }

    private func getCurrentUserIdString() -> String? {
        // Convert User.id (UUID) to String for remote API calls.
        return userManager.currentUser?.id.uuidString
    }
}

// MARK: - Repository Errors

public enum VocabularyRepositoryError: Error, LocalizedError {
    case authenticationRequired
    case unknown
    case custom(String)

    public var errorDescription: String? {
        switch self {
        case .authenticationRequired:
            return "用户需要认证" // "User authentication required"
        case .unknown:
            return "发生未知错误" // "An unknown error occurred"
        case .custom(let message):
            return message
        }
    }
}
