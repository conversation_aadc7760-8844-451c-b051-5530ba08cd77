import Foundation
import Combine

// MARK: - Remote Data Source Protocol

/// 词汇远程数据源协议
public protocol VocabularyRemoteDataSourceProtocol {
    /// 获取词汇列表
    func getWords(category: String?, difficulty: String?, authToken: String) async throws -> [Word]

    /// 获取词汇详情
    func getWordDetail(id: UUID, authToken: String) async throws -> Word

    /// 获取词汇分类
    func getCategories(authToken: String) async throws -> [VocabularyCategory]

    /// 获取所有词汇进度
    func getAllWordProgress(authToken: String, userID: String) async throws -> [WordProgress]

    /// 获取词汇进度
    func getWordProgress(id: UUID, authToken: String, userID: String) async throws -> WordProgress

    /// 更新词汇进度
    func updateWordProgress(_ progress: WordProgress, authToken: String, userID: String) async throws -> WordProgress

    /// 获取已学习词汇
    func getLearnedWords(authToken: String, userID: String) async throws -> [Word]

    /// 标记词汇为已学习
    func markWordAsLearned(wordId: UUID, authToken: String, userID: String) async throws -> Bool

    /// 获取收藏词汇
    func getFavoriteWords(authToken: String, userID: String) async throws -> [Word]

    /// 切换收藏状态
    func toggleFavoriteWord(id: UUID, isFavorite: Bool, authToken: String, userID: String) async throws -> Bool
}

// MARK: - Local Data Source Protocol

/// 词汇本地数据源协议
public protocol VocabularyLocalDataSourceProtocol {
    /// 获取词汇列表
    func getWords(category: String?, difficulty: String?) async throws -> [Word]

    /// 获取词汇详情
    func getWordDetail(id: UUID) async throws -> Word

    /// 保存词汇列表
    func saveWords(_ words: [Word]) async throws

    /// 保存单个词汇
    func saveWord(_ word: Word) async throws

    /// 获取词汇分类
    func getCategories() async throws -> [VocabularyCategory]

    /// 保存词汇分类
    func saveCategories(_ categories: [VocabularyCategory]) async throws

    /// 获取所有词汇进度
    func getAllWordProgress() async throws -> [WordProgress]

    /// 获取词汇进度
    func getWordProgress(id: UUID) async throws -> WordProgress

    /// 保存词汇进度
    func saveWordProgress(_ progress: WordProgress) async throws

    /// 保存所有词汇进度
    func saveAllWordProgress(_ progressList: [WordProgress]) async throws

    /// 获取已学习词汇
    func getLearnedWords() async throws -> [Word]

    /// 保存已学习词汇
    func saveLearnedWords(_ words: [Word]) async throws

    /// 标记词汇为已学习
    func markWordAsLearned(wordId: UUID) async throws

    /// 标记已学习状态为待同步
    func markLearnedForSync(wordId: UUID) async throws

    /// 获取收藏词汇
    func getFavoriteWords() async throws -> [Word]

    /// 保存收藏词汇
    func saveFavoriteWords(_ words: [Word]) async throws

    /// 切换收藏状态
    func toggleFavoriteWord(id: UUID, isFavorite: Bool) async throws

    /// 标记收藏状态为待同步
    func markFavoriteForSync(id: UUID, isFavorite: Bool) async throws

    /// 清除所有数据
    func clearAllData() async throws
}

// MARK: - Protocol Implementations
// 具体实现已移至单独的文件：
// - VocabularyRemoteDataSource.swift
// - VocabularyLocalDataSource.swift

// MARK: - Errors

public enum VocabularyDataSourceError: Error, LocalizedError {
    case notImplemented
    case wordNotFound
    case progressNotFound
    case encodingError(Error)
    case decodingError(Error)
    case storageError(Error)

    public var errorDescription: String? {
        switch self {
        case .notImplemented:
            return "功能尚未实现"
        case .wordNotFound:
            return "词汇未找到"
        case .progressNotFound:
            return "学习进度未找到"
        case .encodingError(let error):
            return "数据编码错误: \(error.localizedDescription)"
        case .decodingError(let error):
            return "数据解码错误: \(error.localizedDescription)"
        case .storageError(let error):
            return "存储错误: \(error.localizedDescription)"
        }
    }
}
