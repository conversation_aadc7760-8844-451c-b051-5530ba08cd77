import Foundation

/// 词汇本地数据源实现
public class VocabularyLocalDataSource: VocabularyLocalDataSourceProtocol {
    // MARK: - Private Properties
    private let storageManager: StorageManagerProtocol

    // MARK: - Storage Keys
    private enum StorageKeys {
        static let words = "cached_vocabulary_words"
        static let categories = "vocabulary_categories"
        static let wordProgress = "vocabulary_word_progress"
        static let learnedWords = "vocabulary_learned_words"
        static let favoriteWords = "vocabulary_favorite_words"
        static let pendingSyncLearned = "pending_sync_learned_words"
        static let pendingSyncFavorites = "pending_sync_favorite_words"
    }

    // MARK: - Initialization
    public init(storageManager: StorageManagerProtocol = StorageManager.shared) {
        self.storageManager = storageManager
    }

    // MARK: - Public Methods

    public func getWords(category: String? = nil, difficulty: String? = nil) async throws -> [Word] {
        do {
            guard let data = UserDefaults.standard.data(forKey: StorageKeys.words) else {
                // 如果没有缓存数据，返回示例数据
                return getFilteredSampleWords(category: category, difficulty: difficulty)
            }

            let words = try JSONDecoder().decode([Word].self, from: data)
            return filterWords(words, category: category, difficulty: difficulty)
        } catch {
            print("Failed to decode cached words: \(error)")
            return getFilteredSampleWords(category: category, difficulty: difficulty)
        }
    }

    public func getWordDetail(id: UUID) async throws -> Word {
        let words = try await getWords(category: nil, difficulty: nil)
        guard let word = words.first(where: { $0.id == id }) else {
            throw VocabularyLocalDataSourceError.wordNotFound
        }
        return word
    }

    public func saveWords(_ words: [Word]) async throws {
        do {
            let data = try JSONEncoder().encode(words)
            UserDefaults.standard.set(data, forKey: StorageKeys.words)
            print("Successfully saved \(words.count) words to local storage")
        } catch {
            throw VocabularyLocalDataSourceError.encodingError(error)
        }
    }

    public func saveWord(_ word: Word) async throws {
        var words = try await getWords(category: nil, difficulty: nil)

        if let index = words.firstIndex(where: { $0.id == word.id }) {
            words[index] = word
            print("Updated word: \(word.text)")
        } else {
            words.append(word)
            print("Added new word: \(word.text)")
        }

        try await saveWords(words)
    }

    public func getCategories() async throws -> [VocabularyCategory] {
        do {
            guard let data = UserDefaults.standard.data(forKey: StorageKeys.categories) else {
                // 返回默认分类
                return getDefaultCategories()
            }

            let categories = try JSONDecoder().decode([VocabularyCategory].self, from: data)
            return categories
        } catch {
            print("Failed to decode cached categories: \(error)")
            return getDefaultCategories()
        }
    }

    public func saveCategories(_ categories: [VocabularyCategory]) async throws {
        do {
            let data = try JSONEncoder().encode(categories)
            UserDefaults.standard.set(data, forKey: StorageKeys.categories)
            print("Successfully saved \(categories.count) categories to local storage")
        } catch {
            throw VocabularyLocalDataSourceError.encodingError(error)
        }
    }

    public func getAllWordProgress() async throws -> [WordProgress] {
        do {
            guard let data = UserDefaults.standard.data(forKey: StorageKeys.wordProgress) else {
                return []
            }

            let progressList = try JSONDecoder().decode([WordProgress].self, from: data)
            return progressList
        } catch {
            throw VocabularyLocalDataSourceError.decodingError(error)
        }
    }

    public func getWordProgress(id: UUID) async throws -> WordProgress {
        let allProgress = try await getAllWordProgress()
        guard let progress = allProgress.first(where: { $0.wordId == id }) else {
            throw VocabularyLocalDataSourceError.progressNotFound
        }
        return progress
    }

    public func saveWordProgress(_ progress: WordProgress) async throws {
        var allProgress = try await getAllWordProgress()

        if let index = allProgress.firstIndex(where: { $0.wordId == progress.wordId }) {
            allProgress[index] = progress
            print("Updated word progress: \(progress.wordId)")
        } else {
            allProgress.append(progress)
            print("Added new word progress: \(progress.wordId)")
        }

        try await saveAllWordProgress(allProgress)
    }

    public func saveAllWordProgress(_ progressList: [WordProgress]) async throws {
        do {
            let data = try JSONEncoder().encode(progressList)
            UserDefaults.standard.set(data, forKey: StorageKeys.wordProgress)
            print("Successfully saved \(progressList.count) word progress records to local storage")
        } catch {
            throw VocabularyLocalDataSourceError.encodingError(error)
        }
    }

    public func getLearnedWords() async throws -> [Word] {
        do {
            guard let data = UserDefaults.standard.data(forKey: StorageKeys.learnedWords) else {
                return []
            }

            let learnedWords = try JSONDecoder().decode([Word].self, from: data)
            return learnedWords
        } catch {
            throw VocabularyLocalDataSourceError.decodingError(error)
        }
    }

    public func saveLearnedWords(_ words: [Word]) async throws {
        do {
            let data = try JSONEncoder().encode(words)
            UserDefaults.standard.set(data, forKey: StorageKeys.learnedWords)
            print("Successfully saved \(words.count) learned words to local storage")
        } catch {
            throw VocabularyLocalDataSourceError.encodingError(error)
        }
    }

    public func markWordAsLearned(wordId: UUID) async throws {
        var learnedWords = try await getLearnedWords()
        let allWords = try await getWords(category: nil, difficulty: nil)

        if let word = allWords.first(where: { $0.id == wordId }),
           !learnedWords.contains(where: { $0.id == wordId }) {
            learnedWords.append(word)
            try await saveLearnedWords(learnedWords)
            print("Marked word as learned: \(word.text)")
        }
    }

    public func markLearnedForSync(wordId: UUID) async throws {
        var pendingSync = getPendingSyncLearned()
        pendingSync.insert(wordId.uuidString)
        savePendingSyncLearned(pendingSync)
        print("Marked learned word for sync: \(wordId)")
    }

    public func getFavoriteWords() async throws -> [Word] {
        do {
            guard let data = UserDefaults.standard.data(forKey: StorageKeys.favoriteWords) else {
                return []
            }

            let favoriteWords = try JSONDecoder().decode([Word].self, from: data)
            return favoriteWords
        } catch {
            throw VocabularyLocalDataSourceError.decodingError(error)
        }
    }

    public func saveFavoriteWords(_ words: [Word]) async throws {
        do {
            let data = try JSONEncoder().encode(words)
            UserDefaults.standard.set(data, forKey: StorageKeys.favoriteWords)
            print("Successfully saved \(words.count) favorite words to local storage")
        } catch {
            throw VocabularyLocalDataSourceError.encodingError(error)
        }
    }

    public func toggleFavoriteWord(id: UUID, isFavorite: Bool) async throws {
        var favoriteWords = try await getFavoriteWords()
        let allWords = try await getWords(category: nil, difficulty: nil)

        if isFavorite {
            if let word = allWords.first(where: { $0.id == id }),
               !favoriteWords.contains(where: { $0.id == id }) {
                favoriteWords.append(word)
                print("Added word to favorites: \(word.text)")
            }
        } else {
            let removedCount = favoriteWords.count
            favoriteWords.removeAll { $0.id == id }
            if favoriteWords.count < removedCount {
                print("Removed word from favorites: \(id)")
            }
        }

        try await saveFavoriteWords(favoriteWords)
    }

    public func markFavoriteForSync(id: UUID, isFavorite: Bool) async throws {
        var pendingSync = getPendingSyncFavorites()
        pendingSync[id.uuidString] = isFavorite
        savePendingSyncFavorites(pendingSync)
        print("Marked favorite for sync: \(id) -> \(isFavorite)")
    }

    public func clearAllData() async throws {
        UserDefaults.standard.removeObject(forKey: StorageKeys.words)
        UserDefaults.standard.removeObject(forKey: StorageKeys.categories)
        UserDefaults.standard.removeObject(forKey: StorageKeys.wordProgress)
        UserDefaults.standard.removeObject(forKey: StorageKeys.learnedWords)
        UserDefaults.standard.removeObject(forKey: StorageKeys.favoriteWords)
        UserDefaults.standard.removeObject(forKey: StorageKeys.pendingSyncLearned)
        UserDefaults.standard.removeObject(forKey: StorageKeys.pendingSyncFavorites)
        print("Cleared all vocabulary data from local storage")
    }

    // MARK: - Private Helper Methods

    private func filterWords(_ words: [Word], category: String?, difficulty: String?) -> [Word] {
        return words.filter { word in
            if let category = category, word.partOfSpeech != category {
                return false
            }
            if let difficulty = difficulty, String(word.difficulty) != difficulty {
                return false
            }
            return true
        }
    }

    private func getFilteredSampleWords(category: String?, difficulty: String?) -> [Word] {
        let sampleWords = Word.sampleWords
        return filterWords(sampleWords, category: category, difficulty: difficulty)
    }

    private func getDefaultCategories() -> [VocabularyCategory] {
        return [
            VocabularyCategory(
                id: UUID(),
                name: "基础词汇",
                description: "日常生活基础词汇",
                iconName: "star.fill",
                color: "blue",
                wordCount: 150
            ),
            VocabularyCategory(
                id: UUID(),
                name: "商务词汇",
                description: "商务和工作场景词汇",
                iconName: "briefcase.fill",
                color: "green",
                wordCount: 120
            ),
            VocabularyCategory(
                id: UUID(),
                name: "旅游词汇",
                description: "旅游和出行相关词汇",
                iconName: "airplane",
                color: "orange",
                wordCount: 80
            ),
            VocabularyCategory(
                id: UUID(),
                name: "学术词汇",
                description: "学术和教育相关词汇",
                iconName: "book.fill",
                color: "purple",
                wordCount: 200
            ),
            VocabularyCategory(
                id: UUID(),
                name: "科技词汇",
                description: "科技和互联网词汇",
                iconName: "laptopcomputer",
                color: "cyan",
                wordCount: 90
            )
        ]
    }

    private func getPendingSyncLearned() -> Set<String> {
        guard let data = UserDefaults.standard.data(forKey: StorageKeys.pendingSyncLearned) else {
            return Set<String>()
        }

        do {
            return try JSONDecoder().decode(Set<String>.self, from: data)
        } catch {
            return Set<String>()
        }
    }

    private func savePendingSyncLearned(_ pendingSync: Set<String>) {
        do {
            let data = try JSONEncoder().encode(pendingSync)
            UserDefaults.standard.set(data, forKey: StorageKeys.pendingSyncLearned)
        } catch {
            print("Failed to save pending sync learned words: \(error)")
        }
    }

    private func getPendingSyncFavorites() -> [String: Bool] {
        guard let data = UserDefaults.standard.data(forKey: StorageKeys.pendingSyncFavorites) else {
            return [:]
        }

        do {
            return try JSONDecoder().decode([String: Bool].self, from: data)
        } catch {
            return [:]
        }
    }

    private func savePendingSyncFavorites(_ pendingSync: [String: Bool]) {
        do {
            let data = try JSONEncoder().encode(pendingSync)
            UserDefaults.standard.set(data, forKey: StorageKeys.pendingSyncFavorites)
        } catch {
            print("Failed to save pending sync favorite words: \(error)")
        }
    }
}

// MARK: - Errors

public enum VocabularyLocalDataSourceError: Error, LocalizedError {
    case wordNotFound
    case progressNotFound
    case encodingError(Error)
    case decodingError(Error)
    case storageError(Error)

    public var errorDescription: String? {
        switch self {
        case .wordNotFound:
            return "词汇未找到"
        case .progressNotFound:
            return "学习进度未找到"
        case .encodingError(let error):
            return "数据编码错误: \(error.localizedDescription)"
        case .decodingError(let error):
            return "数据解码错误: \(error.localizedDescription)"
        case .storageError(let error):
            return "存储错误: \(error.localizedDescription)"
        }
    }
}
