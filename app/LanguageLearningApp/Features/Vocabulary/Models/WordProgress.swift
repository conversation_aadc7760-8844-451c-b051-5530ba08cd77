import Foundation

/// 词汇学习进度模型
public struct WordProgress: Codable, Identifiable {
    /// 进度ID
    public let id: UUID
    /// 词汇ID
    public let wordId: UUID
    /// 用户ID
    public let userId: UUID
    /// 掌握程度（0-1）
    public let masteryLevel: Double
    /// 正确次数
    public let correctCount: Int
    /// 错误次数
    public let incorrectCount: Int
    /// 总学习次数
    public let totalAttempts: Int
    /// 总学习时间（秒）
    public let totalTimeSpent: TimeInterval
    /// 首次学习时间
    public let firstLearnedAt: Date
    /// 最后复习时间
    public let lastReviewedAt: Date
    /// 下次复习时间
    public let nextReviewAt: Date
    /// 复习间隔（天数）
    public let reviewInterval: Int
    /// 是否已掌握
    public let isMastered: Bool
    /// 是否需要同步到服务器
    public var needsSync: Bool
    
    /// 初始化方法
    public init(
        id: UUID = UUID(),
        wordId: UUID,
        userId: UUID,
        masteryLevel: Double = 0.0,
        correctCount: Int = 0,
        incorrectCount: Int = 0,
        totalAttempts: Int = 0,
        totalTimeSpent: TimeInterval = 0,
        firstLearnedAt: Date = Date(),
        lastReviewedAt: Date = Date(),
        nextReviewAt: Date? = nil,
        reviewInterval: Int = 1,
        isMastered: Bool = false,
        needsSync: Bool = false
    ) {
        self.id = id
        self.wordId = wordId
        self.userId = userId
        self.masteryLevel = masteryLevel
        self.correctCount = correctCount
        self.incorrectCount = incorrectCount
        self.totalAttempts = totalAttempts
        self.totalTimeSpent = totalTimeSpent
        self.firstLearnedAt = firstLearnedAt
        self.lastReviewedAt = lastReviewedAt
        self.nextReviewAt = nextReviewAt ?? Calendar.current.date(byAdding: .day, value: reviewInterval, to: Date()) ?? Date()
        self.reviewInterval = reviewInterval
        self.isMastered = isMastered
        self.needsSync = needsSync
    }
    
    /// 计算正确率
    public var accuracy: Double {
        guard totalAttempts > 0 else { return 0.0 }
        return Double(correctCount) / Double(totalAttempts)
    }
    
    /// 是否需要复习
    public var needsReview: Bool {
        return Date() >= nextReviewAt
    }
    
    /// 学习强度（基于时间和次数）
    public var studyIntensity: StudyIntensity {
        let avgTimePerAttempt = totalAttempts > 0 ? totalTimeSpent / Double(totalAttempts) : 0
        
        switch (totalAttempts, avgTimePerAttempt) {
        case (0...2, _):
            return .light
        case (3...10, 0...30):
            return .moderate
        case (3...10, 31...):
            return .intensive
        case (11..., 0...20):
            return .moderate
        case (11..., 21...):
            return .intensive
        default:
            return .light
        }
    }
    
    /// 更新进度
    public func updatedProgress(correct: Bool, timeSpent: TimeInterval) -> WordProgress {
        let newCorrectCount = correct ? correctCount + 1 : correctCount
        let newIncorrectCount = correct ? incorrectCount : incorrectCount + 1
        let newTotalAttempts = totalAttempts + 1
        let newTotalTimeSpent = totalTimeSpent + timeSpent
        
        // 计算新的掌握程度
        let newMasteryLevel = calculateMasteryLevel(
            correctCount: newCorrectCount,
            totalAttempts: newTotalAttempts,
            timeSpent: newTotalTimeSpent
        )
        
        // 计算新的复习间隔
        let newReviewInterval = calculateReviewInterval(masteryLevel: newMasteryLevel, wasCorrect: correct)
        let newNextReviewAt = Calendar.current.date(byAdding: .day, value: newReviewInterval, to: Date()) ?? Date()
        
        return WordProgress(
            id: id,
            wordId: wordId,
            userId: userId,
            masteryLevel: newMasteryLevel,
            correctCount: newCorrectCount,
            incorrectCount: newIncorrectCount,
            totalAttempts: newTotalAttempts,
            totalTimeSpent: newTotalTimeSpent,
            firstLearnedAt: firstLearnedAt,
            lastReviewedAt: Date(),
            nextReviewAt: newNextReviewAt,
            reviewInterval: newReviewInterval,
            isMastered: newMasteryLevel >= 0.8,
            needsSync: true
        )
    }
    
    // MARK: - Private Methods
    
    private func calculateMasteryLevel(correctCount: Int, totalAttempts: Int, timeSpent: TimeInterval) -> Double {
        guard totalAttempts > 0 else { return 0.0 }
        
        // 基础准确率
        let accuracy = Double(correctCount) / Double(totalAttempts)
        
        // 时间因子（快速回答加分）
        let avgTimePerAttempt = timeSpent / Double(totalAttempts)
        let timeFactor: Double
        switch avgTimePerAttempt {
        case 0...10:
            timeFactor = 1.2 // 快速回答，加分
        case 11...30:
            timeFactor = 1.0 // 正常时间
        case 31...60:
            timeFactor = 0.9 // 较慢，轻微减分
        default:
            timeFactor = 0.8 // 很慢，减分
        }
        
        // 练习次数因子（多次练习加分）
        let attemptsFactor = min(1.0 + Double(totalAttempts) * 0.05, 1.5)
        
        // 综合计算掌握程度
        let masteryLevel = accuracy * timeFactor * attemptsFactor
        
        return min(masteryLevel, 1.0) // 确保不超过1.0
    }
    
    private func calculateReviewInterval(masteryLevel: Double, wasCorrect: Bool) -> Int {
        let baseDays: Int
        
        // 根据掌握程度确定基础间隔
        switch masteryLevel {
        case 0.0..<0.3:
            baseDays = 1
        case 0.3..<0.6:
            baseDays = 3
        case 0.6..<0.8:
            baseDays = 7
        case 0.8..<0.9:
            baseDays = 14
        default:
            baseDays = 30
        }
        
        // 如果这次回答错误，缩短间隔
        if !wasCorrect {
            return max(1, baseDays / 2)
        }
        
        return baseDays
    }
}

// MARK: - Supporting Enums

public enum StudyIntensity: String, Codable, CaseIterable {
    case light = "light"
    case moderate = "moderate"
    case intensive = "intensive"
    
    public var displayName: String {
        switch self {
        case .light:
            return "轻度学习"
        case .moderate:
            return "中度学习"
        case .intensive:
            return "深度学习"
        }
    }
    
    public var description: String {
        switch self {
        case .light:
            return "偶尔练习，时间较短"
        case .moderate:
            return "定期练习，时间适中"
        case .intensive:
            return "频繁练习，时间较长"
        }
    }
}

// MARK: - Extensions

extension WordProgress {
    /// 获取学习建议
    public var learningRecommendation: String {
        switch (masteryLevel, needsReview) {
        case (0.0..<0.3, _):
            return "建议多练习基础内容"
        case (0.3..<0.6, true):
            return "需要复习，加强记忆"
        case (0.3..<0.6, false):
            return "继续练习，即将掌握"
        case (0.6..<0.8, true):
            return "复习时间到了，巩固记忆"
        case (0.6..<0.8, false):
            return "掌握良好，保持练习"
        case (0.8..., true):
            return "定期复习，保持熟练度"
        default:
            return "已熟练掌握，偶尔复习即可"
        }
    }
    
    /// 获取进度颜色
    public var progressColor: String {
        switch masteryLevel {
        case 0.0..<0.3:
            return "red"
        case 0.3..<0.6:
            return "orange"
        case 0.6..<0.8:
            return "yellow"
        default:
            return "green"
        }
    }
}
