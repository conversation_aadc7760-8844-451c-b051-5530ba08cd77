import Foundation
import Combine

/// 用户远程数据源
public class UserRemoteDataSource: UserRemoteDataSourceProtocol {
    public typealias T = User
    public typealias ID = UUID

    // 单例实例
    public static let shared = UserRemoteDataSource()

    // API客户端
    private let apiClient: APIClientProtocol

    // API端点
    private let userEndpoint = "/user"
    private let authEndpoint = "/auth"

    // Initializer accessible for injection, but defaults to shared APIClient
    public init(apiClient: APIClientProtocol = APIClient.shared) {
        self.apiClient = apiClient
    }

    private func standardHeaders(authToken: String) -> [String: String] {
        return [
            "Authorization": "Bearer \(authToken)",
            "Content-Type": "application/json",
            "Accept": "application/json"
        ]
    }

    private func basicHeaders() -> [String: String] {
        return [
            "Content-Type": "application/json",
            "Accept": "application/json"
        ]
    }

    /// 获取所有用户 (Example of a method that might not be used or might need specific admin rights)
    public func getAll(authToken: String) -> AnyPublisher<[User], Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + userEndpoint)!
        let headers = standardHeaders(authToken: authToken)
        return Future<[User], Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: headers)
                    let response = try JSONDecoder().decode(UserListResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 根据ID获取用户
    public func getById(_ id: UUID, authToken: String) -> AnyPublisher<User, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(id.uuidString)")!
        let headers = standardHeaders(authToken: authToken)
        return Future<User, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: headers)
                    let response = try JSONDecoder().decode(UserResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 保存用户 (Typically used for updating existing user, creation is via register)
    public func save(_ entity: User, authToken: String) -> AnyPublisher<User, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(userEndpoint)/\(entity.id.uuidString)")!
        let headers = standardHeaders(authToken: authToken)
        let body = try? JSONEncoder().encode(entity) // Ensure User is Encodable
        return Future<User, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: headers, body: body)
                    let response = try JSONDecoder().decode(UserResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 保存多个用户 (If this functionality is truly needed)
    public func saveAll(_ entities: [User], authToken: String) -> AnyPublisher<[User], Error> {
        let publishers = entities.map { save($0, authToken: authToken) }
        return Publishers.MergeMany(publishers)
            .collect()
            .eraseToAnyPublisher()
    }

    /// 删除用户 (This is deleteAccount, but mapped to generic delete for protocol)
    public func delete(_ id: UUID, authToken: String) -> AnyPublisher<Bool, Error> {
        return deleteAccount(userID: id, authToken: authToken)
    }

    /// 获取当前用户信息
    public func getCurrentUser(authToken: String) -> AnyPublisher<User, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/me")!
        let headers = standardHeaders(authToken: authToken)
        return Future<User, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: headers)
                    let response = try JSONDecoder().decode(UserResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 登录
    public func login(username: String, password: String) -> AnyPublisher<User, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(authEndpoint)/login")!
        let headers = basicHeaders()
        let parameters: [String: Any] = [
            "username": username,
            "password": password
        ]
        return Future<User, Error> { promise in // Added return type User, not UserContainer
            Task {
                do {
                    let body = try JSONSerialization.data(withJSONObject: parameters)
                    let data = try await self.apiClient.post(url, headers: headers, body: body)
                    let userContainer = try JSONDecoder().decode(UserContainer.self, from: data)
                    // Store token, perhaps UserContainer includes it or it's in headers
                    // For now, assuming UserContainer.user has the user info and token might be handled by UserManager
                    UserDefaults.standard.set(userContainer.token, forKey: "authToken") // Example: Storing token
                    promise(.success(userContainer.user))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 注册
    public func register(username: String, email: String, password: String) -> AnyPublisher<User, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(authEndpoint)/register")!
        let headers = basicHeaders()
        let parameters: [String: Any] = [
            "username": username,
            "email": email,
            "password": password
        ]
        return Future<User, Error> { promise in // Added return type User, not UserContainer
            Task {
                do {
                    let body = try JSONSerialization.data(withJSONObject: parameters)
                    let data = try await self.apiClient.post(url, headers: headers, body: body)
                    let userContainer = try JSONDecoder().decode(UserContainer.self, from: data)
                    UserDefaults.standard.set(userContainer.token, forKey: "authToken") // Example: Storing token
                    promise(.success(userContainer.user))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 登出
    public func logout(authToken: String) -> AnyPublisher<Bool, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(authEndpoint)/logout")!
        let headers = standardHeaders(authToken: authToken)
        return Future<Bool, Error> { promise in
            Task {
                do {
                    _ = try await self.apiClient.post(url, headers: headers, body: nil)
                    UserDefaults.standard.removeObject(forKey: "authToken") // Example: Clearing token
                    promise(.success(true))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 更新用户资料
    public func updateUserProfile(id: UUID, name: String?, email: String?, avatar: Data?, authToken: String) -> AnyPublisher<User, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(id.uuidString)/profile")!
        let headers = standardHeaders(authToken: authToken)

        // For multipart/form-data if avatar is present, this would be more complex.
        // Assuming JSON body for now for name/email.
        // If avatar is handled, APIClient needs to support multipart uploads.
        var parameters: [String: Any?] = [:]
        parameters["name"] = name
        parameters["email"] = email
        // How avatar: Data is sent needs to be defined by the API (e.g., base64 string, or separate multipart upload)
        // For this example, assuming it's part of JSON if it were a string.
        // If avatar is Data for a file, this simple JSON serialization won't work.

        return Future<User, Error> { promise in
            Task {
                do {
                    let body = try JSONSerialization.data(withJSONObject: parameters.compactMapValues { $0 }, options: .fragmentsAllowed)
                    let data = try await self.apiClient.post(url, headers: headers, body: body)
                    let response = try JSONDecoder().decode(UserResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 更新用户设备令牌
    public func updateUserDeviceToken(userID: UUID, deviceToken: String, authToken: String) -> AnyPublisher<Bool, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(userID.uuidString)/device-token")!
        let headers = standardHeaders(authToken: authToken)
        let parameters: [String: Any] = ["deviceToken": deviceToken]

        return Future<Bool, Error> { promise in
            Task {
                do {
                    let body = try JSONSerialization.data(withJSONObject: parameters)
                    _ = try await self.apiClient.post(url, headers: headers, body: body)
                    promise(.success(true))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 重置密码
    public func resetPassword(email: String) -> AnyPublisher<Bool, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(authEndpoint)/reset-password")!
        let headers = basicHeaders()
        let parameters: [String: Any] = ["email": email]

        return Future<Bool, Error> { promise in
            Task {
                do {
                    let body = try JSONSerialization.data(withJSONObject: parameters)
                    _ = try await self.apiClient.post(url, headers: headers, body: body)
                    promise(.success(true))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 更改密码
    public func changePassword(oldPassword: String, newPassword: String, authToken: String) -> AnyPublisher<Bool, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/change-password")!
        let headers = standardHeaders(authToken: authToken)
        let parameters: [String: Any] = [
            "oldPassword": oldPassword,
            "newPassword": newPassword
        ]

        return Future<Bool, Error> { promise in
            Task {
                do {
                    let body = try JSONSerialization.data(withJSONObject: parameters)
                    _ = try await self.apiClient.post(url, headers: headers, body: body)
                    promise(.success(true))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    /// 删除账户
    public func deleteAccount(userID: UUID, authToken: String) -> AnyPublisher<Bool, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(userID.uuidString)")!
        let headers = standardHeaders(authToken: authToken)

        return Future<Bool, Error> { promise in
            Task {
                do {
                    _ = try await self.apiClient.delete(url, headers: headers, body: nil)
                    UserDefaults.standard.removeObject(forKey: "authToken") // Example: Clearing token
                    promise(.success(true))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }

    // MARK: - Async Implementations

    public func getAllAsync(authToken: String) async throws -> [User] {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + userEndpoint)!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.get(url, headers: headers)
        let response = try JSONDecoder().decode(UserListResponse.self, from: data)
        return response.data
    }

    public func getByIdAsync(_ id: UUID, authToken: String) async throws -> User {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(id.uuidString)")!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.get(url, headers: headers)
        let response = try JSONDecoder().decode(UserResponse.self, from: data)
        return response.data
    }

    public func saveAsync(_ entity: User, authToken: String) async throws -> User {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(entity.id.uuidString)")!
        let headers = standardHeaders(authToken: authToken)
        let body = try JSONEncoder().encode(entity)
        let data = try await apiClient.post(url, headers: headers, body: body)
        let response = try JSONDecoder().decode(UserResponse.self, from: data)
        return response.data
    }

    public func saveAllAsync(_ entities: [User], authToken: String) async throws -> [User] {
        // This can be optimized with TaskGroup if the API supports concurrent requests well.
        var savedUsers: [User] = []
        for entity in entities {
            savedUsers.append(try await saveAsync(entity, authToken: authToken))
        }
        return savedUsers
    }

    public func deleteAsync(_ id: UUID, authToken: String) async throws -> Bool {
        // This maps to deleteAccountAsync if the ID matches current user,
        // otherwise it's a generic delete (which might need admin rights on backend).
        // For simplicity, directly calling deleteAccount if it's the main delete path.
        return try await deleteAccountAsync(userID: id, authToken: authToken)
    }

    public func getCurrentUserAsync(authToken: String) async throws -> User {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/me")!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.get(url, headers: headers)
        let response = try JSONDecoder().decode(UserResponse.self, from: data)
        return response.data
    }

    public func loginAsync(username: String, password: String) async throws -> User {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(authEndpoint)/login")!
        let headers = basicHeaders()
        let parameters: [String: Any] = ["username": username, "password": password]
        let body = try JSONSerialization.data(withJSONObject: parameters)
        let data = try await apiClient.post(url, headers: headers, body: body)
        let userContainer = try JSONDecoder().decode(UserContainer.self, from: data)
        UserDefaults.standard.set(userContainer.token, forKey: "authToken") // TODO: Move to UserManager
        return userContainer.user
    }

    public func registerAsync(username: String, email: String, password: String) async throws -> User {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(authEndpoint)/register")!
        let headers = basicHeaders()
        let parameters: [String: Any] = ["username": username, "email": email, "password": password]
        let body = try JSONSerialization.data(withJSONObject: parameters)
        let data = try await apiClient.post(url, headers: headers, body: body)
        let userContainer = try JSONDecoder().decode(UserContainer.self, from: data)
        UserDefaults.standard.set(userContainer.token, forKey: "authToken") // TODO: Move to UserManager
        return userContainer.user
    }

    public func logoutAsync(authToken: String) async throws -> Bool {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(authEndpoint)/logout")!
        let headers = standardHeaders(authToken: authToken)
        _ = try await apiClient.post(url, headers: headers, body: nil)
        UserDefaults.standard.removeObject(forKey: "authToken") // TODO: Move to UserManager
        return true
    }

    public func updateUserProfileAsync(id: UUID, name: String?, email: String?, avatar: Data?, authToken: String) async throws -> User {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(id.uuidString)/profile")!
        let headers = standardHeaders(authToken: authToken)
        var parameters: [String: Any?] = ["name": name, "email": email]
        // Avatar handling (multipart or base64) needs to be considered based on API capabilities
        let body = try JSONSerialization.data(withJSONObject: parameters.compactMapValues { $0 }, options: .fragmentsAllowed)
        let data = try await apiClient.post(url, headers: headers, body: body)
        let response = try JSONDecoder().decode(UserResponse.self, from: data)
        return response.data
    }

    public func updateUserDeviceTokenAsync(userID: UUID, deviceToken: String, authToken: String) async throws -> Bool {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(userID.uuidString)/device-token")!
        let headers = standardHeaders(authToken: authToken)
        let parameters: [String: Any] = ["deviceToken": deviceToken]
        let body = try JSONSerialization.data(withJSONObject: parameters)
        _ = try await apiClient.post(url, headers: headers, body: body)
        return true
    }

    public func resetPasswordAsync(email: String) async throws -> Bool {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(authEndpoint)/reset-password")!
        let headers = basicHeaders()
        let parameters: [String: Any] = ["email": email]
        let body = try JSONSerialization.data(withJSONObject: parameters)
        _ = try await apiClient.post(url, headers: headers, body: body)
        return true
    }

    public func changePasswordAsync(oldPassword: String, newPassword: String, authToken: String) async throws -> Bool {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/change-password")!
        let headers = standardHeaders(authToken: authToken)
        let parameters: [String: Any] = ["oldPassword": oldPassword, "newPassword": newPassword]
        let body = try JSONSerialization.data(withJSONObject: parameters)
        _ = try await apiClient.post(url, headers: headers, body: body)
        return true
    }

    public func deleteAccountAsync(userID: UUID, authToken: String) async throws -> Bool {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\\(userEndpoint)/\\(userID.uuidString)")!
        let headers = standardHeaders(authToken: authToken)
        _ = try await apiClient.delete(url, headers: headers, body: nil)
        UserDefaults.standard.removeObject(forKey: "authToken") // TODO: Move to UserManager
        return true
    }
}

// Helper Response Structs (Ensure these are defined, possibly globally or scoped if not)
struct UserListResponse: Decodable {
    let data: [User]
    // Add other fields like pagination, status, etc.
}

struct UserResponse: Decodable {
    let data: User
    // Add other fields if necessary
}

// Assumed structure for login/register response that includes the token and user
struct UserContainer: Decodable {
    let token: String
    let user: User
}


