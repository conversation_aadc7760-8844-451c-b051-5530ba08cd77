import Foundation
import Combine

public protocol UserRemoteDataSourceProtocol {
    typealias T = User
    typealias ID = UUID

    // Methods from RemoteDataSourceProtocol (with authToken)
    func getAll(authToken: String) -> AnyPublisher<[User], Error>
    func getById(_ id: ID, authToken: String) -> AnyPublisher<User, Error>
    func save(_ entity: User, authToken: String) -> AnyPublisher<User, Error>
    func saveAll(_ entities: [User], authToken: String) -> AnyPublisher<[User], Error> // If used
    func delete(_ id: ID, authToken: String) -> AnyPublisher<Bool, Error>

    // User-specific methods
    func getCurrentUser(authToken: String) -> AnyPublisher<User, Error>
    
    // Auth methods - these typically don't take an existing authToken
    func login(username: String, password: String) -> AnyPublisher<User, Error> // User & token are in response
    func register(username: String, email: String, password: String) -> AnyPublisher<User, Error> // User & token are in response
    func logout(authToken: String) -> AnyPublisher<Bool, Error> // Needs token to invalidate session on server

    func updateUserProfile(id: UUID, name: String?, email: String?, avatar: Data?, authToken: String) -> AnyPublisher<User, Error>
    func updateUserDeviceToken(userID: UUID, deviceToken: String, authToken: String) -> AnyPublisher<Bool, Error>
    func resetPassword(email: String) -> AnyPublisher<Bool, Error> // Typically no auth token
    func changePassword(oldPassword: String, newPassword: String, authToken: String) -> AnyPublisher<Bool, Error>
    func deleteAccount(userID: UUID, authToken: String) -> AnyPublisher<Bool, Error>
    
    // MARK: - Async versions
    func getAllAsync(authToken: String) async throws -> [User]
    func getByIdAsync(_ id: ID, authToken: String) async throws -> User
    func saveAsync(_ entity: User, authToken: String) async throws -> User
    func saveAllAsync(_ entities: [User], authToken: String) async throws -> [User]
    func deleteAsync(_ id: ID, authToken: String) async throws -> Bool

    func getCurrentUserAsync(authToken: String) async throws -> User
    func loginAsync(username: String, password: String) async throws -> User
    func registerAsync(username: String, email: String, password: String) async throws -> User
    func logoutAsync(authToken: String) async throws -> Bool
    func updateUserProfileAsync(id: UUID, name: String?, email: String?, avatar: Data?, authToken: String) async throws -> User
    func updateUserDeviceTokenAsync(userID: UUID, deviceToken: String, authToken: String) async throws -> Bool
    func resetPasswordAsync(email: String) async throws -> Bool
    func changePasswordAsync(oldPassword: String, newPassword: String, authToken: String) async throws -> Bool
    func deleteAccountAsync(userID: UUID, authToken: String) async throws -> Bool
} 