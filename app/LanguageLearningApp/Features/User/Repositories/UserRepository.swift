import Foundation
import Combine

/// 用户仓库协议
/// 定义用户相关的业务方法
public protocol UserRepositoryProtocol: RepositoryProtocol where T == User, ID == UUID {
    /// 获取当前用户信息
    /// - Returns: 包含用户信息的发布者
    func getCurrentUser() -> AnyPublisher<User, Error>

    /// 登录
    /// - Parameters:
    ///   - username: 用户名
    ///   - password: 密码
    /// - Returns: 包含用户信息的发布者
    func login(username: String, password: String) -> AnyPublisher<User, Error>

    /// 注册
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - password: 密码
    /// - Returns: 包含用户信息的发布者
    func register(username: String, email: String, password: String) -> AnyPublisher<User, Error>

    /// 登出
    /// - Returns: 包含成功状态的发布者
    func logout() -> AnyPublisher<Bool, Error>

    /// 更新用户资料
    /// - Parameters:
    ///   - name: 姓名
    ///   - email: 邮箱
    ///   - avatar: 头像数据
    /// - Returns: 包含更新后用户信息的发布者
    func updateUserProfile(name: String?, email: String?, avatar: Data?) -> AnyPublisher<User, Error>

    /// 重置密码
    /// - Parameter email: 邮箱
    /// - Returns: 包含成功状态的发布者
    func resetPassword(email: String) -> AnyPublisher<Bool, Error>

    /// 修改密码
    /// - Parameters:
    ///   - oldPassword: 旧密码
    ///   - newPassword: 新密码
    /// - Returns: 包含成功状态的发布者
    func changePassword(oldPassword: String, newPassword: String) -> AnyPublisher<Bool, Error>

    /// 删除账户
    /// - Returns: 包含成功状态的发布者
    func deleteAccount() -> AnyPublisher<Bool, Error>

    /// 清除本地缓存
    /// - Returns: 包含操作结果的发布者
    func clearLocalCache() -> AnyPublisher<Bool, Error>

    // MARK: - 异步方法

    /// 异步获取当前用户信息
    /// - Returns: 用户信息
    func getCurrentUserAsync() async throws -> User

    /// 异步登录
    /// - Parameters:
    ///   - username: 用户名
    ///   - password: 密码
    /// - Returns: 用户信息
    func loginAsync(username: String, password: String) async throws -> User

    /// 异步注册
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - password: 密码
    /// - Returns: 用户信息
    func registerAsync(username: String, email: String, password: String) async throws -> User

    /// 异步登出
    /// - Returns: 成功状态
    func logoutAsync() async throws -> Bool

    /// 异步更新用户资料
    /// - Parameters:
    ///   - name: 姓名
    ///   - email: 邮箱
    ///   - avatar: 头像数据
    /// - Returns: 更新后的用户信息
    func updateUserProfileAsync(name: String?, email: String?, avatar: Data?) async throws -> User

    /// 异步重置密码
    /// - Parameter email: 邮箱
    /// - Returns: 成功状态
    func resetPasswordAsync(email: String) async throws -> Bool

    /// 异步修改密码
    /// - Parameters:
    ///   - oldPassword: 旧密码
    ///   - newPassword: 新密码
    /// - Returns: 成功状态
    func changePasswordAsync(oldPassword: String, newPassword: String) async throws -> Bool

    /// 异步删除账户
    /// - Returns: 成功状态
    func deleteAccountAsync() async throws -> Bool

    /// 异步清除本地缓存
    /// - Returns: 成功状态
    func clearLocalCacheAsync() async throws -> Bool
}

/// 用户仓库实现
@MainActor
public class UserRepository: @preconcurrency UserRepositoryProtocol {
    public typealias T = User
    public typealias ID = UUID

    private let remoteDataSource: UserRemoteDataSourceProtocol
    private let localDataSource: UserLocalDataSourceProtocol
    private var userManager: (any UserManagerProtocol)!
    private let shouldFetchRemoteFirst: Bool

    public static let shared: UserRepository = {
        let repository = UserRepository(userManager: nil, shouldFetchRemoteFirst: true)
        return repository
    }()

    private func publisher<Output>(for कार्य: @escaping () throws -> Output) -> AnyPublisher<Output, Error> {
        Future<Output, Error> { promise in
            DispatchQueue.global(qos: .userInitiated).async {
                do { promise(.success(try कार्य())) }
                catch { promise(.failure(error)) }
            }
        }.eraseToAnyPublisher()
    }

    private func performLocalAsyncTask<Output>( कार्य: @escaping () throws -> Output) async throws -> Output {
        try await Task.detached(priority: .userInitiated) {
            try कार्य()
        }.value
    }

    public init(
        remoteDataSource: UserRemoteDataSourceProtocol = UserRemoteDataSource.shared,
        localDataSource: UserLocalDataSourceProtocol = UserLocalDataSource.shared,
        userManager: (any UserManagerProtocol)? = nil,
        shouldFetchRemoteFirst: Bool = true
    ) {
        self.remoteDataSource = remoteDataSource
        self.localDataSource = localDataSource
        if let um = userManager {
            self.userManager = um
        }
        self.shouldFetchRemoteFirst = shouldFetchRemoteFirst
    }

    public func setUserManager(_ manager: any UserManagerProtocol) {
        self.userManager = manager
    }

    private func getAuthToken() -> String? {
        guard userManager != nil else {
            print("Warning: UserRepository.userManager is nil when trying to getAuthToken.")
            return nil
        }
        return self.userManager.authToken
    }
    private func getCurrentUserId() -> UUID? {
        guard userManager != nil else {
            print("Warning: UserRepository.userManager is nil when trying to getCurrentUserId.")
            return nil
        }
        return self.userManager.currentUser?.id
    }

    // MARK: - UserRepositoryProtocol Specific Methods

    public func getCurrentUser() -> AnyPublisher<User, Error> {
        guard let authToken = getAuthToken() else {
            return publisher { try self.localDataSource.getCurrentUser() }
                .compactMap { $0 }
                .catch { _ in Fail(error: RepositoryError.authenticationRequired).eraseToAnyPublisher() }
                .eraseToAnyPublisher()
        }

        let remoteFetch = self.remoteDataSource.getCurrentUser(authToken: authToken)
            .flatMap { [weak self] user -> AnyPublisher<User, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher {
                    _ = try self.localDataSource.save(user: user)
                    return user
                }
            }.eraseToAnyPublisher()

        if self.shouldFetchRemoteFirst {
            return remoteFetch.catch { [weak self] (_: Error) -> AnyPublisher<User, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.getCurrentUser() }
                    .compactMap { $0 }
                    .eraseToAnyPublisher()
            }.eraseToAnyPublisher()
        } else {
            return publisher { try self.localDataSource.getCurrentUser() }
                .compactMap { $0 }
                .catch { [weak self] (_: Error) -> AnyPublisher<User, Error> in
                    guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                    return remoteFetch
                }.eraseToAnyPublisher()
        }
    }

    public func login(username: String, password: String) -> AnyPublisher<User, Error> {
        return self.remoteDataSource.login(username: username, password: password)
            .flatMap { [weak self] user -> AnyPublisher<User, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher {
                    _ = try self.localDataSource.save(user: user)
                    return user
                }
            }.eraseToAnyPublisher()
    }

    public func register(username: String, email: String, password: String) -> AnyPublisher<User, Error> {
        return self.remoteDataSource.register(username: username, email: email, password: password)
            .flatMap { [weak self] user -> AnyPublisher<User, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher {
                    _ = try self.localDataSource.save(user: user)
                    return user
                }
            }.eraseToAnyPublisher()
    }

    public func logout() -> AnyPublisher<Bool, Error> {
        guard let authToken = getAuthToken() else {
            return Fail(error: RepositoryError.authenticationRequired).eraseToAnyPublisher()
        }
        return self.remoteDataSource.logout(authToken: authToken)
            .flatMap { [weak self] success -> AnyPublisher<Bool, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                if success {
                    return self.publisher { _ = try self.localDataSource.clearCurrentUserData(); return true }
                } else {
                    return Just(false).setFailureType(to: Error.self).eraseToAnyPublisher()
                }
            }.eraseToAnyPublisher()
    }

    public func updateUserProfile(name: String?, email: String?, avatar: Data?) -> AnyPublisher<User, Error> {
        guard let authToken = getAuthToken(), let userId = getCurrentUserId() else {
            return Fail(error: RepositoryError.authenticationRequired).eraseToAnyPublisher()
        }
        return self.remoteDataSource.updateUserProfile(id: userId, name: name, email: email, avatar: avatar, authToken: authToken)
            .flatMap { [weak self] updatedUser -> AnyPublisher<User, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher {
                    _ = try self.localDataSource.save(user: updatedUser)
                    return updatedUser
                }
            }.eraseToAnyPublisher()
    }

    public func resetPassword(email: String) -> AnyPublisher<Bool, Error> {
        return self.remoteDataSource.resetPassword(email: email)
    }

    public func changePassword(oldPassword: String, newPassword: String) -> AnyPublisher<Bool, Error> {
        guard let authToken = getAuthToken() else { return Fail(error: RepositoryError.authenticationRequired).eraseToAnyPublisher() }
        return self.remoteDataSource.changePassword(oldPassword: oldPassword, newPassword: newPassword, authToken: authToken)
    }

    public func deleteAccount() -> AnyPublisher<Bool, Error> {
        guard let authToken = getAuthToken(), let userId = getCurrentUserId() else {
            return Fail(error: RepositoryError.authenticationRequired).eraseToAnyPublisher()
        }
        return self.remoteDataSource.deleteAccount(userID: userId, authToken: authToken)
            .flatMap { [weak self] (success: Bool) -> AnyPublisher<Bool, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                if success {
                    return self.publisher { _ = try self.localDataSource.clearAllData(); return true }
                } else {
                    return Just(false).setFailureType(to: Error.self).eraseToAnyPublisher()
                }
            }.eraseToAnyPublisher()
    }

    public func clearLocalCache() -> AnyPublisher<Bool, Error> {
        return publisher { try self.localDataSource.clearAllData(); return true }
    }

    // MARK: - RepositoryProtocol Implementation (Combine)

    public func getAll() -> AnyPublisher<[User], Error> {
        guard let authToken = getAuthToken() else {
            return publisher { try self.localDataSource.getAll() }.eraseToAnyPublisher()
        }

        let remoteFetch = self.remoteDataSource.getAll(authToken: authToken)
            .flatMap { [weak self] (remoteEntities: [User]) -> AnyPublisher<[User], Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher { _ = try self.localDataSource.saveAll(users: remoteEntities); return remoteEntities }
            }.eraseToAnyPublisher()

        if self.shouldFetchRemoteFirst {
            return remoteFetch.catch { [weak self] (_: Error) -> AnyPublisher<[User], Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.getAll() }.eraseToAnyPublisher()
            }.eraseToAnyPublisher()
        } else {
            return publisher { try self.localDataSource.getAll() }
                .catch { [weak self] (_: Error) -> AnyPublisher<[User], Error> in
                    guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                    return remoteFetch
                }.eraseToAnyPublisher()
        }
    }

    public func getById(_ id: UUID) -> AnyPublisher<User?, Error> {
        guard let authToken = getAuthToken() else {
            return publisher { try self.localDataSource.getById(id: id) }.eraseToAnyPublisher()
        }

        let remoteFetch = self.remoteDataSource.getById(id, authToken: authToken)
            .flatMap { [weak self] (user: User) -> AnyPublisher<User?, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher { _ = try self.localDataSource.save(user: user); return user as User? }
            }.eraseToAnyPublisher()

        if self.shouldFetchRemoteFirst {
            return remoteFetch.catch { [weak self] (error: Error) -> AnyPublisher<User?, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                if let appError = error as? AppError, appError == .notFound {
                     return self.publisher { try self.localDataSource.getById(id: id) }.eraseToAnyPublisher()
                } else if error is NetworkError {
                     return self.publisher { try self.localDataSource.getById(id: id) }.eraseToAnyPublisher()
                }
                return Fail(error: error).eraseToAnyPublisher()
            }.eraseToAnyPublisher()
        } else {
            return publisher { try self.localDataSource.getById(id: id) }
                .catch { [weak self] (_: Error) -> AnyPublisher<User?, Error> in
                    guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                    return remoteFetch
                }.eraseToAnyPublisher()
        }
    }

    public func save(_ entity: User) -> AnyPublisher<User, Error> {
        guard let authToken = getAuthToken() else { return Fail(error: RepositoryError.authenticationRequired).eraseToAnyPublisher() }

        let remoteOperation: AnyPublisher<User, Error>
        if entity.id != UUID() && entity.id != UUID(uuidString: "00000000-0000-0000-0000-000000000000") {
             remoteOperation = self.remoteDataSource.updateUserProfile(id: entity.id, name: entity.username, email: entity.email, avatar: nil, authToken: authToken)
        } else {
             return Fail(error: RepositoryError.operationNotSupported("Save for new user with default ID. Use register or ensure ID is valid for update.")).eraseToAnyPublisher()
        }

        return remoteOperation
            .flatMap { [weak self] savedRemoteEntity -> AnyPublisher<User, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher { _ = try self.localDataSource.save(user: savedRemoteEntity); return savedRemoteEntity }
            }
            .catch { [weak self] (error: Error) -> AnyPublisher<User, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                print("UserRepository: Remote save failed for user \\(entity.id). Error: \\(error.localizedDescription). Attempting local save.")
                return self.publisher { _ = try self.localDataSource.save(user: entity); return entity }
            }
            .eraseToAnyPublisher()
    }

    public func saveAll(_ entities: [User]) -> AnyPublisher<[User], Error> {
        guard let _ = getAuthToken() else {
            print("UserRepository: No auth token, performing local-only saveAll.")
            return publisher { _ = try self.localDataSource.saveAll(users: entities); return entities }.eraseToAnyPublisher()
        }
        print("UserRepository: saveAll is performing a local-only batch save. Remote batch not implemented on remoteDataSource.")
        return publisher { _ = try self.localDataSource.saveAll(users: entities); return entities }.eraseToAnyPublisher()
    }

    private func deleteUser(_ entity: User) -> AnyPublisher<Bool, Error> {
        return self.delete(entity.id)
    }

    public func delete(_ id: UUID) -> AnyPublisher<Bool, Error> {
        guard let authToken = getAuthToken() else { return Fail(error: RepositoryError.authenticationRequired).eraseToAnyPublisher() }

        if let currentUserID = getCurrentUserId(), id == currentUserID {
            return deleteAccount()
        }

        return self.remoteDataSource.delete(id, authToken: authToken)
            .flatMap { [weak self] (success: Bool) -> AnyPublisher<Bool, Error> in
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                if success {
                    return self.publisher { _ = try self.localDataSource.delete(id: id); return true }
                } else {
                    return Just(false).setFailureType(to: Error.self).eraseToAnyPublisher()
                }
            }
            .catch { [weak self] (error: Error) -> AnyPublisher<Bool, Error> in
                print("UserRepository: Remote deleteById failed for user \\(id). Error: \\(error.localizedDescription). Attempting local delete.")
                guard let self = self else { return Fail(error: RepositoryError.unknown).eraseToAnyPublisher() }
                return self.publisher { _ = try self.localDataSource.delete(id: id); return true }
            }
            .eraseToAnyPublisher()
    }

    // MARK: - Async/Await Implementation

    public func getCurrentUserAsync() async throws -> User {
        guard let authToken = getAuthToken() else {
            if let localUser = try await performLocalAsyncTask { try self.localDataSource.getCurrentUser() } {
                return localUser
            }
            throw RepositoryError.authenticationRequired
        }
        do {
            let remoteUser = try await self.remoteDataSource.getCurrentUserAsync(authToken: authToken)
            try await performLocalAsyncTask { _ = try self.localDataSource.save(user: remoteUser) }
            return remoteUser
        } catch {
            if let localUser = try await performLocalAsyncTask { try self.localDataSource.getCurrentUser() } {
                return localUser
            }
            throw error
        }
    }

    public func loginAsync(username: String, password: String) async throws -> User {
        let user = try await self.remoteDataSource.loginAsync(username: username, password: password)
        try await performLocalAsyncTask { _ = try self.localDataSource.save(user: user) }
        return user
    }

    public func registerAsync(username: String, email: String, password: String) async throws -> User {
        let user = try await self.remoteDataSource.registerAsync(username: username, email: email, password: password)
        try await performLocalAsyncTask { _ = try self.localDataSource.save(user: user) }
        return user
    }

    public func logoutAsync() async throws -> Bool {
        guard let authToken = getAuthToken() else {
            try await performLocalAsyncTask { try self.localDataSource.clearCurrentUserData() }
            return true
        }
        let success = try await self.remoteDataSource.logoutAsync(authToken: authToken)
        if success {
            try await performLocalAsyncTask { try self.localDataSource.clearCurrentUserData() }
        }
        return success
    }

    public func updateUserProfileAsync(name: String?, email: String?, avatar: Data?) async throws -> User {
        guard let authToken = getAuthToken(), let userId = getCurrentUserId() else {
            throw RepositoryError.authenticationRequired
        }
        let updatedUser = try await self.remoteDataSource.updateUserProfileAsync(id: userId, name: name, email: email, avatar: avatar, authToken: authToken)
        try await performLocalAsyncTask { _ = try self.localDataSource.save(user: updatedUser) }
        return updatedUser
    }

    public func resetPasswordAsync(email: String) async throws -> Bool {
        return try await self.remoteDataSource.resetPasswordAsync(email: email)
    }

    public func changePasswordAsync(oldPassword: String, newPassword: String) async throws -> Bool {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        return try await self.remoteDataSource.changePasswordAsync(oldPassword: oldPassword, newPassword: newPassword, authToken: authToken)
    }

    public func deleteAccountAsync() async throws -> Bool {
        guard let authToken = getAuthToken(), let userId = getCurrentUserId() else {
            throw RepositoryError.authenticationRequired
        }
        let success = try await self.remoteDataSource.deleteAccountAsync(userID: userId, authToken: authToken)
        if success {
            _ = try await performLocalAsyncTask { try self.localDataSource.clearAllData() }
            try await performLocalAsyncTask { try self.localDataSource.clearCurrentUserData() }
        }
        return success
    }

    public func clearLocalCacheAsync() async throws -> Bool {
        _ = try await performLocalAsyncTask { try self.localDataSource.clearAllData() }
        try await performLocalAsyncTask { try self.localDataSource.clearCurrentUserData() }
        return true
    }

    // MARK: - RepositoryProtocol Implementation (Async)
    public func getAllAsync() async throws -> [User] {
        if let authToken = getAuthToken() {
            do {
                let remoteEntities = try await self.remoteDataSource.getAllAsync(authToken: authToken)
                try await performLocalAsyncTask { _ = try self.localDataSource.saveAll(users: remoteEntities) }
                return remoteEntities
            } catch {
                return try await performLocalAsyncTask { try self.localDataSource.getAll() }
            }
        } else {
            return try await performLocalAsyncTask { try self.localDataSource.getAll() }
        }
    }

    public func getByIdAsync(_ id: UUID) async throws -> User? {
        if let authToken = getAuthToken() {
            do {
                let remoteUser = try await self.remoteDataSource.getByIdAsync(id, authToken: authToken)
                try await performLocalAsyncTask { _ = try self.localDataSource.save(user: remoteUser) }
                return remoteUser
            } catch let error as AppError where error == .notFound {
                 return try await performLocalAsyncTask { try self.localDataSource.getById(id: id) }
            } catch is NetworkError {
                 return try await performLocalAsyncTask { try self.localDataSource.getById(id: id) }
            } catch {
                if let localUser = try await performLocalAsyncTask { try self.localDataSource.getById(id: id) } {
                    return localUser
                }
                throw error
            }
        } else {
            return try await performLocalAsyncTask { try self.localDataSource.getById(id: id) }
        }
    }

    public func saveAsync(_ entity: User) async throws -> User {
        guard let authToken = getAuthToken() else {
            _ = try await performLocalAsyncTask { try self.localDataSource.save(user: entity) }
            return entity
        }
        do {
            let savedRemoteEntity = try await self.remoteDataSource.saveAsync(entity, authToken: authToken)
            try await performLocalAsyncTask { _ = try self.localDataSource.save(user: savedRemoteEntity) }
            return savedRemoteEntity
        } catch {
            _ = try await performLocalAsyncTask { try self.localDataSource.save(user: entity) }
            return entity
        }
    }

    public func saveAllAsync(_ entities: [User]) async throws -> [User] {
         guard let _ = getAuthToken() else {
            try await performLocalAsyncTask { _ = try self.localDataSource.saveAll(users: entities) }
            return entities
        }
        print("UserRepository (Async): saveAll is performing a local-only batch save. Remote batch not implemented.")
        try await performLocalAsyncTask { _ = try self.localDataSource.saveAll(users: entities) }
        return entities
    }

    private func deleteUserAsync(_ entity: User) async throws -> Bool {
        return try await self.deleteAsync(entity.id)
    }

    public func deleteAsync(_ id: UUID) async throws -> Bool {
        guard let authToken = getAuthToken() else {
            _ = try await performLocalAsyncTask { try self.localDataSource.delete(id: id) }
            return true
        }

        if let currentUserID = getCurrentUserId(), id == currentUserID {
            return try await deleteAccountAsync()
        }

        if shouldFetchRemoteFirst {
            do {
                let remoteSuccess = try await self.remoteDataSource.deleteAsync(id, authToken: authToken)
                if remoteSuccess {
                    _ = try await performLocalAsyncTask { try self.localDataSource.delete(id: id) }
                    return true
                } else {
                    print("UserRepository: Remote delete for ID \(id) reported false. Considering this a failure.")
                    return false
                }
            } catch {
                print("UserRepository: Remote delete failed for ID \(id). Error: \(error.localizedDescription). Attempting local delete.")
                _ = try await performLocalAsyncTask { try self.localDataSource.delete(id: id) }
                return true
            }
        } else {
            do {
                _ = try await performLocalAsyncTask { try self.localDataSource.delete(id: id) }
                do {
                    _ = try await self.remoteDataSource.deleteAsync(id, authToken: authToken)
                } catch {
                    print("UserRepository: Local-first delete succeeded for ID \(id), but subsequent remote delete failed. Error: \(error.localizedDescription)")
                }
                return true
            } catch {
                print("UserRepository: Local delete failed for ID \(id) in 'local first' mode. Error: \(error.localizedDescription). Attempting remote delete.")
                let remoteSuccess = try await self.remoteDataSource.deleteAsync(id, authToken: authToken)
                if remoteSuccess {
                    _ = try? await performLocalAsyncTask { try self.localDataSource.delete(id: id) }
                    return true
                }
                return false
            }
        }
    }

    public func deleteAll() -> AnyPublisher<Bool, Error> {
        guard let authToken = getAuthToken() else {
            return publisher { _ = try self.localDataSource.clearAllData(); return true }.eraseToAnyPublisher()
        }

        print("UserRepository: deleteAll is performing a local-only deleteAll. Remote deleteAll not implemented on remoteDataSource.")
        return publisher { _ = try self.localDataSource.clearAllData(); return true }.eraseToAnyPublisher()
    }

    public func deleteAllAsync() async throws -> Bool {
        guard let _ = getAuthToken() else {
            _ = try await performLocalAsyncTask { try self.localDataSource.clearAllData() }
            return true
        }
        print("UserRepository (Async): deleteAll is performing a local-only deleteAll. Remote deleteAll not implemented.")
        _ = try await performLocalAsyncTask { try self.localDataSource.clearAllData() }
        return true
    }
}

// Ensure UserLocalDataSource.shared and UserRemoteDataSource.shared are available and conform to their respective protocols.
// Also, ensure User.guest is available.
// And RepositoryError is globally available.
// And RepositoryError is globally available.