import Foundation

/// 用户统计信息
public struct UserStats: Codable {
    /// 用户ID
    public let id: UUID?
    /// 用户ID
    public let userId: UUID?
    /// 当前连续学习天数
    public var currentStreak: Int
    /// 最长连续学习天数
    public var longestStreak: Int
    /// 词汇量
    public var vocabularyCount: Int
    /// 听力练习次数
    public var listeningCount: Int
    /// 口语练习次数
    public var speakingCount: Int
    /// 语法练习次数
    public var grammarCount: Int
    /// 总积分
    public var totalPoints: Int
    /// 完成的挑战数
    public var challengesCompleted: Int
    /// 帮助的用户数
    public var helpedUsers: Int
    /// 总练习时间（秒）
    public var totalPracticeTime: Int64
    /// 平均分数
    public var averageScore: Double
    /// 学习天数
    public var learningDays: Int
    /// 完成的课程数
    public var completedLessons: Int
    /// 最后活跃时间
    public var lastActive: Date?
    /// 更新时间
    public var updatedAt: Date?

    /// 编码键，处理字段名映射
    enum CodingKeys: String, CodingKey {
        case id, userId, currentStreak, longestStreak, vocabularyCount
        case listeningCount, speakingCount, grammarCount, totalPoints
        case challengesCompleted, helpedUsers, totalPracticeTime
        case averageScore, learningDays, completedLessons
        case lastActive, updatedAt
    }

    // MARK: - 便利属性 (向后兼容)

    /// 连续学习天数 (向后兼容)
    public var streakDays: Int {
        get { return currentStreak }
        set { currentStreak = newValue }
    }

    /// 听力练习次数 (向后兼容)
    public var listeningExerciseCount: Int {
        get { return listeningCount }
        set { listeningCount = newValue }
    }

    /// 口语练习次数 (向后兼容)
    public var speakingExerciseCount: Int {
        get { return speakingCount }
        set { speakingCount = newValue }
    }

    /// 积分 (向后兼容)
    public var points: Int {
        get { return totalPoints }
        set { totalPoints = newValue }
    }

    /// 最后登录日期 (向后兼容)
    public var lastLoginDate: Date? {
        get { return lastActive }
        set { lastActive = newValue }
    }

    /// 创建用户统计信息
    /// - Parameters:
    ///   - id: 统计ID
    ///   - userId: 用户ID
    ///   - currentStreak: 当前连续学习天数
    ///   - longestStreak: 最长连续学习天数
    ///   - vocabularyCount: 词汇量
    ///   - listeningCount: 听力练习次数
    ///   - speakingCount: 口语练习次数
    ///   - grammarCount: 语法练习次数
    ///   - totalPoints: 总积分
    ///   - challengesCompleted: 完成的挑战数
    ///   - helpedUsers: 帮助的用户数
    ///   - totalPracticeTime: 总练习时间（秒）
    ///   - averageScore: 平均分数
    ///   - learningDays: 学习天数
    ///   - completedLessons: 完成的课程数
    ///   - lastActive: 最后活跃时间
    ///   - updatedAt: 更新时间
    public init(
        id: UUID? = nil,
        userId: UUID? = nil,
        currentStreak: Int = 0,
        longestStreak: Int = 0,
        vocabularyCount: Int = 0,
        listeningCount: Int = 0,
        speakingCount: Int = 0,
        grammarCount: Int = 0,
        totalPoints: Int = 0,
        challengesCompleted: Int = 0,
        helpedUsers: Int = 0,
        totalPracticeTime: Int64 = 0,
        averageScore: Double = 0.0,
        learningDays: Int = 0,
        completedLessons: Int = 0,
        lastActive: Date? = nil,
        updatedAt: Date? = nil
    ) {
        self.id = id
        self.userId = userId
        self.currentStreak = currentStreak
        self.longestStreak = longestStreak
        self.vocabularyCount = vocabularyCount
        self.listeningCount = listeningCount
        self.speakingCount = speakingCount
        self.grammarCount = grammarCount
        self.totalPoints = totalPoints
        self.challengesCompleted = challengesCompleted
        self.helpedUsers = helpedUsers
        self.totalPracticeTime = totalPracticeTime
        self.averageScore = averageScore
        self.learningDays = learningDays
        self.completedLessons = completedLessons
        self.lastActive = lastActive
        self.updatedAt = updatedAt
    }

    /// 向后兼容的初始化方法
    /// - Parameters:
    ///   - streakDays: 连续学习天数
    ///   - vocabularyCount: 词汇量
    ///   - listeningExerciseCount: 听力练习次数
    ///   - speakingExerciseCount: 口语练习次数
    ///   - points: 积分
    ///   - completedChallenges: 完成的挑战数
    ///   - helpedUsers: 帮助的用户数
    ///   - lastLoginDate: 最后登录日期
    public init(
        streakDays: Int = 0,
        vocabularyCount: Int = 0,
        listeningExerciseCount: Int = 0,
        speakingExerciseCount: Int = 0,
        points: Int = 0,
        completedChallenges: Int = 0,
        helpedUsers: Int = 0,
        lastLoginDate: Date? = nil
    ) {
        self.id = nil
        self.userId = nil
        self.currentStreak = streakDays
        self.longestStreak = streakDays
        self.vocabularyCount = vocabularyCount
        self.listeningCount = listeningExerciseCount
        self.speakingCount = speakingExerciseCount
        self.grammarCount = 0
        self.totalPoints = points
        self.challengesCompleted = completedChallenges
        self.helpedUsers = helpedUsers
        self.totalPracticeTime = 0
        self.averageScore = 0.0
        self.learningDays = 0
        self.completedLessons = 0
        self.lastActive = lastLoginDate
        self.updatedAt = nil
    }

    public static var sample: UserStats {
        UserStats(
            currentStreak: 3,
            longestStreak: 7,
            vocabularyCount: 120,
            listeningCount: 45,
            speakingCount: 30,
            grammarCount: 25,
            totalPoints: 1250,
            challengesCompleted: 5,
            helpedUsers: 2,
            totalPracticeTime: 3600, // 1小时
            averageScore: 85.5,
            learningDays: 15,
            completedLessons: 8,
            lastActive: Date(),
            updatedAt: Date()
        )
    }
}