import Foundation

/// 用户模型
public struct User: Codable, Identifiable {
    /// 用户 ID
    public let id: UUID
    /// 用户名
    public let username: String
    /// 邮箱
    public let email: String
    /// 头像 URL (匹配backend的avatarUrl字段)
    public let avatarUrl: String?
    /// 是否激活
    public let isActive: Bool
    /// 创建时间
    public let createdAt: Date?
    /// 更新时间
    public let updatedAt: Date?
    /// 最后登录时间
    public let lastLoginAt: Date?
    /// 用户设置
    public let settings: UserSettings?
    /// 用户统计
    public let stats: UserStats?
    /// 认证令牌 (仅在登录响应中使用)
    public let token: String?

    /// 编码键，处理字段名映射
    enum CodingKeys: String, CodingKey {
        case id, username, email, isActive, createdAt, lastLoginAt, settings, stats, token
        case avatarUrl = "avatarUrl"
        case updatedAt
    }

    // MARK: - 便利属性 (从stats中获取)

    /// 当前连续学习天数
    public var currentStreak: Int {
        return stats?.currentStreak ?? 0
    }

    /// 词汇量
    public var vocabularyCount: Int {
        return stats?.vocabularyCount ?? 0
    }

    /// 听力练习次数
    public var listeningExerciseCount: Int {
        return stats?.listeningCount ?? 0
    }

    /// 口语练习次数
    public var speakingExerciseCount: Int {
        return stats?.speakingCount ?? 0
    }

    /// 积分
    public var points: Int {
        return stats?.totalPoints ?? 0
    }

    /// 完成的挑战数
    public var completedChallenges: Int {
        return stats?.challengesCompleted ?? 0
    }

    /// 帮助的用户数
    public var helpedUsers: Int {
        return stats?.helpedUsers ?? 0
    }

    /// 头像URL的便利属性 (向后兼容)
    public var avatar: String? {
        return avatarUrl
    }

    /// 创建用户
    /// - Parameters:
    ///   - id: 用户 ID
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - avatarUrl: 头像 URL
    ///   - isActive: 是否激活
    ///   - createdAt: 创建时间
    ///   - updatedAt: 更新时间
    ///   - lastLoginAt: 最后登录时间
    ///   - settings: 用户设置
    ///   - stats: 用户统计
    ///   - token: 认证令牌
    public init(
        id: UUID = UUID(),
        username: String,
        email: String,
        avatarUrl: String? = nil,
        isActive: Bool = true,
        createdAt: Date? = nil,
        updatedAt: Date? = nil,
        lastLoginAt: Date? = nil,
        settings: UserSettings? = nil,
        stats: UserStats? = nil,
        token: String? = nil
    ) {
        self.id = id
        self.username = username
        self.email = email
        self.avatarUrl = avatarUrl
        self.isActive = isActive
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.lastLoginAt = lastLoginAt
        self.settings = settings
        self.stats = stats
        self.token = token
    }

    /// 向后兼容的初始化方法
    /// - Parameters:
    ///   - id: 用户 ID
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - name: 姓名 (已弃用)
    ///   - avatar: 头像 URL (已弃用，使用avatarUrl)
    ///   - createdAt: 创建时间
    ///   - updatedAt: 更新时间
    ///   - lastLoginAt: 最后登录时间
    ///   - isActive: 是否激活
    ///   - settings: 用户设置
    ///   - stats: 用户统计
    ///   - currentStreak: 当前连续学习天数 (已弃用，从stats获取)
    ///   - vocabularyCount: 词汇量 (已弃用，从stats获取)
    ///   - listeningExerciseCount: 听力练习次数 (已弃用，从stats获取)
    ///   - speakingExerciseCount: 口语练习次数 (已弃用，从stats获取)
    ///   - points: 积分 (已弃用，从stats获取)
    ///   - completedChallenges: 完成的挑战数 (已弃用，从stats获取)
    ///   - helpedUsers: 帮助的用户数 (已弃用，从stats获取)
    ///   - token: 认证令牌
    public init(
        id: UUID = UUID(),
        username: String,
        email: String,
        name: String? = nil,
        avatar: String? = nil,
        createdAt: Date? = nil,
        updatedAt: Date? = nil,
        lastLoginAt: Date? = nil,
        isActive: Bool? = true,
        settings: UserSettings? = nil,
        stats: UserStats? = nil,
        currentStreak: Int = 0,
        vocabularyCount: Int = 0,
        listeningExerciseCount: Int = 0,
        speakingExerciseCount: Int = 0,
        points: Int = 0,
        completedChallenges: Int = 0,
        helpedUsers: Int = 0,
        token: String? = nil
    ) {
        self.id = id
        self.username = username
        self.email = email
        self.avatarUrl = avatar // 映射avatar到avatarUrl
        self.isActive = isActive ?? true
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.lastLoginAt = lastLoginAt
        self.settings = settings

        // 如果没有提供stats但有统计数据，创建一个UserStats对象
        if stats == nil && (currentStreak > 0 || vocabularyCount > 0 || points > 0) {
            self.stats = UserStats(
                currentStreak: currentStreak,
                vocabularyCount: vocabularyCount,
                listeningCount: listeningExerciseCount,
                speakingCount: speakingExerciseCount,
                totalPoints: points,
                challengesCompleted: completedChallenges,
                helpedUsers: helpedUsers
            )
        } else {
            self.stats = stats
        }

        self.token = token
    }

    /// 示例用户
    public static var sample: User {
        User(
            username: "testuser",
            email: "<EMAIL>",
            avatarUrl: "https://example.com/avatar.jpg",
            isActive: true,
            createdAt: Date(),
            updatedAt: Date(),
            lastLoginAt: Date(),
            settings: UserSettings.default,
            stats: UserStats.sample
        )
    }
}

/// 用户偏好设置
public struct UserPreferences: Codable {
    public var language: String
    public var theme: String
    public var notifications: Bool
    public var soundEnabled: Bool
    public var dailyGoal: Int
    public var weeklyGoal: Int

    public init(
        language: String = "zh-CN",
        theme: String = "system",
        notifications: Bool = true,
        soundEnabled: Bool = true,
        dailyGoal: Int = 30,
        weeklyGoal: Int = 180
    ) {
        self.language = language
        self.theme = theme
        self.notifications = notifications
        self.soundEnabled = soundEnabled
        self.dailyGoal = dailyGoal
        self.weeklyGoal = weeklyGoal
    }
}

/// 登录响应模型
public struct LoginResponse: Codable {
    /// 是否成功
    public let success: Bool
    /// 消息
    public let message: String
    /// 数据（JWT token）
    public let data: String
}