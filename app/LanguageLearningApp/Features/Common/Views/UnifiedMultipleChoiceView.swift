import SwiftUI

/// 统一的多选题视图，可以同时处理评估问题和练习数据
struct UnifiedMultipleChoiceView: View {
    // 练习数据
    let exerciseData: UnifiedExerciseData
    // 选中的选项
    @Binding var selectedOption: String?
    // 是否已提交答案
    var isAnswerSubmitted: Bool = false
    // 正确答案（如果已提交）
    var correctAnswer: String? = nil
    
    var body: some View {
        VStack(spacing: 10) {
            if let options = exerciseData.options, !options.isEmpty {
                ForEach(options, id: \.self) { option in
                    optionButtonView(option: option)
                }
            } else {
                Text("此问题没有选项")
                    .font(.body)
                    .foregroundColor(.red)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
    }
    
    // 选项按钮视图
    private func optionButtonView(option: String) -> some View {
        Button(action: {
            if !isAnswerSubmitted {
                selectedOption = option
            }
        }) {
            HStack {
                Text(option)
                    .font(.body)
                    .multilineTextAlignment(.leading)
                
                Spacer()
                
                optionIndicatorView(option: option)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(selectedOption == option ? Color.blue : Color.gray.opacity(0.5), lineWidth: 1)
                    .background(
                        selectedOption == option ? Color.blue.opacity(0.1) : Color.clear
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isAnswerSubmitted)
    }
    
    // 选项指示器视图
    private func optionIndicatorView(option: String) -> some View {
        Group {
            if isAnswerSubmitted {
                // 已提交答案，显示正确/错误指示器
                if let correctAnswer = correctAnswer {
                    if option == correctAnswer {
                        // 正确答案
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    } else if option == selectedOption {
                        // 选择的错误答案
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.red)
                    } else {
                        // 其他选项
                        Image(systemName: "circle")
                            .foregroundColor(.gray)
                    }
                } else {
                    // 没有正确答案信息，只显示选中状态
                    if selectedOption == option {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.blue)
                    } else {
                        Image(systemName: "circle")
                            .foregroundColor(.gray)
                    }
                }
            } else {
                // 未提交答案，只显示选中状态
                if selectedOption == option {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                } else {
                    Image(systemName: "circle")
                        .foregroundColor(.gray)
                }
            }
        }
    }
}

// 预览
struct UnifiedMultipleChoiceView_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            // 未提交答案的状态
            UnifiedMultipleChoiceView(
                exerciseData: UnifiedExerciseData(
                    id: "1",
                    type: "multiple-choice",
                    question: "What is the capital of France?",
                    options: ["London", "Paris", "Berlin", "Madrid"],
                    instruction: nil,
                    audioURL: nil,
                    imageURL: nil,
                    explanation: nil,
                    transcript: nil,
                    hint: nil
                ),
                selectedOption: .constant("Paris")
            )
            .padding()
            
            // 已提交答案的状态
            UnifiedMultipleChoiceView(
                exerciseData: UnifiedExerciseData(
                    id: "2",
                    type: "multiple-choice",
                    question: "What is the capital of Germany?",
                    options: ["London", "Paris", "Berlin", "Madrid"],
                    instruction: nil,
                    audioURL: nil,
                    imageURL: nil,
                    explanation: nil,
                    transcript: nil,
                    hint: nil
                ),
                selectedOption: .constant("Paris"),
                isAnswerSubmitted: true,
                correctAnswer: "Berlin"
            )
            .padding()
        }
    }
}
