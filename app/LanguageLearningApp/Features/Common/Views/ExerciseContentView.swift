import SwiftUI

/// 统一的练习数据模型，用于适配不同来源的练习数据
struct UnifiedExerciseData {
    let id: String
    let type: String
    let question: String
    let options: [String]?
    let instruction: String?
    let audioURL: URL?
    let imageURL: URL?
    let explanation: String?
    let transcript: String?
    let hint: String?

    // 从评估问题创建
    static func fromEvaluationQuestion(_ question: EvaluationQuestion) -> UnifiedExerciseData {
        return UnifiedExerciseData(
            id: question.id.uuidString,
            type: question.type.rawValue,
            question: question.content,
            options: question.options,
            instruction: nil,
            audioURL: question.audioURL,
            imageURL: question.imageURL,
            explanation: question.explanation,
            transcript: nil,
            hint: question.explanation
        )
    }

    // 从练习字典创建
    static func fromExerciseDict(_ dict: [String: Any]) -> UnifiedExerciseData {
        let actualExercise = ExerciseDataProcessor.extractActualExercise(from: dict)

        let question = (actualExercise["question"] as? String) ??
                       (actualExercise["content"] as? String) ?? ""

        let audioURLString = (actualExercise["audioURL"] as? String) ?? ""
        let imageURLString = (actualExercise["imageURL"] as? String) ?? ""

        return UnifiedExerciseData(
            id: (actualExercise["id"] as? String) ?? "",
            type: (dict["type"] as? String) ?? "",
            question: question,
            options: actualExercise["options"] as? [String],
            instruction: ExerciseDataProcessor.getExerciseInstruction(from: actualExercise),
            audioURL: audioURLString.isEmpty ? nil : URL(string: audioURLString),
            imageURL: imageURLString.isEmpty ? nil : URL(string: imageURLString),
            explanation: actualExercise["explanation"] as? String,
            transcript: actualExercise["transcript"] as? String,
            hint: ExerciseDataProcessor.getExerciseHint(from: actualExercise)
        )
    }
}

/// 通用练习内容视图
struct ExerciseContentView<AnswerView: View>: View {
    // 练习数据
    let exerciseData: UnifiedExerciseData
    // 当前索引和总数
    let currentIndex: Int
    let totalCount: Int
    // 自定义答案视图构建器
    let answerViewBuilder: (UnifiedExerciseData) -> AnswerView
    // 是否显示提示
    let showHints: Bool
    // 音频播放控制
    var isPlaying: Binding<Bool>?
    var togglePlayback: (() -> Void)?

    init(
        exerciseData: UnifiedExerciseData,
        currentIndex: Int,
        totalCount: Int,
        answerViewBuilder: @escaping (UnifiedExerciseData) -> AnswerView,
        showHints: Bool = true,
        isPlaying: Binding<Bool>? = nil,
        togglePlayback: (() -> Void)? = nil
    ) {
        self.exerciseData = exerciseData
        self.currentIndex = currentIndex
        self.totalCount = totalCount
        self.answerViewBuilder = answerViewBuilder
        self.showHints = showHints
        self.isPlaying = isPlaying
        self.togglePlayback = togglePlayback
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            // 练习标题
            Text("题目 \(currentIndex + 1)/\(totalCount)")
                .font(.headline)
                .foregroundColor(.secondary)
                .padding(.top)

            // 练习内容
            Text(exerciseData.question)
                .font(.title3)
                .fontWeight(.medium)
                .padding(.vertical, 5)
                .fixedSize(horizontal: false, vertical: true)

            // 如果有指导说明，显示它
            if let instruction = exerciseData.instruction, !instruction.isEmpty {
                Text(instruction)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.bottom, 5)
                    .fixedSize(horizontal: false, vertical: true)
            }

            // 如果有音频，显示音频控制
            if let audioURL = exerciseData.audioURL, let isPlaying = isPlaying, let togglePlayback = togglePlayback {
                audioControlView(url: audioURL, isPlaying: isPlaying, togglePlayback: togglePlayback)
            }

            // 如果有图片，显示图片
            if let imageURL = exerciseData.imageURL {
                imageView(url: imageURL)
            }

            // 如果有听力文本，显示折叠的文本
            if let transcript = exerciseData.transcript, !transcript.isEmpty {
                transcriptView(transcript: transcript)
            }

            // 答案视图
            answerViewBuilder(exerciseData)

            // 提示（如果需要）
            if showHints, let hint = exerciseData.hint, !hint.isEmpty {
                HintView(hint: hint)
            }
        }
        .padding(.horizontal)
    }

    // 音频控制视图
    private func audioControlView(url: URL, isPlaying: Binding<Bool>, togglePlayback: @escaping () -> Void) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Button(action: togglePlayback) {
                    HStack {
                        Image(systemName: isPlaying.wrappedValue ? "pause.circle.fill" : "play.circle.fill")
                            .font(.title)
                            .foregroundColor(.blue)

                        Text(isPlaying.wrappedValue ? "暂停" : "播放")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }

                Spacer()

                #if DEBUG
                Text(url.absoluteString)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(1)
                    .truncationMode(.middle)
                #endif
            }
        }
        .padding(.vertical, 5)
    }

    // 图片视图
    private func imageView(url: URL) -> some View {
        AsyncImage(url: url) { phase in
            switch phase {
            case .empty:
                UnifiedLoadingView(message: "Loading image...", size: .medium)
                    .frame(height: 200)
            case .success(let image):
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxHeight: 200)
                    .cornerRadius(8)
            case .failure:
                Image(systemName: "photo")
                    .font(.largeTitle)
                    .foregroundColor(.gray)
                    .frame(height: 200)
            @unknown default:
                EmptyView()
            }
        }
        .padding(.vertical, 5)
    }

    // 听力文本视图
    private func transcriptView(transcript: String) -> some View {
        DisclosureGroup("查看文本") {
            Text(transcript)
                .font(.body)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(8)
        }
        .padding(.vertical, 5)
    }
}

// 预览
struct ExerciseContentView_Previews: PreviewProvider {
    static var previews: some View {
        ExerciseContentView(
            exerciseData: UnifiedExerciseData(
                id: "1",
                type: "multiple-choice",
                question: "What is the capital of France?",
                options: ["London", "Paris", "Berlin", "Madrid"],
                instruction: "Choose the correct answer",
                audioURL: nil,
                imageURL: nil,
                explanation: "Paris is the capital of France",
                transcript: nil,
                hint: "Think about the Eiffel Tower"
            ),
            currentIndex: 0,
            totalCount: 10,
            answerViewBuilder: { _ in
                Text("Answer options would go here")
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
            }
        )
        .padding()
    }
}
