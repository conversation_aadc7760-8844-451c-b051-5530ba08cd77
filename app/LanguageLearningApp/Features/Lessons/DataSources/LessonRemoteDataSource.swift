import Foundation
import Combine

// MARK: - Publisher Extension for async/await
extension Publisher {
    func async() async throws -> Output {
        return try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            cancellable = self
                .sink(
                    receiveCompletion: { completion in
                        switch completion {
                        case .finished:
                            break
                        case .failure(let error):
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { value in
                        continuation.resume(returning: value)
                        cancellable?.cancel()
                    }
                )
        }
    }
}

/// 课程远程数据源协议
public protocol LessonRemoteDataSourceProtocol {
    /// 获取课程列表
    func getLessons(authToken: String) async throws -> [Lesson]

    /// 获取课程详情
    func getLessonDetail(id: String, authToken: String) async throws -> Lesson

    /// 获取所有课程进度
    func getAllProgress(authToken: String, userID: String) async throws -> [LessonProgress]

    /// 获取课程进度
    func getLessonProgress(lessonID: String, authToken: String, userID: String) async throws -> LessonProgress

    /// 更新课程进度 (通过 LessonProgress 对象)
    func updateLessonProgress(_ progress: LessonProgress, authToken: String) async throws -> LessonProgress
    
    /// 更新课程进度 (通过具体字段)
    func updateLessonProgress(lessonID: String, progress: Double, completed: Bool, authToken: String, userID: String) async throws -> LessonProgress

    /// 获取收藏课程
    func getFavoriteLessons(authToken: String, userID: String) async throws -> [Lesson]

    /// 切换收藏状态
    func toggleFavoriteLesson(lessonID: String, isFavorite: Bool, authToken: String, userID: String) async throws -> Bool
}

/// 课程远程数据源实现
public class LessonRemoteDataSource: LessonRemoteDataSourceProtocol {
    // MARK: - Private Properties
    private let apiClient: APIClientProtocol

    // MARK: - API Endpoints
    private enum Endpoints {
        static let lessons = "/lessons"
        static let lessonDetail = "/lessons/{id}"
        static let progress = "/lessons/progress"
        static let lessonProgress = "/lessons/{id}/progress"
        static let favorites = "/lessons/favorites"
        static let toggleFavorite = "/lessons/{id}/favorite"
    }

    // MARK: - Initialization
    public init(apiClient: APIClientProtocol = APIClient.shared) {
        self.apiClient = apiClient
    }

    // MARK: - Public Methods

    public func getLessons(authToken: String) async throws -> [Lesson] {
        let endpoint = APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(Endpoints.lessons),
            method: "GET",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json"
            ],
            bodyData: nil
        )

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(LessonListResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw LessonRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw LessonRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw LessonRemoteDataSourceError.networkError(error)
        }
    }

    public func getLessonDetail(id: String, authToken: String) async throws -> Lesson {
        let url = Endpoints.lessonDetail.replacingOccurrences(of: "{id}", with: id)
        let endpoint = APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(url),
            method: "GET",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json"
            ],
            bodyData: nil
        )

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(LessonResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw LessonRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw LessonRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw LessonRemoteDataSourceError.networkError(error)
        }
    }

    public func getAllProgress(authToken: String, userID: String) async throws -> [LessonProgress] {
        let endpoint = APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(Endpoints.progress),
            method: "GET",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json",
                "User-ID": userID
            ],
            bodyData: nil
        )

        let dataPublisher = apiClient.request(endpoint: endpoint)
        let data = try await dataPublisher.async()
        let response = try JSONDecoder().decode(LessonProgressListResponse.self, from: data)

        if response.success {
            return response.data
        } else {
            throw LessonRemoteDataSourceError.apiError(response.message ?? "Unknown error")
        }
    }

    public func getLessonProgress(lessonID: String, authToken: String, userID: String) async throws -> LessonProgress {
        let url = Endpoints.lessonProgress.replacingOccurrences(of: "{id}", with: lessonID)
        let endpoint = APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(url),
            method: "GET",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json",
                "User-ID": userID
            ],
            bodyData: nil
        )

        let dataPublisher = apiClient.request(endpoint: endpoint)
        let data = try await dataPublisher.async()
        let response = try JSONDecoder().decode(LessonProgressResponse.self, from: data)

        if response.success {
            return response.data
        } else {
            throw LessonRemoteDataSourceError.apiError(response.message ?? "Unknown error")
        }
    }

    public func updateLessonProgress(_ progress: LessonProgress, authToken: String) async throws -> LessonProgress {
        let url = Endpoints.lessonProgress.replacingOccurrences(of: "{id}", with: progress.lessonId)
        let endpoint = APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(url),
            method: "PUT",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json"
            ],
            bodyData: try JSONEncoder().encode(progress)
        )

        let dataPublisher = apiClient.request(endpoint: endpoint)
        let data = try await dataPublisher.async()
        let response = try JSONDecoder().decode(LessonProgressResponse.self, from: data)

        if response.success {
            return response.data
        } else {
            throw LessonRemoteDataSourceError.apiError(response.message ?? "Unknown error")
        }
    }

    public func updateLessonProgress(lessonID: String, progress: Double, completed: Bool, authToken: String, userID: String) async throws -> LessonProgress {
        let url = Endpoints.lessonProgress.replacingOccurrences(of: "{id}", with: lessonID)
        // Construct the body for the request
        let body: [String: Any] = [
            "progress": progress,
            "completed": completed,
            "lessonId": lessonID,
            "userId": userID, 
            "lastAccessedAt": Date().timeIntervalSince1970
        ]
        let bodyData = try JSONSerialization.data(withJSONObject: body, options: [])
        
        let endpoint = APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(url),
            method: "PUT",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json",
                "User-ID": userID 
            ],
            bodyData: bodyData
        )
        
        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(LessonProgressResponse.self, from: data)
            
            if response.success {
                return response.data
            } else {
                throw LessonRemoteDataSourceError.apiError(response.message ?? "Unknown error updating progress")
            }
        } catch let decodingError as DecodingError {
            throw LessonRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw LessonRemoteDataSourceError.networkError(error)
        }
    }

    public func getFavoriteLessons(authToken: String, userID: String) async throws -> [Lesson] {
        let endpoint = APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(Endpoints.favorites),
            method: "GET",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json",
                "User-ID": userID
            ],
            bodyData: nil
        )

        let dataPublisher = apiClient.request(endpoint: endpoint)
        let data = try await dataPublisher.async()
        let response = try JSONDecoder().decode(LessonListResponse.self, from: data)

        if response.success {
            return response.data
        } else {
            throw LessonRemoteDataSourceError.apiError(response.message ?? "Unknown error")
        }
    }

    public func toggleFavoriteLesson(lessonID: String, isFavorite: Bool, authToken: String, userID: String) async throws -> Bool {
        let url = Endpoints.toggleFavorite.replacingOccurrences(of: "{id}", with: lessonID)
        let endpoint = APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(url),
            method: isFavorite ? "POST" : "DELETE",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json",
                "User-ID": userID
            ],
            bodyData: nil
        )

        let dataPublisher = apiClient.request(endpoint: endpoint)
        let data = try await dataPublisher.async()
        let response = try JSONDecoder().decode(SuccessResponse.self, from: data)

        if response.success {
            return true
        } else {
            throw LessonRemoteDataSourceError.apiError(response.message ?? "Unknown error")
        }
    }

    // MARK: - Private Helper Methods

    private func createAuthenticatedEndpoint(url: String, method: String, body: Data? = nil, authToken: String, userID: String) -> APIEndpoint {
        return APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(url),
            method: method,
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json",
                "User-ID": userID
            ],
            bodyData: body
        )
    }

    private func handleAPIResponse<T: Codable>(_ data: Data, responseType: T.Type) throws -> T {
        do {
            let response = try JSONDecoder().decode(T.self, from: data)
            return response
        } catch let decodingError as DecodingError {
            print("Decoding error: \(decodingError)")
            throw LessonRemoteDataSourceError.decodingError(decodingError)
        }
    }
}

// MARK: - Response Models

public struct LessonListResponse: Codable {
    public let success: Bool
    public let data: [Lesson]
    public let message: String?
    public let error: String?
}

public struct LessonResponse: Codable {
    public let success: Bool
    public let data: Lesson
    public let message: String?
    public let error: String?
}

public struct LessonProgressListResponse: Codable {
    public let success: Bool
    public let data: [LessonProgress]
    public let message: String?
    public let error: String?
}

public struct LessonProgressResponse: Codable {
    public let success: Bool
    public let data: LessonProgress
    public let message: String?
    public let error: String?
}

public struct SuccessResponse: Codable {
    public let success: Bool
    public let message: String?
    public let error: String?
}

// MARK: - Errors

public enum LessonRemoteDataSourceError: Error, LocalizedError {
    case apiError(String)
    case networkError(Error)
    case decodingError(Error)
    case invalidResponse

    public var errorDescription: String? {
        switch self {
        case .apiError(let message):
            return "API错误: \(message)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .invalidResponse:
            return "无效的响应数据"
        }
    }
}
