import Foundation
import CoreData

/// 课程本地数据源协议
public protocol LessonLocalDataSourceProtocol {
    /// 获取课程列表
    func getLessons() async throws -> [Lesson]

    /// 获取课程详情
    func getLessonDetail(id: String) async throws -> Lesson

    /// 保存课程列表
    func saveLessons(_ lessons: [Lesson]) async throws

    /// 保存单个课程
    func saveLesson(_ lesson: Lesson) async throws

    /// 获取所有课程进度
    func getAllProgress() async throws -> [LessonProgress]

    /// 获取课程进度
    func getLessonProgress(id: String) async throws -> LessonProgress

    /// 保存课程进度
    func saveLessonProgress(_ progress: LessonProgress) async throws

    /// 保存所有课程进度
    func saveAllProgress(_ progressList: [LessonProgress]) async throws

    /// 获取收藏课程
    func getFavoriteLessons() async throws -> [Lesson]

    /// 保存收藏课程
    func saveFavoriteLessons(_ lessons: [Lesson]) async throws

    /// 切换收藏状态
    func toggleFavoriteLesson(id: String, isFavorite: Bool) async throws

    /// 标记收藏状态为待同步
    func markFavoriteForSync(id: String, isFavorite: Bool) async throws

    /// 清除所有数据
    func clearAllData() async throws
}

/// 课程本地数据源实现
public class LessonLocalDataSource: LessonLocalDataSourceProtocol {
    // MARK: - Private Properties

    // MARK: - Storage Keys
    private enum StorageKeys {
        static let lessons = "cached_lessons"
        static let lessonProgress = "lesson_progress"
        static let favoriteLessons = "favorite_lessons"
        static let pendingSyncFavorites = "pending_sync_favorites"
    }

    // MARK: - Initialization
    public init() {
        // REMOVED: self.storageManager = storageManager
    }

    // MARK: - Public Methods

    public func getLessons() async throws -> [Lesson] {
        do {
            guard let data = UserDefaults.standard.data(forKey: StorageKeys.lessons) else {
                // If no cached data, return an empty array.
                // LessonLocalDataSource is responsible for its own cache.
                return []
            }

            let lessons = try JSONDecoder().decode([Lesson].self, from: data)
            return lessons
        } catch {
            // If decoding fails, it's an error with this data source's cache.
            print("Failed to decode cached lessons: \(error)")
            // Consider throwing the error or returning empty based on desired behavior for corrupted cache.
            // For now, returning empty to avoid crashes, but this should be logged.
            throw LessonLocalDataSourceError.decodingError(error) // Or return [] after logging
        }
    }

    public func getLessonDetail(id: String) async throws -> Lesson {
        let lessons = try await getLessons()
        guard let lesson = lessons.first(where: { $0.id == id }) else {
            throw LessonLocalDataSourceError.lessonNotFound
        }
        return lesson
    }

    public func saveLessons(_ lessons: [Lesson]) async throws {
        do {
            // 保存到 UserDefaults
            let data = try JSONEncoder().encode(lessons)
            UserDefaults.standard.set(data, forKey: StorageKeys.lessons)

            print("Successfully saved \(lessons.count) lessons to local storage")
        } catch {
            throw LessonLocalDataSourceError.encodingError(error)
        }
    }

    public func saveLesson(_ lesson: Lesson) async throws {
        var lessons = try await getLessons()

        // 更新或添加课程
        if let index = lessons.firstIndex(where: { $0.id == lesson.id }) {
            lessons[index] = lesson
            print("Updated lesson: \(lesson.title)")
        } else {
            lessons.append(lesson)
            print("Added new lesson: \(lesson.title)")
        }

        try await saveLessons(lessons)
    }

    public func getAllProgress() async throws -> [LessonProgress] {
        guard let data = UserDefaults.standard.data(forKey: StorageKeys.lessonProgress) else {
            return []
        }

        do {
            let progressList = try JSONDecoder().decode([LessonProgress].self, from: data)
            return progressList
        } catch {
            throw LessonLocalDataSourceError.decodingError(error)
        }
    }

    public func getLessonProgress(id: String) async throws -> LessonProgress {
        let allProgress = try await getAllProgress()
        guard let progress = allProgress.first(where: { $0.lessonId == id }) else {
            // 如果没有进度记录，创建一个默认的
            return LessonProgress(
                id: UUID(),
                lessonId: id,
                userId: getCurrentUserId(),
                progress: 0.0,
                completed: false,
                lastAccessedAt: Date(),
                completedAt: nil
            )
        }
        return progress
    }

    public func saveLessonProgress(_ progress: LessonProgress) async throws {
        var allProgress = try await getAllProgress()

        // 更新或添加进度
        if let index = allProgress.firstIndex(where: { $0.lessonId == progress.lessonId }) {
            allProgress[index] = progress
        } else {
            allProgress.append(progress)
        }

        try await saveAllProgress(allProgress)
    }

    public func saveAllProgress(_ progressList: [LessonProgress]) async throws {
        do {
            let data = try JSONEncoder().encode(progressList)
            UserDefaults.standard.set(data, forKey: StorageKeys.lessonProgress)
        } catch {
            throw LessonLocalDataSourceError.encodingError(error)
        }
    }

    public func getFavoriteLessons() async throws -> [Lesson] {
        guard let data = UserDefaults.standard.data(forKey: StorageKeys.favoriteLessons) else {
            return []
        }

        do {
            let favoriteLessons = try JSONDecoder().decode([Lesson].self, from: data)
            return favoriteLessons
        } catch {
            throw LessonLocalDataSourceError.decodingError(error)
        }
    }

    public func saveFavoriteLessons(_ lessons: [Lesson]) async throws {
        do {
            let data = try JSONEncoder().encode(lessons)
            UserDefaults.standard.set(data, forKey: StorageKeys.favoriteLessons)
        } catch {
            throw LessonLocalDataSourceError.encodingError(error)
        }
    }

    public func toggleFavoriteLesson(id: String, isFavorite: Bool) async throws {
        var favoriteLessons = try await getFavoriteLessons()
        let allLessons = try await getLessons()

        if isFavorite {
            // 添加到收藏
            if let lesson = allLessons.first(where: { $0.id == id }),
               !favoriteLessons.contains(where: { $0.id == id }) {
                favoriteLessons.append(lesson)
                print("Added lesson to favorites: \(lesson.title)")
            }
        } else {
            // 从收藏中移除
            let removedCount = favoriteLessons.count
            favoriteLessons.removeAll { $0.id == id }
            if favoriteLessons.count < removedCount {
                print("Removed lesson from favorites: \(id)")
            }
        }

        try await saveFavoriteLessons(favoriteLessons)
    }

    public func markFavoriteForSync(id: String, isFavorite: Bool) async throws {
        var pendingSync = getPendingSyncFavorites()
        pendingSync[id] = isFavorite
        savePendingSyncFavorites(pendingSync)
        print("Marked favorite for sync: \(id) -> \(isFavorite)")
    }

    public func clearAllData() async throws {
        UserDefaults.standard.removeObject(forKey: StorageKeys.lessons)
        UserDefaults.standard.removeObject(forKey: StorageKeys.lessonProgress)
        UserDefaults.standard.removeObject(forKey: StorageKeys.favoriteLessons)
        UserDefaults.standard.removeObject(forKey: StorageKeys.pendingSyncFavorites)

        print("Cleared lesson-related data from LessonLocalDataSource's UserDefaults.")
    }

    // MARK: - Private Methods

    private func getCurrentUserId() -> UUID {
        if let userIdString = UserDefaults.standard.string(forKey: "currentUserId"),
           let userId = UUID(uuidString: userIdString) {
            return userId
        }

        let tempUserId = UUID()
        UserDefaults.standard.set(tempUserId.uuidString, forKey: "currentUserId")
        return tempUserId
    }

    private func getPendingSyncFavorites() -> [String: Bool] {
        guard let data = UserDefaults.standard.data(forKey: StorageKeys.pendingSyncFavorites) else {
            return [:]
        }

        do {
            return try JSONDecoder().decode([String: Bool].self, from: data)
        } catch {
            return [:]
        }
    }

    private func savePendingSyncFavorites(_ pendingSync: [String: Bool]) {
        do {
            let data = try JSONEncoder().encode(pendingSync)
            UserDefaults.standard.set(data, forKey: StorageKeys.pendingSyncFavorites)
        } catch {
            print("Failed to save pending sync favorites: \(error)")
        }
    }
}

// MARK: - Errors

public enum LessonLocalDataSourceError: Error, LocalizedError {
    case lessonNotFound
    case encodingError(Error)
    case decodingError(Error)
    case storageError(Error)

    public var errorDescription: String? {
        switch self {
        case .lessonNotFound:
            return "课程未找到"
        case .encodingError(let error):
            return "数据编码错误: \(error.localizedDescription)"
        case .decodingError(let error):
            return "数据解码错误: \(error.localizedDescription)"
        case .storageError(let error):
            return "存储错误: \(error.localizedDescription)"
        }
    }
}
