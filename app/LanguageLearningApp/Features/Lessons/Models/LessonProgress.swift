import Foundation

/// 课程学习进度模型
public struct LessonProgress: Codable, Identifiable {
    /// 进度ID（主键）
    public let id: UUID 
    /// 课程ID
    public let lessonId: String
    /// 用户ID
    public let userId: UUID
    /// 学习进度（0.0 - 1.0）
    public var progress: Double
    /// 是否已完成
    public var completed: Bool
    /// 最后访问时间
    public var lastAccessedAt: Date
    /// 完成时间
    public var completedAt: Date?
    /// 学习时长（秒）
    public var timeSpent: TimeInterval
    /// 当前练习ID
    public var currentExerciseId: String?
    /// 是否需要同步
    public var needsSync: Bool
    
    /// 初始化方法
    public init(
        id: UUID = UUID(),
        lessonId: String,
        userId: UUID,
        progress: Double = 0.0,
        completed: Bool = false,
        lastAccessedAt: Date = Date(),
        completedAt: Date? = nil,
        timeSpent: TimeInterval = 0,
        currentExerciseId: String? = nil,
        needsSync: Bool = false
    ) {
        self.id = id
        self.lessonId = lessonId
        self.userId = userId
        self.progress = progress
        self.completed = completed
        self.lastAccessedAt = lastAccessedAt
        self.completedAt = completedAt
        self.timeSpent = timeSpent
        self.currentExerciseId = currentExerciseId
        self.needsSync = needsSync
    }
}

extension LessonProgress {
    /// 更新进度，用于跟踪练习完成情况
    public mutating func update(exerciseId: String, correct: Bool, timeTaken: TimeInterval) {
        // 示例：假设每个课程有5个练习，每个练习占20%的进度
        let progressPerExercise = 0.2
        
        if correct {
            self.progress += progressPerExercise
            if self.progress >= 1.0 {
                self.progress = 1.0
                self.completed = true
                self.completedAt = Date()
            }
        }
        self.timeSpent += timeTaken
        self.lastAccessedAt = Date()
        self.currentExerciseId = exerciseId // or next exercise id
        self.needsSync = true
    }
} 