import AVFoundation
import Combine
import Foundation
import SwiftUI

/// 个性化练习视图模型，处理练习流程逻辑
class PersonalizedPracticeViewModel: ObservableObject {
    // 使用依赖注入的个性化学习服务
    private let personalizedLearningService: PersonalizedLearningServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // 练习状态
    enum PracticeState {
        case loading
        case intro
        case inProgress
        case submitting
        case completing
        case completed
        case error(String)
    }

    // 发布的属性
    @Published var state: PracticeState = .loading
    @Published var practice: [String: Any]?
    @Published var currentExercise: [String: Any]?
    @Published var currentExerciseIndex: Int = 0
    @Published var totalExercises: Int = 0
    @Published var progress: Double = 0.0
    @Published var elapsedTime: TimeInterval = 0
    @Published var result: [String: Any]?
    @Published var errorMessage: String?

    // 计时器
    private var timer: Timer?
    private var startTime: Date?

    /// 初始化
    /// - Parameters:
    ///   - practiceId: 练习ID（如果已知）
    ///   - personalizedLearningService: 个性化学习服务
    init(practiceId: UUID? = nil, personalizedLearningService: PersonalizedLearningServiceProtocol? = nil) {
        print("🎯 [PersonalizedPracticeViewModel] 初始化开始")

        // 尝试从容器解析 PersonalizedLearningService，如果失败则使用默认值
        if let personalizedLearningService = personalizedLearningService {
            self.personalizedLearningService = personalizedLearningService
        } else {
            do {
                self.personalizedLearningService = try DependencyContainer.shared.tryResolve(PersonalizedLearningServiceProtocol.self)
                print("🎯 [PersonalizedPracticeViewModel] 成功从容器解析 PersonalizedLearningService")
            } catch {
                print("🎯 [PersonalizedPracticeViewModel] 无法从容器解析 PersonalizedLearningService，使用默认实例: \(error)")
                self.personalizedLearningService = PersonalizedLearningService.shared
            }
        }

        if let id = practiceId {
            loadPractice(id: id)
        } else {
            loadTodayPractice()
        }
    }

    /// 加载今日练习
    func loadTodayPractice() {
        print("Loading today's practice...")
        state = .loading

        Task {
            do {
                let statusData = try await personalizedLearningService.getPersonalizedLearningStatus()
                await MainActor.run {
                    print("Received learning status data: \(String(describing: statusData))")
                    print("Parsing learning status data structure...")

                    guard let statusDict = statusData as? [String: Any] else {
                        print("Failed to cast statusData to dictionary")
                        self.errorMessage = "数据格式错误"
                        self.state = .error("数据格式错误")
                        return
                    }

                    if let success = statusDict["success"] as? Int, success == 1,
                       let dataDict = statusDict["data"] as? [String: Any] {
                        print("Found success flag and data field in response")
                        if let hasActiveLearningPath = dataDict["hasActiveLearningPath"] as? Int,
                           hasActiveLearningPath == 1,
                           let activeLearningPathIDString = dataDict["activeLearningPathID"] as? String,
                           let pathId = UUID(uuidString: activeLearningPathIDString) {
                            print("Found active learning path with ID: \(activeLearningPathIDString)")
                            var pathInfo: [String: Any] = [
                                "id": activeLearningPathIDString,
                                "learningPathID": activeLearningPathIDString,
                            ]
                            if let progress = dataDict["activeLearningPathProgress"] as? Int {
                                pathInfo["progress"] = progress
                            }
                            self.practice = pathInfo
                            UserDefaults.standard.set(activeLearningPathIDString, forKey: "currentLearningPathID")
                            print("保存当前学习路径ID到UserDefaults: \(activeLearningPathIDString)")
                            self.getNextExerciseFromAPI(pathId: pathId)
                            return
                        }
                        if let hasActivePath = dataDict["hasActivePath"] as? Bool,
                           hasActivePath,
                           let activePathDict = dataDict["activePath"] as? [String: Any],
                           let pathIdString = activePathDict["id"] as? String,
                           let pathId = UUID(uuidString: pathIdString) {
                            print("Found active path with ID: \(pathIdString) (old API structure)")
                            self.practice = activePathDict
                            UserDefaults.standard.set(pathIdString, forKey: "currentLearningPathID")
                            print("保存当前学习路径ID到UserDefaults: \(pathIdString)")
                            self.getNextExerciseFromAPI(pathId: pathId)
                            return
                        }
                    }

                    if let statusDict = statusData as? [String: Any],
                       let hasActivePath = statusDict["hasActivePath"] as? Bool,
                       hasActivePath,
                       let activePathDict = statusDict["activePath"] as? [String: Any],
                       let pathIdString = activePathDict["id"] as? String,
                       let pathId = UUID(uuidString: pathIdString) {
                        print("Found active path with ID: \(pathIdString) (direct structure)")
                        self.practice = activePathDict
                        UserDefaults.standard.set(pathIdString, forKey: "currentLearningPathID")
                        print("保存当前学习路径ID到UserDefaults: \(pathIdString)")
                        self.getNextExerciseFromAPI(pathId: pathId)
                    } else {
                        print("No active learning path available")
                        self.errorMessage = "没有活跃的学习路径，请先完成评估"
                        self.state = .error("没有活跃的学习路径，请先完成评估")
                    }
                }
            } catch {
                await MainActor.run {
                    print("Failed to load learning status: \(error.localizedDescription)")
                    self.handleError(error)
                }
            }
        }
    }

    /// 加载练习详情
    /// - Parameter id: 学习路径ID
    func loadPractice(id: UUID) {
        print("Loading practice with learning path ID: \(id)")
        state = .loading
        getNextExerciseFromAPI(pathId: id)
    }

    /// 处理错误
    /// - Parameter error: 错误
    private func handleError(_ error: Error) {
        var errorMsg = "发生错误"
        var shouldShowError = true

        if let plError = error as? PersonalizedLearningServiceError {
            switch plError {
            case .networkError(let err):
                errorMsg = "网络错误: \(err.localizedDescription)"
                // 对于网络错误，如果有离线数据可用，可以不显示错误
                if let appError = err as? AppError, case .dataNotFound = appError {
                    print("数据未找到错误，使用离线数据")
                    shouldShowError = false
                }
            case .decodingError(let err):
                errorMsg = "数据解析错误: \(err.localizedDescription)"
            case .invalidResponse:
                errorMsg = "无效的服务器响应"
            case .noData:
                errorMsg = "服务器未返回数据"
                // 对于noData错误，如果有离线数据可用，可以不显示错误
                shouldShowError = false
            case .serverError(let message):
                if message.contains("dataNotFound") {
                    print("服务器返回dataNotFound错误，使用离线数据")
                    shouldShowError = false
                } else {
                    errorMsg = "服务器错误: \(message)"
                }
            case .notAuthenticated:
                errorMsg = "用户未登录，请先登录"
            case .unknown:
                errorMsg = "未知错误"
            }
        } else if let appError = error as? AppError, case .dataNotFound = appError {
            print("数据未找到错误，使用离线数据")
            shouldShowError = false
        } else {
            errorMsg = "发生错误: \(error.localizedDescription)"
        }

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if shouldShowError {
                self.errorMessage = errorMsg
                self.state = .error(errorMsg)
            } else {
                // 对于不需要显示错误的情况，保持当前状态或切换到适当的状态
                print("使用离线数据，不显示错误")

                // 如果当前状态是错误状态，切换到加载状态
                if case .error = self.state {
                    self.state = .loading
                }
            }
        }
    }

    /// 开始练习
    func startPractice() {
        print("Starting practice with state: \(state)")

        // 如果已经在进行中，不要重复开始
        if case .inProgress = state {
            print("Practice already in progress, not restarting")
            return
        }

        // 如果练习数据还没有加载，先加载数据
        if practice == nil {
            print("Practice data is nil, loading today's practice...")
            loadTodayPractice()
            return
        }

        guard let practice = practice else {
            state = .error("无法获取练习数据")
            errorMessage = "无法获取练习数据"
            return
        }

        print("Practice data: \(practice)")

        guard let exercises = practice["exercises"] as? [[String: Any]],
            !exercises.isEmpty
        else {
            state = .error("练习不包含任何题目")
            errorMessage = "练习不包含任何题目"
            print("No exercises found in practice data")
            return
        }

        print("Found \(exercises.count) exercises")

        // 设置总题目数
        totalExercises = exercises.count

        // 设置当前题目
        currentExercise = exercises.first
        currentExerciseIndex = 0
        progress = 0.0  // 确保进度从0开始
        startTime = Date()
        startTimer()
        state = .inProgress

        print(
            "Practice started, total exercises: \(totalExercises), current exercise: \(String(describing: currentExercise))"
        )

        // 打印当前题目的类型，帮助调试
        if let typeString = currentExercise?["type"] as? String {
            print("当前题目类型: '\(typeString)'")
        } else {
            print("警告: 当前题目没有类型字段")
        }
    }

    /// 提交答案
    /// - Parameters:
    ///   - answer: 用户答案
    ///   - exerciseId: 练习ID（可选，如果不提供则从currentExercise中获取）
    func submitAnswer(answer: String, exerciseId: String? = nil) {
        print("===== 开始提交答案 =====")
        print("当前状态: \(state)")
        print("提交的答案: \(answer)")
        print("提供的exerciseId: \(exerciseId ?? "无")")

        // 检查当前状态并处理
        switch state {
        case .error:
            print("检测到错误状态，重置为加载状态")
            state = .loading
            errorMessage = nil

            // 如果当前练习为nil，尝试加载练习
            if currentExercise == nil && exerciseId != nil {
                print("当前练习为nil，尝试使用exerciseId加载练习")
                if let id = UUID(uuidString: exerciseId!) {
                    // 创建一个临时的练习数据
                    // 使用提供的exerciseId作为练习ID
                    let tempExercise: [String: Any] = [
                        "id": exerciseId!,
                        "type": "grammar", // 假设是grammar类型，因为这是我们正在修复的问题
                        "data": ["lessonID": exerciseId!]
                    ]
                    currentExercise = tempExercise

                    // 尝试从UserDefaults获取当前学习路径ID
                    let currentPathId = UserDefaults.standard.string(forKey: "currentLearningPathID") ?? UUID().uuidString
                    print("使用学习路径ID: \(currentPathId)，与练习ID不同")

                    // 创建一个临时的practice数据，使用当前学习路径ID
                    practice = ["learningPathID": currentPathId]
                }
            }

        case .submitting:
            print("当前正在提交答案，忽略重复提交")
            return

        case .completing:
            print("当前正在完成练习，忽略提交")
            return

        default:
            break
        }

        // 打印当前练习的详细信息
        if let currentExercise = currentExercise {
            print("当前练习数据: \(currentExercise)")

            // 尝试提取练习类型
            if let type = currentExercise["type"] as? String {
                print("练习类型(直接): \(type)")
            }

            // 检查嵌套结构
            if let data = currentExercise["data"] as? [String: Any] {
                print("练习data字段: \(data)")

                if let exercises = data["exercises"] as? [[String: Any]], !exercises.isEmpty {
                    print("data.exercises数组: \(exercises)")

                    if let firstExercise = exercises.first {
                        print("第一个练习: \(firstExercise)")

                        if let type = firstExercise["type"] as? String {
                            print("练习类型(嵌套): \(type)")
                        }

                        if let exercise = firstExercise["exercise"] as? [String: Any] {
                            print("嵌套练习: \(exercise)")

                            if let type = exercise["type"] as? String {
                                print("练习类型(深度嵌套): \(type)")
                            }
                        }
                    }
                }

                if let lessonID = data["lessonID"] as? String {
                    print("data.lessonID: \(lessonID)")
                }
            }

            // 检查category字段
            if let category = currentExercise["category"] as? String {
                print("练习category: \(category)")
            }

            // 检查口语练习特定字段
            if let prompt = currentExercise["prompt"] as? String {
                print("练习prompt: \(prompt)")
            }

            if let targetPhrase = currentExercise["targetPhrase"] as? String {
                print("练习targetPhrase: \(targetPhrase)")
            }
        } else {
            print("警告: 当前练习为nil")
        }

        // 检查practice数据
        if let practiceData = practice {
            print("练习会话数据: \(practiceData)")

            // 使用practiceData作为字典
            let practiceDict = practiceData

            if let learningPathID = practiceDict["learningPathID"] as? String {
                print("learningPathID: \(learningPathID)")
            }

            if let exercises = practiceDict["exercises"] as? [[String: Any]] {
                print("练习会话包含 \(exercises.count) 个练习")
            }
        } else {
            print("警告: 练习会话数据为nil，但继续提交答案")

            // 如果practice为nil但currentExercise不为nil，我们可以继续提交答案
            if currentExercise == nil {
                print("错误: 当前练习也为nil，无法提交答案")
                errorMessage = "无法提交答案：练习数据无效"
                state = .error("无法提交答案：练习数据无效")
                return
            }

            // 从currentExercise中尝试构建一个临时的practice数据
            if let data = currentExercise?["data"] as? [String: Any],
               let lessonID = data["lessonID"] as? String {
                print("从currentExercise.data.lessonID创建临时practice数据: \(lessonID)")
                // 尝试从UserDefaults获取当前学习路径ID
                let currentPathId = UserDefaults.standard.string(forKey: "currentLearningPathID") ?? UUID().uuidString
                practice = ["learningPathID": currentPathId]
                print("使用学习路径ID: \(currentPathId)，与练习ID不同")
            }
        }

        // 验证答案不为空
        if answer.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            print("错误: 提交的答案为空")
            errorMessage = "无法提交答案：答案不能为空"
            state = .error("无法提交答案：答案不能为空")
            return
        }

        // 获取学习路径ID
        let pathIdString: String
        if let practiceDict = practice as [String: Any]?,
            let learningPathID = practiceDict["learningPathID"] as? String
        {
            print("使用practice.learningPathID作为pathId: \(learningPathID)")
            pathIdString = learningPathID
        } else if let data = currentExercise?["data"] as? [String: Any],
            let lessonID = data["lessonID"] as? String
        {
            // 如果没有learningPathID，尝试使用lessonID
            print("使用currentExercise.data.lessonID作为pathId: \(lessonID)")
            pathIdString = lessonID
        } else {
            print("错误: 无法获取有效的学习路径ID")
            errorMessage = "无法提交答案：学习路径ID无效"
            state = .error("无法提交答案：学习路径ID无效")
            return
        }

        guard let pathId = UUID(uuidString: pathIdString) else {
            print("错误: 学习路径ID不是有效的UUID格式: \(pathIdString)")
            errorMessage = "无法提交答案：学习路径ID格式无效"
            state = .error("无法提交答案：学习路径ID格式无效")
            return
        }

        // 获取练习ID
        let exerciseIdString: String
        if let providedExerciseId = exerciseId {
            // 如果提供了exerciseId，直接使用
            print("使用提供的exerciseId: \(providedExerciseId)")
            exerciseIdString = providedExerciseId
        } else if let currentExercise = currentExercise,
            let currentExerciseId = currentExercise["id"] as? String
        {
            // 否则从currentExercise中获取
            print("使用currentExercise.id作为exerciseId: \(currentExerciseId)")
            exerciseIdString = currentExerciseId
        } else if let data = currentExercise?["data"] as? [String: Any],
            let lessonID = data["lessonID"] as? String
        {
            // 如果currentExercise中没有id，尝试使用data.lessonID
            print("使用currentExercise.data.lessonID作为exerciseId: \(lessonID)")
            exerciseIdString = lessonID
        } else {
            print("错误: 无法获取有效的练习ID")
            errorMessage = "无法提交答案：练习ID无效"
            state = .error("无法提交答案：练习ID无效")
            return
        }

        guard let exerciseId = UUID(uuidString: exerciseIdString) else {
            print("错误: 练习ID不是有效的UUID格式: \(exerciseIdString)")
            errorMessage = "无法提交答案：练习ID格式无效"
            state = .error("无法提交答案：练习ID格式无效")
            return
        }

        // 确定练习类型
        let exerciseType = determineExerciseType()
        print("确定的练习类型: \(exerciseType.rawValue)")

        // 检查口语练习的答案格式
        if exerciseType == .speaking {
            print("检测到口语练习，验证录音答案")

            // 检查答案是否是有效的文件URL
            if answer.hasPrefix("file://") {
                print("答案是文件URL: \(answer)")

                // 验证文件是否存在
                if let fileURL = URL(string: answer) {
                    if FileManager.default.fileExists(atPath: fileURL.path) {
                        print("录音文件存在: \(fileURL.path)")

                        // 获取文件大小
                        do {
                            let attributes = try FileManager.default.attributesOfItem(
                                atPath: fileURL.path)
                            if let fileSize = attributes[.size] as? NSNumber {
                                print("录音文件大小: \(fileSize.intValue) 字节")

                                if fileSize.intValue == 0 {
                                    print("警告: 录音文件大小为0字节")
                                }
                            }
                        } catch {
                            print("无法获取文件属性: \(error.localizedDescription)")
                        }
                    } else {
                        print("警告: 录音文件不存在: \(fileURL.path)")
                    }
                } else {
                    print("警告: 无法从答案创建URL: \(answer)")
                }
            } else {
                print("警告: 口语练习答案不是文件URL: \(answer)")
            }
        }

        state = .submitting
        print("状态已更新为: .submitting")
        print("准备调用API: pathId=\(pathId), exerciseId=\(exerciseId), answer=\(answer)")

        Task {
            do {
                let submissionResult = try await personalizedLearningService.submitPracticeAnswer(practiceId: pathId, exerciseId: exerciseId, answer: answer)
                await MainActor.run {
                    print("Received submission result: \(submissionResult)")
                    if let isCorrect = submissionResult["isCorrect"] as? Bool {
                        self.result = submissionResult
                        if isCorrect {
                            // Correct answer, proceed to complete the exercise or show feedback
                            // Depending on the desired flow, you might call completeExercise()
                            // or show a success message and then load the next exercise.
                            print("答案正确")
                            // For now, let's assume we complete the exercise directly
                            self.completeCurrentExercise(lessonId: exerciseId.uuidString)
                        } else {
                            self.errorMessage = submissionResult["feedback"] as? String ?? "答案错误"
                            self.state = .error(self.errorMessage ?? "答案错误")
                        }
                    } else {
                        self.errorMessage = "提交结果格式错误"
                        self.state = .error("提交结果格式错误")
                    }
                }
            } catch {
                await MainActor.run {
                    print("Failed to submit answer: \(error.localizedDescription)")
                    self.handleError(error)
                }
            }
        }
    }

    /// 确定练习类型
    /// - Returns: 练习类型
    private func determineExerciseType() -> PracticeExerciseType {
        print("开始确定练习类型")
        guard let currentExercise = currentExercise else {
            print("当前练习为nil，返回默认类型: multipleChoice")
            return .multipleChoice
        }

        // 首先检查直接的type字段
        if let typeString = currentExercise["type"] as? String {
            print("找到直接的type字段: \(typeString)")
            if let type = PracticeExerciseType(rawValue: typeString.lowercased()) {
                print("成功转换为枚举类型: \(type)")
                return type
            } else {
                print("无法将'\(typeString)'转换为有效的练习类型")
            }
        }

        // 检查嵌套结构中的type字段
        if let data = currentExercise["data"] as? [String: Any] {
            // 检查data.exercises[0].type
            if let exercises = data["exercises"] as? [[String: Any]], !exercises.isEmpty,
                let firstExercise = exercises.first
            {

                if let typeString = firstExercise["type"] as? String {
                    print("找到data.exercises[0].type字段: \(typeString)")
                    if let type = PracticeExerciseType(rawValue: typeString.lowercased()) {
                        print("成功转换为枚举类型: \(type)")
                        return type
                    }
                }

                // 检查data.exercises[0].exercise.type
                if let exercise = firstExercise["exercise"] as? [String: Any],
                    let typeString = exercise["type"] as? String
                {
                    print("找到data.exercises[0].exercise.type字段: \(typeString)")
                    if let type = PracticeExerciseType(rawValue: typeString.lowercased()) {
                        print("成功转换为枚举类型: \(type)")
                        return type
                    }
                }
            }

            // 检查data.exercise.type
            if let exercise = data["exercise"] as? [String: Any],
                let typeString = exercise["type"] as? String
            {
                print("找到data.exercise.type字段: \(typeString)")
                if let type = PracticeExerciseType(rawValue: typeString.lowercased()) {
                    print("成功转换为枚举类型: \(type)")
                    return type
                }
            }
        }

        // 根据category字段判断
        if let category = currentExercise["category"] as? String {
            print("根据category字段判断类型: \(category)")
            switch category.lowercased() {
            case "speaking", "self introduction", "self-introduction", "pronunciation":
                print("根据category判断为speaking类型")
                return .speaking
            case "listening", "conversation":
                print("根据category判断为listening类型")
                return .listening
            case "writing":
                print("根据category判断为writing类型")
                return .writing
            case "reading":
                print("根据category判断为reading类型")
                return .reading
            default:
                print("category不匹配任何已知类型，默认为multipleChoice")
                return .multipleChoice
            }
        }

        // 根据特定字段判断
        if currentExercise["prompt"] != nil && currentExercise["targetPhrase"] != nil {
            print("根据prompt和targetPhrase字段判断为speaking类型")
            return .speaking
        }

        if currentExercise["audioURL"] != nil && currentExercise["transcript"] != nil {
            print("根据audioURL和transcript字段判断为listening类型")
            return .listening
        }

        // 检查嵌套结构中的特定字段
        if let data = currentExercise["data"] as? [String: Any] {
            if let exercises = data["exercises"] as? [[String: Any]], !exercises.isEmpty,
                let firstExercise = exercises.first
            {

                if let exercise = firstExercise["exercise"] as? [String: Any] {
                    if exercise["prompt"] != nil && exercise["targetPhrase"] != nil {
                        print("根据嵌套的prompt和targetPhrase字段判断为speaking类型")
                        return .speaking
                    }

                    if exercise["audioURL"] != nil && exercise["transcript"] != nil {
                        print("根据嵌套的audioURL和transcript字段判断为listening类型")
                        return .listening
                    }

                    if let category = exercise["category"] as? String {
                        print("根据嵌套的category字段判断类型: \(category)")
                        switch category.lowercased() {
                        case "speaking", "self introduction", "self-introduction", "pronunciation":
                            print("根据嵌套的category判断为speaking类型")
                            return .speaking
                        case "listening", "conversation":
                            print("根据嵌套的category判断为listening类型")
                            return .listening
                        case "writing":
                            print("根据嵌套的category判断为writing类型")
                            return .writing
                        case "reading":
                            print("根据嵌套的category判断为reading类型")
                            return .reading
                        default:
                            print("嵌套的category不匹配任何已知类型，默认为multipleChoice")
                            return .multipleChoice
                        }
                    }
                }
            }
        }

        print("无法确定练习类型，默认为multipleChoice")
        return .multipleChoice
    }

    /// 完成当前练习并获取下一个练习
    /// - Parameter lessonId: 当前练习ID
    func completeCurrentExercise(lessonId: String) {
        guard let practice = practice else {
            errorMessage = "无法完成练习：练习数据无效"
            state = .error("无法完成练习：练习数据无效")
            return
        }

        // 获取学习路径ID
        let pathIdString: String
        if let learningPathID = practice["learningPathID"] as? String {
            pathIdString = learningPathID
        } else if let data = currentExercise?["data"] as? [String: Any],
            let lessonID = data["lessonID"] as? String
        {
            // 如果没有learningPathID，尝试使用lessonID
            pathIdString = lessonID
        } else {
            errorMessage = "无法完成练习：学习路径ID无效"
            state = .error("无法完成练习：学习路径ID无效")
            return
        }

        guard let pathId = UUID(uuidString: pathIdString) else {
            errorMessage = "无法完成练习：学习路径ID格式无效"
            state = .error("无法完成练习：学习路径ID格式无效")
            return
        }

        state = .completing

        print("完成练习: pathId=\(pathId), lessonId=\(lessonId)")

        // 调用API完成当前练习
        Task {
            do {
                let success = try await personalizedLearningService.completeExercise(pathId: pathId, lessonId: lessonId)
                await MainActor.run {
                    if success {
                        // 更新进度，确保在有效范围内（0.0-1.0）
                        let newProgress = self.progress + (1.0 / Double(max(1, self.totalExercises)))
                        self.progress = min(1.0, max(0.0, newProgress))

                        // 获取下一个练习
                        self.getNextExerciseFromAPI(pathId: pathId)
                    } else {
                        self.errorMessage = "无法完成练习"
                        self.state = .error("无法完成练习")
                    }
                }
            } catch {
                await MainActor.run {
                    print("Failed to complete exercise: \(error.localizedDescription)")
                    self.handleError(error)
                }
            }
        }
    }

    /// 从API获取下一个练习
    /// - Parameter pathId: 学习路径ID
    private func getNextExerciseFromAPI(pathId: UUID) {
        print("Fetching next exercise from API with path ID: \(pathId)")
        state = .loading

        Task {
            do {
                let exerciseData = try await personalizedLearningService.getNextExercise(pathId: pathId)
                await MainActor.run {
                    self.processExerciseData(exerciseData)
                }
            } catch {
                await MainActor.run {
                    print("Failed to get next exercise: \(error.localizedDescription)")
                    self.handleError(error)
                }
            }
        }
    }

    /// 处理从API获取的练习数据
    private func processExerciseData(_ exerciseData: Any) {
        print("题目数据: \(exerciseData)")

        // 检查是否有success和message字段，表示所有练习已完成
        if let responseDict = exerciseData as? [String: Any],
           let success = responseDict["success"] as? Int, success == 1,
           let message = responseDict["message"] as? String,
           message.contains("All exercises completed") || message.contains("completed") {

            print("所有练习已完成，显示结果")

            // 如果有data字段，检查completed标志
            if let data = responseDict["data"] as? [String: Any],
               let completed = data["completed"] as? Int, completed == 1 {
                print("确认data.completed=1，所有练习已完成")
                self.showResults()
                return
            }

            // 即使没有data.completed字段，也根据message显示结果
            self.showResults()
            return
        }

        if let exerciseDict = exerciseData as? [String: Any] {
            // 检查是否所有练习都已完成
            if let completed = exerciseDict["completed"] as? Bool, completed {
                // 所有练习已完成，显示结果
                print("检测到completed=true，所有练习已完成")
                self.showResults()
            } else if let data = exerciseDict["data"] as? [String: Any],
                    let completed = data["completed"] as? Int, completed == 1 {
                // 检查嵌套的data.completed字段
                print("检测到data.completed=1，所有练习已完成")
                self.showResults()
            } else {
                // 设置新的当前练习
                self.currentExercise = exerciseDict
                self.currentExerciseIndex += 1

                // 发送通知，表示新练习已加载，可以重置视图状态
                NotificationCenter.default.post(
                    name: NSNotification.Name("NewExerciseLoaded"), object: nil)

                // 更新状态为进行中
                self.state = .inProgress

                // 打印当前题目的类型，帮助调试
                if let typeString = exerciseDict["type"] as? String {
                    print("获取到新题目，类型: '\(typeString)'")
                } else {
                    print("警告: 获取到的新题目没有类型字段")
                    print("题目数据: \(exerciseDict)")
                }
            }
        } else {
            // 如果返回的不是字典，可能是所有练习已完成
            print("返回的不是字典，假设所有练习已完成")
            self.showResults()
        }
    }

    /// 显示练习结果
    private func showResults() {
        // 计算练习时间（秒）
        let duration = Int(elapsedTime)

        // 创建结果对象
        let result: [String: Any] = [
            "id": UUID().uuidString,
            "practiceType": practice?["type"] as? String ?? "mixed",
            "score": calculateScore(),
            "totalPoints": 100,
            "duration": duration,
            "completedAt": Date(),
            "feedback": self.generateFeedback(score: calculateScore()),
            "recommendations": self.generateRecommendations(practice: practice ?? [:]),
        ]

        self.result = result
        self.stopTimer()
        self.state = .completed
    }

    /// 计算得分
    /// - Returns: 得分（0-100）
    private func calculateScore() -> Int {
        guard let practice = practice,
            let exercises = practice["exercises"] as? [[String: Any]]
        else {
            return 0
        }

        let totalCount = exercises.count
        let correctCount = exercises.filter { $0["isCorrect"] as? Bool == true }.count

        // 计算得分（百分比）
        return totalCount > 0 ? Int(Double(correctCount) / Double(totalCount) * 100.0) : 0
    }

    // 已经不再需要这个方法，因为我们现在使用 completeCurrentExercise 和 getNextExerciseFromAPI 方法
    // 保留这个方法的签名，但将其标记为已弃用，以防有其他地方调用它
    /// 完成练习（已弃用，请使用 completeCurrentExercise 方法）
    @available(*, deprecated, message: "请使用 completeCurrentExercise 方法")
    func completePractice() {
        print("警告：调用了已弃用的 completePractice 方法，请使用 completeCurrentExercise 方法")

        // 如果当前有练习，尝试完成它
        if let currentExercise = currentExercise,
            let currentExerciseId = currentExercise["id"] as? String
        {
            completeCurrentExercise(lessonId: currentExerciseId)
        } else {
            // 如果没有当前练习，直接显示结果
            showResults()
        }
    }

    /// 保存练习会话
    /// - Parameters:
    ///   - score: 得分
    ///   - duration: 持续时间（秒）
    private func savePracticeSession() {
        guard let type = currentExercise?["type"] as? String,
              let score = result?["score"] as? Int else {
            print("无法保存练习会话：缺少类型或分数")
            // Optionally, inform the user or log this error more formally
            return
        }

        let duration = Int(elapsedTime)

        print("Saving practice session - Type: \(type), Duration: \(duration), Score: \(score)")

        Task {
            do {
                let success = try await personalizedLearningService.savePracticeSession(type: type, duration: duration, score: score)
                await MainActor.run {
                    if success {
                        print("练习会话已成功保存")
                        // Optionally, update UI or perform other actions upon successful save
                    } else {
                        print("保存练习会话失败")
                        // Optionally, show an error message to the user
                        self.errorMessage = "无法保存练习会话"
                        // Consider if state should change, e.g., self.state = .error("无法保存练习会话")
                    }
                }
            } catch {
                await MainActor.run {
                    print("保存练习会话时出错: \(error.localizedDescription)")
                    // More specific error handling might be needed here.
                    // For example, if it is a network error, you might want to retry or offer offline saving.
                    self.handleError(error)
                }
            }
        }
    }

    /// 生成反馈
    /// - Parameter score: 得分
    /// - Returns: 反馈文本
    private func generateFeedback(score: Int) -> String {
        if score >= 90 {
            return "太棒了！你的表现非常出色，继续保持！"
        } else if score >= 75 {
            return "做得很好！你的表现良好，还有一些提升空间。"
        } else if score >= 60 {
            return "不错的尝试！多加练习，你会做得更好。"
        } else {
            return "这次练习有些困难，但不要气馁，继续努力！"
        }
    }

    /// 生成建议
    /// - Parameter practice: 练习数据
    /// - Returns: 建议列表
    private func generateRecommendations(practice: [String: Any]) -> [String] {
        var recommendations: [String] = []

        // 根据练习类型生成建议
        if let type = practice["type"] as? String {
            switch type {
            case "vocabulary":
                recommendations.append("使用词汇卡片加强记忆")
                recommendations.append("尝试在日常对话中使用这些词汇")
            case "grammar":
                recommendations.append("复习相关语法规则")
                recommendations.append("做更多的句型转换练习")
            case "listening":
                recommendations.append("多听相关主题的音频")
                recommendations.append("尝试不看字幕观看视频")
            case "speaking":
                recommendations.append("多进行口语练习")
                recommendations.append("找语伴进行对话练习")
            case "mixed":
                recommendations.append("均衡练习各种类型的题目")
                recommendations.append("重点关注你的弱项")
            default:
                recommendations.append("继续坚持每日练习")
            }
        }

        // 添加通用建议
        recommendations.append("每天保持学习习惯")

        return recommendations
    }

    /// 开始计时器
    private func startTimer() {
        // 每秒更新已用时间
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateElapsedTime()
        }
    }

    /// 更新已用时间
    private func updateElapsedTime() {
        guard let startTime = startTime else { return }
        elapsedTime = Date().timeIntervalSince(startTime)
    }

    /// 停止计时器
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    /// 获取已用时间的格式化字符串
    /// - Returns: 格式化的时间字符串（mm:ss）
    func formattedElapsedTime() -> String {
        let minutes = Int(elapsedTime) / 60
        let seconds = Int(elapsedTime) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    deinit {
        stopTimer()
    }
}