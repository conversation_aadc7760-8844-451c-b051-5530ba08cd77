import Foundation
import Combine
import SwiftUI

/// 练习项目模型
struct PracticeItem: Identifiable, Sendable {
    let id: UUID
    let title: String
    let type: String
    let iconName: String
    let durationMinutes: Int
    let difficulty: String
    let color: SendableColor

    init(id: UUID = UUID(), title: String, type: String, iconName: String, durationMinutes: Int, difficulty: String, color: SendableColor) {
        self.id = id
        self.title = title
        self.type = type
        self.iconName = iconName
        self.durationMinutes = durationMinutes
        self.difficulty = difficulty
        self.color = color
    }
}

/// 最近学习项目模型
struct RecentLearningItem: Identifiable, Sendable {
    let id: UUID
    let title: String
    let subtitle: String
    let iconName: String
    let timeAgo: String
    let color: SendableColor

    init(id: UUID = UUID(), title: String, subtitle: String, iconName: String, timeAgo: String, color: SendableColor) {
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self.timeAgo = timeAgo
        self.color = color
    }
}

/// 每日练习仪表盘视图模型
@MainActor
class DailyPracticeDashboardViewModel: ObservableObject {
    // 服务
    private let practiceManager: PracticeManager
    private let evaluationManager: EvaluationManager
    private let userManager: UserManager
    private let learningPathService: PersonalizedLearningServiceProtocol
    private let statsService: DailyPracticeStatsServiceProtocol

    // 用户信息
    @Published var userName: String = "学习者"
    @Published var welcomeMessage: String = "今天是学习中文的好日子！"

    // 今日目标
    @Published var minutesLearned: Int = 0
    @Published var dailyGoalMinutes: Int = 30
    @Published var practicesCompleted: Int = 0
    @Published var dailyGoalPractices: Int = 5

    // 学习路径
    @Published var currentLevel: String = "N5"
    @Published var targetLevel: String = "中级 (B1)"
    @Published var learningPathProgress: CGFloat = 0.0

    // 推荐练习
    @Published var recommendedPractices: [PracticeItem] = []

    // 最近学习
    @Published var recentLearningItems: [RecentLearningItem] = []

    // 评估
    @Published var shouldShowEvaluationPrompt: Bool = false
    @Published var currentEvaluationId: UUID?
    @Published var navigateToEvaluation: Bool = false

    @Published private(set) var hasEvaluations: Bool = false
    @Published private(set) var currentEvaluation: Evaluation?
    @Published private(set) var error: Error?

    // 取消令牌
    private var cancellables = Set<AnyCancellable>()

    /// 初始化方法
    /// - Parameters:
    ///   - practiceManager: 练习管理器
    ///   - evaluationManager: 评估管理器
    ///   - userManager: 用户管理器
    ///   - learningPathService: 学习路径服务
    ///   - statsService: 统计服务
    init(
        practiceManager: PracticeManager = DependencyContainer.shared.resolve(PracticeManager.self),
        evaluationManager: EvaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self),
        userManager: UserManager = DependencyContainer.shared.resolve(UserManager.self),
        learningPathService: PersonalizedLearningServiceProtocol = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self),
        statsService: DailyPracticeStatsServiceProtocol = DependencyContainer.shared.resolve(DailyPracticeStatsServiceProtocol.self)
    ) {
        self.practiceManager = practiceManager
        self.evaluationManager = evaluationManager
        self.userManager = userManager
        self.learningPathService = learningPathService
        self.statsService = statsService
        setupSubscriptions()
    }

    /// 设置订阅
    private func setupSubscriptions() {
        // 监听可用评估
        evaluationManager.$availableEvaluations
            .receive(on: DispatchQueue.main)
            .sink { [weak self] evaluations in
                self?.hasEvaluations = !evaluations.isEmpty

                // 检查是否需要显示评估提示
                if evaluations.isEmpty {
                    self?.shouldShowEvaluationPrompt = true
                } else if let latestEvaluation = evaluations.first {
                    // 检查最近一次评估是否是30天以前
                    let daysSinceLastEvaluation = Calendar.current.dateComponents([.day], from: latestEvaluation.createdAt, to: Date()).day ?? 0
                    self?.shouldShowEvaluationPrompt = daysSinceLastEvaluation >= 30
                }
            }
            .store(in: &cancellables)

        // 监听当前评估
        evaluationManager.$currentEvaluation
            .receive(on: DispatchQueue.main)
            .sink { [weak self] evaluation in
                self?.currentEvaluation = evaluation
                if let evaluation = evaluation {
                    self?.currentEvaluationId = evaluation.id
                    self?.navigateToEvaluation = true
                }
            }
            .store(in: &cancellables)

        // 监听错误
        evaluationManager.$error
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.error = error
            }
            .store(in: &cancellables)
    }

    // 计算属性
    var learningTimeProgress: CGFloat {
        return min(CGFloat(minutesLearned) / CGFloat(dailyGoalMinutes), 1.0)
    }

    var practicesProgress: CGFloat {
        return min(CGFloat(practicesCompleted) / CGFloat(dailyGoalPractices), 1.0)
    }

    /// 加载仪表盘数据
    func loadDashboardData() {
        // 加载用户信息
        loadUserInfo()

        // 加载今日目标
        loadDailyGoals()

        // 加载学习路径
        loadLearningPath()

        // 加载推荐练习
        loadRecommendedPractices()

        // 加载最近学习
        loadRecentLearning()

        // 检查是否需要评估
        checkEvaluationStatus()
    }

    /// 加载用户信息
    private func loadUserInfo() {
        if let user = userManager.currentUser {
            self.userName = user.username

            // 根据时间生成欢迎消息
            let hour = Calendar.current.component(.hour, from: Date())
            if hour < 12 {
                self.welcomeMessage = "早上好！开始新的一天的学习吧。"
            } else if hour < 18 {
                self.welcomeMessage = "下午好！继续努力学习吧。"
            } else {
                self.welcomeMessage = "晚上好！再复习一下今天学到的内容吧。"
            }
        }
    }

    /// 加载今日目标
    private func loadDailyGoals() {
        // 首先尝试从 API 获取统计数据
        statsService.getTodayStats()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure = completion {
                        // 如果 API 失败，使用本地数据计算
                        self?.loadDailyGoalsFromLocalData()
                    }
                },
                receiveValue: { [weak self] stats in
                    self?.updateDailyGoalsFromStats(stats)
                }
            )
            .store(in: &cancellables)
    }

    /// 从统计数据更新今日目标
    private func updateDailyGoalsFromStats(_ stats: DailyPracticeStats) {
        self.minutesLearned = stats.minutesLearned
        self.practicesCompleted = stats.practicesCompleted
        self.dailyGoalMinutes = userManager.currentUser?.settings?.dailyGoalMinutes ?? 30
        self.dailyGoalPractices = userManager.currentUser?.settings?.dailyGoalPractices ?? 5
    }

    /// 从本地数据加载今日目标
    private func loadDailyGoalsFromLocalData() {
        // 从 PracticeManager 获取本地练习数据
        Task {
            await practiceManager.loadPracticeHistoryFromAPI { [weak self] in
                DispatchQueue.main.async {
                    self?.updateDailyGoalsFromPracticeHistory()
                }
            }
        }
    }

    /// 从练习历史更新今日目标
    private func updateDailyGoalsFromPracticeHistory() {
        // 使用统计服务从本地数据计算统计
        let stats = statsService.calculateStatsFromLocalData(practiceManager.recentPractices, for: Date())
        updateDailyGoalsFromStats(stats)
    }

    /// 加载学习路径
    private func loadLearningPath() {
        Task {
            do {
                let learningPath = try await learningPathService.getCurrentLearningPath()
                await MainActor.run {
                    self.currentLevel = learningPath.currentLevel
                    self.targetLevel = learningPath.targetLevel
                    // self.learningPathProgress = CGFloat(learningPath.progress) // Commented out due to missing property
                }
            } catch {
                await MainActor.run {
                    // Handle error appropriately
                    print("Failed to load learning path: \(error.localizedDescription)")
                    // Update UI to reflect error state, e.g., show an error message
                    self.error = error // Assuming you have an @Published var error: Error?
                }
            }
        }
    }

    /// 加载推荐练习
    private func loadRecommendedPractices() {
        Task {
            do {
                let learningPath = try await learningPathService.getCurrentLearningPath()
                await MainActor.run {
                    // 基于学习路径生成推荐练习
                    self.generateRecommendedPracticesFromPath(learningPath)
                }
            } catch {
                await MainActor.run {
                    // 如果获取学习路径失败，使用基于历史的推荐
                    print("Failed to load learning path for recommendations, falling back to history: \(error.localizedDescription)")
                    self.loadRecommendedPracticesFromHistory()
                }
            }
        }
    }

    /// 从练习历史生成推荐练习
    private func loadRecommendedPracticesFromHistory() {
        let recentSessions = practiceManager.recentPractices.prefix(10)
        let practiceTypes = Array(Set(recentSessions.map { $0.type }))

        // 基于最近练习类型生成推荐
        var recommendations: [PracticeItem] = []

        // 如果最近没有词汇练习，推荐词汇练习
        if !practiceTypes.contains(.vocabulary) {
            recommendations.append(PracticeItem(
                title: "词汇练习",
                type: "vocabulary",
                iconName: "textformat.abc",
                durationMinutes: 10,
                difficulty: "初级",
                color: .blue
            ))
        }

        // 如果最近没有语法练习，推荐语法练习
        if !practiceTypes.contains(.grammar) {
            recommendations.append(PracticeItem(
                title: "语法练习",
                type: "grammar",
                iconName: "text.alignleft",
                durationMinutes: 15,
                difficulty: "中级",
                color: .green
            ))
        }

        // 如果最近没有听力练习，推荐听力练习
        if !practiceTypes.contains(.listening) {
            recommendations.append(PracticeItem(
                title: "听力练习",
                type: "listening",
                iconName: "ear",
                durationMinutes: 12,
                difficulty: "初级",
                color: .orange
            ))
        }

        // 如果没有推荐，提供默认推荐
        if recommendations.isEmpty {
            recommendations = [
                PracticeItem(
                    title: "综合练习",
                    type: "mixed",
                    iconName: "square.stack.fill",
                    durationMinutes: 20,
                    difficulty: "中级",
                    color: .purple
                )
            ]
        }

        self.recommendedPractices = recommendations
    }

    /// 从学习路径生成推荐练习
    private func generateRecommendedPracticesFromPath(_ learningPath: PersonalizedLearningPath) {
        // 基于学习路径的当前阶段生成推荐
        var recommendations: [PracticeItem] = []

        // 根据当前级别推荐适当的练习
        let difficulty = learningPath.currentLevel

        recommendations.append(contentsOf: [
            PracticeItem(
                title: "个性化词汇",
                type: "vocabulary",
                iconName: "textformat.abc",
                durationMinutes: 15,
                difficulty: difficulty,
                color: .blue
            ),
            PracticeItem(
                title: "个性化语法",
                type: "grammar",
                iconName: "text.alignleft",
                durationMinutes: 20,
                difficulty: difficulty,
                color: .green
            ),
            PracticeItem(
                title: "个性化听力",
                type: "listening",
                iconName: "ear",
                durationMinutes: 18,
                difficulty: difficulty,
                color: .orange
            )
        ])

        self.recommendedPractices = recommendations
    }

    /// 加载最近学习
    private func loadRecentLearning() {
        // 从 PracticeManager 获取最近的练习会话
        let recentSessions = practiceManager.recentPractices.prefix(5)

        let recentItems = recentSessions.map { session in
            RecentLearningItem(
                title: getDisplayTitle(for: session.type),
                subtitle: getDisplaySubtitle(for: session),
                iconName: iconName(for: session.type.rawValue),
                timeAgo: timeAgo(from: session.endTime),
                color: colorForPracticeType(session.type)
            )
        }

        self.recentLearningItems = Array(recentItems)

        // 如果没有最近学习记录，显示空状态提示
        if recentLearningItems.isEmpty {
            self.recentLearningItems = [
                RecentLearningItem(
                    title: "开始你的学习之旅",
                    subtitle: "完成第一个练习来查看学习历史",
                    iconName: "star.fill",
                    timeAgo: "",
                    color: .yellow
                )
            ]
        }
    }

    /// 获取练习类型的显示标题
    private func getDisplayTitle(for type: PracticeSession.PracticeType) -> String {
        switch type {
        case .vocabulary:
            return "词汇练习"
        case .grammar:
            return "语法练习"
        case .listening:
            return "听力练习"
        case .speaking:
            return "口语练习"
        case .reading:
            return "阅读练习"
        case .writing:
            return "写作练习"
        case .pronunciation:
            return "发音练习"
        case .comprehensive:
            return "综合练习"
        }
    }

    /// 获取练习会话的显示副标题
    private func getDisplaySubtitle(for session: PracticeSession) -> String {
        let durationMinutes = Int(session.duration / 60)
        let scoreText = session.score > 0 ? "得分 \(session.score)" : ""

        if durationMinutes > 0 && !scoreText.isEmpty {
            return "练习 \(durationMinutes) 分钟，\(scoreText)"
        } else if durationMinutes > 0 {
            return "练习 \(durationMinutes) 分钟"
        } else if !scoreText.isEmpty {
            return scoreText
        } else {
            return "已完成练习"
        }
    }

    /// 检查评估状态
    private func checkEvaluationStatus() {
        // 使用评估管理器加载可用评估
        evaluationManager.loadAvailableEvaluations()
    }

    /// 开始评估
    func startEvaluation() {
        // 先加载可用评估，然后在订阅中处理导航
        evaluationManager.loadAvailableEvaluations()

        // 如果已经有可用评估，直接开始第一个
        if let firstEvaluation = evaluationManager.availableEvaluations.first {
            evaluationManager.startEvaluation(id: firstEvaluation.id)
        }
    }

    /// 获取练习类型对应的图标名称
    private func iconName(for type: String) -> String {
        switch type {
        case "vocabulary":
            return "textformat.abc"
        case "grammar":
            return "text.alignleft"
        case "listening":
            return "ear"
        case "speaking":
            return "waveform"
        case "reading":
            return "book"
        case "writing":
            return "pencil"
        default:
            return "square.stack.fill"
        }
    }

    /// 获取练习类型对应的颜色
    private func color(for type: String) -> Color {
        switch type {
        case "vocabulary":
            return .blue
        case "grammar":
            return .green
        case "listening":
            return .orange
        case "speaking":
            return .purple
        case "reading":
            return .red
        case "writing":
            return .pink
        default:
            return .gray
        }
    }

    /// 计算时间间隔
    private func timeAgo(from date: Date) -> String {
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.minute, .hour, .day], from: date, to: now)

        if let day = components.day, day > 0 {
            return "\(day)天前"
        } else if let hour = components.hour, hour > 0 {
            return "\(hour)小时前"
        } else if let minute = components.minute, minute > 0 {
            return "\(minute)分钟前"
        } else {
            return "刚刚"
        }
    }

    private func colorForPracticeType(_ type: PracticeSession.PracticeType) -> SendableColor {
        switch type {
        case .vocabulary: return .blue
        case .grammar: return .green
        case .listening: return .orange
        case .speaking: return SendableColor(red: 0.8, green: 0.2, blue: 0.2)
        case .reading: return .purple
        case .writing: return SendableColor(red: 0.3, green: 0.3, blue: 0.8)
        case .pronunciation: return SendableColor(red: 0.0, green: 0.6, blue: 0.6)
        case .comprehensive: return SendableColor(red: 0.5, green: 0.5, blue: 0.5)
        }
    }
}
