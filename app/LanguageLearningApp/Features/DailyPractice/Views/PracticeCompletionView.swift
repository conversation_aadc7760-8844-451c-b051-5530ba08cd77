import SwiftUI

/// 练习完成视图
struct PracticeCompletionView: View {
    @ObservedObject var viewModel: PersonalizedPracticeViewModel
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 标题
                Text("练习完成")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(Color.primary)
                    .padding(.top)

                if case .completed = viewModel.state, let result = viewModel.result {
                    // 结果摘要
                    resultSummaryView(result)

                    // 详细分析
                    detailedAnalysisView(result)

                    // 改进建议
                    if let recommendations = result["recommendations"] as? [String], !recommendations.isEmpty {
                        improvementSuggestionsView(recommendations)
                    }

                    // 分享成果
                    shareResultsView(result)

                    // 下一步按钮
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("返回主页")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .padding(.vertical)
                } else if case .completing = viewModel.state {
                    // 加载中
                    ProgressView()
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                } else {
                    // 错误信息
                    Text(viewModel.errorMessage ?? "加载结果失败")
                        .font(.headline)
                        .foregroundColor(.red)
                        .padding()
                }
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(colorScheme == .dark ? Color.black : Color.white)
        }
        .navigationTitle("练习完成")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
    }

    /// 结果摘要视图
    /// - Parameter result: 练习结果
    /// - Returns: 摘要视图
    private func resultSummaryView(_ result: [String: Any]) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("练习总结")
                .font(.headline)
                .fontWeight(.semibold)

            HStack(spacing: 20) {
                // 得分
                VStack {
                    Text("\(result["score"] as? Int ?? 0)")
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(.blue)

                    Text("得分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)

                // 正确率
                VStack {
                    Text(String(format: "%.1f%%", calculateCorrectPercentage(result)))
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(correctPercentageColor(calculateCorrectPercentage(result)))

                    Text("正确率")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)

                // 用时
                VStack {
                    Text(formatDuration(result["duration"] as? Int ?? 0))
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.purple)

                    Text("用时")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)

            // 表现评级
            HStack {
                Text("表现评级:")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Text(localizedPerformanceLevel(calculatePerformanceLevel(result)))
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(performanceLevelColor(calculatePerformanceLevel(result)))
                    .padding(.horizontal, 10)
                    .padding(.vertical, 5)
                    .background(performanceLevelColor(calculatePerformanceLevel(result)).opacity(0.1))
                    .cornerRadius(5)
            }
            .padding(.top, 5)
        }
    }

    /// 详细分析视图
    /// - Parameter result: 练习结果
    /// - Returns: 详细分析视图
    private func detailedAnalysisView(_ result: [String: Any]) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("详细分析")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 10) {
                // 正确题目
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)

                    Text("正确题目: \(result["correctCount"] as? Int ?? 0)/\(result["totalCount"] as? Int ?? 0)")
                        .font(.subheadline)
                }

                // 得分率
                HStack {
                    Image(systemName: "chart.bar.fill")
                        .foregroundColor(.blue)

                    Text("得分率: \(String(format: "%.1f%%", calculateScorePercentage(result)))")
                        .font(.subheadline)
                }

                // 练习类型
                HStack {
                    Image(systemName: practiceTypeIcon(DailyPracticeType(rawValue: result["practiceType"] as? String ?? "mixed") ?? .mixed))
                        .foregroundColor(.purple)

                    Text("练习类型: \(localizedPracticeType(DailyPracticeType(rawValue: result["practiceType"] as? String ?? "mixed") ?? .mixed))")
                        .font(.subheadline)
                }

                // 完成时间
                HStack {
                    Image(systemName: "clock.fill")
                        .foregroundColor(.orange)

                    Text("完成时间: \(formatDate(result["completedAt"] as? Date ?? Date()))")
                        .font(.subheadline)
                }
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 改进建议视图
    /// - Parameter recommendations: 建议列表
    /// - Returns: 建议视图
    private func improvementSuggestionsView(_ recommendations: [String]?) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("改进建议")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 10) {
                ForEach(recommendations ?? [], id: \.self) { recommendation in
                    HStack(alignment: .top) {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                            .font(.system(size: 16))
                            .frame(width: 20, height: 20)

                        Text(recommendation)
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                    .padding(.vertical, 2)
                }
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 分享成果视图
    /// - Parameter result: 练习结果
    /// - Returns: 分享视图
    private func shareResultsView(_ result: [String: Any]) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("分享成果")
                .font(.headline)
                .fontWeight(.semibold)

            Button(action: {
                // 分享逻辑
                let practiceType = DailyPracticeType(rawValue: result["practiceType"] as? String ?? "mixed") ?? .mixed
                let score = result["score"] as? Int ?? 0
                let correctPercentage = calculateCorrectPercentage(result)
                let shareText = "我在语言学习应用中完成了\(localizedPracticeType(practiceType))练习，得分\(score)分，正确率\(String(format: "%.1f%%", correctPercentage))！"
                let activityViewController = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = windowScene.windows.first?.rootViewController {
                    rootViewController.present(activityViewController, animated: true, completion: nil)
                }
            }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                        .font(.headline)

                    Text("分享我的成果")
                        .font(.headline)
                }
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(10)
            }
        }
    }

    /// 获取练习类型图标
    /// - Parameter type: 练习类型
    /// - Returns: 图标名称
    private func practiceTypeIcon(_ type: DailyPracticeType) -> String {
        switch type {
        case .vocabulary:
            return "textformat.abc"
        case .grammar:
            return "text.book.closed"
        case .listening:
            return "ear"
        case .speaking:
            return "mic"
        case .reading:
            return "book"
        case .writing:
            return "pencil"
        case .mixed:
            return "square.grid.2x2"
        }
    }

    /// 获取本地化的练习类型名称
    /// - Parameter type: 练习类型
    /// - Returns: 本地化名称
    private func localizedPracticeType(_ type: DailyPracticeType) -> String {
        switch type {
        case .vocabulary:
            return "词汇"
        case .grammar:
            return "语法"
        case .listening:
            return "听力"
        case .speaking:
            return "口语"
        case .reading:
            return "阅读"
        case .writing:
            return "写作"
        case .mixed:
            return "综合"
        }
    }

    /// 获取本地化的表现级别
    /// - Parameter level: 表现级别
    /// - Returns: 本地化名称
    private func localizedPerformanceLevel(_ level: PerformanceLevel) -> String {
        switch level {
        case .excellent:
            return "优秀"
        case .good:
            return "良好"
        case .satisfactory:
            return "满意"
        case .needsImprovement:
            return "需要改进"
        }
    }

    /// 获取表现级别颜色
    /// - Parameter level: 表现级别
    /// - Returns: 对应的颜色
    private func performanceLevelColor(_ level: PerformanceLevel) -> Color {
        switch level {
        case .excellent:
            return .green
        case .good:
            return .blue
        case .satisfactory:
            return .yellow
        case .needsImprovement:
            return .orange
        }
    }

    /// 获取正确率颜色
    /// - Parameter percentage: 正确率
    /// - Returns: 对应的颜色
    private func correctPercentageColor(_ percentage: Double) -> Color {
        if percentage >= 90 {
            return .green
        } else if percentage >= 75 {
            return .blue
        } else if percentage >= 60 {
            return .yellow
        } else {
            return .orange
        }
    }

    /// 格式化持续时间
    /// - Parameter seconds: 秒数
    /// - Returns: 格式化的时间字符串
    private func formatDuration(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let seconds = seconds % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    /// 格式化日期
    /// - Parameter date: 日期
    /// - Returns: 格式化的日期字符串
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    /// 计算正确率
    /// - Parameter result: 练习结果
    /// - Returns: 正确率
    private func calculateCorrectPercentage(_ result: [String: Any]) -> Double {
        let correctCount = result["correctCount"] as? Int ?? 0
        let totalCount = result["totalCount"] as? Int ?? 1 // 避免除以零

        return Double(correctCount) / Double(totalCount) * 100.0
    }

    /// 计算得分率
    /// - Parameter result: 练习结果
    /// - Returns: 得分率
    private func calculateScorePercentage(_ result: [String: Any]) -> Double {
        let score = result["score"] as? Int ?? 0
        let totalPoints = result["totalPoints"] as? Int ?? 1 // 避免除以零

        return Double(score) / Double(totalPoints) * 100.0
    }

    /// 计算表现级别
    /// - Parameter result: 练习结果
    /// - Returns: 表现级别
    private func calculatePerformanceLevel(_ result: [String: Any]) -> PerformanceLevel {
        let percentage = calculateScorePercentage(result)

        if percentage >= 90 {
            return .excellent
        } else if percentage >= 75 {
            return .good
        } else if percentage >= 60 {
            return .satisfactory
        } else {
            return .needsImprovement
        }
    }
}

struct PracticeCompletionView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            PracticeCompletionView(viewModel: PersonalizedPracticeViewModel())
        }
    }
}
