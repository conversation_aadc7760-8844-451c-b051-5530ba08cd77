import SwiftUI
import AVFoundation

/// 听力题视图组件
struct DailyPracticeListeningView: View {
    let exercise: [String: Any]
    @Binding var selectedOption: String?
    @Binding var isPlaying: Bool

    var togglePlayback: () -> Void

    // 从嵌套结构中提取实际的练习数据
    private var actualExercise: [String: Any] {
        if let data = exercise["data"] as? [String: Any] {
            if let exercises = data["exercises"] as? [[String: Any]], !exercises.isEmpty {
                if let firstExercise = exercises.first {
                    if let innerExercise = firstExercise["exercise"] as? [String: Any] {
                        return innerExercise
                    }
                    return firstExercise
                }
            }
            return data
        }
        return exercise
    }

    // 获取当前问题
    private var currentQuestion: [String: Any]? {
        if let questions = actualExercise["questions"] as? [[String: Any]], !questions.isEmpty {
            return questions.first
        }
        return nil
    }

    // 获取问题文本
    private var questionText: String {
        if let question = currentQuestion?["question"] as? String {
            return question
        }
        return "请听音频并选择正确答案"
    }

    // 获取选项
    private var options: [String] {
        if let question = currentQuestion, let options = question["options"] as? [String] {
            return options
        }
        return ExerciseDataProcessor.getExerciseOptions(from: actualExercise) ?? []
    }

    var body: some View {
        VStack(spacing: 20) {
            // 显示标题
            if let title = actualExercise["title"] as? String {
                Text(title)
                    .font(.headline)
                    .multilineTextAlignment(.center)
                    .padding(.bottom, 5)
            }

            // 显示问题
            Text(questionText)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            // 如果有transcript，显示一个切换按钮来查看文本
            if let transcript = actualExercise["transcript"] as? String {
                DisclosureGroup("查看文本") {
                    Text(transcript)
                        .font(.body)
                        .padding()
                        .background(Color(UIColor.systemGray6))
                        .cornerRadius(8)
                }
                .padding(.vertical, 5)
            }

            // 播放音频按钮
            Button(action: togglePlayback) {
                HStack {
                    Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.white)

                    Text(isPlaying ? "暂停" : "播放音频")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.blue)
                .cornerRadius(10)
            }

            // 显示音频URL（调试用，实际应用可以移除）
            if let audioURL = actualExercise["audioURL"] as? String {
                Text("音频: \(audioURL)")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.bottom, 5)
            }

            // 选项
            if !options.isEmpty {
                ForEach(options, id: \.self) { option in
                    Button(action: {
                        selectedOption = option
                    }) {
                        HStack {
                            Text(option)
                                .font(.body)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)

                            Spacer()

                            if selectedOption == option {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(selectedOption == option ? Color.blue.opacity(0.1) : Color(UIColor.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
}

#if DEBUG
struct DailyPracticeListeningView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleExercise: [String: Any] = [
            "data": [
                "exercises": [
                    [
                        "exercise": [
                            "title": "Basic Greetings",
                            "transcript": "Hello! My name is John. Nice to meet you. What's your name?",
                            "audioURL": "https://example.com/audio/basic_greetings.mp3",
                            "questions": [
                                [
                                    "question": "What is the speaker's name?",
                                    "options": ["John", "James", "Jack", "Jim"],
                                    "correctAnswer": 0
                                ],
                                [
                                    "question": "What does the speaker ask?",
                                    "options": ["What's your name?", "How old are you?", "Where are you from?", "What do you do?"],
                                    "correctAnswer": 0
                                ]
                            ]
                        ],
                        "type": "listening"
                    ]
                ]
            ]
        ]

        return DailyPracticeListeningView(
            exercise: sampleExercise,
            selectedOption: .constant("John"),
            isPlaying: .constant(false),
            togglePlayback: {}
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
#endif
