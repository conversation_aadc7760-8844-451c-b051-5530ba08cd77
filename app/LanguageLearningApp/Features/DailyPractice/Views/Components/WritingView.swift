import SwiftUI

/// 写作题视图组件
struct WritingView: View {
    @Binding var textInput: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("请写出您的回答:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            TextEditor(text: $textInput)
                .frame(minHeight: 150)
                .padding(5)
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)
                .autocapitalization(.none)
        }
    }
}

#if DEBUG
struct WritingView_Previews: PreviewProvider {
    static var previews: some View {
        WritingView(textInput: .constant("这是一个示例答案"))
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
#endif
