import SwiftUI

/// 答案反馈覆盖层视图
struct FeedbackOverlayView: View {
    let isCorrect: Bool
    let explanation: String?
    @Binding var showFeedback: Bool
    
    var body: some View {
        VStack {
            Spacer()
            
            VStack(spacing: 16) {
                Image(systemName: isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(isCorrect ? .green : .red)
                
                Text(isCorrect ? "回答正确!" : "回答错误")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(isCorrect ? .green : .red)
                
                if let explanation = explanation {
                    Text(explanation)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding()
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(UIColor.systemBackground))
                    .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
            )
            .padding(.horizontal, 32)
            
            Spacer()
        }
        .background(Color.black.opacity(0.5))
        .edgesIgnoringSafeArea(.all)
        .onAppear {
            // 2秒后自动隐藏反馈
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [self] in
                withAnimation {
                    showFeedback = false
                }
            }
        }
    }
}

#if DEBUG
struct FeedbackOverlayView_Previews: PreviewProvider {
    static var previews: some View {
        FeedbackOverlayView(
            isCorrect: true,
            explanation: "正确答案是'苹果'。Apple在中文中的意思是苹果。",
            showFeedback: .constant(true)
        )
    }
}
#endif
