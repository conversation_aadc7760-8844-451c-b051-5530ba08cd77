import SwiftUI
import AVFoundation

/// 口语题视图组件
struct DailyPracticeSpeakingView: View {
    let exercise: [String: Any]
    @Binding var isRecording: Bool
    @Binding var recordingURL: URL?
    @Binding var isPlaying: Bool
    
    var toggleRecording: () -> Void
    var togglePlayback: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // 显示目标短语
            if let targetPhrase = exercise["targetPhrase"] as? String {
                VStack(alignment: .leading, spacing: 5) {
                    Text("参考句型:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                    
                    Text(targetPhrase)
                        .font(.body)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.bottom, 10)
            }
            
            // 显示示例句子
            if let exampleSentence = exercise["exampleSentence"] as? String {
                VStack(alignment: .leading, spacing: 5) {
                    Text("示例:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                    
                    Text(exampleSentence)
                        .font(.body)
                        .padding()
                        .background(Color.green.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.bottom, 10)
            }
            
            Text("请录制您的回答")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 录音按钮
            Button(action: toggleRecording) {
                VStack {
                    Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(isRecording ? .red : .blue)
                    
                    Text(isRecording ? "停止录音" : "开始录音")
                        .font(.caption)
                        .foregroundColor(isRecording ? .red : .blue)
                        .padding(.top, 5)
                }
                .padding()
                .background(
                    Circle()
                        .fill(Color(UIColor.systemGray6))
                        .frame(width: 120, height: 120)
                )
            }
            
            // 播放录音按钮（如果已录制）
            if recordingURL != nil {
                Button(action: togglePlayback) {
                    HStack {
                        Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                            .foregroundColor(.blue)
                        
                        Text(isPlaying ? "暂停" : "播放录音")
                            .foregroundColor(.blue)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.blue, lineWidth: 1)
                    )
                }
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(UIColor.systemGray6))
        .cornerRadius(10)
    }
}

#if DEBUG
struct DailyPracticeSpeakingView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleExercise: [String: Any] = [
            "targetPhrase": "I would like to introduce myself.",
            "exampleSentence": "Hello, my name is John and I am a student."
        ]
        
        return DailyPracticeSpeakingView(
            exercise: sampleExercise,
            isRecording: .constant(false),
            recordingURL: .constant(nil),
            isPlaying: .constant(false),
            toggleRecording: {},
            togglePlayback: {}
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
#endif
