import Foundation

/// 每日练习类型
enum DailyPracticeType: String, Codable {
    /// 词汇练习
    case vocabulary = "vocabulary"
    /// 语法练习
    case grammar = "grammar"
    /// 听力练习
    case listening = "listening"
    /// 口语练习
    case speaking = "speaking"
    /// 阅读练习
    case reading = "reading"
    /// 写作练习
    case writing = "writing"
    /// 混合练习
    case mixed = "mixed"
}

/// 每日练习模型
struct DailyPractice: Identifiable, Codable {
    /// 练习ID
    let id: UUID
    /// 用户ID
    let userID: UUID
    /// 学习路径ID
    let learningPathID: UUID?
    /// 练习日期
    let date: Date
    /// 练习类型
    let type: DailyPracticeType
    /// 练习标题
    let title: String
    /// 练习描述
    let description: String
    /// 估计完成时间（分钟）
    let estimatedTimeMinutes: Int
    /// 练习题目
    var exercises: [PracticeExercise]
    /// 是否已完成
    var isCompleted: Bool
    /// 完成时间
    var completedAt: Date?
    /// 得分
    var score: Int?
    /// 创建时间
    let createdAt: Date
    /// 更新时间
    var updatedAt: Date
    
    /// 计算完成百分比
    var completionPercentage: Double {
        guard !exercises.isEmpty else { return 0 }
        
        let completedExercises = exercises.filter { $0.isCompleted }.count
        return Double(completedExercises) / Double(exercises.count) * 100.0
    }
    
    /// 获取下一个未完成的练习
    func nextIncompleteExercise() -> PracticeExercise? {
        return exercises.first { !$0.isCompleted }
    }
}

/// 每日练习响应
struct DailyPracticeResponse: Codable {
    let data: DailyPractice
}

/// 每日练习列表响应
struct DailyPracticeListResponse: Codable {
    let data: [DailyPractice]
}
