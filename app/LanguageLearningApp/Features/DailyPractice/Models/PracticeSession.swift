import Foundation

/// 练习会话模型，代表用户的一次练习活动
public struct PracticeSession: Codable, Identifiable, Equatable {
    /// 唯一标识符
    public let id: UUID
    
    /// 用户ID
    public let userID: UUID
    
    /// 练习类型
    public let type: PracticeType
    
    /// 开始时间
    public let startTime: Date
    
    /// 结束时间
    public let endTime: Date
    
    /// 持续时间（秒）
    public let duration: TimeInterval
    
    /// 得分
    public let score: Int
    
    /// 是否完成
    public let completed: Bool
    
    /// 是否已同步到服务器
    public var isSynced: Bool
    
    /// 初始化方法
    /// - Parameters:
    ///   - id: 唯一标识符
    ///   - userID: 用户ID
    ///   - type: 练习类型
    ///   - startTime: 开始时间
    ///   - endTime: 结束时间
    ///   - duration: 持续时间（秒）
    ///   - score: 得分
    ///   - completed: 是否完成
    ///   - isSynced: 是否已同步到服务器
    public init(
        id: UUID = UUID(),
        userID: UUID,
        type: PracticeType,
        startTime: Date = Date(),
        endTime: Date = Date(),
        duration: TimeInterval,
        score: Int,
        completed: Bool = true,
        isSynced: Bool = false
    ) {
        self.id = id
        self.userID = userID
        self.type = type
        self.startTime = startTime
        self.endTime = endTime
        self.duration = duration
        self.score = score
        self.completed = completed
        self.isSynced = isSynced
    }
    
    /// 练习类型枚举
    public enum PracticeType: String, Codable, CaseIterable {
        case vocabulary = "vocabulary"
        case grammar = "grammar"
        case speaking = "speaking"
        case listening = "listening"
        case reading = "reading"
        case writing = "writing"
        case pronunciation = "pronunciation"
        case comprehensive = "comprehensive"
        
        /// 显示名称
        public var displayName: String {
            switch self {
            case .vocabulary:
                return "词汇"
            case .grammar:
                return "语法"
            case .speaking:
                return "口语"
            case .listening:
                return "听力"
            case .reading:
                return "阅读"
            case .writing:
                return "写作"
            case .pronunciation:
                return "发音"
            case .comprehensive:
                return "综合"
            }
        }
    }
}

/// 练习会话列表响应
public struct PracticeSessionListResponse: Codable {
    /// 是否成功
    public let success: Bool
    
    /// 消息
    public let message: String?
    
    /// 练习会话列表
    public let data: [PracticeSession]
    
    /// 错误信息
    public let error: String?
    
    /// 初始化方法
    /// - Parameters:
    ///   - success: 是否成功
    ///   - message: 消息
    ///   - data: 练习会话列表
    ///   - error: 错误信息
    public init(success: Bool, message: String?, data: [PracticeSession], error: String?) {
        self.success = success
        self.message = message
        self.data = data
        self.error = error
    }
} 