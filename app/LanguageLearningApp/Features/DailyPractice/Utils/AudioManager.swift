import Foundation
import AVFoundation

/// 音频管理工具类
class AudioManager {
    static let shared = AudioManager()
    
    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?
    
    private init() {}
    
    /// 开始录音
    /// - Returns: 录音文件URL，如果失败则返回nil
    func startRecording() -> URL? {
        let audioSession = AVAudioSession.sharedInstance()
        
        do {
            try audioSession.setCategory(.playAndRecord, mode: .default)
            try audioSession.setActive(true)
            
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let audioFilename = documentsPath.appendingPathComponent("recording-\(Date().timeIntervalSince1970).m4a")
            
            let settings = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100,
                AVNumberOfChannelsKey: 2,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
            ]
            
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.record()
            
            return audioFilename
            
        } catch {
            print("录音失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 停止录音
    func stopRecording() {
        audioRecorder?.stop()
        audioRecorder = nil
    }
    
    /// 播放音频
    /// - Parameter url: 音频文件URL
    /// - Returns: 是否成功开始播放
    func playAudio(from url: URL) -> Bool {
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.play()
            return true
        } catch {
            print("播放音频失败: \(error.localizedDescription)")
            return false
        }
    }
    
    /// 暂停音频播放
    func pauseAudio() {
        audioPlayer?.pause()
    }
    
    /// 停止音频播放
    func stopAudio() {
        audioPlayer?.stop()
        audioPlayer = nil
    }
    
    /// 检查是否正在播放
    /// - Returns: 是否正在播放
    func isPlaying() -> Bool {
        return audioPlayer?.isPlaying ?? false
    }
}
