import Foundation

/// 练习数据处理工具类
/// 用于处理从API返回的各种格式的练习数据
class ExerciseDataProcessor {
    
    /// 从原始练习数据中提取实际的练习内容
    /// - Parameter exercise: 原始练习数据
    /// - Returns: 处理后的练习数据
    static func extractActualExercise(from exercise: [String: Any]) -> [String: Any] {
        // 检查是否有嵌套的exercise字段 - 处理多种可能的嵌套结构
        if let data = exercise["data"] as? [String: Any] {
            if let nestedExercise = data["exercise"] as? [String: Any] {
                // 数据结构: {"data": {"exercise": {...}}}
                return nestedExercise
            } else if let exercises = data["exercises"] as? [[String: Any]], !exercises.isEmpty {
                // 数据结构: {"data": {"exercises": [{...}]}}
                if let firstExercise = exercises.first {
                    if let innerExercise = firstExercise["exercise"] as? [String: Any] {
                        // 数据结构: {"data": {"exercises": [{"exercise": {...}}]}}
                        var result = innerExercise
                        // 检查外层是否有type字段
                        if let type = firstExercise["type"] as? String {
                            result["type"] = type
                        }
                        return result
                    }
                    return firstExercise
                }
            }
            // 如果data中没有exercise或exercises字段，但有其他有用的字段
            var result = data
            // 将外层的一些关键字段合并到结果中
            ["type", "category", "id", "title", "description"].forEach { key in
                if let value = exercise[key] {
                    result[key] = value
                }
            }
            return result
        }
        
        // 如果没有data字段，直接返回原始数据
        return exercise
    }
    
    /// 从练习数据和问题中创建问题练习字典
    /// - Parameters:
    ///   - exercise: 练习数据
    ///   - question: 问题数据
    /// - Returns: 问题练习字典
    static func createQuestionExercise(from exercise: [String: Any], question: [String: Any]) -> [String: Any] {
        var questionExercise = exercise
        
        // 将问题的字段合并到练习中
        for (key, value) in question {
            questionExercise[key] = value
        }
        
        return questionExercise
    }
    
    /// 获取练习的标题
    /// - Parameter exercise: 练习数据
    /// - Returns: 标题
    static func getExerciseTitle(from exercise: [String: Any]) -> String {
        if let title = exercise["title"] as? String {
            return title
        } else if let question = exercise["question"] as? String {
            return question
        } else if let content = exercise["content"] as? String {
            // 如果内容太长，截取一部分作为标题
            let maxLength = 50
            if content.count > maxLength {
                let endIndex = content.index(content.startIndex, offsetBy: maxLength)
                return String(content[..<endIndex]) + "..."
            }
            return content
        }
        return "练习题"
    }
    
    /// 获取练习的指导说明
    /// - Parameter exercise: 练习数据
    /// - Returns: 指导说明
    static func getExerciseInstruction(from exercise: [String: Any]) -> String? {
        if let instruction = exercise["instruction"] as? String {
            return instruction
        } else if let description = exercise["description"] as? String {
            return description
        }
        return nil
    }
    
    /// 获取练习的选项
    /// - Parameter exercise: 练习数据
    /// - Returns: 选项数组
    static func getExerciseOptions(from exercise: [String: Any]) -> [String]? {
        if let options = exercise["options"] as? [String] {
            return options
        } else if let choices = exercise["choices"] as? [String] {
            return choices
        } else if let answers = exercise["answers"] as? [String] {
            return answers
        }
        return nil
    }
    
    /// 获取练习的提示
    /// - Parameter exercise: 练习数据
    /// - Returns: 提示
    static func getExerciseHint(from exercise: [String: Any]) -> String? {
        if let explanation = exercise["explanation"] as? String {
            return explanation
        } else if let hint = exercise["hint"] as? String {
            return hint
        }
        return nil
    }
}
