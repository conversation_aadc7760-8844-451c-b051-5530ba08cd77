import Foundation
import Combine

/// 每日练习统计服务协议
public protocol DailyPracticeStatsServiceProtocol {
    /// 获取今日练习统计
    /// - Returns: 包含今日统计的发布者
    func getTodayStats() -> AnyPublisher<DailyPracticeStats, Error>

    /// 获取指定日期的练习统计
    /// - Parameter date: 指定日期
    /// - Returns: 包含指定日期统计的发布者
    func getStatsForDate(_ date: Date) -> AnyPublisher<DailyPracticeStats, Error>

    /// 获取本周练习统计
    /// - Returns: 包含本周统计的发布者
    func getWeeklyStats() -> AnyPublisher<WeeklyPracticeStats, Error>

    /// 获取指定周的练习统计
    /// - Parameter weekStartDate: 周开始日期
    /// - Returns: 包含指定周统计的发布者
    func getWeeklyStats(for weekStartDate: Date) -> AnyPublisher<WeeklyPracticeStats, Error>

    /// 从本地练习数据计算统计
    /// - Parameters:
    ///   - sessions: 练习会话列表
    ///   - date: 指定日期
    /// - Returns: 计算出的统计数据
    func calculateStatsFromLocalData(_ sessions: [PracticeSession], for date: Date) -> DailyPracticeStats
}

/// 每日练习统计服务实现
public class DailyPracticeStatsService: DailyPracticeStatsServiceProtocol {
    // 单例实例
    public static let shared = DailyPracticeStatsService()

    // API 客户端
    private let apiClient: APIClientProtocol

    /// 初始化方法
    /// - Parameter apiClient: API 客户端
    public init(apiClient: APIClientProtocol = APIClient.shared) {
        self.apiClient = apiClient
    }

    /// 获取今日练习统计
    /// - Returns: 包含今日统计的发布者
    public func getTodayStats() -> AnyPublisher<DailyPracticeStats, Error> {
        return getStatsForDate(Date())
    }

    /// 获取指定日期的练习统计
    /// - Parameter date: 指定日期
    /// - Returns: 包含指定日期统计的发布者
    public func getStatsForDate(_ date: Date) -> AnyPublisher<DailyPracticeStats, Error> {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)

        let url = APIEndpoint.dailyPracticeStats.url
        let urlWithDate = url.appendingPathComponent("?date=\(dateString)")
        let endpoint = APIEndpoint.custom(url: urlWithDate, method: "GET", headers: [:], bodyData: nil)

        return apiClient.request(endpoint: endpoint)
            .tryMap { data -> DailyPracticeStats in
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let response = try decoder.decode(PracticeStatsResponse.self, from: data)
                return response.data
            }
            .eraseToAnyPublisher()
    }

    /// 获取本周练习统计
    /// - Returns: 包含本周统计的发布者
    public func getWeeklyStats() -> AnyPublisher<WeeklyPracticeStats, Error> {
        let calendar = Calendar.current
        let today = Date()
        let weekStartDate = calendar.dateInterval(of: .weekOfYear, for: today)?.start ?? today

        return getWeeklyStats(for: weekStartDate)
    }

    /// 获取指定周的练习统计
    /// - Parameter weekStartDate: 周开始日期
    /// - Returns: 包含指定周统计的发布者
    public func getWeeklyStats(for weekStartDate: Date) -> AnyPublisher<WeeklyPracticeStats, Error> {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: weekStartDate)

        let url = APIEndpoint.weeklyPracticeStats.url
        let urlWithDate = url.appendingPathComponent("?week_start=\(dateString)")
        let endpoint = APIEndpoint.custom(url: urlWithDate, method: "GET", headers: [:], bodyData: nil)

        return apiClient.request(endpoint: endpoint)
            .tryMap { data -> WeeklyPracticeStats in
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let response = try decoder.decode(WeeklyPracticeStatsResponse.self, from: data)
                return response.data
            }
            .eraseToAnyPublisher()
    }
}

// MARK: - 异步方法扩展

extension DailyPracticeStatsService {
    /// 异步获取今日练习统计
    /// - Returns: 今日统计
    public func getTodayStatsAsync() async throws -> DailyPracticeStats {
        return try await getStatsForDateAsync(Date())
    }

    /// 异步获取指定日期的练习统计
    /// - Parameter date: 指定日期
    /// - Returns: 指定日期统计
    public func getStatsForDateAsync(_ date: Date) async throws -> DailyPracticeStats {
        return try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            cancellable = getStatsForDate(date)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { stats in
                        continuation.resume(returning: stats)
                    }
                )
        }
    }

    /// 异步获取本周练习统计
    /// - Returns: 本周统计
    public func getWeeklyStatsAsync() async throws -> WeeklyPracticeStats {
        let calendar = Calendar.current
        let today = Date()
        let weekStartDate = calendar.dateInterval(of: .weekOfYear, for: today)?.start ?? today

        return try await getWeeklyStatsAsync(for: weekStartDate)
    }

    /// 异步获取指定周的练习统计
    /// - Parameter weekStartDate: 周开始日期
    /// - Returns: 指定周统计
    public func getWeeklyStatsAsync(for weekStartDate: Date) async throws -> WeeklyPracticeStats {
        return try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            cancellable = getWeeklyStats(for: weekStartDate)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { stats in
                        continuation.resume(returning: stats)
                    }
                )
        }
    }
}

// MARK: - 本地缓存扩展

extension DailyPracticeStatsService {
    /// 从本地练习数据计算统计
    /// - Parameters:
    ///   - sessions: 练习会话列表
    ///   - date: 指定日期
    /// - Returns: 计算出的统计数据
    public func calculateStatsFromLocalData(_ sessions: [PracticeSession], for date: Date) -> DailyPracticeStats {
        let calendar = Calendar.current
        let targetDate = calendar.startOfDay(for: date)

        // 过滤出指定日期的会话
        let dailySessions = sessions.filter { session in
            calendar.isDate(session.endTime, inSameDayAs: targetDate)
        }

        // 计算统计数据
        let minutesLearned = dailySessions.reduce(0) { total, session in
            total + Int(session.duration / 60)
        }

        let practicesCompleted = dailySessions.count

        let totalScore = dailySessions.reduce(0) { total, session in
            total + session.score
        }

        let averageScore = practicesCompleted > 0 ? totalScore / practicesCompleted : 0

        // 按类型统计练习数
        var practicesByType: [String: Int] = [:]
        for session in dailySessions {
            let typeKey = session.type.rawValue
            practicesByType[typeKey, default: 0] += 1
        }

        // 计算连续天数（简化版本）
        let streak = calculateStreakFromSessions(sessions, upTo: date)

        return DailyPracticeStats(
            date: targetDate,
            minutesLearned: minutesLearned,
            practicesCompleted: practicesCompleted,
            totalScore: totalScore,
            averageScore: averageScore,
            practicesByType: practicesByType,
            streak: streak
        )
    }

    /// 从会话数据计算连续天数
    /// - Parameters:
    ///   - sessions: 练习会话列表
    ///   - date: 截止日期
    /// - Returns: 连续天数
    private func calculateStreakFromSessions(_ sessions: [PracticeSession], upTo date: Date) -> Int {
        let calendar = Calendar.current
        let sortedSessions = sessions.sorted { $0.endTime > $1.endTime }

        var streak = 0
        var currentDate = calendar.startOfDay(for: date)

        while true {
            let hasSessionOnDate = sortedSessions.contains { session in
                calendar.isDate(session.endTime, inSameDayAs: currentDate)
            }

            if hasSessionOnDate {
                streak += 1
                currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else {
                break
            }
        }

        return streak
    }
}
