import Foundation
import Combine

/// 练习会话远程数据源实现
public class PracticeRemoteDataSource: RemoteDataSourceProtocol {
    // 数据类型是练习会话
    public typealias T = PracticeSession

    // 标识符类型是UUID
    public typealias ID = UUID

    // API客户端
    private let apiClient: APIClientProtocol

    // API端点
    private let endpoint = "/practice-sessions"

    // 单例实例 - 使用懒加载避免在依赖注册过程中触发解析
    public static let shared: PracticeRemoteDataSource = {
        do {
            let apiClient = try DependencyContainer.shared.tryResolve(APIClientProtocol.self)
            print("🌐 [PracticeRemoteDataSource] 成功从容器解析 APIClient")
            return PracticeRemoteDataSource(apiClient: apiClient)
        } catch {
            print("🌐 [PracticeRemoteDataSource] 无法从容器解析 APIClient，使用默认实例: \(error)")
            return PracticeRemoteDataSource(apiClient: APIClient.shared)
        }
    }()

    /// 初始化方法
    /// - Parameter apiClient: API客户端
    public init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    /// 获取所有练习会话
    /// - Returns: 包含练习会话列表的发布者
    public func getAll() -> AnyPublisher<[PracticeSession], Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        let endpoint = APIEndpoint.custom(url: url, method: "GET", headers: [:], bodyData: nil)
        return apiClient.request(endpoint: endpoint)
            .tryMap { data -> [PracticeSession] in
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let response = try decoder.decode(PracticeSessionListResponse.self, from: data)
                return response.data
            }
            .eraseToAnyPublisher()
    }

    /// 根据ID获取练习会话
    /// - Parameter id: 会话ID
    /// - Returns: 包含练习会话的发布者
    public func getById(_ id: ID) -> AnyPublisher<PracticeSession, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        let endpoint = APIEndpoint.custom(url: url, method: "GET", headers: [:], bodyData: nil)
        return apiClient.request(endpoint: endpoint)
            .tryMap { data -> PracticeSession in
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                return try decoder.decode(PracticeSession.self, from: data)
            }
            .eraseToAnyPublisher()
    }

    /// 保存练习会话
    /// - Parameter entity: 要保存的练习会话
    /// - Returns: 包含保存后练习会话的发布者
    public func save(_ entity: PracticeSession) -> AnyPublisher<PracticeSession, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        let parameters: [String: Any] = [
            "type": entity.type.rawValue,
            "duration": entity.duration,
            "score": entity.score,
            "endTime": ISO8601DateFormatter().string(from: entity.endTime)
        ]
        let body = try? JSONSerialization.data(withJSONObject: parameters)
        let endpoint = APIEndpoint.custom(url: url, method: "POST", headers: ["Content-Type": "application/json"], bodyData: body)
        return apiClient.request(endpoint: endpoint)
            .tryMap { data -> PracticeSession in
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                return try decoder.decode(PracticeSession.self, from: data)
            }
            .eraseToAnyPublisher()
    }

    /// 保存多个练习会话
    /// - Parameter entities: 要保存的练习会话列表
    /// - Returns: 包含保存后练习会话列表的发布者
    public func saveAll(_ entities: [PracticeSession]) -> AnyPublisher<[PracticeSession], Error> {
        // 将多个会话保存请求组合为一个Publisher
        let publishers = entities.map { save($0) }

        return Publishers.MergeMany(publishers)
            .collect()
            .eraseToAnyPublisher()
    }

    /// 删除练习会话
    /// - Parameter id: 要删除的会话ID
    /// - Returns: 包含删除成功标志的发布者
    public func delete(_ id: ID) -> AnyPublisher<Bool, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        var headers = ["Content-Type": "application/json"]
        if let token = UserDefaults.standard.string(forKey: "authToken") {
            headers["Authorization"] = "Bearer \(token)"
        }

        let endpoint = APIEndpoint.custom(url: url, method: "DELETE", headers: headers, bodyData: nil)
        return apiClient.request(endpoint: endpoint)
            .tryMap { data -> Bool in
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let success = json["success"] as? Bool {
                    return success
                }
                return false
            }
            .eraseToAnyPublisher()
    }

    // MARK: - 异步方法

    /// 异步获取所有练习会话
    /// - Returns: 练习会话列表
    public func getAllAsync() async throws -> [PracticeSession] {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        let endpoint = APIEndpoint.custom(url: url, method: "GET", headers: [:], bodyData: nil)
        let publisher = apiClient.request(endpoint: endpoint)
        let data = try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            cancellable = publisher
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { data in
                        continuation.resume(returning: data)
                    }
                )
        }

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let response = try decoder.decode(PracticeSessionListResponse.self, from: data)
        return response.data
    }

    /// 异步根据ID获取练习会话
    /// - Parameter id: 会话ID
    /// - Returns: 练习会话
    public func getByIdAsync(_ id: ID) async throws -> PracticeSession {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        let endpoint = APIEndpoint.custom(url: url, method: "GET", headers: [:], bodyData: nil)
        let publisher = apiClient.request(endpoint: endpoint)
        let data = try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            cancellable = publisher
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { data in
                        continuation.resume(returning: data)
                    }
                )
        }

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        return try decoder.decode(PracticeSession.self, from: data)
    }

    /// 异步保存练习会话
    /// - Parameter entity: 要保存的练习会话
    /// - Returns: 保存后的练习会话
    public func saveAsync(_ entity: PracticeSession) async throws -> PracticeSession {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        let parameters: [String: Any] = [
            "type": entity.type.rawValue,
            "duration": entity.duration,
            "score": entity.score,
            "endTime": ISO8601DateFormatter().string(from: entity.endTime)
        ]
        let body = try? JSONSerialization.data(withJSONObject: parameters)
        let endpoint = APIEndpoint.custom(url: url, method: "POST", headers: ["Content-Type": "application/json"], bodyData: body)
        let publisher = apiClient.request(endpoint: endpoint)
        let data = try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            cancellable = publisher
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { data in
                        continuation.resume(returning: data)
                    }
                )
        }

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        return try decoder.decode(PracticeSession.self, from: data)
    }

    /// 异步保存多个练习会话
    /// - Parameter entities: 要保存的练习会话列表
    /// - Returns: 保存后的练习会话列表
    public func saveAllAsync(_ entities: [PracticeSession]) async throws -> [PracticeSession] {
        var savedSessions: [PracticeSession] = []

        for entity in entities {
            let savedSession = try await saveAsync(entity)
            savedSessions.append(savedSession)
        }

        return savedSessions
    }

    /// 异步删除练习会话
    /// - Parameter id: 要删除的会话ID
    /// - Returns: 删除是否成功
    public func deleteAsync(_ id: ID) async throws -> Bool {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        var headers = ["Content-Type": "application/json"]
        if let token = UserDefaults.standard.string(forKey: "authToken") {
            headers["Authorization"] = "Bearer \(token)"
        }

        let endpoint = APIEndpoint.custom(url: url, method: "DELETE", headers: headers, bodyData: nil)
        let publisher = apiClient.request(endpoint: endpoint)
        let data = try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            cancellable = publisher
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { data in
                        continuation.resume(returning: data)
                    }
                )
        }

        if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
           let success = json["success"] as? Bool {
            return success
        }
        return false
    }

    // MARK: - 辅助方法

    /// 格式化日期为ISO8601字符串
    /// - Parameter date: 要格式化的日期
    /// - Returns: 格式化后的日期字符串
    private func formatDate(_ date: Date) -> String {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter.string(from: date)
    }
}
