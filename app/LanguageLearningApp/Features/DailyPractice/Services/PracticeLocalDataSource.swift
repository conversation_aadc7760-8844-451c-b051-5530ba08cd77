import Foundation

/// 练习会话本地数据源实现
public class PracticeLocalDataSource: LocalDataSourceProtocol {
    // 数据类型是练习会话
    public typealias T = PracticeSession
    
    // 标识符类型是UUID
    public typealias ID = UUID
    
    // 存储键
    private let storageKey = "practice_sessions"
    
    // 单例实例
    public static let shared = PracticeLocalDataSource()
    
    // 私有初始化方法，确保单例模式
    private init() {}
    
    /// 获取所有练习会话
    /// - Returns: 练习会话列表
    public func getAll() throws -> [PracticeSession] {
        guard let data = UserDefaults.standard.data(forKey: storageKey) else {
            return []
        }
        
        do {
            let sessions = try JSONDecoder().decode([PracticeSession].self, from: data)
            return sessions
        } catch {
            throw error
        }
    }
    
    /// 根据ID获取练习会话
    /// - Parameter id: 会话ID
    /// - Returns: 练习会话（如存在）
    public func getById(_ id: UUID) throws -> PracticeSession? {
        let sessions = try getAll()
        return sessions.first { $0.id == id }
    }
    
    /// 保存练习会话
    /// - Parameter entity: 要保存的练习会话
    /// - Returns: 保存后的练习会话
    public func save(_ entity: PracticeSession) throws -> PracticeSession {
        var sessions = try getAll()
        
        // 移除相同ID的现有会话
        sessions.removeAll { $0.id == entity.id }
        
        // 添加新会话
        sessions.append(entity)
        
        // 保存所有会话
        let data = try JSONEncoder().encode(sessions)
        UserDefaults.standard.set(data, forKey: storageKey)
        
        return entity
    }
    
    /// 保存多个练习会话
    /// - Parameter entities: 要保存的练习会话列表
    /// - Returns: 保存后的练习会话列表
    public func saveAll(_ entities: [PracticeSession]) throws -> [PracticeSession] {
        var sessions = try getAll()
        
        // 移除相同ID的现有会话
        for entity in entities {
            sessions.removeAll { $0.id == entity.id }
        }
        
        // 添加新会话
        sessions.append(contentsOf: entities)
        
        // 保存所有会话
        let data = try JSONEncoder().encode(sessions)
        UserDefaults.standard.set(data, forKey: storageKey)
        
        return entities
    }
    
    /// 删除练习会话
    /// - Parameter id: 要删除的会话ID
    /// - Returns: 删除是否成功
    public func delete(_ id: UUID) throws -> Bool {
        var sessions = try getAll()
        
        // 确保会话存在
        guard sessions.contains(where: { $0.id == id }) else {
            return false
        }
        
        // 移除会话
        sessions.removeAll { $0.id == id }
        
        // 保存更新后的会话列表
        let data = try JSONEncoder().encode(sessions)
        UserDefaults.standard.set(data, forKey: storageKey)
        
        return true
    }
    
    /// 获取未同步的练习会话
    /// - Returns: 未同步的练习会话列表
    public func getUnsyncedSessions() throws -> [PracticeSession] {
        let sessions = try getAll()
        return sessions.filter { !$0.isSynced }
    }
    
    /// 将会话标记为已同步
    /// - Parameter id: 会话ID
    /// - Returns: 操作是否成功
    public func markAsSynced(id: UUID) throws -> Bool {
        guard var session = try getById(id) else {
            return false
        }
        
        // 创建标记为已同步的新会话
        var updatedSession = session
        updatedSession.isSynced = true
        
        // 保存更新后的会话
        _ = try save(updatedSession)
        
        return true
    }
    
    /// 清除本地缓存的所有练习会话
    /// - Returns: 操作是否成功
    public func clearAll() -> Bool {
        UserDefaults.standard.removeObject(forKey: storageKey)
        return true
    }
} 