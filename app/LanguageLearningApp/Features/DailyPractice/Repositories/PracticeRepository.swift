import Foundation
import Combine

/// 练习会话仓库协议
/// 定义练习会话相关的业务方法
public protocol PracticeRepositoryProtocol: RepositoryProtocol where T == PracticeSession, ID == UUID {
    /// 获取用户的练习历史
    /// - Parameter userID: 用户ID
    /// - Returns: 包含练习历史的发布者
    func getPracticeHistory(userID: UUID) -> AnyPublisher<[PracticeSession], Error>

    /// 获取未同步的练习会话
    /// - Returns: 包含未同步练习会话的发布者
    func getUnsyncedSessions() -> AnyPublisher<[PracticeSession], Error>

    /// 将会话标记为已同步
    /// - Parameter id: 会话ID
    /// - Returns: 包含操作结果的发布者
    func markAsSynced(id: UUID) -> AnyPublisher<Bool, Error>

    /// 清除本地缓存的所有练习会话
    /// - Returns: 包含操作结果的发布者
    func clearLocalCache() -> AnyPublisher<Bool, Error>

    /// 同步所有未同步的练习会话
    /// - Returns: 包含同步结果的发布者
    func syncUnsyncedSessions() -> AnyPublisher<Bool, Error>

    /// 异步获取用户的练习历史
    /// - Parameter userID: 用户ID
    /// - Returns: 练习历史
    func getPracticeHistoryAsync(userID: UUID) async throws -> [PracticeSession]

    /// 异步获取未同步的练习会话
    /// - Returns: 未同步的练习会话
    func getUnsyncedSessionsAsync() async throws -> [PracticeSession]

    /// 异步将会话标记为已同步
    /// - Parameter id: 会话ID
    /// - Returns: 操作结果
    func markAsSyncedAsync(id: UUID) async throws -> Bool

    /// 异步清除本地缓存的所有练习会话
    /// - Returns: 操作结果
    func clearLocalCacheAsync() async throws -> Bool

    /// 异步同步所有未同步的练习会话
    /// - Returns: 同步结果
    func syncUnsyncedSessionsAsync() async throws -> Bool
}

/// 练习会话仓库实现
public class PracticeRepository: BaseRepository<PracticeSession, UUID, PracticeLocalDataSource, PracticeRemoteDataSource>, PracticeRepositoryProtocol {
    // 单例实例
    public static let shared = PracticeRepository(
        localDataSource: PracticeLocalDataSource.shared,
        remoteDataSource: PracticeRemoteDataSource.shared
    )

    /// 初始化方法
    /// - Parameters:
    ///   - localDataSource: 本地数据源
    ///   - remoteDataSource: 远程数据源
    public init(localDataSource: PracticeLocalDataSource, remoteDataSource: PracticeRemoteDataSource) {
        super.init(localDataSource: localDataSource, remoteDataSource: remoteDataSource)
    }

    /// 获取用户的练习历史
    /// - Parameter userID: 用户ID
    /// - Returns: 包含练习历史的发布者
    public func getPracticeHistory(userID: UUID) -> AnyPublisher<[PracticeSession], Error> {
        return getAll()
            .map { sessions in
                // 过滤出指定用户的会话并按完成时间排序
                return sessions
                    .filter { $0.userID == userID }
                    .sorted { $0.endTime > $1.endTime }
            }
            .eraseToAnyPublisher()
    }

    /// 获取未同步的练习会话
    /// - Returns: 包含未同步练习会话的发布者
    public func getUnsyncedSessions() -> AnyPublisher<[PracticeSession], Error> {
        return Future<[PracticeSession], Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.unknown))
                return
            }

            do {
                let unsyncedSessions = try self.localDataSource.getUnsyncedSessions()
                promise(.success(unsyncedSessions))
            } catch {
                promise(.failure(RepositoryError.localDataSourceError(error)))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 将会话标记为已同步
    /// - Parameter id: 会话ID
    /// - Returns: 包含操作结果的发布者
    public func markAsSynced(id: UUID) -> AnyPublisher<Bool, Error> {
        return Future<Bool, Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.unknown))
                return
            }

            do {
                let success = try self.localDataSource.markAsSynced(id: id)
                promise(.success(success))
            } catch {
                promise(.failure(RepositoryError.localDataSourceError(error)))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 清除本地缓存的所有练习会话
    /// - Returns: 包含操作结果的发布者
    public func clearLocalCache() -> AnyPublisher<Bool, Error> {
        return Future<Bool, Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.unknown))
                return
            }

            let success = self.localDataSource.clearAll()
            promise(.success(success))
        }
        .eraseToAnyPublisher()
    }

    /// 同步所有未同步的练习会话
    /// - Returns: 包含同步结果的发布者
    public func syncUnsyncedSessions() -> AnyPublisher<Bool, Error> {
        return getUnsyncedSessions()
            .flatMap { [weak self] unsyncedSessions -> AnyPublisher<[PracticeSession], Error> in
                guard let self = self, !unsyncedSessions.isEmpty else {
                    return Just([])
                        .setFailureType(to: Error.self)
                        .eraseToAnyPublisher()
                }

                return self.remoteDataSource.saveAll(unsyncedSessions)
            }
            .flatMap { [weak self] savedSessions -> AnyPublisher<Bool, Error> in
                guard let self = self, !savedSessions.isEmpty else {
                    return Just(true)
                        .setFailureType(to: Error.self)
                        .eraseToAnyPublisher()
                }

                // 创建一个发布者序列，为每个已保存的会话标记为已同步
                let markAsSyncedPublishers = savedSessions.map { self.markAsSynced(id: $0.id) }

                return Publishers.Sequence(sequence: markAsSyncedPublishers)
                    .flatMap(maxPublishers: .max(5)) { $0 }
                    .collect()
                    .map { results in
                        // 如果所有会话都成功标记为已同步，则返回true
                        return !results.contains(false)
                    }
                    .eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }

    // MARK: - 异步方法

    /// 异步获取用户的练习历史
    /// - Parameter userID: 用户ID
    /// - Returns: 练习历史
    public func getPracticeHistoryAsync(userID: UUID) async throws -> [PracticeSession] {
        let sessions = try await getAllAsync()

        // 过滤出指定用户的会话并按完成时间排序
        return sessions
            .filter { $0.userID == userID }
            .sorted { $0.endTime > $1.endTime }
    }

    /// 异步获取未同步的练习会话
    /// - Returns: 未同步的练习会话
    public func getUnsyncedSessionsAsync() async throws -> [PracticeSession] {
        return try localDataSource.getUnsyncedSessions()
    }

    /// 异步将会话标记为已同步
    /// - Parameter id: 会话ID
    /// - Returns: 操作结果
    public func markAsSyncedAsync(id: UUID) async throws -> Bool {
        return try localDataSource.markAsSynced(id: id)
    }

    /// 异步清除本地缓存的所有练习会话
    /// - Returns: 操作结果
    public func clearLocalCacheAsync() async throws -> Bool {
        return localDataSource.clearAll()
    }

    /// 异步同步所有未同步的练习会话
    /// - Returns: 同步结果
    public func syncUnsyncedSessionsAsync() async throws -> Bool {
        let unsyncedSessions = try await getUnsyncedSessionsAsync()

        guard !unsyncedSessions.isEmpty else {
            return true // 没有未同步的会话，视为同步成功
        }

        let savedSessions = try await remoteDataSource.saveAllAsync(unsyncedSessions)

        // 将保存的会话标记为已同步
        for session in savedSessions {
            _ = try await markAsSyncedAsync(id: session.id)
        }

        return true
    }
}