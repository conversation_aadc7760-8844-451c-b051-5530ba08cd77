import SwiftUI

struct ThemeSettingsView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @Environment(\.presentationMode) var presentationMode
    @State private var showingThemeChangedAlert = false
    @State private var previousTheme: ThemeMode?
    
    var body: some View {
        ZStack {
            StyledContainer {
                VStack(spacing: 24) {
                    // 标题和说明
                    VStack(spacing: 16) {
                        Text(localizationManager.localizedString(LocalizationKey.theme_settings))
                            .font(AppTheme.Typography.title1)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                        
                        Text(localizationManager.localizedString(LocalizationKey.theme_settings_subtitle))
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }
                    .padding(.top, 30)
                    
                    // 主题选项
                    VStack(spacing: 16) {
                        ForEach(ThemeMode.allCases, id: \.self) { theme in
                            ThemeOptionButton(
                                theme: theme,
                                isSelected: themeManager.currentTheme == theme,
                                action: {
                                    withAnimation {
                                        previousTheme = themeManager.currentTheme
                                        themeManager.currentTheme = theme
                                        showingThemeChangedAlert = true
                                    }
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    Spacer()
                }
            }
        }
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarItems(trailing: closeButton)
    }
    
    private var closeButton: some View {
        Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.title3)
                .foregroundColor(AppTheme.Colors.textSecondary)
        }
    }
}

struct ThemeOptionButton: View {
    let theme: ThemeMode
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(theme.displayName)
                    .font(AppTheme.Typography.body)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(AppTheme.Colors.primary)
                }
            }
            .padding()
            .background(AppTheme.Colors.card)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
        }
    }
}

struct ThemeSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        ThemeSettingsView()
    }
} 