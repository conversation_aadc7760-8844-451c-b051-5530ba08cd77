import Foundation
import SwiftUI

/// 依赖项错误
public enum DependencyError: Error, LocalizedError {
    case notRegistered(String)
    case creationFailed(String)

    public var errorDescription: String? {
        switch self {
        case .notRegistered(let key):
            return "依赖项未注册: \(key)"
        case .creationFailed(let key):
            return "无法创建依赖项: \(key)"
        }
    }
}

/// 依赖项工厂协议
public protocol DependencyFactory {
    /// 创建依赖项
    /// - Returns: 依赖项实例
    func create() -> Any
}

/// 类型擦除的依赖项工厂
private struct AnyDependencyFactory<T>: DependencyFactory {
    private let factory: () -> T

    init(factory: @escaping () -> T) {
        self.factory = factory
    }

    func create() -> Any {
        return factory()
    }
}

/// 依赖项容器
public class DependencyContainer {
    // 单例实例
    public static let shared = DependencyContainer()

    // 依赖项注册表
    private var factories: [String: DependencyFactory] = [:]

    // 单例实例注册表
    private var singletons: [String: Any] = [:]

    // 正在创建的实例集合（用于检测循环依赖）
    private var creatingInstances: Set<String> = []

    // 初始化方法为私有，确保单例模式
    private init() {}

    /// 注册依赖项
    /// - Parameters:
    ///   - type: 依赖项类型
    ///   - factory: 依赖项工厂
    public func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        print("🔧 [DependencyContainer] 注册依赖项: \(key)")
        factories[key] = AnyDependencyFactory(factory: factory)
        print("✅ [DependencyContainer] 成功注册依赖项: \(key)")
    }

    /// 注册单例依赖项
    /// - Parameters:
    ///   - type: 依赖项类型
    ///   - factory: 依赖项工厂
    public func registerSingleton<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        print("🔧 [DependencyContainer] 注册单例依赖项: \(key)")
        // 注册工厂而不是立即创建实例，避免循环依赖
        factories[key] = AnyDependencyFactory(factory: factory)
        print("✅ [DependencyContainer] 成功注册单例依赖项: \(key)")
    }

    /// 解析依赖项
    /// - Parameter type: 依赖项类型
    /// - Returns: 依赖项实例
    public func resolve<T>(_ type: T.Type) -> T {
        let key = String(describing: type)

        // 首先检查是否已注册为单例
        if let singleton = singletons[key] as? T {
            return singleton
        }

        // 检查是否正在创建中（防止无限循环）
        if creatingInstances.contains(key) {
            print("⚠️ [DependencyContainer] 检测到循环依赖: \(key)")
            print("📋 [DependencyContainer] 当前正在创建的实例:")
            for creatingKey in creatingInstances {
                print("   - \(creatingKey)")
            }
            fatalError("检测到循环依赖: \(key)")
        }

        // 然后检查是否已注册为工厂
        guard let factory = factories[key] else {
            print("❌ [DependencyContainer] 依赖项未注册: \(key)")
            print("📋 [DependencyContainer] 当前已注册的依赖项:")
            for registeredKey in factories.keys.sorted() {
                print("   - \(registeredKey)")
            }
            print("📋 [DependencyContainer] 当前已创建的单例:")
            for singletonKey in singletons.keys.sorted() {
                print("   - \(singletonKey)")
            }
            fatalError("依赖项未注册: \(key)")
        }

        // 标记正在创建
        creatingInstances.insert(key)

        guard let instance = factory.create() as? T else {
            creatingInstances.remove(key)
            print("❌ [DependencyContainer] 无法创建依赖项: \(key)")
            fatalError("无法创建依赖项: \(key)")
        }

        // 移除创建标记并缓存实例
        creatingInstances.remove(key)
        singletons[key] = instance

        return instance
    }

    /// 尝试解析依赖项（不抛出 fatalError）
    /// - Parameter type: 依赖项类型
    /// - Returns: 依赖项实例或 nil
    public func tryResolve<T>(_ type: T.Type) throws -> T {
        let key = String(describing: type)
        print("🔍 [DependencyContainer] 尝试安全解析依赖项: \(key)")

        // 首先检查是否已注册为单例
        if let singleton = singletons[key] as? T {
            print("✅ [DependencyContainer] 找到已存在的单例: \(key)")
            return singleton
        }

        // 然后检查是否已注册为工厂
        guard let factory = factories[key] else {
            print("❌ [DependencyContainer] 依赖项未注册: \(key)")
            print("📋 [DependencyContainer] 当前已注册的依赖项:")
            for registeredKey in factories.keys.sorted() {
                print("   - \(registeredKey)")
            }
            throw DependencyError.notRegistered(key)
        }

        print("🏭 [DependencyContainer] 创建新实例: \(key)")
        guard let instance = factory.create() as? T else {
            print("❌ [DependencyContainer] 无法创建依赖项: \(key)")
            throw DependencyError.creationFailed(key)
        }

        // 如果这是第一次创建，将其存储为单例
        singletons[key] = instance
        print("✅ [DependencyContainer] 成功创建并缓存单例: \(key)")

        return instance
    }
}

/// 依赖项注入容器的SwiftUI扩展
public extension DependencyContainer {
    /// 创建具有注入依赖项的视图
    /// - Parameters:
    ///   - type: 视图类型
    ///   - dependencies: 依赖项
    /// - Returns: 具有注入依赖项的视图
    func makeView<V: View, D>(_ type: V.Type, withDependency dependency: D) -> some View {
        let viewWithDependency = self.resolve(type) as? (D) -> V
        guard let view = viewWithDependency?(dependency) else {
            fatalError("无法创建视图: \(type)")
        }
        return view
    }
}

/// 依赖项提供器
@propertyWrapper
public struct Inject<T> {
    private let container: DependencyContainer
    public var wrappedValue: T

    public init(container: DependencyContainer = .shared) {
        self.container = container
        self.wrappedValue = container.resolve(T.self)
    }
}

/// 工厂依赖项提供器
@propertyWrapper
public struct InjectFactory<T, D> {
    private let container: DependencyContainer
    public var wrappedValue: (D) -> T

    public init(container: DependencyContainer = .shared) {
        self.container = container
        self.wrappedValue = { dependency in
            let factory = container.resolve(((D) -> T).self)
            return factory(dependency)
        }
    }
}