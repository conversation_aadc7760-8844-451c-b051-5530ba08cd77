#!/usr/bin/env swift

import Foundation

let filePath = "LanguageLearningApp/Features/Evaluation/Services/EvaluationService.swift"
let fileContent = try String(contentsOfFile: filePath, encoding: .utf8)

// Add the EvaluationAPIResponse parsing code before the existing data field parsing
let searchString = """
                // 尝试解析JSON并手动构建Evaluation
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    print("尝试手动解析JSON")

                    // 检查是否有data字段
"""

let replaceString = """
                // 尝试解析JSON并手动构建Evaluation
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    print("尝试手动解析JSON")
                    
                    // 尝试将整个JSON转换为EvaluationAPIResponse
                    let jsonData = try JSONSerialization.data(withJSONObject: json)
                    if let apiResponse = try? decoder.decode(EvaluationAPIResponse.self, from: jsonData) {
                        print("成功解码为EvaluationAPIResponse")
                        if let apiEvaluation = apiResponse.data {
                            let evaluation = apiEvaluation.toEvaluation()
                            print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                            return evaluation
                        } else {
                            print("EvaluationAPIResponse中data字段为nil")
                        }
                    } else {
                        print("无法解码为EvaluationAPIResponse")
                    }

                    // 检查是否有data字段
"""

let updatedContent = fileContent.replacingOccurrences(of: searchString, with: replaceString)

try updatedContent.write(toFile: filePath, atomically: true, encoding: .utf8)
print("File updated successfully")
