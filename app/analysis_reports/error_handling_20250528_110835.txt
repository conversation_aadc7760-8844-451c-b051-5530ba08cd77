=== 错误处理模式分析 ===

错误类型定义:
LanguageLearningApp/Features/DailyPractice/Services/PersonalizedLearningService.swift:enum PersonalizedLearningServiceError: Error {
LanguageLearningApp/Features/Lessons/DataSources/LessonRemoteDataSource.swift:public enum LessonRemoteDataSourceError: Error, LocalizedError {
LanguageLearningApp/Features/Lessons/DataSources/LessonLocalDataSource.swift:public enum LessonLocalDataSourceError: Error, LocalizedError {
LanguageLearningApp/Features/Lessons/Repositories/LessonRepository.swift:public enum LessonRepositoryError: Error, LocalizedError {
LanguageLearningApp/Features/Evaluation/Repositories/EvaluationRepository.swift:// public enum RepositoryError: Error, LocalizedError {
LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyDataSourceProtocols.swift:public enum VocabularyDataSourceError: Error, LocalizedError {
LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyLocalDataSource.swift:public enum VocabularyLocalDataSourceError: Error, LocalizedError {
LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyRemoteDataSource.swift:public enum VocabularyRemoteDataSourceError: Error, LocalizedError {
LanguageLearningApp/Features/Vocabulary/Repositories/VocabularyRepository.swift:public enum VocabularyRepositoryError: Error, LocalizedError {
LanguageLearningApp/Repositories/BaseRepository.swift:public enum RepositoryError: Error, Equatable {
LanguageLearningApp/Utilities/AppError.swift:enum AppError: Error, Equatable {
LanguageLearningApp/Utilities/AppError.swift:enum ErrorSeverity {
LanguageLearningApp/API/NetworkError.swift:public enum NetworkError: Error {
LanguageLearningApp/Services/TTS/SystemTTSEngine.swift:enum TTSError: Error {
LanguageLearningApp/Services/TTS/TTSManager.swift:    public enum TTSError: Error {

错误处理使用:
     905
