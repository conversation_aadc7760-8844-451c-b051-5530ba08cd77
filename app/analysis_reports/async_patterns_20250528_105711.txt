=== 异步模式使用分析 ===

Combine Publisher 使用:
     393
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    func getPracticeHistory(userID: UUID) -> AnyPublisher<[PracticeSession], Error>
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    func getUnsyncedSessions() -> AnyPublisher<[PracticeSession], Error>
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    func markAsSynced(id: UUID) -> AnyPublisher<Bool, Error>
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    func clearLocalCache() -> AnyPublisher<Bool, Error>
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    func syncUnsyncedSessions() -> AnyPublisher<Bool, Error>
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    public func getPracticeHistory(userID: UUID) -> AnyPublisher<[PracticeSession], Error> {
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:            .eraseToAnyPublisher()
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    public func getUnsyncedSessions() -> AnyPublisher<[PracticeSession], Error> {
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:        .eraseToAnyPublisher()
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    public func markAsSynced(id: UUID) -> AnyPublisher<Bool, Error> {

async/await 使用:
     516
LanguageLearningApp/Services/TTS/TTSManager.swift:    // New async function to update engine based on availability
