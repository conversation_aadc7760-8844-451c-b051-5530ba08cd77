=== 单例模式使用分析 ===

发现的单例使用:
LanguageLearningApp/ViewModels/AchievementViewModel.swift:    private let apiDataSource = APIDataSourceManager.shared
LanguageLearningApp/ViewModels/AchievementViewModel.swift:        networkService: NetworkServiceProtocol = DependencyContainer.shared.resolve(NetworkServiceProtocol.self),
LanguageLearningApp/ViewModels/AchievementViewModel.swift:        userManager: UserManager = DependencyContainer.shared.resolve(UserManager.self)
LanguageLearningApp/ViewModels/WordLearningViewModel.swift:        networkService: NetworkServiceProtocol = NetworkService.shared,
LanguageLearningApp/ViewModels/WordLearningViewModel.swift:        userManager: any UserManagerProtocol = UserManager.shared,
LanguageLearningApp/ViewModels/WordLearningViewModel.swift:        errorManager: ErrorManager = ErrorManager.shared,
LanguageLearningApp/ViewModels/WordLearningViewModel.swift:        ttsManager: TTSManager = TTSManager.shared
LanguageLearningApp/ViewModels/SpeechRecognitionManager.swift:            let audioSession = AVAudioSession.sharedInstance()
LanguageLearningApp/ViewModels/AchievementManager.swift:        userManager: any UserManagerProtocol = DependencyContainer.shared.resolve(UserManager.self),
LanguageLearningApp/ViewModels/AchievementManager.swift:        lessonManager: any LessonManagerProtocol = DependencyContainer.shared.resolve(LessonManager.self),
LanguageLearningApp/ViewModels/AchievementManager.swift:        storageManager: StorageManagerProtocol = DependencyContainer.shared.resolve(StorageManagerProtocol.self),
LanguageLearningApp/ViewModels/AchievementManager.swift:        networkService: NetworkServiceProtocol = DependencyContainer.shared.resolve(NetworkServiceProtocol.self),
LanguageLearningApp/ViewModels/AchievementManager.swift:        errorManager: ErrorManager = ErrorManager.shared
LanguageLearningApp/ViewModels/DailyPracticeViewModel.swift:    private let apiDataSource = APIDataSourceManager.shared
LanguageLearningApp/ViewModels/DailyPracticeViewModel.swift:        vocabularyManager: VocabularyManager = VocabularyManager.shared,
LanguageLearningApp/ViewModels/DailyPracticeViewModel.swift:        errorManager: ErrorManager = ErrorManager.shared,
LanguageLearningApp/ViewModels/DailyPracticeViewModel.swift:        practiceManager: PracticeManager = DependencyContainer.shared.resolve(PracticeManager.self)
LanguageLearningApp/ViewModels/ListeningViewModel.swift:        self.networkService = networkService ?? NetworkService.shared
LanguageLearningApp/ViewModels/ListeningViewModel.swift:        self.errorManager = errorManager ?? ErrorManager.shared
LanguageLearningApp/ViewModels/ListeningViewModel.swift:        self.userManager = userManager ?? UserManager.shared

静态单例定义:
LanguageLearningApp/ViewModels/AchievementManager.swift:    static let shared = AchievementManager()
LanguageLearningApp/ViewModels/ErrorManager.swift:    static let shared = ErrorManager()
LanguageLearningApp/DI/DependencyContainer.swift:    public static let shared = DependencyContainer()
LanguageLearningApp/DI/DependencyRegistry.swift:            // if it's already MainActor isolated by its own static let shared property.
LanguageLearningApp/Core/Sync/DataSyncManager.swift:    public static let shared = DataSyncManager()
LanguageLearningApp/Config/MockDataProvider.swift:    static let shared = MockDataProvider()
LanguageLearningApp/Managers/MockAccountManager.swift:    static let shared = MockAccountManager()
LanguageLearningApp/Managers/APIDataSourceManager.swift:    public static let shared = APIDataSourceManager()
LanguageLearningApp/Features/DailyPractice/Utils/AudioManager.swift:    static let shared = AudioManager()
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:    public static let shared = PracticeRepository(
LanguageLearningApp/Features/DailyPractice/Services/PracticeManager.swift:    public static let shared = PracticeManager(repository: PracticeRepository.shared)
LanguageLearningApp/Features/DailyPractice/Services/PersonalizedLearningService.swift:    static let shared = PersonalizedLearningService(apiClient: APIClient.shared)
LanguageLearningApp/Features/DailyPractice/Services/PracticeLocalDataSource.swift:    public static let shared = PracticeLocalDataSource()
LanguageLearningApp/Features/DailyPractice/Services/PracticeRemoteDataSource.swift:    public static let shared = PracticeRemoteDataSource(apiClient: DependencyContainer.shared.resolve(APIClientProtocol.self))
LanguageLearningApp/Features/DailyPractice/Services/DailyPracticeStatsService.swift:    public static let shared = DailyPracticeStatsService()
LanguageLearningApp/Features/User/Repositories/UserRepository.swift:    public static let shared = UserRepository()
LanguageLearningApp/Features/User/Services/UserManager.swift:    public static let shared = UserManager()
LanguageLearningApp/Features/User/Services/UserLocalDataSource.swift:    public static let shared = UserLocalDataSource()
LanguageLearningApp/Features/User/Services/UserRemoteDataSource.swift:    public static let shared = UserRemoteDataSource()
LanguageLearningApp/Features/Lessons/Repositories/LessonRepository.swift:    public static let shared = LessonRepository()
LanguageLearningApp/Features/Lessons/Services/LessonManager.swift:    public static let shared = LessonManager()
LanguageLearningApp/Features/Evaluation/Services/EvaluationRemoteDataSource.swift:    public static let shared = EvaluationRemoteDataSource(apiClient: DependencyContainer.shared.resolve(APIClientProtocol.self))
LanguageLearningApp/Features/Evaluation/Services/EvaluationLocalDataSource.swift:    public static let shared = EvaluationLocalDataSource()
LanguageLearningApp/Features/Vocabulary/Repositories/VocabularyRepository.swift:    public static let shared = VocabularyRepository()
LanguageLearningApp/Features/Vocabulary/Services/VocabularyManager.swift:    public static let shared = VocabularyManager()
LanguageLearningApp/Shared/NetworkMonitor.swift:    public static let shared = NetworkMonitor()
LanguageLearningApp/Utilities/Logger.swift:    public static let shared = Logger()
LanguageLearningApp/Utilities/LocalizationManager.swift:    static let shared = LocalizationManager()
LanguageLearningApp/Theme/AppTheme.swift:    static let shared = ThemeManager()
LanguageLearningApp/API/APIClient.swift:    public static let shared = APIClient()
LanguageLearningApp/API/NetworkService.swift:    public static let shared = NetworkService()
LanguageLearningApp/API/NetworkAdapter.swift:    public static let shared = NetworkAdapter()
LanguageLearningApp/Data/CoreDataManager.swift:    public static let shared = CoreDataManager()
LanguageLearningApp/Services/StorageManager.swift:    public static let shared = StorageManager()
LanguageLearningApp/Services/TTS/TTSManager.swift:    static let shared = TTSManager()
