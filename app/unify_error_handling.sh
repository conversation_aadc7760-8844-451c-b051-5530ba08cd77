#!/bin/bash

echo "🔧 开始统一错误处理..."

# 创建备份目录
BACKUP_DIR="backups/error_handling_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 备份文件到: $BACKUP_DIR"

# 需要修改的文件列表
FILES_TO_UPDATE=(
    "LanguageLearningApp/Views/LoginView.swift"
    "LanguageLearningApp/Views/RegisterView.swift"
    "LanguageLearningApp/Views/ForgotPasswordView.swift"
    "LanguageLearningApp/Views/ContentView.swift"
    "LanguageLearningApp/Main/MainTabView.swift"
)

# 备份文件
echo "📋 备份需要修改的文件..."
for file in "${FILES_TO_UPDATE[@]}"; do
    if [ -f "$file" ]; then
        mkdir -p "$BACKUP_DIR/$(dirname "$file")"
        cp "$file" "$BACKUP_DIR/$file"
        echo "  备份: $file"
    fi
done

echo ""
echo "🎯 统一错误处理策略："
echo "1. 移除视图中的自定义错误显示逻辑"
echo "2. 使用统一的 ErrorManager"
echo "3. 添加 .withErrorHandling() 修饰符"
echo "4. 将错误消息通过 ErrorManager 显示"

echo ""
echo "✅ 备份完成！"
echo "📝 接下来需要手动修改以下文件："
for file in "${FILES_TO_UPDATE[@]}"; do
    if [ -f "$file" ]; then
        echo "  - $file"
    fi
done

echo ""
echo "🔧 修改指南："
echo "1. 移除 @State private var errorMessage = \"\" 变量"
echo "2. 移除自定义错误显示 UI"
echo "3. 在错误处理中使用 errorManager.showError() 替代设置 errorMessage"
echo "4. 在视图根部添加 .withErrorHandling() 修饰符"

echo ""
echo "📖 示例修改："
echo "// 旧方式："
echo "errorMessage = \"登录失败\""
echo ""
echo "// 新方式："
echo "errorManager.showError(.authenticationFailed(\"登录失败\"))"
