Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -scheme LanguageLearningApp -destination "platform=iOS Simulator,name=iPhone 16" build

User defaults from command line:
    IDEPackageSupportUseBuiltinSCM = YES

Resolve Package Graph


Resolved source packages:
  SwiftyBeaver: https://github.com/SwiftyBeaver/SwiftyBeaver @ 2.1.1

Prepare packages

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (3 targets)
    Target 'LanguageLearningApp' in project 'LanguageLearningApp'
        ➜ Explicit dependency on target 'SwiftyBeaver' in project 'SwiftyBeaver'
    Target 'SwiftyBeaver' in project 'SwiftyBeaver'
        ➜ Explicit dependency on target '<PERSON>yBeaver' in project 'SwiftyBeaver'
    Target 'SwiftyBeaver' in project 'SwiftyBeaver' (no dependencies)

GatherProvisioningInputs
2025-05-28 12:58:48.613 xcodebuild[50797:*********]  DVTDeveloperAccountManager: Failed to load <NAME_EMAIL>: Error Domain=DVTDeveloperAccountCredentialsError Code=0 "Invalid credentials in <NAME_EMAIL>, missing Xcode-Token" UserInfo={NSLocalizedDescription=Invalid credentials in <NAME_EMAIL>, missing Xcode-Token}

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/momc --dry-run --action generate --swift-version 5.0 --sdkroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk --iphonesimulator-deployment-target 18.0 --module LanguageLearningApp /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Data/LanguageLearningApp.xcdatamodeld /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --print-asset-tag-combinations --output-format xml1 /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Resources/Colors/Colors.xcassets /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Assets.xcassets

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

Build description signature: df78a10f9dae82e5bd3687be5ebf1f47
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/XCBuildData/df78a10f9dae82e5bd3687be5ebf1f47.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.SwiftFileList (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.SwiftFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.SwiftConstValuesFileList (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.SwiftConstValuesFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.LinkFileList (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.LinkFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-OutputFileMap.json (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-OutputFileMap.json

ProcessProductPackaging /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app-Simulated.xcent (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    
    Entitlements:
    
    {
    "application-identifier" = "RST57S693T.com.hunterx.LanguageLearningApp";
}
    
    builtin-productPackagingUtility /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app-Simulated.xcent

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app-Simulated.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app-Simulated.xcent.der (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app-Simulated.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app-Simulated.xcent.der --raw

ProcessProductPackaging /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app.xcent (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    
    Entitlements:
    
    {
}
    
    builtin-productPackagingUtility /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app.xcent

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app.xcent.der (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp.app.xcent.der --raw

SwiftDriver LanguageLearningApp normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name LanguageLearningApp -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.SwiftFileList -DDEBUG -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -target x86_64-apple-ios18.0-simulator -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Index.noindex/DataStore -enable-experimental-feature OpaqueTypeErasure -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64 -c -j16 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp-76fc4419f69b09e4895c7435382537e3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-Swift.h -working-directory /Users/<USER>/Documents/workspace/hunter/languagelearning/app -experimental-emit-module-separately -disable-cmo

SwiftCompile normal x86_64 Compiling\ LanguageLearningApp.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.swift (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    builtin-swiftTaskExecution -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/sources-1 -primary-file /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64 -target x86_64-apple-ios18.0-simulator -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-experimental-feature OpaqueTypeErasure -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Documents/workspace/hunter/languagelearning/app -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Documents/workspace/hunter/languagelearning/app -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp-76fc4419f69b09e4895c7435382537e3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources -Xcc -DDEBUG\=1 -module-name LanguageLearningApp -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.1 -target-sdk-name iphonesimulator18.1 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.o -index-unit-output-path /LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Index.noindex/DataStore -index-system-modules

SwiftCompile normal x86_64 /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.swift (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -c -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/sources-1 -primary-file /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64 -target x86_64-apple-ios18.0-simulator -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-experimental-feature OpaqueTypeErasure -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Documents/workspace/hunter/languagelearning/app -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Documents/workspace/hunter/languagelearning/app -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp-76fc4419f69b09e4895c7435382537e3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources -Xcc -DDEBUG\=1 -module-name LanguageLearningApp -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.1 -target-sdk-name iphonesimulator18.1 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.o -index-unit-output-path /LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-980d48e935fbc301aea32b79fcaa62c4.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Index.noindex/DataStore -index-system-modules
/Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Services/LessonManager.swift:30:73: warning: main actor-isolated static property 'shared' can not be referenced from a nonisolated context; this is an error in the Swift 6 language mode
    public init(repository: LessonRepositoryProtocol = LessonRepository.shared) {
                                                                        ^
/Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Repositories/LessonRepository.swift:37:23: note: static property declared here
    public static let shared = LessonRepository()
                      ^

SwiftEmitModule normal x86_64 Emitting\ module\ for\ LanguageLearningApp (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    builtin-swiftTaskExecution -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -emit-module -experimental-skip-non-inlinable-function-bodies-without-types /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/APIClient.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/APIEndpoint.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/APIResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/APIResponseWrapper.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/CustomURLSessionDelegate.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkAdapter.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkError.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkService.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkServiceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkTypes.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Components/LanguageComponents.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Components/UIComponents.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Config/AppEnvironment.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Config/GlobalImports.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Config/MockDataProvider.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Core/Network/APIEndpoint+Extensions.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Core/Sync/DataSyncManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Data/CoreDataManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/DI/DependencyContainer.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/DI/DependencyRegistry.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/DI/ViewModelFactory.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Extensions/Color+Hex.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Common/Views/ExerciseContentView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Common/Views/UnifiedFeedbackOverlayView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Common/Views/UnifiedMultipleChoiceView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/DailyPractice.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/DailyPracticeStats.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/InitiatePersonalizedLearningResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/PracticeExercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/PracticeResult.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/PracticeSession.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/DailyPracticeStatsService.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PersonalizedLearningService.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PersonalizedLearningServiceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PracticeLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PracticeManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PracticeRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Utils/AudioManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Utils/ExerciseDataProcessor.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/ViewModels/DailyPracticeDashboardViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/ViewModels/PersonalizedPracticeViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/ViewModels/ProgressTrackingViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/DailyPracticeListeningView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/DailyPracticeSpeakingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/FeedbackOverlayView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/FillInView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/HintView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/MatchingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/MultipleChoiceView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/OrderingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/ReadingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/WritingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/DailyPracticeDashboardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/InitiatePersonalizedLearningView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/PracticeCompletionView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/PracticeSessionView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/ProgressTrackingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/Evaluation.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationAnswer.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationAPIResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationQuestion+Extensions.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationResult.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationResultAPIResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationResultListResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/PersonalizedLearningPath.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Repositories/EvaluationRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationLocalDataSourceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationRemoteDataSourceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationEntryViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationResultViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationEntryView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationHistoryView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationIntroView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationQuestionView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationResultLoadingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationResultView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/LearningPathView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/StartNewEvaluationView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/DataSources/LessonLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/DataSources/LessonRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Models/Lesson.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Models/LessonCategoryModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Models/LessonProgress.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Repositories/LessonRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Services/LessonManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Settings/Views/ThemeSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Models/User.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Models/UserSettings.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Models/UserStats.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Repositories/UserRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserLocalDataSourceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserRemoteDataSourceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyDataSourceProtocols.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/Models/WordProgress.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/Repositories/VocabularyRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/Services/VocabularyManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Main/MainTabView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Managers/APIDataSourceManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Managers/MockAccountManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/Achievement.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/Exercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/ExerciseResults.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/GrammarExercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/ListeningExercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/PracticeCardModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/SpeakingExercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/StorageDataType.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/TTSSettings.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/TTSVoice.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/UserAchievement.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/Word.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/AchievementManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/ErrorManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/EvaluationManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/LessonManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/LocalizationManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/PracticeManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/RepositoryProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/SpeechRecognitionManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/StorageManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/ThemeManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/TTSManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/UserManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/VocabularyManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Repositories/BaseRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/AcapelaTTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/AppleNeuralTTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/KokoroTTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/PlaceholderTTSEngines.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/ResembleAITTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/SystemTTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/TTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/TTSManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/StorageManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Shared/NetworkMonitor.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/CommonTypes.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/EvaluationResults.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/LearningPathTypes.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/LessonTypes.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/SendableColor.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Theme/AppTheme.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/AppearanceManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/AppError.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/ErrorHandler.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/LocalizationManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/Logger.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/NotificationNames.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/AchievementManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/AchievementViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/DailyPracticeViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/ErrorManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/GrammarViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/ListeningViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/SpeakingViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/SpeechRecognitionManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/WordLearningViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/CardContentView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/CardDetailsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/ExerciseCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/GrammarCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/ListeningCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/PracticeCardContainerView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/SpeakingCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/WordCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/AchievementCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/EnvironmentIndicator.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/ErrorView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/LearningGoalRow.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/SecretTapGesture.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/StyledFormComponents.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/SwipeCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Lesson/LessonEditView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Lesson/LessonExerciseView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Lesson/LessonStartView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Modifiers/GyroscopeModifier.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/AboutView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/AccountSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/EnvironmentSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/KokoroTTSTestView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/LanguageSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/NotificationSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/SettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/TTSSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/TTSTestView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/AchievementView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/AppSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ContentView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/DailyPracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/EnhancedLessonDetailView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ExerciseView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ForgotPasswordView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/GrammarPracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/LessonDetailView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/LessonListView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ListeningChallengeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ListeningPracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ListeningView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/LoginView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/LogoView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/PracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ProfileView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/RegisterView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/SpeakingExerciseView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/SpeakingPracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/SpeakingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/WordLearningView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserEntity+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserEntity+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserSettingsEntity+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserSettingsEntity+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserStatsEntity+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserStatsEntity+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/LanguageLearningApp+CoreDataModel.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/GeneratedAssetSymbols.swift -target x86_64-apple-ios18.0-simulator -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-experimental-feature OpaqueTypeErasure -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Documents/workspace/hunter/languagelearning/app -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Documents/workspace/hunter/languagelearning/app -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp-76fc4419f69b09e4895c7435382537e3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources -Xcc -DDEBUG\=1 -module-name LanguageLearningApp -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.1 -target-sdk-name iphonesimulator18.1 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -emit-module-doc-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.swiftdoc -emit-module-source-info-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.swiftsourceinfo -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-Swift.h -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-master-emit-module.dia -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-master-emit-module.d -o /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.swiftmodule -emit-abi-descriptor-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.abi.json
EmitSwiftModule normal x86_64 (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
    cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -emit-module -experimental-skip-non-inlinable-function-bodies-without-types /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/APIClient.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/APIEndpoint.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/APIResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/APIResponseWrapper.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/CustomURLSessionDelegate.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkAdapter.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkError.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkService.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkServiceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/API/NetworkTypes.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Components/LanguageComponents.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Components/UIComponents.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Config/AppEnvironment.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Config/GlobalImports.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Config/MockDataProvider.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Core/Network/APIEndpoint+Extensions.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Core/Sync/DataSyncManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Data/CoreDataManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/DI/DependencyContainer.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/DI/DependencyRegistry.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/DI/ViewModelFactory.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Extensions/Color+Hex.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Common/Views/ExerciseContentView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Common/Views/UnifiedFeedbackOverlayView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Common/Views/UnifiedMultipleChoiceView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/DailyPractice.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/DailyPracticeStats.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/InitiatePersonalizedLearningResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/PracticeExercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/PracticeResult.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Models/PracticeSession.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/DailyPracticeStatsService.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PersonalizedLearningService.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PersonalizedLearningServiceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PracticeLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PracticeManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Services/PracticeRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Utils/AudioManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Utils/ExerciseDataProcessor.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/ViewModels/DailyPracticeDashboardViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/ViewModels/PersonalizedPracticeViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/ViewModels/ProgressTrackingViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/DailyPracticeListeningView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/DailyPracticeSpeakingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/FeedbackOverlayView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/FillInView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/HintView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/MatchingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/MultipleChoiceView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/OrderingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/ReadingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/Components/WritingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/DailyPracticeDashboardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/InitiatePersonalizedLearningView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/PracticeCompletionView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/PracticeSessionView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/DailyPractice/Views/ProgressTrackingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/Evaluation.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationAnswer.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationAPIResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationQuestion+Extensions.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationResult.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationResultAPIResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/EvaluationResultListResponse.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Models/PersonalizedLearningPath.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Repositories/EvaluationRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationLocalDataSourceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Services/EvaluationRemoteDataSourceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationEntryViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationResultViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationEntryView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationHistoryView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationIntroView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationQuestionView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationResultLoadingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationResultView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/EvaluationView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/LearningPathView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Evaluation/Views/StartNewEvaluationView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/DataSources/LessonLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/DataSources/LessonRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Models/Lesson.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Models/LessonCategoryModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Models/LessonProgress.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Repositories/LessonRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Lessons/Services/LessonManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Settings/Views/ThemeSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Models/User.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Models/UserSettings.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Models/UserStats.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Repositories/UserRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserLocalDataSourceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/User/Services/UserRemoteDataSourceProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyDataSourceProtocols.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyLocalDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyRemoteDataSource.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/Models/WordProgress.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/Repositories/VocabularyRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Features/Vocabulary/Services/VocabularyManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Main/MainTabView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Managers/APIDataSourceManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Managers/MockAccountManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/Achievement.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/Exercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/ExerciseResults.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/GrammarExercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/ListeningExercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/PracticeCardModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/SpeakingExercise.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/StorageDataType.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/TTSSettings.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/TTSVoice.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/UserAchievement.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Models/Word.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/AchievementManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/ErrorManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/EvaluationManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/LessonManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/LocalizationManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/PracticeManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/RepositoryProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/SpeechRecognitionManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/StorageManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/ThemeManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/TTSManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/UserManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Protocols/VocabularyManagerProtocol.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Repositories/BaseRepository.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/AcapelaTTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/AppleNeuralTTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/KokoroTTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/PlaceholderTTSEngines.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/ResembleAITTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/SystemTTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/TTSEngine.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/TTS/TTSManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Services/StorageManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Shared/NetworkMonitor.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/CommonTypes.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/EvaluationResults.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/LearningPathTypes.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/LessonTypes.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/SharedModels/SendableColor.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Theme/AppTheme.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/AppearanceManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/AppError.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/ErrorHandler.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/LocalizationManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/Logger.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Utilities/NotificationNames.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/AchievementManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/AchievementViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/DailyPracticeViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/ErrorManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/GrammarViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/ListeningViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/SpeakingViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/SpeechRecognitionManager.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/ViewModels/WordLearningViewModel.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/CardContentView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/CardDetailsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/ExerciseCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/GrammarCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/ListeningCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/PracticeCardContainerView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/SpeakingCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Cards/WordCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/AchievementCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/EnvironmentIndicator.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/ErrorView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/LearningGoalRow.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/SecretTapGesture.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/StyledFormComponents.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Components/SwipeCardView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Lesson/LessonEditView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Lesson/LessonExerciseView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Lesson/LessonStartView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Modifiers/GyroscopeModifier.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/AboutView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/AccountSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/EnvironmentSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/KokoroTTSTestView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/LanguageSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/NotificationSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/SettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/TTSSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/Settings/TTSTestView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/AchievementView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/AppSettingsView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ContentView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/DailyPracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/EnhancedLessonDetailView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ExerciseView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ForgotPasswordView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/GrammarPracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/LessonDetailView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/LessonListView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ListeningChallengeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ListeningPracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ListeningView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/LoginView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/LogoView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/PracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/ProfileView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/RegisterView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/SpeakingExerciseView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/SpeakingPracticeView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/SpeakingView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/Views/WordLearningView.swift /Users/<USER>/Documents/workspace/hunter/languagelearning/app/LanguageLearningApp/LanguageLearningApp.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserEntity+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserEntity+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserSettingsEntity+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserSettingsEntity+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserStatsEntity+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/UserStatsEntity+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/CoreDataGenerated/LanguageLearningApp/LanguageLearningApp+CoreDataModel.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/GeneratedAssetSymbols.swift -target x86_64-apple-ios18.0-simulator -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-experimental-feature OpaqueTypeErasure -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Documents/workspace/hunter/languagelearning/app -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Documents/workspace/hunter/languagelearning/app -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp-76fc4419f69b09e4895c7435382537e3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/LanguageLearningApp-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/DerivedSources -Xcc -DDEBUG\=1 -module-name LanguageLearningApp -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.1 -target-sdk-name iphonesimulator18.1 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -emit-module-doc-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.swiftdoc -emit-module-source-info-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.swiftsourceinfo -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-Swift.h -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-master-emit-module.dia -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-master-emit-module.d -o /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.swiftmodule -emit-abi-descriptor-path /Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp.abi.json
In flight operation:   <DVTOperationGroup: 0x7ff441ad8940; state = AREfc>
     <IDEBuildOperationGroup: 0x7ff4e27d5880; state = AREfc>
         <DVTOperationGroup: 0x7ff4e27d7aa0; state = AREfc>
             <IDEXCBuildSupportCore.IDEXCBuildServiceBuildOperation:0x7ff441a3a830:AREfc>
         <DVTOperationGroup: 0x7ff441ad4100; state = AReFc>
             <IDESwiftPackageCore.IDESwiftPackagePreambleCompletionOperation: 0x7ff441ad2650; state = AReFc>
         <DVTOperation: 0x7ff441ad6d20; state = aReFc>
         <__DVTSwiftAsyncOperation: 0x7ff441ad5a30; state = AReFc>
         <__DVTSwiftAsyncOperation: 0x7ff441ad6030; state = AReFc>
         <__DVTSwiftAsyncOperation: 0x7ff441ad5d40; state = AReFc>
         <DVTOperation: 0x7ff441ad6a90; state = aReFc>
         <__DVTSwiftAsyncOperation: 0x7ff441ad4de0; state = AReFc>
         <DVTOperation: 0x7ff441ad6fc0; state = arefc>
         <DVTOperation: 0x7ff441ad77a0; state = arefc>
<DVTOperationGroup: 0x7ff441ad8940; state = AREfc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   -[DVTOperationGroup init] (in DVTFoundation)
  2   +[DVTOperationGroup operationGroupWithSuboperations:] (in DVTFoundation)
  3   -[IDEScheme _groupAndImposeDependenciesForOrderedOperations:] (in IDEFoundation)
  4   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  5   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  6   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  7   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
  8   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
  9   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 10   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 12   XcodeBuildMain (in libxcodebuildLoader.dylib)
 13   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 14   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 15   main (in xcodebuild)
 16   start (in dyld)

<IDEBuildOperationGroup: 0x7ff4e27d5880; state = AREfc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   -[DVTOperationGroup init] (in DVTFoundation)
  2   -[IDEBuildOperationGroup initWithBuildOperations:otherOperations:buildLog:] (in IDEFoundation)
  3   -[IDEBuildOperationGroup initWithBuildOperations:buildLog:] (in IDEFoundation)
  4   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  5   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  6   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  7   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  8   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
  9   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 10   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 13   XcodeBuildMain (in libxcodebuildLoader.dylib)
 14   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 15   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 16   main (in xcodebuild)
 17   start (in dyld)

<DVTOperationGroup: 0x7ff4e27d7aa0; state = AREfc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   -[DVTOperationGroup init] (in DVTFoundation)
  2   +[DVTOperationGroup operationGroupWithSuboperations:] (in DVTFoundation)
  3   -[IDEBuildOperationGroup initWithBuildOperations:otherOperations:buildLog:] (in IDEFoundation)
  4   -[IDEBuildOperationGroup initWithBuildOperations:buildLog:] (in IDEFoundation)
  5   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  6   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  7   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  8   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  9   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
 10   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 13   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 14   XcodeBuildMain (in libxcodebuildLoader.dylib)
 15   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 16   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 17   main (in xcodebuild)
 18   start (in dyld)

<IDEXCBuildSupportCore.IDEXCBuildServiceBuildOperation:0x7ff441a3a830:AREfc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   -[IDEBuildOperation initWithConfiguration:] (in IDEFoundation)
  2   specialized IDEXCBuildServiceBuildOperation.init(configuration:) (in IDEXCBuildSupportCore)
  3   @objc IDEXCBuildServiceBuildOperation.init(configuration:) (in IDEXCBuildSupportCore)
  4   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  5   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  6   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  7   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  8   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
  9   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 10   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 13   XcodeBuildMain (in libxcodebuildLoader.dylib)
 14   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 15   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 16   main (in xcodebuild)
 17   start (in dyld)

<DVTOperationGroup: 0x7ff441ad4100; state = AReFc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   -[DVTOperationGroup init] (in DVTFoundation)
  2   specialized DVTOperationGroup.init<A>(suboperations:) (in DVTFoundation)
  3   DVTOperationGroup.init<A>(suboperations:) (in DVTFoundation)
  4   specialized IDESwiftWorkspace.schemeOperationPreambleOperationGroup(for:schemeOperationParameters:buildables:buildParameters:logRecorder:) (in IDESwiftPackageCore)
  5   @objc IDESwiftWorkspace.schemeOperationPreambleOperationGroup(for:schemeOperationParameters:buildables:buildParameters:logRecorder:) (in IDESwiftPackageCore)
  6   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  7   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  8   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  9   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
 10   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
 11   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 13   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 14   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 15   XcodeBuildMain (in libxcodebuildLoader.dylib)
 16   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 17   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 18   main (in xcodebuild)
 19   start (in dyld)

<IDESwiftPackageCore.IDESwiftPackagePreambleCompletionOperation: 0x7ff441ad2650; state = AReFc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   @objc DVTAsyncOperation.init() (in DVTFoundation)
  2   specialized IDESwiftWorkspace.schemeOperationPreambleOperationGroup(for:schemeOperationParameters:buildables:buildParameters:logRecorder:) (in IDESwiftPackageCore)
  3   @objc IDESwiftWorkspace.schemeOperationPreambleOperationGroup(for:schemeOperationParameters:buildables:buildParameters:logRecorder:) (in IDESwiftPackageCore)
  4   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  5   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  6   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  7   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  8   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
  9   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 10   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 13   XcodeBuildMain (in libxcodebuildLoader.dylib)
 14   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 15   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 16   main (in xcodebuild)
 17   start (in dyld)

<DVTOperation: 0x7ff441ad6d20; state = aReFc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   +[DVTOperation(DVTConveniences) operationWithBlock:] (in DVTFoundation)
  2   -[IDESchemeAction setUpActionDependenciesForCorePhaseOperation:shouldRunPostActionsBlock:prePhaseEnvironmentPopulationBlock:postPhaseEnvironmentPopulationBlock:handlePreActionsActivityLogSection:handlePostActionsActivityLogSection:buildParameters:schemeActionResultOperation:error:] (in IDEFoundation)
  3   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  4   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  5   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  6   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  7   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
  8   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
  9   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 10   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 12   XcodeBuildMain (in libxcodebuildLoader.dylib)
 13   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 14   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 15   main (in xcodebuild)
 16   start (in dyld)

<__DVTSwiftAsyncOperation: 0x7ff441ad5a30; state = AReFc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   @objc DVTAsyncOperation.init() (in DVTFoundation)
  2   @objc static DVTOperation.operationWithMainThreadBlock(_:) (in DVTFoundation)
  3   -[IDESchemeAction _setUpOperationsForPackageLoadingInWorkspace:handlePreActionsActivityLogSection:isCleanOperation:] (in IDEFoundation)
  4   -[IDESchemeAction setUpActionDependenciesForCorePhaseOperation:shouldRunPostActionsBlock:prePhaseEnvironmentPopulationBlock:postPhaseEnvironmentPopulationBlock:handlePreActionsActivityLogSection:handlePostActionsActivityLogSection:buildParameters:schemeActionResultOperation:error:] (in IDEFoundation)
  5   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  6   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  7   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  8   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  9   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
 10   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 13   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 14   XcodeBuildMain (in libxcodebuildLoader.dylib)
 15   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 16   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 17   main (in xcodebuild)
 18   start (in dyld)

<__DVTSwiftAsyncOperation: 0x7ff441ad6030; state = AReFc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   @objc DVTAsyncOperation.init() (in DVTFoundation)
  2   @objc static DVTOperation.operationWithMainThreadBlock(_:) (in DVTFoundation)
  3   -[IDESchemeAction _setUpOperationsForPackageLoadingInWorkspace:handlePreActionsActivityLogSection:isCleanOperation:] (in IDEFoundation)
  4   -[IDESchemeAction setUpActionDependenciesForCorePhaseOperation:shouldRunPostActionsBlock:prePhaseEnvironmentPopulationBlock:postPhaseEnvironmentPopulationBlock:handlePreActionsActivityLogSection:handlePostActionsActivityLogSection:buildParameters:schemeActionResultOperation:error:] (in IDEFoundation)
  5   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  6   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  7   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  8   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  9   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
 10   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 13   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 14   XcodeBuildMain (in libxcodebuildLoader.dylib)
 15   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 16   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 17   main (in xcodebuild)
 18   start (in dyld)

<__DVTSwiftAsyncOperation: 0x7ff441ad5d40; state = AReFc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   @objc DVTAsyncOperation.init() (in DVTFoundation)
  2   static DVTOperation.awaiting(_:) (in DVTFoundation)
  3   @objc static IDESchemeAction.operationToWaitForFinishedLoadingOperation(of:) (in IDEFoundation)
  4   -[IDESchemeAction _setUpOperationsForPackageLoadingInWorkspace:handlePreActionsActivityLogSection:isCleanOperation:] (in IDEFoundation)
  5   -[IDESchemeAction setUpActionDependenciesForCorePhaseOperation:shouldRunPostActionsBlock:prePhaseEnvironmentPopulationBlock:postPhaseEnvironmentPopulationBlock:handlePreActionsActivityLogSection:handlePostActionsActivityLogSection:buildParameters:schemeActionResultOperation:error:] (in IDEFoundation)
  6   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  7   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  8   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  9   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
 10   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
 11   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 13   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 14   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 15   XcodeBuildMain (in libxcodebuildLoader.dylib)
 16   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 17   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 18   main (in xcodebuild)
 19   start (in dyld)

<DVTOperation: 0x7ff441ad6a90; state = aReFc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   +[DVTOperation(DVTConveniences) operationWithBlock:] (in DVTFoundation)
  2   -[IDESchemeAction setUpActionDependenciesForCorePhaseOperation:shouldRunPostActionsBlock:prePhaseEnvironmentPopulationBlock:postPhaseEnvironmentPopulationBlock:handlePreActionsActivityLogSection:handlePostActionsActivityLogSection:buildParameters:schemeActionResultOperation:error:] (in IDEFoundation)
  3   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  4   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  5   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  6   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  7   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
  8   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
  9   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 10   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 12   XcodeBuildMain (in libxcodebuildLoader.dylib)
 13   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 14   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 15   main (in xcodebuild)
 16   start (in dyld)

<__DVTSwiftAsyncOperation: 0x7ff441ad4de0; state = AReFc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   @objc DVTAsyncOperation.init() (in DVTFoundation)
  2   static DVTOperation.awaiting(_:) (in DVTFoundation)
  3   @objc static IDESchemeAction.operationToWaitForFinishedLoadingOperation(of:) (in IDEFoundation)
  4   -[IDESchemeAction setUpActionDependenciesForCorePhaseOperation:shouldRunPostActionsBlock:prePhaseEnvironmentPopulationBlock:postPhaseEnvironmentPopulationBlock:handlePreActionsActivityLogSection:handlePostActionsActivityLogSection:buildParameters:schemeActionResultOperation:error:] (in IDEFoundation)
  5   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  6   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  7   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  8   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  9   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
 10   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 12   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 13   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 14   XcodeBuildMain (in libxcodebuildLoader.dylib)
 15   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 16   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 17   main (in xcodebuild)
 18   start (in dyld)

<DVTOperation: 0x7ff441ad6fc0; state = arefc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   +[DVTOperation(DVTConveniences) operationWithBlock:] (in DVTFoundation)
  2   -[IDESchemeAction setUpActionDependenciesForCorePhaseOperation:shouldRunPostActionsBlock:prePhaseEnvironmentPopulationBlock:postPhaseEnvironmentPopulationBlock:handlePreActionsActivityLogSection:handlePostActionsActivityLogSection:buildParameters:schemeActionResultOperation:error:] (in IDEFoundation)
  3   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  4   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  5   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  6   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  7   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
  8   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
  9   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 10   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 11   -[Xcode3
SwiftDriverJobDiscovery normal x86_64 Compiling LanguageLearningApp.swift (in target 'LanguageLearningApp' from project 'LanguageLearningApp')

note: Removed stale file '/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-e4c14c5e46e6efbdc2442c1cc13b4456.o'

note: Removed stale file '/Users/<USER>/Library/Developer/Xcode/DerivedData/LanguageLearningApp-eywlxiglqsxautfdmqwvpalbksfo/Build/Intermediates.noindex/LanguageLearningApp.build/Debug-iphonesimulator/LanguageLearningApp.build/Objects-normal/x86_64/LanguageLearningApp-e4c14c5e46e6efbdc2442c1cc13b4456.swiftconstvalues'

note: Disabling hardened runtime with ad-hoc codesigning. (in target 'LanguageLearningApp' from project 'LanguageLearningApp')
** BUILD INTERRUPTED **

CommandLineBuildTool run] (in Xcode3Core)
 12   XcodeBuildMain (in libxcodebuildLoader.dylib)
 13   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 14   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 15   main (in xcodebuild)
 16   start (in dyld)

<DVTOperation: 0x7ff441ad77a0; state = arefc> created at:
  0   -[DVTOperation init] (in DVTFoundation)
  1   +[DVTOperation(DVTConveniences) operationWithBlock:] (in DVTFoundation)
  2   -[IDESchemeAction setUpActionDependenciesForCorePhaseOperation:shouldRunPostActionsBlock:prePhaseEnvironmentPopulationBlock:postPhaseEnvironmentPopulationBlock:handlePreActionsActivityLogSection:handlePostActionsActivityLogSection:buildParameters:schemeActionResultOperation:error:] (in IDEFoundation)
  3   -[IDEScheme _buildOperationGroupForSchemeOperationParameters:primaryBuildParameters:variantSpecifiers:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:schemeActionRecord:error:] (in IDEFoundation)
  4   -[IDEScheme _executionOperationForSchemeOperationParameters:build:onlyBuild:buildParameters:title:buildLog:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:actionCallbackBlock:] (in IDEFoundation)
  5   -[IDEScheme buildOnlyTaskForSchemeOperationParameters:buildLog:dontActuallyRunCommands:buildParameters:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  6   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:deviceAvailableChecker:error:completionBlock:] (in IDEFoundation)
  7   -[IDEScheme schemeOperationForSchemeOperationParameters:buildLog:overridingProperties:overridingBuildConfiguration:dontActuallyRunCommands:restorePersistedBuildResults:error:completionBlock:] (in IDEFoundation)
  8   -[Xcode3CommandLineBuildTool doSchemeBuildWithRunDestinations:runDestinationIdx:wantsSkip:buildActionTimingSection:timingSection:buildAction:isMoreDesinationsPending:actionResultBlock:title:buildLog:executionEnvironment:restorePersistedBuildResults:operationToEnqueue:error:] (in Xcode3Core)
  9   -[Xcode3CommandLineBuildTool doBuildForBuildAction:timingSection:colorize:colorizeFailure:error:] (in Xcode3Core)
 10   -[Xcode3CommandLineBuildTool _buildWithTimingSection:] (in Xcode3Core)
 11   -[Xcode3CommandLineBuildTool run] (in Xcode3Core)
 12   XcodeBuildMain (in libxcodebuildLoader.dylib)
 13   -[XcodebuildPreIDEHandler loadXcode3ProjectSupportAndRunXcode3CommandLineBuildToolWithArguments:] (in xcodebuild)
 14   -[XcodebuildPreIDEHandler runWithArguments:] (in xcodebuild)
 15   main (in xcodebuild)
 16   start (in dyld)

