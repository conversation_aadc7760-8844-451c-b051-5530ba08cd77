#!/usr/bin/env swift

import Foundation

let filePath = "LanguageLearningApp/Features/Evaluation/Services/EvaluationService.swift"
let fileContent = try String(contentsOfFile: filePath, encoding: .utf8)

let oldCode = """
                // 尝试解析JSON并手动构建Evaluation
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    print("尝试手动解析JSON")

                    // 检查是否有data字段
                    if let dataDict = json["data"] as? [String: Any] {
                        print("找到data字段，尝试解析")

                        // 尝试将data字段转换为JSON数据
                        let dataData = try JSONSerialization.data(withJSONObject: dataDict)

                        // 尝试解码data字段为Evaluation
                        if let evaluation = try? decoder.decode(Evaluation.self, from: dataData) {
                            print("成功从data字段解码为Evaluation")
                            print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                            return evaluation
                        }
                    }
                }
"""

let newCode = """
                // 尝试解析JSON并手动构建Evaluation
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    print("尝试手动解析JSON")
                    
                    // 尝试将整个JSON转换为EvaluationAPIResponse
                    let jsonData = try JSONSerialization.data(withJSONObject: json)
                    if let apiResponse = try? decoder.decode(EvaluationAPIResponse.self, from: jsonData) {
                        print("成功解码为EvaluationAPIResponse")
                        if let apiEvaluation = apiResponse.data {
                            let evaluation = apiEvaluation.toEvaluation()
                            print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                            return evaluation
                        } else {
                            print("EvaluationAPIResponse中data字段为nil")
                        }
                    } else {
                        print("无法解码为EvaluationAPIResponse")
                    }

                    // 检查是否有data字段
                    if let dataDict = json["data"] as? [String: Any] {
                        print("找到data字段，尝试解析")

                        // 尝试将data字段转换为JSON数据
                        let dataData = try JSONSerialization.data(withJSONObject: dataDict)

                        // 尝试解码data字段为Evaluation
                        if let evaluation = try? decoder.decode(Evaluation.self, from: dataData) {
                            print("成功从data字段解码为Evaluation")
                            print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                            return evaluation
                        }
                    }
                }
"""

let updatedContent = fileContent.replacingOccurrences(of: oldCode, with: newCode)

try updatedContent.write(toFile: filePath, atomically: true, encoding: .utf8)
print("File updated successfully")
