#!/bin/bash

echo "🔧 验证语言学习应用..."

# 切换到应用目录
cd /Users/<USER>/Documents/workspace/hunter/languagelearning/app

echo "📱 构建应用..."
xcodebuild -scheme LanguageLearningApp -destination 'platform=iOS Simulator,name=iPhone 16' build > build.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    
    echo "🚀 启动模拟器..."
    xcrun simctl boot "iPhone 16" 2>/dev/null || echo "模拟器已运行"
    
    echo "📦 安装应用..."
    APP_PATH=$(find ~/Library/Developer/Xcode/DerivedData -name "LanguageLearningApp.app" -path "*/Debug-iphonesimulator/*" | head -1)
    
    if [ -n "$APP_PATH" ]; then
        echo "找到应用路径: $APP_PATH"
        xcrun simctl install "iPhone 16" "$APP_PATH"
        
        if [ $? -eq 0 ]; then
            echo "✅ 应用安装成功！"
            
            echo "🎯 启动应用..."
            xcrun simctl launch "iPhone 16" com.hunterx.LanguageLearningApp
            
            if [ $? -eq 0 ]; then
                echo "🎉 应用启动成功！"
                echo "✅ 依赖注入系统验证完成"
            else
                echo "❌ 应用启动失败"
                exit 1
            fi
        else
            echo "❌ 应用安装失败"
            exit 1
        fi
    else
        echo "❌ 找不到应用文件"
        exit 1
    fi
else
    echo "❌ 构建失败"
    echo "构建日志:"
    tail -20 build.log
    exit 1
fi

echo "🎯 验证完成！"
