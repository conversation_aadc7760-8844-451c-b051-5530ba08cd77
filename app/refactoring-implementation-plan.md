# 重构实施计划 - 详细步骤

## 🎯 Phase 1: 架构统一化 (Week 1-2)

### Step 1: 清理重复服务文件

#### 1.1 识别重复服务
```bash
# 当前重复的服务文件
Services/UserService.swift          → Features/User/Services/UserManager.swift
Services/LessonService.swift        → Features/Lessons/Services/LessonManager.swift  
Services/EvaluationService.swift    → Features/Evaluation/Services/EvaluationManager.swift
Services/PersonalizedLearningService.swift → Features/DailyPractice/Services/PersonalizedLearningManager.swift
```

#### 1.2 执行清理脚本
```bash
# 运行清理脚本
bash cleanup_old_files.sh

# 验证清理结果
find LanguageLearningApp/Services -name "*.swift" -type f
```

#### 1.3 更新所有引用
需要更新的文件类型:
- ViewModels 中的服务引用
- DependencyRegistry 中的注册
- 测试文件中的导入

### Step 2: 统一依赖注入架构

#### 2.1 移除单例模式
**目标文件**:
- `UserManager.shared` → 依赖注入
- `PracticeManager.shared` → 依赖注入
- `AchievementManager.shared` → 依赖注入
- `ErrorManager.shared` → 依赖注入

#### 2.2 实现ViewModelFactory
```swift
// 新建文件: LanguageLearningApp/DI/ViewModelFactory.swift
protocol ViewModelFactory {
    func makeWordLearningViewModel() -> WordLearningViewModel
    func makeListeningViewModel() -> ListeningViewModel
    func makeSpeakingViewModel() -> SpeakingViewModel
    // ... 其他ViewModel
}

class DefaultViewModelFactory: ViewModelFactory {
    private let container: DependencyContainer
    
    init(container: DependencyContainer = .shared) {
        self.container = container
    }
    
    func makeWordLearningViewModel() -> WordLearningViewModel {
        return WordLearningViewModel(
            userManager: container.resolve(UserManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self)
        )
    }
    // ... 实现其他方法
}
```

#### 2.3 更新主应用入口
```swift
// 更新 LanguageLearningApp.swift
@main
struct LanguageLearningApp: App {
    private let container = DependencyContainer.shared
    private let viewModelFactory: ViewModelFactory
    
    init() {
        DependencyRegistry.registerDependencies()
        self.viewModelFactory = DefaultViewModelFactory(container: container)
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(container.resolve(UserManagerProtocol.self))
                .environmentObject(container.resolve(ErrorManagerProtocol.self))
                .environmentObject(viewModelFactory)
        }
    }
}
```

### Step 3: 统一状态管理模式

#### 3.1 标准化ViewModel注入
**当前问题**:
```swift
// 不一致的注入方式
@StateObject private var viewModel = WordLearningViewModel()  // 直接实例化
@ObservedObject var achievementManager = AchievementManager.shared  // 单例
@EnvironmentObject private var errorManager: ErrorManager  // 环境对象
```

**统一后的方式**:
```swift
// 统一使用环境对象注入
@EnvironmentObject private var viewModelFactory: ViewModelFactory
@EnvironmentObject private var userManager: UserManagerProtocol
@EnvironmentObject private var errorManager: ErrorManagerProtocol

// 在视图中创建ViewModel
@StateObject private var viewModel: WordLearningViewModel

init() {
    // 在onAppear中初始化
}

var body: some View {
    // ...
    .onAppear {
        if _viewModel.wrappedValue == nil {
            _viewModel.wrappedValue = viewModelFactory.makeWordLearningViewModel()
        }
    }
}
```

#### 3.2 统一异步模式
**目标**: 全面采用 async/await，移除 Combine Publisher

**重构步骤**:
1. 更新所有Manager的方法签名
2. 更新ViewModel中的调用方式
3. 更新错误处理机制

```swift
// 重构前 (Combine)
func getCurrentUser() -> AnyPublisher<User, Error>

// 重构后 (async/await)
func getCurrentUser() async throws -> User
```

## 🎯 Phase 2: 代码质量提升 (Week 3-4)

### Step 4: 完善测试覆盖

#### 4.1 建立测试基础设施
```swift
// 新建文件: LanguageLearningAppTests/TestUtilities/TestContainer.swift
class TestContainer {
    static func createTestContainer() -> DependencyContainer {
        let container = DependencyContainer()
        
        // 注册Mock实现
        container.registerSingleton(UserManagerProtocol.self) {
            MockUserManager()
        }
        container.registerSingleton(ErrorManagerProtocol.self) {
            MockErrorManager()
        }
        
        return container
    }
}
```

#### 4.2 完善Mock实现
需要创建的Mock类:
- `MockUserManager`
- `MockPracticeManager`
- `MockLessonManager`
- `MockEvaluationManager`
- `MockAPIClient`

#### 4.3 编写单元测试
**测试覆盖目标**:
- 所有Manager类的核心方法
- 所有ViewModel的业务逻辑
- 所有Repository的数据操作
- 错误处理场景

### Step 5: 统一错误处理

#### 5.1 统一错误类型
```swift
// 扩展 AppError 枚举
enum AppError: Error {
    // 网络错误
    case networkError(String)
    case serverError(String)
    case requestTimeout
    case noInternetConnection
    
    // 认证错误
    case authenticationFailed(String)
    case userNotFound
    case invalidCredentials
    
    // 数据错误
    case dataCorrupted
    case decodingError(Error)
    case saveFailed(Error)
    
    // 业务逻辑错误
    case invalidInput(String)
    case operationNotAllowed
    case resourceNotFound
    
    // 系统错误
    case unknown
    case customError(String)
}
```

#### 5.2 实现全局错误处理中间件
```swift
// 新建文件: LanguageLearningApp/Middleware/ErrorHandlingMiddleware.swift
struct ErrorHandlingMiddleware: ViewModifier {
    @EnvironmentObject private var errorManager: ErrorManagerProtocol
    
    func body(content: Content) -> some View {
        content
            .onReceive(NotificationCenter.default.publisher(for: .appErrorOccurred)) { notification in
                if let error = notification.object as? AppError {
                    errorManager.showError(error)
                }
            }
    }
}

extension View {
    func withGlobalErrorHandling() -> some View {
        self.modifier(ErrorHandlingMiddleware())
    }
}
```

## 🎯 Phase 3: 性能优化 (Week 5-6)

### Step 6: 内存管理优化

#### 6.1 实现对象池模式
```swift
// 新建文件: LanguageLearningApp/Utilities/ObjectPool.swift
class ObjectPool<T: AnyObject> {
    private var objects: [T] = []
    private let createObject: () -> T
    private let resetObject: (T) -> Void
    private let maxSize: Int
    
    init(maxSize: Int = 10, 
         createObject: @escaping () -> T,
         resetObject: @escaping (T) -> Void) {
        self.maxSize = maxSize
        self.createObject = createObject
        self.resetObject = resetObject
    }
    
    func borrowObject() -> T {
        if let object = objects.popLast() {
            return object
        }
        return createObject()
    }
    
    func returnObject(_ object: T) {
        guard objects.count < maxSize else { return }
        resetObject(object)
        objects.append(object)
    }
}
```

#### 6.2 优化图片缓存
```swift
// 扩展现有的缓存机制
class ImageCacheManager {
    private let cache = NSCache<NSString, UIImage>()
    private let objectPool: ObjectPool<UIImageView>
    
    init() {
        cache.countLimit = 100
        cache.totalCostLimit = 50 * 1024 * 1024 // 50MB
        
        objectPool = ObjectPool<UIImageView>(
            maxSize: 20,
            createObject: { UIImageView() },
            resetObject: { imageView in
                imageView.image = nil
                imageView.contentMode = .scaleAspectFit
            }
        )
    }
}
```

## 📋 实施检查清单

### Week 1 任务
- [ ] 执行清理脚本，删除重复服务文件
- [ ] 更新所有服务引用点
- [ ] 实现ViewModelFactory
- [ ] 更新主应用入口

### Week 2 任务  
- [ ] 统一状态管理模式
- [ ] 重构所有ViewModel注入方式
- [ ] 统一异步模式 (async/await)
- [ ] 更新错误处理机制

### Week 3 任务
- [ ] 建立测试基础设施
- [ ] 完善Mock实现
- [ ] 编写Manager类单元测试
- [ ] 编写ViewModel单元测试

### Week 4 任务
- [ ] 编写Repository测试
- [ ] 实现集成测试
- [ ] 添加UI自动化测试
- [ ] 验证测试覆盖率

### Week 5-6 任务
- [ ] 实现对象池模式
- [ ] 优化缓存机制
- [ ] 性能监控实现
- [ ] 内存泄漏检测

## 🚨 风险评估

### 高风险项
1. **状态管理重构** - 可能影响现有功能
2. **异步模式统一** - 需要大量代码修改
3. **依赖注入迁移** - 可能引入运行时错误

### 缓解措施
1. 分阶段实施，每个阶段都要通过完整测试
2. 保持向后兼容，逐步迁移
3. 建立完整的回滚机制
4. 增加自动化测试覆盖

---

**计划制定时间**: 2025-01-27  
**预计完成时间**: 2025-03-10 (6周)  
**负责人**: 开发团队  
**审核人**: 架构师
