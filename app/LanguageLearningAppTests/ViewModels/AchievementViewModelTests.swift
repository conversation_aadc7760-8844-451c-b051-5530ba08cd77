import XCTest
import Combine
@testable import LanguageLearningApp

class AchievementViewModelTests: XCTestCase {
    
    // 模拟服务
    var mockNetworkService: MockNetworkService!
    var mockUserManager: MockUserManager!
    
    // 视图模型
    var viewModel: AchievementViewModel!
    
    // Combine 取消令牌
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        
        // 初始化模拟服务
        mockNetworkService = MockNetworkService()
        mockUserManager = MockUserManager()
        
        // 初始化视图模型，注入模拟服务
        viewModel = AchievementViewModel(
            networkService: mockNetworkService,
            userManager: mockUserManager
        )
        
        cancellables = []
    }
    
    override func tearDown() {
        mockNetworkService = nil
        mockUserManager = nil
        viewModel = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Tests
    
    func testLoadUserAchievementsFromAPI_Success() async {
        // 准备
        let testAchievements = [
            UserAchievement(
                id: UUID(),
                achievementId: UUID(),
                userId: UUID(),
                achievementType: .streak,
                title: "连续学习3天",
                description: "连续学习3天",
                icon: "flame.fill",
                color: "FF9500",
                requirement: 3,
                reward: 100,
                progress: 2,
                isUnlocked: false,
                rewardClaimed: false,
                unlockedDate: nil
            ),
            UserAchievement(
                id: UUID(),
                achievementId: UUID(),
                userId: UUID(),
                achievementType: .vocabulary,
                title: "词汇大师",
                description: "学习100个单词",
                icon: "textformat.abc",
                color: "007AFF",
                requirement: 100,
                reward: 200,
                progress: 50,
                isUnlocked: false,
                rewardClaimed: false,
                unlockedDate: nil
            )
        ]
        
        // 设置模拟响应
        mockNetworkService.mockResponse = testAchievements
        
        // 执行
        await viewModel.loadUserAchievementsFromAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertEqual(viewModel.userAchievements.count, 2, "应该加载2个成就")
        XCTAssertEqual(viewModel.userAchievements[0].title, "连续学习3天", "第一个成就标题应该匹配")
        XCTAssertEqual(viewModel.userAchievements[1].title, "词汇大师", "第二个成就标题应该匹配")
        XCTAssertFalse(viewModel.isLoading, "加载状态应该为false")
        XCTAssertNil(viewModel.error, "错误应该为nil")
    }
    
    func testLoadUserAchievementsFromAPI_Error() async {
        // 准备
        let testError = NSError(domain: "TestError", code: -1, userInfo: [NSLocalizedDescriptionKey: "测试错误"])
        
        // 设置模拟响应
        mockNetworkService.mockError = testError
        
        // 执行
        await viewModel.loadUserAchievementsFromAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertFalse(viewModel.isLoading, "加载状态应该为false")
        XCTAssertNotNil(viewModel.error, "错误应该不为nil")
    }
    
    func testClaimReward_Success() {
        // 准备
        let testAchievement = UserAchievement(
            id: UUID(),
            achievementId: UUID(),
            userId: UUID(),
            achievementType: .streak,
            title: "连续学习3天",
            description: "连续学习3天",
            icon: "flame.fill",
            color: "FF9500",
            requirement: 3,
            reward: 100,
            progress: 3,
            isUnlocked: true,
            rewardClaimed: false,
            unlockedDate: Date()
        )
        
        // 设置视图模型状态
        viewModel.userAchievements = [testAchievement]
        
        // 执行
        viewModel.claimReward(for: testAchievement)
        
        // 验证
        XCTAssertTrue(viewModel.userAchievements[0].rewardClaimed, "奖励领取状态应该为true")
        XCTAssertEqual(mockUserManager.updatedPoints, 100, "积分应该增加100")
    }
    
    func testClaimRewardFromAPI_Success() async {
        // 准备
        let testAchievement = UserAchievement(
            id: UUID(),
            achievementId: UUID(),
            userId: UUID(),
            achievementType: .streak,
            title: "连续学习3天",
            description: "连续学习3天",
            icon: "flame.fill",
            color: "FF9500",
            requirement: 3,
            reward: 100,
            progress: 3,
            isUnlocked: true,
            rewardClaimed: false,
            unlockedDate: Date()
        )
        
        let updatedAchievement = UserAchievement(
            id: testAchievement.id,
            achievementId: testAchievement.achievementId,
            userId: testAchievement.userId,
            achievementType: .streak,
            title: "连续学习3天",
            description: "连续学习3天",
            icon: "flame.fill",
            color: "FF9500",
            requirement: 3,
            reward: 100,
            progress: 3,
            isUnlocked: true,
            rewardClaimed: true,
            unlockedDate: testAchievement.unlockedDate
        )
        
        // 设置模拟用户
        let testUser = User(
            id: UUID(),
            username: "测试用户",
            email: "<EMAIL>",
            name: "测试用户",
            avatar: nil,
            createdAt: Date(),
            updatedAt: Date(),
            lastLoginAt: Date(),
            isActive: true,
            settings: UserSettings.default,
            stats: UserStats(
                streakDays: 5,
                vocabularyCount: 10,
                listeningExerciseCount: 5,
                speakingExerciseCount: 3,
                points: 100,
                completedChallenges: 2,
                helpedUsers: 1,
                lastLoginDate: Date()
            )
        )
        mockUserManager.mockUser = testUser
        
        // 设置模拟响应
        mockNetworkService.mockResponse = updatedAchievement
        
        // 设置视图模型状态
        viewModel.userAchievements = [testAchievement]
        
        // 执行
        await viewModel.claimRewardFromAPI(for: testAchievement)
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertTrue(viewModel.userAchievements[0].rewardClaimed, "奖励领取状态应该为true")
        XCTAssertEqual(mockUserManager.updatedPoints, 200, "积分应该增加100")
        XCTAssertFalse(viewModel.isLoading, "加载状态应该为false")
    }
    
    func testGetUnclaimedRewards_ReturnsCorrectSum() {
        // 准备
        let testAchievements = [
            UserAchievement(
                id: UUID(),
                achievementId: UUID(),
                userId: UUID(),
                achievementType: .streak,
                title: "连续学习3天",
                description: "连续学习3天",
                icon: "flame.fill",
                color: "FF9500",
                requirement: 3,
                reward: 100,
                progress: 3,
                isUnlocked: true,
                rewardClaimed: false,
                unlockedDate: Date()
            ),
            UserAchievement(
                id: UUID(),
                achievementId: UUID(),
                userId: UUID(),
                achievementType: .vocabulary,
                title: "词汇大师",
                description: "学习100个单词",
                icon: "textformat.abc",
                color: "007AFF",
                requirement: 100,
                reward: 200,
                progress: 100,
                isUnlocked: true,
                rewardClaimed: false,
                unlockedDate: Date()
            ),
            UserAchievement(
                id: UUID(),
                achievementId: UUID(),
                userId: UUID(),
                achievementType: .listening,
                title: "听力专家",
                description: "完成50个听力练习",
                icon: "ear.fill",
                color: "34C759",
                requirement: 50,
                reward: 150,
                progress: 50,
                isUnlocked: true,
                rewardClaimed: true,
                unlockedDate: Date()
            )
        ]
        
        // 设置视图模型状态
        viewModel.userAchievements = testAchievements
        
        // 执行
        let unclaimedRewards = viewModel.getUnclaimedRewards()
        
        // 验证
        XCTAssertEqual(unclaimedRewards, 300, "未领取的奖励总和应该是300")
    }
}
