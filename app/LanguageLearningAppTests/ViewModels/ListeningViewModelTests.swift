import XCTest
import Combine
@testable import LanguageLearningApp

class ListeningViewModelTests: XCTestCase {
    
    // 模拟服务
    var mockNetworkService: MockNetworkService!
    var mockErrorManager: MockErrorManager!
    var mockUserManager: MockUserManager!
    var mockTTSManager: MockTTSManager!
    
    // 视图模型
    var viewModel: ListeningViewModel!
    
    override func setUp() {
        super.setUp()
        
        // 初始化模拟服务
        mockNetworkService = MockNetworkService()
        mockErrorManager = MockErrorManager()
        mockUserManager = MockUserManager()
        mockTTSManager = MockTTSManager()
        
        // 初始化视图模型，注入模拟服务
        viewModel = ListeningViewModel(
            networkService: mockNetworkService,
            errorManager: mockErrorManager,
            userManager: mockUserManager,
            ttsManager: mockTTSManager
        )
    }
    
    override func tearDown() {
        mockNetworkService = nil
        mockErrorManager = nil
        mockUserManager = nil
        mockTTSManager = nil
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - Tests
    
    func testLoadListeningExercisesFromAPI_Success() async {
        // 准备
        let testExercises = [
            ListeningExercise(
                id: UUID(),
                title: "测试听力练习1",
                transcript: "这是一个测试听力练习",
                audioURL: URL(string: "https://example.com/audio1.mp3")!,
                difficulty: "初级",
                category: "日常对话",
                questions: [
                    ListeningQuestion(
                        id: UUID(),
                        question: "问题1",
                        options: ["选项1", "选项2", "选项3", "选项4"],
                        correctAnswer: 1
                    )
                ],
                createdAt: Date()
            ),
            ListeningExercise(
                id: UUID(),
                title: "测试听力练习2",
                transcript: "这是另一个测试听力练习",
                audioURL: URL(string: "https://example.com/audio2.mp3")!,
                difficulty: "中级",
                category: "新闻",
                questions: [
                    ListeningQuestion(
                        id: UUID(),
                        question: "问题1",
                        options: ["选项1", "选项2", "选项3", "选项4"],
                        correctAnswer: 2
                    )
                ],
                createdAt: Date()
            )
        ]
        
        // 设置模拟响应
        mockNetworkService.mockResponse = testExercises
        
        // 执行
        await viewModel.loadListeningExercisesFromAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertEqual(viewModel.exercises.count, 2, "应该加载2个练习")
        XCTAssertEqual(viewModel.currentExercise?.title, "测试听力练习1", "当前练习应该是第一个练习")
        XCTAssertEqual(viewModel.currentQuestionIndex, 0, "当前问题索引应该是0")
        XCTAssertNil(viewModel.selectedAnswer, "选择的答案应该为nil")
        XCTAssertFalse(viewModel.isAnswerSubmitted, "答案提交状态应该为false")
        XCTAssertFalse(viewModel.isAnswerCorrect, "答案正确状态应该为false")
        XCTAssertFalse(viewModel.showTranscript, "显示文本状态应该为false")
    }
    
    func testLoadListeningExercisesFromAPI_Error() async {
        // 准备
        let testError = NSError(domain: "TestError", code: -1, userInfo: [NSLocalizedDescriptionKey: "测试错误"])
        
        // 设置模拟响应
        mockNetworkService.mockError = testError
        
        // 执行
        await viewModel.loadListeningExercisesFromAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertEqual(mockErrorManager.lastErrorType, .networkError("加载听力练习失败，请检查网络连接"), "应该显示网络错误")
    }
    
    func testSubmitAnswerToAPI_Success() async {
        // 准备
        let testExercise = ListeningExercise(
            id: UUID(),
            title: "测试听力练习",
            transcript: "这是一个测试听力练习",
            audioURL: URL(string: "https://example.com/audio.mp3")!,
            difficulty: "初级",
            category: "日常对话",
            questions: [
                ListeningQuestion(
                    id: UUID(),
                    question: "问题1",
                    options: ["选项1", "选项2", "选项3", "选项4"],
                    correctAnswer: 1
                )
            ],
            createdAt: Date()
        )
        
        let testResponse = APIResponse(
            success: true,
            message: "提交成功",
            data: nil,
            error: nil,
            isCorrect: true,
            points: 10
        )
        
        // 设置模拟用户
        let testUser = User(
            id: UUID(),
            username: "测试用户",
            email: "<EMAIL>",
            name: "测试用户",
            avatar: nil,
            createdAt: Date(),
            updatedAt: Date(),
            lastLoginAt: Date(),
            isActive: true,
            settings: UserSettings.default,
            stats: UserStats(
                streakDays: 5,
                vocabularyCount: 10,
                listeningExerciseCount: 5,
                speakingExerciseCount: 3,
                points: 100,
                completedChallenges: 2,
                helpedUsers: 1,
                lastLoginDate: Date()
            )
        )
        mockUserManager.mockUser = testUser
        
        // 设置模拟响应
        mockNetworkService.mockResponse = testResponse
        
        // 设置视图模型状态
        viewModel.currentExercise = testExercise
        viewModel.selectedAnswer = 1
        
        // 执行
        await viewModel.submitAnswerToAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertTrue(viewModel.isAnswerCorrect, "答案正确状态应该为true")
        XCTAssertEqual(mockUserManager.updatedListeningExerciseCount, 6, "听力练习计数应该增加1")
        XCTAssertEqual(mockUserManager.updatedPoints, 110, "积分应该增加10")
    }
    
    func testPlayAudio_Success() {
        // 准备
        let testExercise = ListeningExercise(
            id: UUID(),
            title: "测试听力练习",
            transcript: "这是一个测试听力练习",
            audioURL: URL(string: "https://example.com/audio.mp3")!,
            difficulty: "初级",
            category: "日常对话",
            questions: [
                ListeningQuestion(
                    id: UUID(),
                    question: "问题1",
                    options: ["选项1", "选项2", "选项3", "选项4"],
                    correctAnswer: 1
                )
            ],
            createdAt: Date()
        )
        
        // 设置视图模型状态
        viewModel.currentExercise = testExercise
        
        // 执行
        viewModel.playAudio()
        
        // 验证
        XCTAssertTrue(viewModel.isPlaying, "播放状态应该为true")
        XCTAssertEqual(mockTTSManager.lastPlayedText, "这是一个测试听力练习", "播放的文本应该匹配")
        XCTAssertEqual(mockTTSManager.lastPlayedLanguageCode, "en-US", "播放的语言代码应该匹配")
    }
}

// MARK: - 模拟服务实现

/// 模拟TTS管理器
class MockTTSManager: TTSManager {
    var lastPlayedText: String?
    var lastPlayedLanguageCode: String?
    var mockError: Error?
    
    override func playSample(text: String, languageCode: String, completion: @escaping (Error?) -> Void) {
        lastPlayedText = text
        lastPlayedLanguageCode = languageCode
        
        if let error = mockError {
            completion(error)
        } else {
            completion(nil)
        }
    }
    
    override func stopSample() {
        // 不需要实现
    }
}

/// 模拟用户管理器扩展
extension MockUserManager {
    var updatedListeningExerciseCount: Int?
    
    override func updateUserListeningExerciseCount(_ count: Int) {
        updatedListeningExerciseCount = count
    }
}
