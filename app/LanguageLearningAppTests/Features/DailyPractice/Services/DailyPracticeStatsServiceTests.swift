import XCTest
import Combine
@testable import LanguageLearningApp

class DailyPracticeStatsServiceTests: XCTestCase {
    
    var statsService: DailyPracticeStatsService!
    var mockAPIClient: MockAPIClient!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        mockAPIClient = MockAPIClient()
        statsService = DailyPracticeStatsService(apiClient: mockAPIClient)
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        statsService = nil
        mockAPIClient = nil
        cancellables = nil
        super.tearDown()
    }
    
    func testGetTodayStats_Success() {
        // 准备
        let expectation = XCTestExpectation(description: "Get today stats")
        let mockStats = DailyPracticeStats(
            date: Date(),
            minutesLearned: 30,
            practicesCompleted: 3,
            totalScore: 85,
            averageScore: 28,
            practicesByType: ["vocabulary": 2, "grammar": 1],
            streak: 5
        )
        
        let mockResponse = PracticeStatsResponse(
            success: true,
            data: mockStats,
            message: nil
        )
        
        let responseData = try! JSONEncoder().encode(mockResponse)
        mockAPIClient.mockResponse = responseData
        
        // 执行
        statsService.getTodayStats()
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        XCTFail("Expected success, got error: \(error)")
                    }
                },
                receiveValue: { stats in
                    // 验证
                    XCTAssertEqual(stats.minutesLearned, 30)
                    XCTAssertEqual(stats.practicesCompleted, 3)
                    XCTAssertEqual(stats.totalScore, 85)
                    XCTAssertEqual(stats.averageScore, 28)
                    XCTAssertEqual(stats.streak, 5)
                    expectation.fulfill()
                }
            )
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testCalculateStatsFromLocalData() {
        // 准备
        let today = Date()
        let sessions = [
            PracticeSession(
                userID: UUID(),
                type: .vocabulary,
                startTime: today.addingTimeInterval(-1800), // 30分钟前开始
                endTime: today,
                duration: 1800, // 30分钟
                score: 85,
                completed: true,
                isSynced: true
            ),
            PracticeSession(
                userID: UUID(),
                type: .grammar,
                startTime: today.addingTimeInterval(-900), // 15分钟前开始
                endTime: today,
                duration: 900, // 15分钟
                score: 92,
                completed: true,
                isSynced: true
            )
        ]
        
        // 执行
        let stats = statsService.calculateStatsFromLocalData(sessions, for: today)
        
        // 验证
        XCTAssertEqual(stats.minutesLearned, 45) // 30 + 15 分钟
        XCTAssertEqual(stats.practicesCompleted, 2)
        XCTAssertEqual(stats.totalScore, 177) // 85 + 92
        XCTAssertEqual(stats.averageScore, 88) // (85 + 92) / 2
        XCTAssertEqual(stats.practicesByType["vocabulary"], 1)
        XCTAssertEqual(stats.practicesByType["grammar"], 1)
    }
    
    func testCalculateStatsFromLocalData_EmptySessions() {
        // 准备
        let today = Date()
        let sessions: [PracticeSession] = []
        
        // 执行
        let stats = statsService.calculateStatsFromLocalData(sessions, for: today)
        
        // 验证
        XCTAssertEqual(stats.minutesLearned, 0)
        XCTAssertEqual(stats.practicesCompleted, 0)
        XCTAssertEqual(stats.totalScore, 0)
        XCTAssertEqual(stats.averageScore, 0)
        XCTAssertEqual(stats.streak, 0)
        XCTAssertTrue(stats.practicesByType.isEmpty)
    }
    
    func testCalculateStatsFromLocalData_DifferentDates() {
        // 准备
        let today = Date()
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: today)!
        
        let sessions = [
            PracticeSession(
                userID: UUID(),
                type: .vocabulary,
                startTime: today.addingTimeInterval(-1800),
                endTime: today,
                duration: 1800,
                score: 85,
                completed: true,
                isSynced: true
            ),
            PracticeSession(
                userID: UUID(),
                type: .grammar,
                startTime: yesterday.addingTimeInterval(-900),
                endTime: yesterday,
                duration: 900,
                score: 92,
                completed: true,
                isSynced: true
            )
        ]
        
        // 执行 - 只计算今天的统计
        let todayStats = statsService.calculateStatsFromLocalData(sessions, for: today)
        
        // 验证 - 只包含今天的会话
        XCTAssertEqual(todayStats.minutesLearned, 30) // 只有今天的30分钟
        XCTAssertEqual(todayStats.practicesCompleted, 1) // 只有今天的1个练习
        XCTAssertEqual(todayStats.totalScore, 85) // 只有今天的分数
        XCTAssertEqual(todayStats.averageScore, 85)
        XCTAssertEqual(todayStats.practicesByType["vocabulary"], 1)
        XCTAssertNil(todayStats.practicesByType["grammar"]) // 昨天的练习不计入
    }
}

// MARK: - Mock API Client

class MockAPIClient: APIClientProtocol {
    var mockResponse: Data?
    var mockError: Error?
    
    func request(endpoint: APIEndpoint) -> AnyPublisher<Data, Error> {
        if let error = mockError {
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        if let data = mockResponse {
            return Just(data)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }
        
        return Fail(error: APIError.noData).eraseToAnyPublisher()
    }
    
    func upload(endpoint: APIEndpoint, data: Data, fileName: String, mimeType: String) -> AnyPublisher<Data, Error> {
        return request(endpoint: endpoint)
    }
}

// MARK: - Mock API Error

enum APIError: Error {
    case noData
}
