import Foundation
import Combine
@testable import LanguageLearningApp

/// 模拟API客户端，用于单元测试
class MockAPIClient: APIClientProtocol {
    // 模拟网络连接状态
    var isNetworkAvailable: Bool = true
    
    // 存储预定义的响应数据
    private var mockResponses: [String: Any] = [:]
    private var mockErrors: [String: Error] = [:]
    
    // 记录调用历史
    private(set) var requestHistory: [(endpoint: String, method: String, parameters: [String: Any]?)] = []
    
    /// 设置模拟响应
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - response: 响应数据
    func setMockResponse(for endpoint: String, response: Any) {
        mockResponses[endpoint] = response
    }
    
    /// 设置模拟错误
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - error: 错误
    func setMockError(for endpoint: String, error: Error) {
        mockErrors[endpoint] = error
    }
    
    /// 清除所有模拟数据和历史记录
    func reset() {
        mockResponses.removeAll()
        mockErrors.removeAll()
        requestHistory.removeAll()
    }
    
    // MARK: - APIClientProtocol Implementation
    
    func request(endpoint: APIEndpoint) -> AnyPublisher<Data, Error> {
        let endpointString = endpoint.url.absoluteString
        
        // 记录请求
        requestHistory.append((endpoint: endpointString, method: endpoint.method, parameters: nil))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpointString] {
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpointString] {
            if let data = response as? Data {
                return Just(data)
                    .setFailureType(to: Error.self)
                    .eraseToAnyPublisher()
            } else {
                do {
                    let data = try JSONSerialization.data(withJSONObject: response, options: [])
                    return Just(data)
                        .setFailureType(to: Error.self)
                        .eraseToAnyPublisher()
                } catch {
                    return Fail(error: error).eraseToAnyPublisher()
                }
            }
        }
        
        // 默认返回空数据
        return Just(Data())
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func get(_ endpoint: String) -> AnyPublisher<Any, Error> {
        // 记录请求
        requestHistory.append((endpoint: endpoint, method: "GET", parameters: nil))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpoint] {
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpoint] {
            return Just(response)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }
        
        // 默认返回空字典
        return Just([:] as [String: Any])
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func post(_ endpoint: String, parameters: [String: Any]?) -> AnyPublisher<Any, Error> {
        // 记录请求
        requestHistory.append((endpoint: endpoint, method: "POST", parameters: parameters))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpoint] {
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpoint] {
            return Just(response)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }
        
        // 默认返回空字典
        return Just([:] as [String: Any])
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func get<T: Decodable>(_ endpoint: String, as type: T.Type) -> AnyPublisher<T, Error> {
        // 记录请求
        requestHistory.append((endpoint: endpoint, method: "GET", parameters: nil))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpoint] {
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpoint] {
            if let typedResponse = response as? T {
                return Just(typedResponse)
                    .setFailureType(to: Error.self)
                    .eraseToAnyPublisher()
            } else {
                do {
                    let data = try JSONSerialization.data(withJSONObject: response, options: [])
                    let decoder = JSONDecoder()
                    let decodedResponse = try decoder.decode(T.self, from: data)
                    return Just(decodedResponse)
                        .setFailureType(to: Error.self)
                        .eraseToAnyPublisher()
                } catch {
                    return Fail(error: error).eraseToAnyPublisher()
                }
            }
        }
        
        // 默认返回错误
        return Fail(error: AppError.dataNotFound).eraseToAnyPublisher()
    }
    
    func post<T: Decodable>(_ endpoint: String, parameters: [String: Any]?, as type: T.Type) -> AnyPublisher<T, Error> {
        // 记录请求
        requestHistory.append((endpoint: endpoint, method: "POST", parameters: parameters))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpoint] {
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpoint] {
            if let typedResponse = response as? T {
                return Just(typedResponse)
                    .setFailureType(to: Error.self)
                    .eraseToAnyPublisher()
            } else {
                do {
                    let data = try JSONSerialization.data(withJSONObject: response, options: [])
                    let decoder = JSONDecoder()
                    let decodedResponse = try decoder.decode(T.self, from: data)
                    return Just(decodedResponse)
                        .setFailureType(to: Error.self)
                        .eraseToAnyPublisher()
                } catch {
                    return Fail(error: error).eraseToAnyPublisher()
                }
            }
        }
        
        // 默认返回错误
        return Fail(error: AppError.dataNotFound).eraseToAnyPublisher()
    }
    
    // MARK: - Async/Await API
    
    func get(_ endpoint: String) async throws -> Any {
        // 记录请求
        requestHistory.append((endpoint: endpoint, method: "GET", parameters: nil))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpoint] {
            throw error
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpoint] {
            return response
        }
        
        // 默认返回空字典
        return [:] as [String: Any]
    }
    
    func post(_ endpoint: String, parameters: [String: Any]?) async throws -> Any {
        // 记录请求
        requestHistory.append((endpoint: endpoint, method: "POST", parameters: parameters))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpoint] {
            throw error
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpoint] {
            return response
        }
        
        // 默认返回空字典
        return [:] as [String: Any]
    }
    
    func get<T: Decodable>(_ endpoint: String, as type: T.Type) async throws -> T {
        // 记录请求
        requestHistory.append((endpoint: endpoint, method: "GET", parameters: nil))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpoint] {
            throw error
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpoint] {
            if let typedResponse = response as? T {
                return typedResponse
            } else {
                do {
                    let data = try JSONSerialization.data(withJSONObject: response, options: [])
                    let decoder = JSONDecoder()
                    return try decoder.decode(T.self, from: data)
                } catch {
                    throw error
                }
            }
        }
        
        // 默认返回错误
        throw AppError.dataNotFound
    }
    
    func post<T: Decodable>(_ endpoint: String, parameters: [String: Any]?, as type: T.Type) async throws -> T {
        // 记录请求
        requestHistory.append((endpoint: endpoint, method: "POST", parameters: parameters))
        
        // 检查是否有预定义的错误
        if let error = mockErrors[endpoint] {
            throw error
        }
        
        // 检查是否有预定义的响应
        if let response = mockResponses[endpoint] {
            if let typedResponse = response as? T {
                return typedResponse
            } else {
                do {
                    let data = try JSONSerialization.data(withJSONObject: response, options: [])
                    let decoder = JSONDecoder()
                    return try decoder.decode(T.self, from: data)
                } catch {
                    throw error
                }
            }
        }
        
        // 默认返回错误
        throw AppError.dataNotFound
    }
}
