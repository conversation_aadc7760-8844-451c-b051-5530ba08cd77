#!/bin/bash

# Define the file path
FILE_PATH="LanguageLearningApp/Features/Evaluation/Services/EvaluationService.swift"

# Define the line numbers to insert the code
START_LINE=285
END_LINE=285

# Define the code to insert
CODE_TO_INSERT='
                // 尝试解码为 EvaluationAPIResponse
                if let apiResponse = try? decoder.decode(EvaluationAPIResponse.self, from: data) {
                    print("成功解码为EvaluationAPIResponse")
                    if let apiEvaluation = apiResponse.data {
                        let evaluation = apiEvaluation.toEvaluation()
                        print("评估包含 \(evaluation.sections.count) 个部分，总共 \(evaluation.totalQuestions) 个问题")
                        return evaluation
                    } else {
                        print("EvaluationAPIResponse中data字段为nil")
                    }
                } else {
                    print("无法解码为EvaluationAPIResponse")
                }'

# Insert the code at the specified line
sed -i '' "${START_LINE}a\\
${CODE_TO_INSERT}
" "$FILE_PATH"

echo "Code inserted successfully"
