package v1_test // Or your preferred test package name

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"languagelearning/controllers"
	v1 "languagelearning/controllers/v1" // Adjust if your router is in a different package path

	// Import your models if needed for constructing request bodies, e.g.:
	// "languagelearning/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// setupRouter initializes a new Gin router with all your application routes for testing.
// IMPORTANT: You MUST adapt the controller instantiations below to provide
// the correct dependencies (services, DB connections, etc.) for your controllers.
func setupRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New() // Use gin.New() for a clean router in tests

	// --- Instantiate your controllers here ---
	// Replace 'nil' or placeholder dependencies with actual or mocked dependencies.
	// Example:
	// db := database.ConnectTestDB() // Or get a mock DB
	// authService := services.NewAuthService(db)
	// authController := controllers.NewAuthController(authService, /* other deps */)

	// You'll need to create constructors (New...) for your controllers if they don't exist
	// or adapt the instantiation based on how they are created in your main application setup.

	// Placeholder instantiations - ADAPT THESE:
	authController := controllers.NewAuthController( /* pass necessary dependencies */ )
	userController := controllers.NewUserController( /* pass necessary dependencies */ )
	lessonController := controllers.NewLessonController( /* pass necessary dependencies */ )
	notificationController := controllers.NewNotificationController( /* pass necessary dependencies */ )
	exerciseRelationController := controllers.NewExerciseRelationController( /* pass necessary dependencies */ )
	personalizedLearningController := controllers.NewPersonalizedLearningController( /* pass necessary dependencies */ )
	learningPathController := controllers.NewLearningPathController( /* pass necessary dependencies */ )
	achievementController := controllers.NewAchievementController( /* pass necessary dependencies */ )
	evaluationController := controllers.NewEvaluationController( /* pass necessary dependencies */ )
	exerciseController := controllers.NewExerciseController( /* pass necessary dependencies */ )
	wordController := controllers.NewWordController( /* pass necessary dependencies */ )
	practiceController := controllers.NewPracticeController( /* pass necessary dependencies */ )

	apiRouter := v1.NewAPIRouter(
		authController,
		userController,
		lessonController,
		notificationController,
		exerciseRelationController,
		personalizedLearningController,
		learningPathController,
		achievementController,
		evaluationController,
		exerciseController,
		wordController,
		practiceController,
	)

	apiRouter.RegisterRoutes(router)
	return router
}

// getAuthToken returns a placeholder JWT token for authenticated requests.
// IMPORTANT: Replace this with a function that generates or provides a VALID
// test JWT token for your application.
func getAuthToken(t *testing.T) string {
	// Example: return "Bearer actual.test.jwt.token"
	t.Log("Using placeholder JWT token. Replace getAuthToken for proper authenticated tests.")
	return "Bearer your_test_jwt_token_here"
}

// Helper to create a request and record response
func performRequest(r http.Handler, method, path string, body interface{}, token ...string) *httptest.ResponseRecorder {
	var reqBody *bytes.Buffer
	if body != nil {
		payloadBytes, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(payloadBytes)
	} else {
		reqBody = bytes.NewBuffer(nil)
	}

	req, _ := http.NewRequest(method, path, reqBody)
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	if len(token) > 0 && token[0] != "" {
		req.Header.Set("Authorization", token[0])
	}

	rr := httptest.NewRecorder()
	r.ServeHTTP(rr, req)
	return rr
}

// --- Test Cases ---

func TestHealthCheckEndpoints(t *testing.T) {
	router := setupRouter() // Static handlers, controller instantiation might not be critical for these
	endpoints := []string{"/health", "/readiness", "/liveness"}
	for _, endpoint := range endpoints {
		t.Run(endpoint, func(t *testing.T) {
			rr := performRequest(router, http.MethodGet, endpoint, nil)
			assert.NotEqual(t, http.StatusInternalServerError, rr.Code, "Expected non-500 error for "+endpoint)
			// assert.Equal(t, http.StatusOK, rr.Code) // Or specific code
		})
	}
}

func TestSwaggerEndpoint(t *testing.T) {
	router := setupRouter()
	t.Run("/swagger/*any", func(t *testing.T) {
		rr := performRequest(router, http.MethodGet, "/swagger/index.html", nil) // Test a common swagger path
		assert.NotEqual(t, http.StatusInternalServerError, rr.Code)
		// assert.Equal(t, http.StatusOK, rr.Code) // Swagger usually returns 200
	})
}

func TestPublicAuthRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/auth"

	testCases := []struct {
		name     string
		method   string
		path     string
		body     interface{}
		wantCode int // Expected if not 5xx (e.g. 200, 201, 400 for bad request) - for smoke, primarily not 500
	}{
		{name: "Login", method: http.MethodPost, path: "/login", body: map[string]string{"email": "<EMAIL>", "password": "password123"}},
		{name: "Register", method: http.MethodPost, path: "/register", body: map[string]string{"username": "testuser", "email": "<EMAIL>", "password": "password123"}},
		{name: "ResetPassword", method: http.MethodPost, path: "/reset-password", body: map[string]string{"email": "<EMAIL>"}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			rr := performRequest(router, tc.method, basePath+tc.path, tc.body)
			assert.NotEqual(t, http.StatusInternalServerError, rr.Code, "Expected non-500 error for "+tc.path)
			// If you know the expected success/fail (non-500) code, assert it:
			// if tc.wantCode != 0 { assert.Equal(t, tc.wantCode, rr.Code) }
		})
	}
}

func TestPublicLessonRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/lessons"

	testCases := []struct {
		name   string
		method string
		path   string
	}{
		{name: "GetLessons", method: http.MethodGet, path: ""},
		{name: "GetLessonByID", method: http.MethodGet, path: "/test-lesson-id"}, // Replace with a valid/invalid ID for more specific tests
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			rr := performRequest(router, tc.method, basePath+tc.path, nil)
			assert.NotEqual(t, http.StatusInternalServerError, rr.Code, "Expected non-500 error for "+tc.path)
			// e.g. GET /lessons/nonexistent-id might return 404, which is fine for a smoke test.
		})
	}
}

// --- Authenticated Routes ---
// Base function for testing authenticated routes
func testAuthenticatedRoute(t *testing.T, router *gin.Engine, method, path string, body interface{}, note string) {
	// Test without token (expect 401)
	t.Run(note+"_NoToken", func(t *testing.T) {
		rr := performRequest(router, method, path, body)
		assert.Equal(t, http.StatusUnauthorized, rr.Code, "Expected 401 Unauthorized for "+path+" without token")
	})

	// Test with token (expect non-500 and non-401)
	t.Run(note+"_WithToken", func(t *testing.T) {
		token := getAuthToken(t)
		rr := performRequest(router, method, path, body, token)
		assert.NotEqual(t, http.StatusInternalServerError, rr.Code, "Expected non-500 error for "+path+" with token")
		assert.NotEqual(t, http.StatusUnauthorized, rr.Code, "Expected non-401 error for "+path+" with token (check token validity or middleware)")
		// Add more specific status code checks if known, e.g. http.StatusOK or http.StatusNoContent
	})
}

func TestAuthenticatedAuthRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/auth"
	testAuthenticatedRoute(t, router, http.MethodDelete, basePath+"/logout", nil, "Logout")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/change-password", map[string]string{"old_password": "old", "new_password": "new"}, "ChangePassword")
}

func TestAuthenticatedUserRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/user"
	// Add more specific bodies as needed
	profileBody := map[string]string{"name": "Updated Test User"}
	settingsBody := map[string]bool{"enable_notifications": false}

	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/profile", nil, "GetUserProfile")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/profile", profileBody, "UpdateUserProfile")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/settings", nil, "GetUserSettings")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/settings", settingsBody, "UpdateUserSettings")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/stats", nil, "GetUserStats")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/streak", nil, "GetLearningStreak")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/progress/weekly", nil, "GetProgressReport") // Example period
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/words", nil, "GetUserWordList")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/achievements", nil, "GetUserAchievements")
}

func TestAuthenticatedNotificationRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/notifications"
	prefsBody := map[string]interface{}{"email_summary": "daily"} // Example body

	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"", nil, "GetNotifications")
	testAuthenticatedRoute(t, router, http.MethodPatch, basePath+"/test-notif-id/read", nil, "MarkNotificationAsRead")
	testAuthenticatedRoute(t, router, http.MethodPatch, basePath+"/read-all", nil, "MarkAllNotificationsAsRead")
	testAuthenticatedRoute(t, router, http.MethodPatch, basePath+"/test-notif-id/archive", nil, "ArchiveNotification")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/preferences", nil, "GetNotificationPreferences")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/preferences", prefsBody, "UpdateNotificationPreferences")
}

func TestAuthenticatedLessonProgressRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/lessons"
	progressBody := map[string]interface{}{"completed_percentage": 50, "last_viewed_at": "2023-01-01T10:00:00Z"}

	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/test-lesson-id/progress", nil, "GetLessonProgress")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/test-lesson-id/progress", progressBody, "UpdateLessonProgress")
}

func TestPersonalizedLearningRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/personalized-learning"
	initiateBody := map[string]string{"goal": "conversational_fluency"}

	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/status", nil, "GetPersonalizedLearningStatus")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/initiate", initiateBody, "InitiatePersonalizedLearning")
}

func TestExerciseRelationRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/exercise-relations"
	relationBody := map[string]string{"source_id": "ex1", "target_id": "ex2", "type": "prerequisite"}
	difficultyBody := map[string]float64{"difficulty_score": 0.75}

	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"", relationBody, "CreateRelation")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/test-relation-id", relationBody, "UpdateRelation")
	testAuthenticatedRoute(t, router, http.MethodDelete, basePath+"/test-relation-id", nil, "DeleteRelation")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/exercise/test-exercise-id", nil, "GetExerciseRelations")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/exercise/test-exercise-id/difficulty", difficultyBody, "UpdateExerciseDifficultyMetadata")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/recommendations", nil, "RecommendExercises")
}

func TestLearningPathRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/learning-paths"
	// Define appropriate bodies for POST/PUT requests
	createPathBody := map[string]string{"name": "My Test Path", "target_level": "B2"}
	addLessonBody := map[string]string{"lesson_id": "lesson123"}
	// ... other bodies

	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"", nil, "GetLearningPaths")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/test-path-id", nil, "GetLearningPathDetail")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"", createPathBody, "CreateLearningPath")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/test-path-id/lessons", addLessonBody, "AddLessonToPath")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/test-path-id", createPathBody, "UpdateLearningPath") // Reuse or adapt body
	testAuthenticatedRoute(t, router, http.MethodDelete, basePath+"/test-path-id", nil, "DeleteLearningPath")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/recommendations", nil, "GetRecommendedLearningPaths")
	// ... (Continue for all learning path routes, defining bodies as needed) ...
	// Example:
	// testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/test-path-id/complete-exercise/test-lesson-id", map[string]string{"exercise_id":"ex1"}, "CompleteExerciseInPath")
}

func TestEvaluationRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/evaluations"
	createEvalBody := map[string]string{"type": "placement_test", "language_id": "lang_es"}
	answerBody := map[string]interface{}{"question_id": "q1", "answer": "option_a"}

	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"", nil, "GetAvailableEvaluations")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/test-eval-id", nil, "GetEvaluationDetails")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"", createEvalBody, "CreateEvaluation")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/test-eval-id/start", nil, "StartEvaluation")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/test-eval-id/answer", answerBody, "SubmitEvaluationAnswer")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/test-eval-id/complete", nil, "CompleteEvaluation")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/test-eval-id/results", nil, "GetEvaluationResults")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/history", nil, "GetUserEvaluationHistory")
}

func TestAchievementRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/achievements"
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"", nil, "GetAchievements")
	testAuthenticatedRoute(t, router, http.MethodPatch, basePath+"/test-achieve-id/claim", nil, "ClaimAchievementReward")
}

func TestWordRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/words"
	markLearnedBody := map[string]bool{"is_learned": true}

	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"", nil, "GetWords")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/test-word-id", nil, "GetWordDetail")
	testAuthenticatedRoute(t, router, http.MethodPut, basePath+"/test-word-id/learned", markLearnedBody, "MarkWordAsLearned")
}

func TestPracticeRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1/practice"
	sessionBody := map[string]interface{}{"type": "vocabulary", "duration_minutes": 15, "score": 85}

	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/history", nil, "GetPracticeHistory")
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/recommended", nil, "GetRecommendedPractice")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/session", sessionBody, "SavePracticeSession")
}

func TestExerciseSubmissionRoutes(t *testing.T) {
	router := setupRouter()
	basePath := "/api/v1"                                              // Base for these specific exercise routes
	submitBody := map[string]interface{}{"answer": "some answer text"} // Generic body, adapt per exercise type

	// Grammar
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/grammar/exercises", nil, "GetGrammarExercises")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/grammar/exercises/test-ex-id/submit", submitBody, "SubmitGrammarAnswer")
	// Speaking
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/speaking/exercises", nil, "GetSpeakingExercises")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/speaking/exercises/test-ex-id/submit", submitBody, "SubmitSpeakingAnswer") // Body might be multipart/form-data for audio
	// Listening
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/listening/exercises", nil, "GetListeningExercises")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/listening/exercises/test-ex-id/submit", submitBody, "SubmitListeningAnswer")
	// Word
	testAuthenticatedRoute(t, router, http.MethodGet, basePath+"/word/exercises", nil, "GetWordExercises")
	testAuthenticatedRoute(t, router, http.MethodPost, basePath+"/word/exercises/test-ex-id/submit", submitBody, "SubmitWordAnswer")
}

// TODO: Add tests for Admin Routes if/when they are implemented and registered.
// func TestAdminLessonRoutes(t *testing.T) { ... }
