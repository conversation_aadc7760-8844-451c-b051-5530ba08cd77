package utils

import (
	"testing"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"languagelearning/models"
)

// SetupTestDB 创建测试数据库
func SetupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}
	
	// 自动迁移测试表
	err = db.AutoMigrate(
		&models.User{},
		&models.Lesson{},
		&models.Exercise{},
		// 添加其他模型...
	)
	if err != nil {
		t.Fatalf("Failed to migrate test database: %v", err)
	}
	
	return db
}

// CreateTestUser 创建测试用户
func CreateTestUser(t *testing.T, db *gorm.DB) *models.User {
	user := &models.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "hashedpassword",
		IsActive: true,
	}
	
	if err := db.Create(user).Error; err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}
	
	return user
}
