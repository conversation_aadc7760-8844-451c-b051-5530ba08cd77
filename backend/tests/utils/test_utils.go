package utils

import (
	"bytes"
	"encoding/json"
	"languagelearning/config"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

// SetupTestEnvironment 设置测试环境
func SetupTestEnvironment() {
	// 设置GIN为测试模式
	gin.SetMode(gin.TestMode)

	// 确保使用测试数据库
	os.Setenv("DATABASE_URL", "postgres://postgres:postgres@localhost:5432/languagelearning_test?sslmode=disable")
}

// CreateTestRouter 创建测试路由器
func CreateTestRouter() *gin.Engine {
	router := gin.Default()
	return router
}

// MakeRequest 发送HTTP请求并返回响应
func MakeRequest(method, url string, router *gin.Engine, body interface{}) *httptest.ResponseRecorder {
	var reqBody []byte
	var err error

	// 如果有请求体，将其序列化为JSON
	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			log.Fatalf("Failed to marshal request body: %v", err)
		}
	}

	// 创建请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.Fatalf("Failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 记录响应
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	return w
}

// ParseResponse 解析响应体
func ParseResponse(t *testing.T, w *httptest.ResponseRecorder, target interface{}) {
	err := json.Unmarshal(w.Body.Bytes(), target)
	assert.NoError(t, err, "Failed to parse response")
}

// LoadTestConfig 加载测试配置
func LoadTestConfig() *config.Config {
	// 设置测试环境变量
	os.Setenv("GIN_MODE", "test")
	os.Setenv("DATABASE_URL", "postgres://postgres:postgres@localhost:5432/languagelearning_test?sslmode=disable")
	os.Setenv("JWT_SECRET", "test-secret-key")

	return config.LoadConfig()
}

// CleanTestDatabase 清理测试数据库
func CleanTestDatabase(db *gorm.DB, tables ...string) {
	for _, table := range tables {
		db.Exec("TRUNCATE TABLE " + table + " CASCADE")
	}
}

// CreateAuthHeader 创建带有JWT的认证头
func CreateAuthHeader(token string) map[string]string {
	return map[string]string{
		"Authorization": "Bearer " + token,
	}
}

// AddAuthHeader 向请求添加认证头
func AddAuthHeader(req *http.Request, token string) {
	req.Header.Set("Authorization", "Bearer "+token)
}
