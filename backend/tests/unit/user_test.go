package unit

import (
	"testing"
	"github.com/stretchr/testify/assert"
	"languagelearning/tests/utils"
	"languagelearning/models"
)

func TestUserModel(t *testing.T) {
	// 设置测试数据库
	db := utils.SetupTestDB(t)
	
	// 创建测试用户
	user := utils.CreateTestUser(t, db)
	
	// 验证用户创建
	assert.NotEmpty(t, user.ID)
	assert.Equal(t, "testuser", user.Username)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.True(t, user.IsActive)
	
	// 测试密码验证
	err := user.CheckPassword("wrongpassword")
	assert.Error(t, err)
}

func TestUserToEntity(t *testing.T) {
	// 设置测试数据库
	db := utils.SetupTestDB(t)
	
	// 创建测试用户
	user := utils.CreateTestUser(t, db)
	
	// 测试转换方法
	entity := user.ToEntity()
	
	assert.Equal(t, user.ID, entity.ID)
	assert.Equal(t, user.Username, entity.Username)
	assert.Equal(t, user.Email, entity.Email)
}
