FROM alpine:3.16

# Install required packages
RUN apk add --no-cache \
    bash \
    curl \
    jq \
    grep \
    sed

# Set working directory
WORKDIR /app

# We'll use volume mounting instead of copying the script
# This allows for easier development and testing without rebuilding the image

# Make scripts executable
RUN chmod +x /app/*.sh 2>/dev/null || true

# Default script to run
ENV TEST_SCRIPT=api_test.sh

# Set the entrypoint
ENTRYPOINT ["/bin/bash", "-c", "chmod +x /app/*.sh && /bin/bash /app/${TEST_SCRIPT}"]
