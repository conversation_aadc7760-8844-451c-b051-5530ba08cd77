#!/bin/bash

# API Testing Script for Language Learning System
# This script tests the complete flow of the language learning system:
# 1. User registration/login
# 2. Initiating personalized learning
# 3. Completing an evaluation
# 4. Verifying automatic learning path creation
# 5. Getting and completing exercises
# 6. Verifying automatic learning path updates
# 7. Getting recommendations for the next learning path
# 8. Creating a learning path from recommendation
#
# Note: The system automatically updates learning paths and exercise sets based on user performance.
# There's no need to manually call update APIs as the system handles this automatically.

# Configuration
API_URL=${API_URL:-"http://api:8080/api/v1"}
AUTH_TOKEN=""

# Generate a random suffix for the username to avoid conflicts
RANDOM_SUFFIX=$(date +%s)
USER_EMAIL="test$<EMAIL>"
USER_PASSWORD="Test123!"
USER_NAME="Test User"
USER_USERNAME="testuser$RANDOM_SUFFIX"  # Added username field with random suffix

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to make API calls
call_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local auth_header=""

    # Debug output
    echo -e "${YELLOW}Making API call: $method $endpoint${NC}"
    if [ -n "$AUTH_TOKEN" ]; then
        echo -e "${YELLOW}Using auth token: $AUTH_TOKEN${NC}"
        # Note: We need to use eval with curl when using variables in the header
        if [ -n "$data" ]; then
            # Use -w to output the HTTP status code
            response=$(eval curl -s -w ',"status":%{http_code}' -X $method \"$API_URL$endpoint\" \
                -H \"Content-Type: application/json\" \
                -H \"Authorization: Bearer $AUTH_TOKEN\" \
                -d \'$data\')
        else
            # Use -w to output the HTTP status code
            response=$(eval curl -s -w ',"status":%{http_code}' -X $method \"$API_URL$endpoint\" \
                -H \"Content-Type: application/json\" \
                -H \"Authorization: Bearer $AUTH_TOKEN\")
        fi
    else
        echo -e "${YELLOW}No auth token provided${NC}"
        if [ -n "$data" ]; then
            # Use -w to output the HTTP status code
            response=$(curl -s -w ',"status":%{http_code}' -X $method "$API_URL$endpoint" \
                -H "Content-Type: application/json" \
                -d "$data")
        else
            # Use -w to output the HTTP status code
            response=$(curl -s -w ',"status":%{http_code}' -X $method "$API_URL$endpoint" \
                -H "Content-Type: application/json")
        fi
    fi

    echo "$response"
}

# Function to check if a test passed
check_result() {
    local test_name=$1
    local response=$2
    local expected_status=${3:-200}  # Default expected status is 200

    # Check if the response contains success:true
    if echo "$response" | grep -q '"success":true'; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        return 0
    # Check if the response contains a message (might be a success without success:true)
    elif echo "$response" | grep -q '"message"' && ! echo "$response" | grep -q '"success":false'; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        return 0
    # Special case for 400 status with "already answered" message
    elif echo "$response" | grep -q 'already answered'; then
        echo -e "${YELLOW}✓ $test_name - Already processed${NC}"
        return 0
    else
        echo -e "${RED}✗ $test_name failed${NC}"
        echo -e "${RED}Response: $response${NC}"
        return 1
    fi
}

# Function to extract value from JSON response
extract_value() {
    local json=$1
    local key=$2
    echo "$json" | grep -o "\"$key\":[^,}]*" | sed -E 's/"'$key'":"|"//g'
}

# Wait for API to be ready
echo -e "${YELLOW}Waiting for API to be ready...${NC}"
# Extract the base URL without the /api/v1 part
BASE_URL=$(echo $API_URL | sed 's/\/api\/v1//')

# Function to check health endpoint
check_health() {
    # Get the health status with HTTP status code
    health_response=$(curl -s -w ',"status":%{http_code}' $BASE_URL/health)

    # Extract the HTTP status code
    http_status=$(echo "$health_response" | grep -o '"status":[0-9]*' | grep -o '[0-9]*')

    # Check if the HTTP status is 200 and the response contains the expected status
    if [[ "$http_status" == "200" ]] && \
       (echo "$health_response" | grep -q '"status":"UP"' || \
        echo "$health_response" | grep -q '"status":"up"' || \
        echo "$health_response" | grep -q '"status":"OK"' || \
        echo "$health_response" | grep -q '"status":"ok"'); then
        echo -e "${GREEN}Health check passed (Status: $http_status)${NC}"
        return 0
    else
        echo -e "${RED}Health check failed (Status: $http_status)${NC}"
        echo -e "${RED}Response: $health_response${NC}"
        return 1
    fi
}

# Wait until the health check passes
until check_health; do
    printf '.'
    sleep 5
done

echo -e "${GREEN}API is ready! Health check passed.${NC}"

# Step 1: Register a new user
echo -e "\n${YELLOW}Step 1: Registering a new user${NC}"
register_data="{\"username\":\"$USER_USERNAME\",\"email\":\"$USER_EMAIL\",\"password\":\"$USER_PASSWORD\"}"
register_response=$(call_api "POST" "/auth/register" "$register_data")

# Check the HTTP status code in the curl response
http_status=$(echo "$register_response" | grep -o '"status":[0-9]*' | grep -o '[0-9]*')
echo -e "${YELLOW}Registration HTTP status: $http_status${NC}"
echo -e "${YELLOW}Registration response: $register_response${NC}"

# If registration succeeds (status 200 or 201) or fails with 400 (user might already exist), proceed
if [[ "$http_status" == "200" || "$http_status" == "201" ]]; then
    echo -e "${GREEN}User registered successfully${NC}"
    # Extract token from the data field
    AUTH_TOKEN=$(echo "$register_response" | grep -o '"token":"[^"]*' | head -1 | sed 's/"token":"//g')
    if [ -z "$AUTH_TOKEN" ]; then
        # Try to extract from the data.token field
        AUTH_TOKEN=$(echo "$register_response" | grep -o '"data":{[^}]*}' | grep -o '"token":"[^"]*' | sed 's/"token":"//g')
    fi
else
    echo -e "${YELLOW}Registration failed with status $http_status. Trying with a different username...${NC}"

    # Generate a new random suffix
    RANDOM_SUFFIX=$(date +%s)
    USER_USERNAME="testuser$RANDOM_SUFFIX"
    USER_EMAIL="test$<EMAIL>"

    echo -e "${YELLOW}Trying with new username: $USER_USERNAME${NC}"

    # Try registering with the new username
    register_data="{\"username\":\"$USER_USERNAME\",\"email\":\"$USER_EMAIL\",\"password\":\"$USER_PASSWORD\"}"
    register_response=$(call_api "POST" "/auth/register" "$register_data")

    http_status=$(echo "$register_response" | grep -o '"status":[0-9]*' | grep -o '[0-9]*')
    echo -e "${YELLOW}Registration HTTP status: $http_status${NC}"
    echo -e "${YELLOW}Registration response: $register_response${NC}"

    if [[ "$http_status" == "200" || "$http_status" == "201" ]]; then
        echo -e "${GREEN}User registered successfully with new username${NC}"
        # Extract token from the data field
        AUTH_TOKEN=$(echo "$register_response" | grep -o '"token":"[^"]*' | head -1 | sed 's/"token":"//g')
        if [ -z "$AUTH_TOKEN" ]; then
            # Try to extract from the data.token field
            AUTH_TOKEN=$(echo "$register_response" | grep -o '"data":{[^}]*}' | grep -o '"token":"[^"]*' | sed 's/"token":"//g')
        fi
    else
        echo -e "${RED}Registration failed again with status $http_status. Cannot proceed with tests.${NC}"
        exit 1
    fi
fi

if [ -z "$AUTH_TOKEN" ]; then
    echo -e "${RED}Failed to extract auth token from response. Cannot proceed with tests.${NC}"
    exit 1
fi

echo -e "${GREEN}Auth Token: $AUTH_TOKEN${NC}"

# Verify the token works by making a simple authenticated request
echo -e "${YELLOW}Verifying auth token...${NC}"
verify_response=$(call_api "GET" "/user/profile" "")
echo -e "${YELLOW}Verification response: $verify_response${NC}"

# Check if the response contains success:true
if echo "$verify_response" | grep -q '"success":true'; then
    echo -e "${GREEN}Auth token verified successfully${NC}"
else
    echo -e "${RED}Auth token verification failed. Cannot proceed with tests.${NC}"
    echo -e "${RED}Verification response: $verify_response${NC}"
    exit 1
fi

# Step 2: Initiate personalized learning
echo -e "\n${YELLOW}Step 2: Initiating personalized learning${NC}"
initiate_response=$(call_api "POST" "/personalized-learning/initiate" "")
check_result "Initiate personalized learning" "$initiate_response"

# Extract evaluation ID
eval_id=$(echo "$initiate_response" | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')
echo -e "${YELLOW}Raw initiate response: $initiate_response${NC}"

# Check if evaluation ID is empty
if [ -z "$eval_id" ]; then
    # Try to extract from the data field
    eval_id=$(echo "$initiate_response" | grep -o '"data":{[^}]*}' | grep -o '"id":"[^"]*' | sed 's/"id":"//g')

    # If still empty, try to extract from a different format
    if [ -z "$eval_id" ]; then
        eval_id=$(echo "$initiate_response" | grep -o '"evaluation":{[^}]*}' | grep -o '"id":"[^"]*' | sed 's/"id":"//g')
    fi
fi

if [ -z "$eval_id" ]; then
    echo -e "${RED}Failed to extract evaluation ID from response. Cannot proceed with tests.${NC}"
    exit 1
fi

echo -e "${GREEN}Evaluation ID: $eval_id${NC}"

# Step 3: Start the evaluation
echo -e "\n${YELLOW}Step 3: Starting the evaluation${NC}"
start_eval_response=$(call_api "POST" "/evaluations/$eval_id/start" "")
check_result "Start evaluation" "$start_eval_response"

# Step 4: Submit answers for the evaluation
echo -e "\n${YELLOW}Step 4: Submitting answers for the evaluation${NC}"
# Get the questions first
eval_details_response=$(call_api "GET" "/evaluations/$eval_id" "")
check_result "Get evaluation details" "$eval_details_response"

# For simplicity, we'll submit correct answers for all questions
# In a real test, you would parse the questions and submit appropriate answers
# Here we're just simulating a user getting a high score

# Submit 10 answers (assuming the evaluation has at least 10 questions)
for i in {1..10}; do
    answer_data="{\"questionId\":$i,\"answer\":\"correct_answer\"}"
    answer_response=$(call_api "POST" "/evaluations/$eval_id/answer" "$answer_data")
    # For answer submission, we accept 200, 201, or 400 (already answered)
    if echo "$answer_response" | grep -q '"status":400' && echo "$answer_response" | grep -q 'already answered'; then
        echo -e "${YELLOW}✓ Submit answer $i - Question already answered${NC}"
    else
        check_result "Submit answer $i" "$answer_response"
    fi
done

# Step 5: Complete the evaluation
echo -e "\n${YELLOW}Step 5: Completing the evaluation${NC}"
complete_eval_response=$(call_api "POST" "/evaluations/$eval_id/complete" "")
check_result "Complete evaluation" "$complete_eval_response"

# Step 6: Verify learning path was created automatically
echo -e "\n${YELLOW}Step 6: Verifying learning path creation${NC}"
# Wait a moment for the learning path to be created
sleep 2
learning_paths_response=$(call_api "GET" "/learning-paths" "")
check_result "Get learning paths" "$learning_paths_response"

# Extract the learning path ID
learning_path_id=$(echo "$learning_paths_response" | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')
echo -e "${YELLOW}Raw learning paths response: $learning_paths_response${NC}"

# Check if learning path ID is empty
if [ -z "$learning_path_id" ]; then
    # Try to extract from the data field
    learning_path_id=$(echo "$learning_paths_response" | grep -o '"data":\[{[^}]*}' | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')

    # If still empty, try to extract from a different format
    if [ -z "$learning_path_id" ]; then
        learning_path_id=$(echo "$learning_paths_response" | grep -o '"learningPaths":\[{[^}]*}' | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')
    fi
fi

if [ -z "$learning_path_id" ]; then
    echo -e "${RED}Failed to extract learning path ID from response. Cannot proceed with tests.${NC}"
    exit 1
fi

echo -e "${GREEN}Learning Path ID: $learning_path_id${NC}"

# Step 7: Get the next exercise
echo -e "\n${YELLOW}Step 7: Getting the next exercise${NC}"
next_exercise_response=$(call_api "GET" "/learning-paths/$learning_path_id/next-exercise" "")
check_result "Get next exercise" "$next_exercise_response"

# Extract the lesson ID
lesson_id=$(echo "$next_exercise_response" | grep -o '"lessonID":"[^"]*' | head -1 | sed 's/"lessonID":"//g')
echo -e "${YELLOW}Raw next exercise response: $next_exercise_response${NC}"

# Check if lesson ID is empty
if [ -z "$lesson_id" ]; then
    # Try to extract from the data field
    lesson_id=$(echo "$next_exercise_response" | grep -o '"data":{[^}]*}' | grep -o '"lessonID":"[^"]*' | sed 's/"lessonID":"//g')

    # If still empty, try to extract from a different format
    if [ -z "$lesson_id" ]; then
        # Try lessonId (camelCase)
        lesson_id=$(echo "$next_exercise_response" | grep -o '"lessonId":"[^"]*' | head -1 | sed 's/"lessonId":"//g')

        # If still empty, try lesson_id (snake_case)
        if [ -z "$lesson_id" ]; then
            lesson_id=$(echo "$next_exercise_response" | grep -o '"lesson_id":"[^"]*' | head -1 | sed 's/"lesson_id":"//g')
        fi
    fi
fi

if [ -z "$lesson_id" ]; then
    echo -e "${RED}Failed to extract lesson ID from response. Cannot proceed with tests.${NC}"
    exit 1
fi

echo -e "${GREEN}Lesson ID: $lesson_id${NC}"

# Step 8: Complete the exercise
echo -e "\n${YELLOW}Step 8: Completing the exercise${NC}"
complete_exercise_response=$(call_api "POST" "/learning-paths/$learning_path_id/complete-exercise/$lesson_id" "")
check_result "Complete exercise" "$complete_exercise_response"

# Step 9: Verify learning path was updated
echo -e "\n${YELLOW}Step 9: Verifying learning path update${NC}"
updated_path_response=$(call_api "GET" "/learning-paths/$learning_path_id" "")
check_result "Get updated learning path" "$updated_path_response"

# Step 10: Get recommendations for next learning path
echo -e "\n${YELLOW}Step 10: Getting recommendations for next learning path${NC}"
recommendations_response=$(call_api "GET" "/learning-paths/recommendations?evaluationId=$eval_id" "")
check_result "Get recommendations" "$recommendations_response"

# Step 11: Create a learning path from recommendation
echo -e "\n${YELLOW}Step 11: Creating a learning path from recommendation${NC}"
create_from_recommendation_response=$(call_api "POST" "/learning-paths/recommendations/create?evaluationId=$eval_id" "")
check_result "Create from recommendation" "$create_from_recommendation_response"

# Summary
echo -e "\n${GREEN}Test completed!${NC}"
echo -e "The test has verified the complete flow of the language learning system:"
echo -e "1. User registration/login"
echo -e "2. Initiating personalized learning"
echo -e "3. Completing an evaluation"
echo -e "4. Verifying automatic learning path creation"
echo -e "5. Getting and completing exercises"
echo -e "6. Verifying automatic learning path updates"
echo -e "7. Getting recommendations for the next learning path"
echo -e "8. Creating a learning path from recommendation"
echo -e ""
echo -e "Note: The system automatically updates learning paths and exercise sets based on user performance."
echo -e "There's no need to manually call update APIs as the system handles this automatically."
