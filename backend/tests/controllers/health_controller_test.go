package controllers_test

import (
	"encoding/json"
	"languagelearning/controllers"
	testUtils "languagelearning/tests/utils"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupHealthRouter() *gin.Engine {
	router := testUtils.CreateTestRouter()
	router.GET("/health", controllers.GetHealth)
	router.GET("/readiness", controllers.GetReadiness)
	router.GET("/liveness", controllers.GetLiveness)
	return router
}

func TestGetHealth(t *testing.T) {
	// 设置测试环境
	testUtils.SetupTestEnvironment()
	router := setupHealthRouter()

	// 发送请求
	w := testUtils.MakeRequest(http.MethodGet, "/health", router, nil)

	// 检查响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应内容
	assert.True(t, response["success"].(bool))
	assert.Equal(t, "Service is healthy", response["message"])

	// 检查数据字段
	data, ok := response["data"].(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, "ok", data["status"])
}

func TestGetReadiness(t *testing.T) {
	// 设置测试环境
	testUtils.SetupTestEnvironment()
	router := setupHealthRouter()

	// 发送请求
	w := testUtils.MakeRequest(http.MethodGet, "/readiness", router, nil)

	// 检查响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应内容
	assert.True(t, response["success"].(bool))
	data, ok := response["data"].(map[string]interface{})
	assert.True(t, ok)
	assert.Contains(t, data, "ready")
}

func TestGetLiveness(t *testing.T) {
	// 设置测试环境
	testUtils.SetupTestEnvironment()
	router := setupHealthRouter()

	// 发送请求
	w := testUtils.MakeRequest(http.MethodGet, "/liveness", router, nil)

	// 检查响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应内容
	assert.True(t, response["success"].(bool))
	data, ok := response["data"].(map[string]interface{})
	assert.True(t, ok)
	assert.Contains(t, data, "alive")
}
