#!/bin/bash

# Adaptive Learning Testing Script for Language Learning System
# This script tests the adaptive learning capabilities of the system:
# 1. User registration/login
# 2. Initiating personalized learning
# 3. Completing an evaluation
# 4. Completing multiple rounds of exercises with different success rates
# 5. Verifying that each new exercise set is appropriately customized based on previous performance
#
# The script will test three scenarios:
# - High success rate (80-100%): Should lead to more challenging exercises
# - Medium success rate (40-60%): Should maintain similar difficulty level
# - Low success rate (0-20%): Should lead to easier exercises

# Configuration
API_URL=${API_URL:-"http://api:8080/api/v1"}
AUTH_TOKEN=""

# Generate a random suffix for the username to avoid conflicts
RANDOM_SUFFIX=$(date +%s)
USER_EMAIL="adaptive$<EMAIL>"
USER_PASSWORD="Test123!"
USER_USERNAME="adaptive$RANDOM_SUFFIX"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to make API calls
call_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local auth_header=""

    # Debug output
    echo -e "${YELLOW}Making API call: $method $endpoint${NC}"
    if [ -n "$AUTH_TOKEN" ]; then
        if [ -n "$data" ]; then
            response=$(eval curl -s -w ',"status":%{http_code}' -X $method \"$API_URL$endpoint\" \
                -H \"Content-Type: application/json\" \
                -H \"Authorization: Bearer $AUTH_TOKEN\" \
                -d \'$data\')
        else
            response=$(eval curl -s -w ',"status":%{http_code}' -X $method \"$API_URL$endpoint\" \
                -H \"Content-Type: application/json\" \
                -H \"Authorization: Bearer $AUTH_TOKEN\")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w ',"status":%{http_code}' -X $method "$API_URL$endpoint" \
                -H "Content-Type: application/json" \
                -d "$data")
        else
            response=$(curl -s -w ',"status":%{http_code}' -X $method "$API_URL$endpoint" \
                -H "Content-Type: application/json")
        fi
    fi

    echo "$response"
}

# Function to check if a test passed
check_result() {
    local test_name=$1
    local response=$2
    local expected_status=${3:-200}  # Default expected status is 200

    # Check if the response contains success:true
    if echo "$response" | grep -q '"success":true'; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        return 0
    # Check if the response contains a message (might be a success without success:true)
    elif echo "$response" | grep -q '"message"' && ! echo "$response" | grep -q '"success":false'; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        return 0
    # Special case for 400 status with "already answered" message
    elif echo "$response" | grep -q 'already answered'; then
        echo -e "${YELLOW}✓ $test_name - Already processed${NC}"
        return 0
    else
        echo -e "${RED}✗ $test_name failed${NC}"
        echo -e "${RED}Response: $response${NC}"
        return 1
    fi
}

# Function to extract value from JSON response
extract_value() {
    local json=$1
    local key=$2
    echo "$json" | grep -o "\"$key\":[^,}]*" | sed -E 's/"'$key'":"|"//g'
}

# Function to extract array from JSON response
extract_array() {
    local json=$1
    local key=$2
    echo "$json" | grep -o "\"$key\":\[[^\]]*\]" | sed -E 's/"'$key'"://g'
}

# Function to check health endpoint
check_health() {
    health_response=$(curl -s -w ',"status":%{http_code}' $BASE_URL/health)
    http_status=$(echo "$health_response" | grep -o '"status":[0-9]*' | grep -o '[0-9]*')
    if [[ "$http_status" == "200" ]] && \
       (echo "$health_response" | grep -q '"status":"UP"' || \
        echo "$health_response" | grep -q '"status":"up"' || \
        echo "$health_response" | grep -q '"status":"OK"' || \
        echo "$health_response" | grep -q '"status":"ok"'); then
        echo -e "${GREEN}Health check passed (Status: $http_status)${NC}"
        return 0
    else
        echo -e "${RED}Health check failed (Status: $http_status)${NC}"
        echo -e "${RED}Response: $health_response${NC}"
        return 1
    fi
}

# Function to complete a round of exercises with specified success rate and test next exercise adaptation
complete_exercise_round() {
    local learning_path_id=$1
    local success_rate=$2
    local round_number=$3
    local total_exercises=10  # Increase the number of exercises to better test adaptation

    echo -e "\n${BLUE}=== Round $round_number: Completing exercises with $success_rate% success rate ===${NC}"

    # Get the learning path details to analyze before starting
    echo -e "\n${YELLOW}Getting learning path details before round $round_number${NC}"
    path_before_response=$(call_api "GET" "/learning-paths/$learning_path_id" "")
    check_result "Get learning path before round $round_number" "$path_before_response"

    # Extract and display difficulty level and focus areas
    difficulty_before=$(echo "$path_before_response" | grep -o '"level":"[^"]*' | sed -E 's/"level":"//g')
    focus_areas_before=$(extract_array "$path_before_response" "focusAreas")
    echo -e "${BLUE}Before Round $round_number - Difficulty: $difficulty_before, Focus Areas: $focus_areas_before${NC}"

    # Complete exercises in this round
    for i in $(seq 1 $total_exercises); do
        # Get the next exercise
        echo -e "\n${YELLOW}Getting next exercise ($i/$total_exercises) for round $round_number${NC}"
        next_exercise_response=$(call_api "GET" "/learning-paths/$learning_path_id/next-exercise" "")

        # Debug output
        echo -e "${YELLOW}DEBUG: Next exercise response:${NC}"
        echo "$next_exercise_response" | jq . 2>/dev/null || echo "$next_exercise_response"

        check_result "Get next exercise ($i/$total_exercises) for round $round_number" "$next_exercise_response"

        # Check if all exercises are completed
        if echo "$next_exercise_response" | grep -q '"completed":true'; then
            echo -e "${YELLOW}All exercises completed for this learning path${NC}"
            break
        fi

        # Check if the response contains an error
        if echo "$next_exercise_response" | grep -q '"success":false'; then
            echo -e "${YELLOW}Received error response. Skipping to next round.${NC}"
            echo -e "${YELLOW}Response: $next_exercise_response${NC}"
            break
        fi

        # Extract exercise details for analysis
        exercise_type=$(echo "$next_exercise_response" | grep -o '"type":"[^"]*' | head -1 | sed 's/"type":"//g')
        exercise_difficulty=$(echo "$next_exercise_response" | grep -o '"difficulty":"[^"]*' | head -1 | sed 's/"difficulty":"//g')
        echo -e "${BLUE}Exercise $i - Type: $exercise_type, Difficulty: $exercise_difficulty${NC}"

        # Extract the lesson ID
        lesson_id=$(echo "$next_exercise_response" | grep -o '"lessonID":"[^"]*' | head -1 | sed 's/"lessonID":"//g')
        if [ -z "$lesson_id" ]; then
            lesson_id=$(echo "$next_exercise_response" | grep -o '"lessonId":"[^"]*' | head -1 | sed 's/"lessonId":"//g')
            if [ -z "$lesson_id" ]; then
                lesson_id=$(echo "$next_exercise_response" | grep -o '"lesson_id":"[^"]*' | head -1 | sed 's/"lesson_id":"//g')
                if [ -z "$lesson_id" ]; then
                    # Try to extract from data object
                    lesson_id=$(echo "$next_exercise_response" | grep -o '"data":{[^}]*}' | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')
                fi
            fi
        fi

        if [ -z "$lesson_id" ]; then
            echo -e "${YELLOW}Failed to extract lesson ID from response. Skipping to next round.${NC}"
            echo -e "${YELLOW}Response: $next_exercise_response${NC}"
            break
        fi

        # Determine if this exercise should be successful based on success rate
        random_number=$((RANDOM % 100))
        is_successful=$([[ $random_number -lt $success_rate ]] && echo "true" || echo "false")

        # Complete the exercise with the determined success
        echo -e "${YELLOW}Completing exercise $i with success=$is_successful${NC}"
        complete_data="{\"isSuccessful\":$is_successful}"
        complete_exercise_response=$(call_api "POST" "/learning-paths/$learning_path_id/complete-exercise/$lesson_id" "$complete_data")

        # Debug output
        echo -e "${YELLOW}DEBUG: Complete exercise response:${NC}"
        echo "$complete_exercise_response" | jq . 2>/dev/null || echo "$complete_exercise_response"

        check_result "Complete exercise $i for round $round_number" "$complete_exercise_response"

        # If we've completed half the exercises, check if the next exercise has adapted
        if [ $i -eq $(($total_exercises / 2)) ]; then
            echo -e "\n${BLUE}=== Mid-round check: Verifying exercise adaptation ===${NC}"

            # Get the next exercise to see if it has adapted
            mid_check_response=$(call_api "GET" "/learning-paths/$learning_path_id/next-exercise" "")

            # Debug output
            echo -e "${YELLOW}DEBUG: Mid-round next exercise check response:${NC}"
            echo "$mid_check_response" | jq . 2>/dev/null || echo "$mid_check_response"

            check_result "Mid-round next exercise check" "$mid_check_response"

            # Extract exercise details for analysis
            mid_exercise_type=$(echo "$mid_check_response" | grep -o '"type":"[^"]*' | head -1 | sed 's/"type":"//g')
            mid_exercise_difficulty=$(echo "$mid_check_response" | grep -o '"difficulty":"[^"]*' | head -1 | sed 's/"difficulty":"//g')

            echo -e "${BLUE}Mid-round Exercise - Type: $mid_exercise_type, Difficulty: $mid_exercise_difficulty${NC}"

            # Compare with initial exercise
            if [[ "$mid_exercise_type" != "$exercise_type" || "$mid_exercise_difficulty" != "$exercise_difficulty" ]]; then
                echo -e "${GREEN}✓ Exercise has adapted during the round!${NC}"
            else
                echo -e "${YELLOW}Exercise has not changed significantly during the round.${NC}"
            fi
        fi
    done

    # Get the learning path details to analyze after completion
    echo -e "\n${YELLOW}Getting learning path details after round $round_number${NC}"
    path_after_response=$(call_api "GET" "/learning-paths/$learning_path_id" "")
    check_result "Get learning path after round $round_number" "$path_after_response"

    # Extract and display difficulty level and focus areas
    difficulty_after=$(echo "$path_after_response" | grep -o '"level":"[^"]*' | sed -E 's/"level":"//g')
    focus_areas_after=$(extract_array "$path_after_response" "focusAreas")
    echo -e "${BLUE}After Round $round_number - Difficulty: $difficulty_after, Focus Areas: $focus_areas_after${NC}"

    # Analyze changes
    echo -e "\n${BLUE}=== Analysis of Round $round_number (Success Rate: $success_rate%) ===${NC}"
    if [[ "$difficulty_before" != "$difficulty_after" ]]; then
        echo -e "${GREEN}✓ Difficulty level changed from $difficulty_before to $difficulty_after${NC}"
    else
        echo -e "${YELLOW}Difficulty level remained at $difficulty_after${NC}"
    fi

    if [[ "$focus_areas_before" != "$focus_areas_after" ]]; then
        echo -e "${GREEN}✓ Focus areas changed from $focus_areas_before to $focus_areas_after${NC}"
    else
        echo -e "${YELLOW}Focus areas remained the same: $focus_areas_after${NC}"
    fi

    # Return the updated learning path ID (in case it changed)
    echo "$learning_path_id"
}

# Wait for API to be ready
echo -e "${YELLOW}Waiting for API to be ready...${NC}"
BASE_URL=$(echo $API_URL | sed 's/\/api\/v1//')
until check_health; do
    printf '.'
    sleep 5
done
echo -e "${GREEN}API is ready! Health check passed.${NC}"

# Step 1: Register a new user
echo -e "\n${BLUE}=== Step 1: Registering a new user ===${NC}"
register_data="{\"username\":\"$USER_USERNAME\",\"email\":\"$USER_EMAIL\",\"password\":\"$USER_PASSWORD\"}"
register_response=$(call_api "POST" "/auth/register" "$register_data")
http_status=$(echo "$register_response" | grep -o '"status":[0-9]*' | grep -o '[0-9]*')

if [[ "$http_status" == "200" || "$http_status" == "201" ]]; then
    echo -e "${GREEN}User registered successfully${NC}"
    AUTH_TOKEN=$(echo "$register_response" | grep -o '"token":"[^"]*' | head -1 | sed 's/"token":"//g')
    if [ -z "$AUTH_TOKEN" ]; then
        AUTH_TOKEN=$(echo "$register_response" | grep -o '"data":{[^}]*}' | grep -o '"token":"[^"]*' | sed 's/"token":"//g')
    fi
else
    echo -e "${RED}Registration failed with status $http_status. Cannot proceed with tests.${NC}"
    exit 1
fi

if [ -z "$AUTH_TOKEN" ]; then
    echo -e "${RED}Failed to extract auth token from response. Cannot proceed with tests.${NC}"
    exit 1
fi

# Step 2: Initiate personalized learning
echo -e "\n${BLUE}=== Step 2: Initiating personalized learning ===${NC}"
initiate_response=$(call_api "POST" "/personalized-learning/initiate" "")
check_result "Initiate personalized learning" "$initiate_response"

# Extract evaluation ID
eval_id=$(echo "$initiate_response" | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')
if [ -z "$eval_id" ]; then
    eval_id=$(echo "$initiate_response" | grep -o '"data":{[^}]*}' | grep -o '"id":"[^"]*' | sed 's/"id":"//g')
    if [ -z "$eval_id" ]; then
        eval_id=$(echo "$initiate_response" | grep -o '"evaluation":{[^}]*}' | grep -o '"id":"[^"]*' | sed 's/"id":"//g')
    fi
fi

if [ -z "$eval_id" ]; then
    echo -e "${RED}Failed to extract evaluation ID from response. Cannot proceed with tests.${NC}"
    exit 1
fi

# Step 3: Complete the evaluation
echo -e "\n${BLUE}=== Step 3: Completing the evaluation ===${NC}"
start_eval_response=$(call_api "POST" "/evaluations/$eval_id/start" "")
check_result "Start evaluation" "$start_eval_response"

# Submit 10 answers with high scores to get a good initial learning path
for i in {1..10}; do
    answer_data="{\"questionId\":$i,\"answer\":\"correct_answer\"}"
    answer_response=$(call_api "POST" "/evaluations/$eval_id/answer" "$answer_data")
    if echo "$answer_response" | grep -q '"status":400' && echo "$answer_response" | grep -q 'already answered'; then
        echo -e "${YELLOW}✓ Submit answer $i - Question already answered${NC}"
    else
        check_result "Submit answer $i" "$answer_response"
    fi
done

complete_eval_response=$(call_api "POST" "/evaluations/$eval_id/complete" "")
check_result "Complete evaluation" "$complete_eval_response"

# Step 4: Get the initial learning path
echo -e "\n${BLUE}=== Step 4: Getting the initial learning path ===${NC}"
sleep 2  # Wait for learning path creation
learning_paths_response=$(call_api "GET" "/learning-paths" "")
check_result "Get learning paths" "$learning_paths_response"

# Extract the learning path ID
learning_path_id=$(echo "$learning_paths_response" | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')
if [ -z "$learning_path_id" ]; then
    learning_path_id=$(echo "$learning_paths_response" | grep -o '"data":\[{[^}]*}' | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')
    if [ -z "$learning_path_id" ]; then
        learning_path_id=$(echo "$learning_paths_response" | grep -o '"learningPaths":\[{[^}]*}' | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//g')
    fi
fi

if [ -z "$learning_path_id" ]; then
    echo -e "${RED}Failed to extract learning path ID from response. Cannot proceed with tests.${NC}"
    exit 1
fi

# Step 5: Complete multiple rounds of learning paths with different success rates

# Round 1: Medium success rate (50%)
echo -e "\n${BLUE}=== Round 1: Completing learning path with 50% success rate ===${NC}"
learning_path_id=$(complete_exercise_round "$learning_path_id" 50 1)

# Check if the learning path has more exercises
echo -e "\n${BLUE}=== Checking if the learning path has more exercises after Round 1 ===${NC}"
next_exercise_check=$(call_api "GET" "/learning-paths/$learning_path_id/next-exercise" "")

# Debug output
echo -e "${YELLOW}DEBUG: Next exercise check response:${NC}"
echo "$next_exercise_check" | jq . 2>/dev/null || echo "$next_exercise_check"

# Check if all exercises are completed
if echo "$next_exercise_check" | grep -q '"completed":true'; then
    echo -e "${YELLOW}All exercises completed for this learning path. The system should automatically provide new exercises in the next round.${NC}"
else
    echo -e "${GREEN}Learning path still has exercises. Continuing with current path.${NC}"
fi

# Round 2: High success rate (90%)
echo -e "\n${BLUE}=== Round 2: Completing learning path with 90% success rate ===${NC}"
learning_path_id=$(complete_exercise_round "$learning_path_id" 90 2)

# Check if the learning path has more exercises
echo -e "\n${BLUE}=== Checking if the learning path has more exercises after Round 2 ===${NC}"
next_exercise_check=$(call_api "GET" "/learning-paths/$learning_path_id/next-exercise" "")

# Debug output
echo -e "${YELLOW}DEBUG: Next exercise check response:${NC}"
echo "$next_exercise_check" | jq . 2>/dev/null || echo "$next_exercise_check"

# Check if all exercises are completed
if echo "$next_exercise_check" | grep -q '"completed":true'; then
    echo -e "${YELLOW}All exercises completed for this learning path. The system should automatically provide new exercises in the next round.${NC}"
else
    echo -e "${GREEN}Learning path still has exercises. Continuing with current path.${NC}"
fi

# Round 3: Low success rate (10%)
echo -e "\n${BLUE}=== Round 3: Completing learning path with 10% success rate ===${NC}"
learning_path_id=$(complete_exercise_round "$learning_path_id" 10 3)

# Summary
echo -e "\n${GREEN}Adaptive Learning Test completed!${NC}"
echo -e "The test has verified the adaptive learning capabilities of the system:"
echo -e "1. User registration/login"
echo -e "2. Initiating personalized learning"
echo -e "3. Completing an evaluation"
echo -e "4. Completing multiple rounds of exercises with different success rates:"
echo -e "   - Round 1: Medium success rate (50%)"
echo -e "   - Round 2: High success rate (90%)"
echo -e "   - Round 3: Low success rate (10%)"
echo -e "5. Verifying that exercises adapt during a learning path based on performance"
echo -e "6. Verifying that each new exercise is appropriately customized based on previous performance"
echo -e ""
echo -e "The test demonstrates the system's ability to:"
echo -e "- Adapt exercise difficulty and type within a single learning path"
echo -e "- Provide personalized exercises based on user performance"
echo -e "- Automatically update exercise sets without requiring manual API calls"
echo -e "- Seamlessly transition between exercises without requiring explicit creation of new learning paths"
echo -e ""
echo -e "This 'complete then next' testing approach verifies that the system properly adapts"
echo -e "to user performance in real-time, providing a truly personalized learning experience."
echo -e "The system automatically provides the next appropriate exercise based on the user's"
echo -e "performance history, without requiring any manual intervention or API calls to create"
echo -e "new learning paths."
