# API Testing for Language Learning System

This directory contains automated tests for the Language Learning API. The tests verify the complete flow of the system, from user registration to personalized learning path creation and exercise completion.

## Basic Test Flow

The basic test script (`api_test.sh`) tests the following flow:

1. User registration/login
2. Initiating personalized learning
3. Completing an evaluation
4. Verifying automatic learning path creation
5. Getting and completing exercises
6. Verifying automatic learning path updates
7. Getting recommendations for the next learning path
8. Creating a learning path from recommendation

Note: The system automatically updates learning paths and exercise sets based on user performance. There's no need to manually call update APIs as the system handles this automatically.

## Adaptive Learning Test Flow

The adaptive learning test script (`adaptive_learning_test.sh`) focuses on testing the system's ability to adapt to user performance:

1. User registration/login
2. Initiating personalized learning
3. Completing an evaluation
4. Completing multiple rounds of exercises with different success rates:
   - Round 1: Medium success rate (50%)
   - Round 2: High success rate (90%)
   - Round 3: Low success rate (10%)
5. Verifying that exercises adapt during a learning path based on performance
6. Verifying that each new exercise is appropriately customized based on previous performance

This test verifies that the system properly adapts the difficulty and focus areas of exercises based on the user's performance in real-time. It follows a "complete then next" approach, where after completing each exercise, the system automatically provides the next appropriate exercise without requiring manual API calls to create new learning paths.

The test demonstrates the system's ability to:
- Adapt exercise difficulty and type within a single learning path
- Provide personalized exercises based on user performance
- Automatically update exercise sets without requiring manual API calls
- Seamlessly transition between exercises without requiring explicit creation of new learning paths

## Running the Tests

You can run the tests using the provided `run_tests.sh` script in the root directory:

```bash
./run_tests.sh
```

This script will:
1. Start the required services (PostgreSQL, API, migrations)
2. Seed the database with sample data
3. Run the basic API tests
4. Report the test results

### Running the Adaptive Learning Tests

To run the adaptive learning tests specifically:

```bash
# Make the script executable
chmod +x tests/adaptive_learning_test.sh

# Run the adaptive learning tests
./tests/adaptive_learning_test.sh
```

## Manual Testing

If you want to run the tests manually, you can use Docker Compose directly:

```bash
# Start the required services
docker-compose up -d postgres api migrate seed

# Run the basic tests
docker-compose up --build api-test

# Run the adaptive learning tests
docker exec -it languagelearning-api /bin/sh -c "cd /app && ./tests/adaptive_learning_test.sh"
```

## Test Results

The test script will output detailed results for each step of the test flow. A successful test will show green checkmarks (✓) for each step, while failures will show red X marks (✗).

## Troubleshooting

If the tests fail, check the following:

1. Make sure the API service is running and accessible
2. Verify that the database migrations have been applied successfully
3. Ensure that the database has been seeded with sample data
4. Check the API logs for any errors:
   ```bash
   docker-compose logs api
   ```

## Extending the Tests

To add more tests, modify the `api_test.sh` script. The script uses simple curl commands to interact with the API, making it easy to add new test cases.

## API Flow

The language learning system follows a streamlined API flow that emphasizes automation and personalization:

### Initial Setup Flow
1. **User Registration**: `POST /auth/register` - Creates a new user account
2. **Initiate Personalized Learning**: `POST /personalized-learning/initiate` - Starts the personalized learning process and creates an evaluation
3. **Complete Evaluation**:
   - `POST /evaluations/:id/start` - Begins the evaluation
   - `POST /evaluations/:id/answer` - Submits answers to evaluation questions
   - `POST /evaluations/:id/complete` - Completes the evaluation
   - System automatically creates a personalized learning path based on evaluation results

### Exercise Flow
1. **Get Next Exercise**: `GET /learning-paths/:id/next-exercise` - Retrieves the next exercise in the learning path
   - If all exercises are completed, returns `{"completed": true}`
   - Otherwise, returns the next exercise details
2. **Complete Exercise**: `POST /learning-paths/:id/complete-exercise/:lessonId` - Marks an exercise as completed
   - System automatically updates the exercise set based on performance
   - System automatically adjusts difficulty and focus areas based on performance
3. **Repeat**: Continue getting and completing exercises
   - Each new exercise is automatically customized based on previous performance
   - No need to manually create new learning paths or exercise sets

### Key Points
- The system follows a "complete then next" pattern
- After completing an exercise, simply request the next one
- No manual API calls needed to update exercise sets or create new learning paths
- The system automatically adapts to user performance in real-time

## System Automation Features

The language learning system includes several automation features:

1. **Automatic Learning Path Creation**: After completing an evaluation, the system automatically creates a personalized learning path based on the user's performance.

2. **Automatic Exercise Set Updates**: As users complete exercises, the system automatically updates their exercise sets based on their performance, without requiring manual API calls.

3. **Adaptive Learning**: The system automatically adjusts the difficulty and focus areas of exercises based on the user's progress and performance.

4. **Personalized Recommendations**: The system provides personalized recommendations for the next learning path based on the user's completed learning paths and performance.

5. **Seamless Exercise Progression**: The system automatically provides the next appropriate exercise after completion, without requiring manual creation of new learning paths.

These automation features ensure that users receive a highly customized learning experience without requiring manual intervention.
