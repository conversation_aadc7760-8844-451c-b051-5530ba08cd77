package tests

import (
	"context"
	"testing"

	"languagelearning/domain/user/repository/impl"
	"languagelearning/domain/user/entity"
	"languagelearning/models"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRepositoryPatternMigration 测试repository模式迁移是否成功
func TestRepositoryPatternMigration(t *testing.T) {
	// 设置测试数据库连接
	if models.DB == nil {
		t.Skip("Database not available for testing")
	}

	ctx := context.Background()

	t.Run("UserRepository_CRUD_Operations", func(t *testing.T) {
		// 创建repository实例
		userRepo := impl.NewUserRepository(models.DB)

		// 测试数据
		testUser := &entity.User{
			ID:       uuid.New(),
			Username: "test_user_" + uuid.New().String()[:8],
			Email:    "test_" + uuid.New().String()[:8] + "@example.com",
			Password: "hashed_password",
		}

		// 测试Create
		err := userRepo.Create(ctx, testUser)
		require.NoError(t, err, "Failed to create user via repository")

		// 测试GetByID
		retrievedUser, err := userRepo.GetByID(ctx, testUser.ID)
		require.NoError(t, err, "Failed to get user by ID via repository")
		assert.Equal(t, testUser.Username, retrievedUser.Username)
		assert.Equal(t, testUser.Email, retrievedUser.Email)

		// 测试GetByEmail
		userByEmail, err := userRepo.GetByEmail(ctx, testUser.Email)
		require.NoError(t, err, "Failed to get user by email via repository")
		assert.Equal(t, testUser.ID, userByEmail.ID)

		// 测试Update
		testUser.Username = "updated_username"
		err = userRepo.Update(ctx, testUser)
		require.NoError(t, err, "Failed to update user via repository")

		// 验证更新
		updatedUser, err := userRepo.GetByID(ctx, testUser.ID)
		require.NoError(t, err, "Failed to get updated user")
		assert.Equal(t, "updated_username", updatedUser.Username)

		// 测试Delete
		err = userRepo.Delete(ctx, testUser.ID)
		require.NoError(t, err, "Failed to delete user via repository")

		// 验证删除
		_, err = userRepo.GetByID(ctx, testUser.ID)
		assert.Error(t, err, "User should not exist after deletion")
	})

	t.Run("Repository_Pattern_Consistency", func(t *testing.T) {
		// 验证所有repository都遵循相同的模式
		userRepo := impl.NewUserRepository(models.DB)
		
		// 测试repository接口是否正确实现
		assert.NotNil(t, userRepo, "UserRepository should be properly initialized")
		
		// 测试context支持
		ctx := context.Background()
		_, err := userRepo.GetAll(ctx)
		// 这里我们不关心结果，只关心方法是否存在且接受context
		_ = err // 忽略错误，因为可能没有数据
	})
}

// TestServiceLayerRepositoryUsage 测试服务层是否正确使用repository
func TestServiceLayerRepositoryUsage(t *testing.T) {
	if models.DB == nil {
		t.Skip("Database not available for testing")
	}

	t.Run("Services_Use_Repository_Pattern", func(t *testing.T) {
		// 这个测试验证服务层不再直接使用GORM
		// 通过检查是否能正常创建服务实例来验证依赖注入是否正确
		
		// 创建repository
		userRepo := impl.NewUserRepository(models.DB)
		assert.NotNil(t, userRepo, "UserRepository should be created successfully")
		
		// 如果有其他repository，也可以在这里测试
		// 例如：
		// achievementRepo := achievementImpl.NewAchievementRepository(models.DB)
		// assert.NotNil(t, achievementRepo, "AchievementRepository should be created successfully")
	})
}

// BenchmarkRepositoryPerformance 基准测试repository性能
func BenchmarkRepositoryPerformance(b *testing.B) {
	if models.DB == nil {
		b.Skip("Database not available for benchmarking")
	}

	ctx := context.Background()
	userRepo := impl.NewUserRepository(models.DB)

	// 创建测试用户
	testUser := &entity.User{
		ID:       uuid.New(),
		Username: "bench_user",
		Email:    "<EMAIL>",
		Password: "hashed_password",
	}

	err := userRepo.Create(ctx, testUser)
	if err != nil {
		b.Fatalf("Failed to create test user: %v", err)
	}

	defer func() {
		_ = userRepo.Delete(ctx, testUser.ID)
	}()

	b.Run("GetByID", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := userRepo.GetByID(ctx, testUser.ID)
			if err != nil {
				b.Fatalf("GetByID failed: %v", err)
			}
		}
	})

	b.Run("GetByEmail", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := userRepo.GetByEmail(ctx, testUser.Email)
			if err != nil {
				b.Fatalf("GetByEmail failed: %v", err)
			}
		}
	})
}
