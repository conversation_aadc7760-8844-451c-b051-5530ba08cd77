package main

import (
	"bufio"
	"fmt"
	"os"
	"regexp"
	"strings"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run convert_seeds_to_helpers.go <seed_file_path>")
		os.Exit(1)
	}

	filePath := os.Args[1]
	fmt.Printf("Converting %s to use helper functions...\n", filePath)

	// Read the file
	content, err := os.ReadFile(filePath)
	if err != nil {
		fmt.Printf("Error reading file: %v\n", err)
		os.Exit(1)
	}

	// Convert the content
	converted := convertGrammarExercises(string(content))

	// Write back to file
	err = os.WriteFile(filePath, []byte(converted), 0644)
	if err != nil {
		fmt.Printf("Error writing file: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Successfully converted %s\n", filePath)
}

func convertGrammarExercises(content string) string {
	// Pattern to match GrammarExercise struct literals
	pattern := `\{\s*ID:\s*uuid\.New\(\),\s*Title:\s*"([^"]+)",\s*Question:\s*"([^"]+)",\s*Options:\s*pq\.StringArray\{([^}]+)\},\s*CorrectAnswer:\s*"([^"]+)",\s*Explanation:\s*"([^"]+)",\s*Category:\s*"([^"]+)",\s*Difficulty:\s*models\.(\w+),\s*Instruction:\s*"([^"]+)",\s*ExampleSentence:\s*"([^"]+)",\s*LanguageID:\s*englishLanguage\.ID,\s*\}`

	re := regexp.MustCompile(pattern)
	
	// Replace each match with helper function call
	result := re.ReplaceAllStringFunc(content, func(match string) string {
		// Extract fields using regex groups
		matches := re.FindStringSubmatch(match)
		if len(matches) != 10 {
			return match // Return original if parsing fails
		}

		title := matches[1]
		question := matches[2]
		optionsStr := matches[3]
		correctAnswer := matches[4]
		explanation := matches[5]
		category := matches[6]
		difficulty := matches[7]
		instruction := matches[8]
		exampleSentence := matches[9]

		// Parse options
		options := parseOptions(optionsStr)

		// Generate helper function call
		return fmt.Sprintf(`helpers.NewGrammarExercise(
			"%s",
			"%s",
			"%s",
			"%s",
			"%s",
			"%s",
			"%s",
			%s,
			models.%s,
			englishLanguage.ID,
		)`, title, question, correctAnswer, explanation, category, instruction, exampleSentence, options, difficulty)
	})

	return result
}

func parseOptions(optionsStr string) string {
	// Remove quotes and split by comma
	optionsStr = strings.TrimSpace(optionsStr)
	parts := strings.Split(optionsStr, ",")
	
	var options []string
	for _, part := range parts {
		part = strings.TrimSpace(part)
		part = strings.Trim(part, `"`)
		if part != "" {
			options = append(options, fmt.Sprintf(`"%s"`, part))
		}
	}
	
	return fmt.Sprintf("[]string{%s}", strings.Join(options, ", "))
}

// Alternative simpler approach - line by line replacement
func convertLineByLine(content string) string {
	lines := strings.Split(content, "\n")
	var result []string
	
	inExercise := false
	exerciseLines := []string{}
	
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		
		// Start of exercise
		if strings.Contains(trimmed, "ID:") && strings.Contains(trimmed, "uuid.New()") {
			inExercise = true
			exerciseLines = []string{line}
			continue
		}
		
		// End of exercise
		if inExercise && strings.Contains(trimmed, "},") {
			exerciseLines = append(exerciseLines, line)
			// Convert the exercise
			converted := convertSingleExercise(exerciseLines)
			result = append(result, converted...)
			inExercise = false
			exerciseLines = []string{}
			continue
		}
		
		// Inside exercise
		if inExercise {
			exerciseLines = append(exerciseLines, line)
			continue
		}
		
		// Regular line
		result = append(result, line)
	}
	
	return strings.Join(result, "\n")
}

func convertSingleExercise(lines []string) []string {
	// Extract fields from lines
	fields := make(map[string]string)
	
	for _, line := range lines {
		if strings.Contains(line, "Title:") {
			fields["title"] = extractStringValue(line)
		} else if strings.Contains(line, "Question:") {
			fields["question"] = extractStringValue(line)
		} else if strings.Contains(line, "CorrectAnswer:") {
			fields["correctAnswer"] = extractStringValue(line)
		} else if strings.Contains(line, "Explanation:") {
			fields["explanation"] = extractStringValue(line)
		} else if strings.Contains(line, "Category:") {
			fields["category"] = extractStringValue(line)
		} else if strings.Contains(line, "Instruction:") {
			fields["instruction"] = extractStringValue(line)
		} else if strings.Contains(line, "ExampleSentence:") {
			fields["exampleSentence"] = extractStringValue(line)
		} else if strings.Contains(line, "Difficulty:") {
			fields["difficulty"] = extractDifficultyValue(line)
		} else if strings.Contains(line, "Options:") {
			fields["options"] = extractOptionsValue(line)
		}
	}
	
	// Generate helper function call
	return []string{
		fmt.Sprintf("\t\thelpers.NewGrammarExercise("),
		fmt.Sprintf("\t\t\t%s,", fields["title"]),
		fmt.Sprintf("\t\t\t%s,", fields["question"]),
		fmt.Sprintf("\t\t\t%s,", fields["correctAnswer"]),
		fmt.Sprintf("\t\t\t%s,", fields["explanation"]),
		fmt.Sprintf("\t\t\t%s,", fields["category"]),
		fmt.Sprintf("\t\t\t%s,", fields["instruction"]),
		fmt.Sprintf("\t\t\t%s,", fields["exampleSentence"]),
		fmt.Sprintf("\t\t\t%s,", fields["options"]),
		fmt.Sprintf("\t\t\tmodels.%s,", fields["difficulty"]),
		fmt.Sprintf("\t\t\tenglishLanguage.ID,"),
		fmt.Sprintf("\t\t),"),
	}
}

func extractStringValue(line string) string {
	// Extract string value between quotes
	re := regexp.MustCompile(`"([^"]*)"`)
	matches := re.FindStringSubmatch(line)
	if len(matches) > 1 {
		return fmt.Sprintf(`"%s"`, matches[1])
	}
	return `""`
}

func extractDifficultyValue(line string) string {
	if strings.Contains(line, "models.Easy") {
		return "Easy"
	} else if strings.Contains(line, "models.Medium") {
		return "Medium"
	} else if strings.Contains(line, "models.Hard") {
		return "Hard"
	}
	return "Easy"
}

func extractOptionsValue(line string) string {
	// Extract options array
	re := regexp.MustCompile(`pq\.StringArray\{([^}]+)\}`)
	matches := re.FindStringSubmatch(line)
	if len(matches) > 1 {
		return parseOptions(matches[1])
	}
	return "[]string{}"
}
