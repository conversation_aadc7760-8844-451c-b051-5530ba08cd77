#!/usr/bin/env python3
"""
Advanced script to update Go controllers to use the new response mechanism.
This script handles complex patterns and edge cases that sed cannot handle well.
"""

import os
import re
import sys
import shutil
from pathlib import Path

# Mapping of old utils functions to new response functions
FUNCTION_MAPPINGS = {
    'utils.RespondWithSuccess': 'response.Success',
    'utils.RespondWithServerError': 'response.InternalError',
    'utils.RespondWithNotFound': 'response.NotFound',
    'utils.RespondWithUnauthorized': 'response.Unauthorized',
    'utils.RespondWithValidationError': 'response.ValidationError',
    'utils.HandleValidationErrors': 'response.HandleValidationErrors',
}

def add_response_import(content):
    """Add response import if not already present."""
    if '"languagelearning/utils/response"' in content:
        return content

    # Find the utils import and add response import after it
    pattern = r'(\s*"languagelearning/utils"\s*\n)'
    replacement = r'\1\t"languagelearning/utils/response"\n'

    if re.search(pattern, content):
        content = re.sub(pattern, replacement, content)
    else:
        # If utils import not found, add both imports
        import_pattern = r'(import \(\s*\n)'
        if re.search(import_pattern, content):
            replacement = r'\1\t"languagelearning/utils"\n\t"languagelearning/utils/response"\n'
            content = re.sub(import_pattern, replacement, content, count=1)

    return content

def fix_variable_conflicts(content):
    """Fix variable name conflicts with response package."""
    # Replace 'response :=' with 'responseData :=' to avoid conflicts
    patterns = [
        (r'\bresponse\s*:=\s*gin\.H\{', 'responseData := gin.H{'),
        (r'\bresponse\s*:=\s*([^=\n]+)', r'responseData := \1'),
        (r'response\["([^"]+)"\]', r'responseData["\1"]'),
        (r',\s*response\s*,', ', responseData,'),
        (r'\(response\s*,', '(responseData,'),
        (r'response\.Success\(([^,]+),\s*[^,]+,\s*response\s*,', r'response.Success(\1, http.StatusOK, responseData,'),
        # Fix method calls on response variable
        (r'\bresponse\.Success\(', 'response.Success('),
        (r'\bresponse\.NotFound\(', 'response.NotFound('),
        (r'\bresponse\.InternalError\(', 'response.InternalError('),
        (r'\bresponse\.ValidationError\(', 'response.ValidationError('),
        (r'\bresponse\.Unauthorized\(', 'response.Unauthorized('),
        (r'\bresponse\.BadRequest\(', 'response.BadRequest('),
    ]

    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)

    return content

def handle_respond_with_error(content):
    """Handle complex utils.RespondWithError patterns."""
    # Pattern 1: utils.RespondWithError(ctx, http.StatusBadRequest, utils.NewErrorResponse("CODE", "message"))
    pattern1 = r'utils\.RespondWithError\(([^,]+),\s*http\.StatusBadRequest,\s*utils\.NewErrorResponse\([^,]+,\s*([^)]+)\)\)'
    replacement1 = r'response.BadRequest(\1, \2)'
    content = re.sub(pattern1, replacement1, content)

    # Pattern 2: utils.RespondWithError(ctx, statusCode, err, message)
    pattern2 = r'utils\.RespondWithError\(([^,]+),\s*[^,]+,\s*[^,]+,\s*([^)]+)\)'
    replacement2 = r'response.BadRequest(\1, \2)'
    content = re.sub(pattern2, replacement2, content)

    # Pattern 3: Any remaining utils.RespondWithError calls
    pattern3 = r'utils\.RespondWithError\('
    replacement3 = 'response.BadRequest('
    content = re.sub(pattern3, replacement3, content)

    return content

def fix_additional_issues(content):
    """Fix additional issues found after initial conversion."""
    # Remove unused utils import if no utils functions are used
    if not re.search(r'utils\.[A-Z]', content):
        content = re.sub(r'\s*"languagelearning/utils"\s*\n', '', content)

    # Fix AppError references
    content = content.replace('models.AppError', 'errors.AppError')
    content = content.replace('&models.AppError', '&errors.AppError')
    content = content.replace('(*models.AppError)', '(*errors.AppError)')

    # Add errors import if AppError is used
    if 'errors.AppError' in content and '"languagelearning/utils/errors"' not in content:
        # Find the utils/response import and add errors import after it
        pattern = r'(\s*"languagelearning/utils/response"\s*\n)'
        replacement = r'\1\t"languagelearning/utils/errors"\n'
        content = re.sub(pattern, replacement, content)

    # Fix variable conflicts more thoroughly
    # Look for patterns like: response, err := someFunction()
    content = re.sub(r'\bresponse,\s*err\s*:=', 'responseData, err :=', content)
    content = re.sub(r'\bresponse\s*,\s*err\s*:=', 'responseData, err :=', content)

    # Fix method calls on response variables that should be responseData
    lines = content.split('\n')
    fixed_lines = []

    for line in lines:
        # If line contains response.SomeMethod but not the package response.
        if re.search(r'\s+response\.(Success|NotFound|InternalError|ValidationError|Unauthorized|BadRequest)\(', line):
            # Check if this is a method call on a variable (not the package)
            # Look for indentation or assignment patterns
            if re.search(r'^\s+response\.(Success|NotFound|InternalError|ValidationError|Unauthorized|BadRequest)\(', line):
                # This is likely a method call on a variable, change to responseData
                line = re.sub(r'\bresponse\.', 'responseData.', line)
                # But then change it back to the package call
                line = re.sub(r'responseData\.(Success|NotFound|InternalError|ValidationError|Unauthorized|BadRequest)\(', r'response.\1(', line)
        fixed_lines.append(line)

    content = '\n'.join(fixed_lines)

    return content

def update_file_content(content):
    """Update the content of a Go file."""
    # Add response import
    content = add_response_import(content)

    # Replace function calls
    for old_func, new_func in FUNCTION_MAPPINGS.items():
        content = content.replace(old_func, new_func)

    # Handle special cases
    content = handle_respond_with_error(content)
    content = fix_variable_conflicts(content)
    content = fix_additional_issues(content)

    return content

def update_file(file_path):
    """Update a single Go file."""
    print(f"📝 Updating {file_path}...")

    # Read the file
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
    except Exception as e:
        print(f"❌ Error reading {file_path}: {e}")
        return False

    # Check if file needs updating
    needs_update = any(func in original_content for func in FUNCTION_MAPPINGS.keys())
    needs_update = needs_update or 'utils.RespondWithError' in original_content

    if not needs_update:
        print(f"⏭️  Skipping {file_path} (already updated)")
        return False

    # Create backup
    backup_path = f"{file_path}.backup"
    try:
        shutil.copy2(file_path, backup_path)
    except Exception as e:
        print(f"❌ Error creating backup for {file_path}: {e}")
        return False

    # Update content
    updated_content = update_file_content(original_content)

    # Write updated content
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print(f"✅ Updated {file_path}")
        return True
    except Exception as e:
        print(f"❌ Error writing {file_path}: {e}")
        # Restore from backup
        shutil.copy2(backup_path, file_path)
        return False

def main():
    """Main function."""
    print("🚀 Starting advanced controller update script...")

    controllers_dir = Path("controllers")
    if not controllers_dir.exists():
        print(f"❌ Controllers directory not found: {controllers_dir}")
        sys.exit(1)

    updated_count = 0
    skipped_count = 0
    error_count = 0

    # Find all Go files in controllers directory and subdirectories
    go_files = list(controllers_dir.rglob("*.go"))

    for file_path in go_files:
        try:
            if update_file(file_path):
                updated_count += 1
            else:
                skipped_count += 1
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")
            error_count += 1

    print("\n🎉 Controller update completed!")
    print("📊 Summary:")
    print(f"   - Updated files: {updated_count}")
    print(f"   - Skipped files: {skipped_count}")
    print(f"   - Errors: {error_count}")
    print("\n💡 Next steps:")
    print("   1. Review the changes: git diff")
    print("   2. Test the application: go run main.go")
    print("   3. Run tests: go test ./...")
    print("   4. If everything works, remove backup files: rm controllers/*.backup")

if __name__ == "__main__":
    main()
