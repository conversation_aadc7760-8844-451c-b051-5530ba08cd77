package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"strings"
)

// LessonDefinition 表示一个Lesson定义
type LessonDefinition struct {
	Path        string
	PackageName string
	StructName  string
	Fields      []string
	Methods     []string
}

// LessonUsage 表示Lesson的使用情况
type LessonUsage struct {
	FilePath    string
	ImportPath  string
	UsageType   string // "import", "field", "parameter", "return"
	LineNumber  int
	Context     string
}

func main() {
	fmt.Println("🔍 分析Lesson重复定义使用情况...")
	
	// 查找所有Lesson定义
	definitions := findLessonDefinitions()
	
	// 查找所有Lesson使用
	usages := findLessonUsages()
	
	// 生成报告
	generateReport(definitions, usages)
}

func findLessonDefinitions() []LessonDefinition {
	var definitions []LessonDefinition
	
	// 定义要检查的路径
	paths := []string{
		"models/lesson.go",
		"domain/lesson/entity/lesson.go",
		"domain/learning/entity/lesson.go",
		"domain/learning/lesson/entity/lesson.go",
	}
	
	for _, path := range paths {
		if definition := analyzeLessonFile(path); definition != nil {
			definitions = append(definitions, *definition)
		}
	}
	
	return definitions
}

func analyzeLessonFile(filePath string) *LessonDefinition {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil
	}
	
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, filePath, nil, parser.ParseComments)
	if err != nil {
		fmt.Printf("❌ 解析文件失败 %s: %v\n", filePath, err)
		return nil
	}
	
	definition := &LessonDefinition{
		Path:        filePath,
		PackageName: node.Name.Name,
		Fields:      []string{},
		Methods:     []string{},
	}
	
	// 遍历AST查找Lesson结构体
	ast.Inspect(node, func(n ast.Node) bool {
		switch x := n.(type) {
		case *ast.TypeSpec:
			if x.Name.Name == "Lesson" {
				definition.StructName = x.Name.Name
				if structType, ok := x.Type.(*ast.StructType); ok {
					for _, field := range structType.Fields.List {
						for _, name := range field.Names {
							definition.Fields = append(definition.Fields, name.Name)
						}
					}
				}
			}
		case *ast.FuncDecl:
			// 查找Lesson的方法
			if x.Recv != nil && len(x.Recv.List) > 0 {
				if starExpr, ok := x.Recv.List[0].Type.(*ast.StarExpr); ok {
					if ident, ok := starExpr.X.(*ast.Ident); ok && ident.Name == "Lesson" {
						definition.Methods = append(definition.Methods, x.Name.Name)
					}
				} else if ident, ok := x.Recv.List[0].Type.(*ast.Ident); ok && ident.Name == "Lesson" {
					definition.Methods = append(definition.Methods, x.Name.Name)
				}
			}
		}
		return true
	})
	
	return definition
}

func findLessonUsages() []LessonUsage {
	var usages []LessonUsage
	
	// 遍历所有Go文件
	err := filepath.Walk(".", func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 跳过vendor和.git目录
		if strings.Contains(path, "vendor/") || strings.Contains(path, ".git/") {
			return nil
		}
		
		// 只处理Go文件
		if !strings.HasSuffix(path, ".go") {
			return nil
		}
		
		// 分析文件中的Lesson使用
		fileUsages := analyzeLessonUsageInFile(path)
		usages = append(usages, fileUsages...)
		
		return nil
	})
	
	if err != nil {
		fmt.Printf("❌ 遍历文件失败: %v\n", err)
	}
	
	return usages
}

func analyzeLessonUsageInFile(filePath string) []LessonUsage {
	var usages []LessonUsage
	
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, filePath, nil, parser.ParseComments)
	if err != nil {
		return usages
	}
	
	// 检查imports
	for _, imp := range node.Imports {
		importPath := strings.Trim(imp.Path.Value, "\"")
		if strings.Contains(importPath, "lesson") || strings.Contains(importPath, "learning") {
			usages = append(usages, LessonUsage{
				FilePath:    filePath,
				ImportPath:  importPath,
				UsageType:   "import",
				LineNumber:  fset.Position(imp.Pos()).Line,
				Context:     importPath,
			})
		}
	}
	
	// 检查类型使用
	ast.Inspect(node, func(n ast.Node) bool {
		switch x := n.(type) {
		case *ast.SelectorExpr:
			if x.Sel.Name == "Lesson" {
				usages = append(usages, LessonUsage{
					FilePath:   filePath,
					UsageType:  "selector",
					LineNumber: fset.Position(x.Pos()).Line,
					Context:    fmt.Sprintf("%s.Lesson", getIdentName(x.X)),
				})
			}
		case *ast.Ident:
			if x.Name == "Lesson" {
				usages = append(usages, LessonUsage{
					FilePath:   filePath,
					UsageType:  "identifier",
					LineNumber: fset.Position(x.Pos()).Line,
					Context:    "Lesson",
				})
			}
		}
		return true
	})
	
	return usages
}

func getIdentName(expr ast.Expr) string {
	if ident, ok := expr.(*ast.Ident); ok {
		return ident.Name
	}
	return "unknown"
}

func generateReport(definitions []LessonDefinition, usages []LessonUsage) {
	fmt.Println("\n📊 Lesson定义分析报告")
	fmt.Println("=" + strings.Repeat("=", 50))
	
	// 定义统计
	fmt.Printf("\n🏗️  发现 %d 个Lesson定义:\n", len(definitions))
	for i, def := range definitions {
		fmt.Printf("\n%d. %s\n", i+1, def.Path)
		fmt.Printf("   包名: %s\n", def.PackageName)
		fmt.Printf("   字段数: %d\n", len(def.Fields))
		fmt.Printf("   方法数: %d\n", len(def.Methods))
		
		if len(def.Fields) > 0 {
			fmt.Printf("   主要字段: %s\n", strings.Join(def.Fields[:min(5, len(def.Fields))], ", "))
		}
		if len(def.Methods) > 0 {
			fmt.Printf("   主要方法: %s\n", strings.Join(def.Methods[:min(3, len(def.Methods))], ", "))
		}
	}
	
	// 使用统计
	fmt.Printf("\n📈 发现 %d 个Lesson使用:\n", len(usages))
	
	// 按文件分组统计
	fileUsageCount := make(map[string]int)
	for _, usage := range usages {
		fileUsageCount[usage.FilePath]++
	}
	
	fmt.Printf("\n📁 按文件统计使用情况:\n")
	for file, count := range fileUsageCount {
		fmt.Printf("   %s: %d次\n", file, count)
	}
	
	// 按导入路径分组
	importUsages := make(map[string][]LessonUsage)
	for _, usage := range usages {
		if usage.UsageType == "import" {
			importUsages[usage.ImportPath] = append(importUsages[usage.ImportPath], usage)
		}
	}
	
	fmt.Printf("\n📦 按导入路径统计:\n")
	for importPath, usageList := range importUsages {
		fmt.Printf("   %s: %d个文件\n", importPath, len(usageList))
	}
	
	// 生成清理建议
	generateCleanupRecommendations(definitions, usages)
}

func generateCleanupRecommendations(definitions []LessonDefinition, usages []LessonUsage) {
	fmt.Println("\n💡 清理建议")
	fmt.Println("=" + strings.Repeat("=", 50))
	
	fmt.Println("\n🎯 推荐的清理策略:")
	
	// 分析每个定义的复杂度和使用情况
	for _, def := range definitions {
		usageCount := countUsagesForDefinition(def, usages)
		complexity := len(def.Fields) + len(def.Methods)
		
		fmt.Printf("\n📋 %s:\n", def.Path)
		fmt.Printf("   复杂度: %d (字段: %d, 方法: %d)\n", complexity, len(def.Fields), len(def.Methods))
		fmt.Printf("   使用次数: %d\n", usageCount)
		
		// 给出建议
		if strings.Contains(def.Path, "models/") {
			fmt.Printf("   建议: 保留作为数据库模型，简化业务逻辑\n")
		} else if complexity > 20 && usageCount > 5 {
			fmt.Printf("   建议: 作为主要业务实体保留\n")
		} else if usageCount < 3 {
			fmt.Printf("   建议: 考虑移除或合并到其他定义\n")
		} else {
			fmt.Printf("   建议: 需要进一步分析\n")
		}
	}
	
	fmt.Println("\n🚀 推荐的实施步骤:")
	fmt.Println("1. 保留 models/lesson.go 作为数据库模型")
	fmt.Println("2. 选择最完善的domain实体作为主要业务实体")
	fmt.Println("3. 创建转换方法在repository层")
	fmt.Println("4. 逐步迁移所有引用")
	fmt.Println("5. 移除重复定义")
}

func countUsagesForDefinition(def LessonDefinition, usages []LessonUsage) int {
	count := 0
	for _, usage := range usages {
		if strings.Contains(usage.ImportPath, def.PackageName) || 
		   strings.Contains(usage.FilePath, filepath.Dir(def.Path)) {
			count++
		}
	}
	return count
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
