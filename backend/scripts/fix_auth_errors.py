#!/usr/bin/env python3
"""
Script to fix auth service errors by replacing utils.ErrorResponse with standard errors.
"""

import re

def fix_auth_service():
    file_path = "domain/auth/service/impl/auth_service.go"
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Add errors import if not present
    if '"errors"' not in content:
        content = re.sub(
            r'import \(\s*\n\s*"context"\s*\n\s*"time"',
            'import (\n\t"context"\n\t"errors"\n\t"time"',
            content
        )
    
    # Replace all utils.ErrorResponse patterns with simple errors.New
    patterns = [
        # Pattern for invalid credentials
        (r'return "", &utils\.ErrorResponse\{\s*Code:\s*"invalid_credentials",\s*Message:\s*"[^"]*",\s*\}', 
         'return "", errors.New("invalid credentials")'),
        
        # Pattern for user not found
        (r'return "", &utils\.ErrorResponse\{\s*Code:\s*"user_not_found",\s*Message:\s*"[^"]*",\s*\}', 
         'return "", errors.New("user not found")'),
        
        # Pattern for user inactive
        (r'return "", &utils\.ErrorResponse\{\s*Code:\s*"user_inactive",\s*Message:\s*"[^"]*",\s*\}', 
         'return "", errors.New("user account is not active")'),
        
        # Pattern for invalid token
        (r'return uuid\.Nil, &utils\.ErrorResponse\{\s*Code:\s*"invalid_token",\s*Message:\s*"[^"]*",\s*\}', 
         'return uuid.Nil, errors.New("invalid token")'),
        
        # Pattern for user not found (uuid.Nil return)
        (r'return uuid\.Nil, &utils\.ErrorResponse\{\s*Code:\s*"user_not_found",\s*Message:\s*"[^"]*",\s*\}', 
         'return uuid.Nil, errors.New("user not found")'),
        
        # Pattern for user inactive (uuid.Nil return)
        (r'return uuid\.Nil, &utils\.ErrorResponse\{\s*Code:\s*"user_inactive",\s*Message:\s*"[^"]*",\s*\}', 
         'return uuid.Nil, errors.New("user account is not active")'),
        
        # Pattern for change password errors
        (r'return &utils\.ErrorResponse\{\s*Code:\s*"user_not_found",\s*Message:\s*"[^"]*",\s*\}', 
         'return errors.New("user not found")'),
        
        (r'return &utils\.ErrorResponse\{\s*Code:\s*"invalid_password",\s*Message:\s*"[^"]*",\s*\}', 
         'return errors.New("invalid password")'),
    ]
    
    # Apply all patterns
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # Remove utils/response import if no longer needed
    if 'utils/response' in content and 'response.' not in content:
        content = re.sub(r'\s*"languagelearning/utils/response"\s*\n', '', content)
    
    # Write the file back
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed auth service errors in {file_path}")

if __name__ == "__main__":
    fix_auth_service()
