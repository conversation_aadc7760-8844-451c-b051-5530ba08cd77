#!/bin/bash

# This script generates the OpenAPI specification file

# Make sure the script is executable
# chmod +x scripts/generate_openapi.sh

# Install swag if not already installed
if ! command -v swag &> /dev/null; then
    echo "Installing swag..."
    go install github.com/swaggo/swag/cmd/swag@latest
fi

# Generate Swagger documentation
echo "Generating Swagger documentation..."
swag init -g main.go -o ./cmd/docs/swagger

# Check if the generation was successful
if [ $? -eq 0 ]; then
    echo "Swagger documentation generated successfully!"
    echo "You can access the Swagger UI at: http://localhost:8080/swagger/index.html"
else
    echo "Failed to generate Swagger documentation."
    exit 1
fi

# Copy the swagger.json file to the root directory for convenience
echo "Copying swagger.json to the root directory..."
cp ./cmd/docs/swagger/swagger.json ./openapi.json

echo "Done! OpenAPI specification is available at ./openapi.json"
