# Exercise 定义清理计划

## 🔍 当前状况分析

### 三个不同的Exercise定义

#### 1. `models/exercise.go` (数据库层)
- **用途**: 数据库映射，包含GORM标签
- **特点**: 
  - 包含多个具体类型 (Exercise, GrammarExercise, ListeningExercise, SpeakingExercise)
  - 中文常量定义 (ExMultipleChoice = "选择题")
  - 简单的Difficulty枚举
- **使用场景**: 数据库迁移、GORM操作

#### 2. `domain/exercise/entity/exercise.go` (练习领域)
- **用途**: 练习领域的业务实体
- **特点**:
  - 英文常量定义 (MultipleChoice = "multiple_choice")
  - 复杂的ExerciseDifficulty值对象
  - 包含统计信息 (ExerciseStats)
  - 引用 `domain/learning/entity` 的Language和Duration
- **使用场景**: 练习相关的业务逻辑

#### 3. `domain/learning/entity/exercise.go` (学习领域)
- **用途**: 学习领域的练习实体
- **特点**:
  - 英文常量定义 (VocabularyExercise = "vocabulary")
  - 包含Title, Description, Content字段
  - 自定义的ExerciseDifficulty和Language
  - 更多业务方法 (Publish, Unpublish)
- **使用场景**: 学习路径、课程管理

## 🎯 清理策略

### Phase 1: 统一业务实体
**目标**: 将 `domain/exercise/entity/exercise.go` 作为主要的业务实体

**原因**:
1. 它位于专门的exercise领域
2. 设计更加完善，包含统计信息
3. 已经被repository层使用

### Phase 2: 简化数据库模型
**目标**: 保留 `models/exercise.go` 仅作为数据库映射

**策略**:
1. 保留基础的Exercise模型用于数据库映射
2. 移除具体的类型模型 (GrammarExercise, ListeningExercise等)
3. 统一使用Exercise + Type字段的方式

### Phase 3: 移除重复定义
**目标**: 删除 `domain/learning/entity/exercise.go` 中的Exercise定义

**策略**:
1. 更新所有引用指向 `domain/exercise/entity`
2. 迁移有用的字段和方法到主要实体
3. 保留ExerciseRelation在learning领域

## 📝 实施步骤

### Step 1: 增强主要Exercise实体
在 `domain/exercise/entity/exercise.go` 中添加缺失的字段：
- Title, Description, Content (来自learning实体)
- Publish/Unpublish方法

### Step 2: 创建类型映射
创建常量映射，统一不同领域的类型定义：
```go
// 统一类型映射
var TypeMapping = map[string]ExerciseType{
    "vocabulary": VocabularyExercise,
    "grammar":    GrammarExercise,
    "listening":  ListeningExercise,
    // ...
}
```

### Step 3: 更新Repository转换方法
修改repository中的转换方法，处理不同的类型映射

### Step 4: 逐步迁移引用
1. 更新 `domain/learning` 中的引用
2. 更新service层的引用
3. 更新controller层的引用

### Step 5: 清理未使用的定义
删除重复的定义文件

## 🔧 技术实施

### 1. 增强主要Exercise实体
```go
// 在 domain/exercise/entity/exercise.go 中添加
type Exercise struct {
    // 现有字段...
    Title       string `json:"title"`
    Description string `json:"description"`
    Content     string `json:"content"`
    // ...
}

// 添加业务方法
func (e *Exercise) Publish() { /* ... */ }
func (e *Exercise) Unpublish() { /* ... */ }
```

### 2. 统一类型常量
```go
// 创建类型映射和转换函数
func ConvertLearningType(learningType string) ExerciseType {
    mapping := map[string]ExerciseType{
        "vocabulary": VocabularyExercise,
        "grammar":    GrammarExercise,
        // ...
    }
    return mapping[learningType]
}
```

### 3. 更新Repository
```go
// 更新转换方法处理新字段
func (r *GormExerciseRepository) mapToEntity(model models.Exercise) *entity.Exercise {
    return &entity.Exercise{
        // 映射所有字段，包括新添加的
        Title:       model.Title,
        Description: model.Description,
        // ...
    }
}
```

## 🎯 预期结果

### 清理后的结构
```
models/
└── exercise.go              # 简化的数据库模型

domain/
├── exercise/
│   └── entity/
│       └── exercise.go      # 统一的业务实体
└── learning/
    └── entity/
        └── exercise_relation.go  # 仅保留关系定义
```

### 统一的类型系统
- 所有业务逻辑使用 `domain/exercise/entity.Exercise`
- 数据库操作使用 `models.Exercise`
- Repository负责两者之间的转换
- 类型常量统一使用英文定义

## ⚠️ 风险和注意事项

1. **向后兼容性**: 确保API响应格式不变
2. **数据库迁移**: 可能需要数据迁移脚本
3. **测试覆盖**: 确保所有转换逻辑都有测试
4. **渐进式迁移**: 分步骤进行，避免大规模破坏性更改
