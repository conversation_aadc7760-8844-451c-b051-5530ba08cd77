package main

import (
	"fmt"
	"languagelearning/models"
	"languagelearning/seeds"
	"log"
)

func main() {
	// Initialize database connection
	// models.InitDB()
	log.Println("Database connected successfully")

	// Run only the vocabulary seeder
	log.Println("Starting vocabulary seeding...")
	err := seeds.SeedVocabulary(models.DB)
	if err != nil {
		log.Fatalf("Error seeding vocabulary: %v", err)
	}

	// Count the vocabulary exercises in the database
	var count int64
	models.DB.Model(&models.GrammarExercise{}).Where("category = ?", "Vocabulary").Count(&count)

	fmt.Printf("Successfully seeded vocabulary data! Total vocabulary exercises: %d\n", count)
}
