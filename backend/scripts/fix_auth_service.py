#!/usr/bin/env python3
"""
Script to fix auth service errors by replacing utils.ErrorResponse with standard errors.
"""

import re

def fix_auth_service():
    file_path = "services/auth/auth.go"
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Add errors import if not present
    if '"errors"' not in content:
        content = re.sub(
            r'(import \(\s*\n)',
            r'\1\t"errors"\n',
            content
        )
    
    # Replace all utils.ErrorResponse patterns with simple errors.New
    patterns = [
        # Pattern for invalid credentials
        (r'return nil, &utils\.ErrorResponse\{\s*Code:\s*"invalid_credentials",\s*Message:\s*"[^"]*",\s*\}', 
         'return nil, errors.New("invalid credentials")'),
        
        # Pattern for username exists
        (r'return nil, &utils\.ErrorResponse\{\s*Code:\s*"username_exists",\s*Message:\s*"[^"]*",\s*\}', 
         'return nil, errors.New("username already exists")'),
        
        # Pattern for email exists
        (r'return nil, &utils\.ErrorResponse\{\s*Code:\s*"email_exists",\s*Message:\s*"[^"]*",\s*\}', 
         'return nil, errors.New("email already registered")'),
        
        # Pattern for user not found
        (r'return &utils\.ErrorResponse\{\s*Code:\s*"user_not_found",\s*Message:\s*"[^"]*",\s*\}', 
         'return errors.New("user not found")'),
        
        # Pattern for invalid password
        (r'return &utils\.ErrorResponse\{\s*Code:\s*"invalid_password",\s*Message:\s*"[^"]*",\s*\}', 
         'return errors.New("invalid password")'),
        
        # Pattern for invalid refresh token
        (r'return "", "", &utils\.ErrorResponse\{\s*Code:\s*"invalid_refresh_token",\s*Message:\s*"[^"]*",\s*\}', 
         'return "", "", errors.New("invalid refresh token")'),
        
        # Pattern for user inactive
        (r'return "", "", &utils\.ErrorResponse\{\s*Code:\s*"user_inactive",\s*Message:\s*"[^"]*",\s*\}', 
         'return "", "", errors.New("user account is not active")'),
    ]
    
    # Apply all patterns
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # Write the file back
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed auth service errors in {file_path}")

if __name__ == "__main__":
    fix_auth_service()
