#!/bin/bash

# <PERSON>ript to update all controllers to use the new response mechanism
# This script replaces old utils.RespondWith* calls with new response.* calls

echo "🚀 Starting controller update script..."

# Define the controllers directory
CONTROLLERS_DIR="controllers"

# Check if controllers directory exists
if [ ! -d "$CONTROLLERS_DIR" ]; then
    echo "❌ Controllers directory not found: $CONTROLLERS_DIR"
    exit 1
fi

# Function to update a single file
update_file() {
    local file="$1"
    echo "📝 Updating $file..."
    
    # Create a backup
    cp "$file" "$file.backup"
    
    # Add response import if not already present
    if ! grep -q '"languagelearning/utils/response"' "$file"; then
        # Find the utils import line and add response import after it
        sed -i.tmp '/languagelearning\/utils"/a\
	"languagelearning/utils/response"' "$file"
        rm -f "$file.tmp"
    fi
    
    # Replace utils.RespondWith* calls with response.* calls
    sed -i.tmp 's/utils\.RespondWithSuccess(/response.Success(/g' "$file"
    sed -i.tmp 's/utils\.RespondWithServerError(/response.InternalError(/g' "$file"
    sed -i.tmp 's/utils\.RespondWithNotFound(/response.NotFound(/g' "$file"
    sed -i.tmp 's/utils\.RespondWithUnauthorized(/response.Unauthorized(/g' "$file"
    sed -i.tmp 's/utils\.RespondWithValidationError(/response.ValidationError(/g' "$file"
    sed -i.tmp 's/utils\.HandleValidationErrors(/response.HandleValidationErrors(/g' "$file"
    
    # Handle utils.RespondWithError calls - these need special handling
    # Replace utils.RespondWithError with response.BadRequest for most cases
    sed -i.tmp 's/utils\.RespondWithError(ctx, http\.StatusBadRequest, utils\.NewErrorResponse([^,]*,[^)]*)/response.BadRequest(ctx/g' "$file"
    sed -i.tmp 's/utils\.RespondWithError(ctx, http\.StatusBadRequest, [^,]*, \([^)]*\))/response.BadRequest(ctx, \1)/g' "$file"
    
    # Clean up any remaining utils.RespondWithError calls
    sed -i.tmp 's/utils\.RespondWithError(/response.BadRequest(/g' "$file"
    
    # Handle variable name conflicts (response vs response package)
    # Replace 'response :=' with 'responseData :=' to avoid conflicts
    sed -i.tmp 's/response := /responseData := /g' "$file"
    sed -i.tmp 's/response\[/responseData[/g' "$file"
    sed -i.tmp 's/, response,/, responseData,/g' "$file"
    sed -i.tmp 's/(response,/(responseData,/g' "$file"
    
    # Remove temporary files
    rm -f "$file.tmp"
    
    echo "✅ Updated $file"
}

# Function to check if file needs updating
needs_update() {
    local file="$1"
    if grep -q "utils\.RespondWith\|utils\.HandleValidationErrors" "$file"; then
        return 0  # needs update
    else
        return 1  # doesn't need update
    fi
}

# Counter for updated files
updated_count=0
skipped_count=0

# Process all .go files in controllers directory
for file in "$CONTROLLERS_DIR"/*.go; do
    if [ -f "$file" ]; then
        if needs_update "$file"; then
            update_file "$file"
            ((updated_count++))
        else
            echo "⏭️  Skipping $file (already updated)"
            ((skipped_count++))
        fi
    fi
done

# Process subdirectories
for subdir in "$CONTROLLERS_DIR"/*/; do
    if [ -d "$subdir" ]; then
        for file in "$subdir"*.go; do
            if [ -f "$file" ]; then
                if needs_update "$file"; then
                    update_file "$file"
                    ((updated_count++))
                else
                    echo "⏭️  Skipping $file (already updated)"
                    ((skipped_count++))
                fi
            fi
        done
    fi
done

echo ""
echo "🎉 Controller update completed!"
echo "📊 Summary:"
echo "   - Updated files: $updated_count"
echo "   - Skipped files: $skipped_count"
echo ""
echo "💡 Next steps:"
echo "   1. Review the changes: git diff"
echo "   2. Test the application: go run main.go"
echo "   3. Run tests: go test ./..."
echo "   4. If everything works, remove backup files: rm controllers/*.backup"
echo ""
