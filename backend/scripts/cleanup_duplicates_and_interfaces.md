# 清理重复定义和统一接口命名规范

## 🎯 目标

1. 清理 `models/` 和 `domain/*/entity/` 的重复定义
2. 统一接口命名规范
3. 建立清晰的数据转换层

## 📊 当前问题分析

### 重复定义问题

#### 1. Exercise 相关
- ❌ `models/exercise.go` - 包含多个exercise类型 (Exercise, GrammarExercise, ListeningExercise, SpeakingExercise)
- ❌ `domain/exercise/entity/exercise.go` - 通用exercise实体
- ❌ `domain/learning/entity/exercise.go` - 学习领域的exercise实体

#### 2. User 相关
- ✅ `models/user.go` - 数据库模型 (保留)
- ✅ `domain/user/entity/user.go` - 业务实体 (保留)
- ✅ 已有转换方法

#### 3. Lesson 相关
- ❌ `models/lesson.go` - 数据库模型
- ❌ `domain/lesson/entity/lesson.go` - 课程领域实体
- ❌ `domain/learning/entity/lesson.go` - 学习领域实体

#### 4. Notification 相关
- ❌ `models/notification.go` - 数据库模型
- ✅ `domain/notification/entity/notification.go` - 业务实体 (保留)

### 接口命名不一致问题

#### 当前命名模式
- `UserService` ✅ (正确)
- `WordService` ✅ (正确)
- `UserRepository` ✅ (正确)
- `ExerciseRepository` ✅ (正确)
- `Module` ❌ (应该是 `ModuleInterface` 或保持简洁)
- `TransactionManager` ❌ (应该是 `TransactionManagerInterface`)

## 🚀 实施计划

### Phase 1: 统一接口命名规范

#### 规范定义
1. **Service接口**: 以 `Service` 结尾
2. **Repository接口**: 以 `Repository` 结尾
3. **Manager接口**: 以 `Manager` 结尾
4. **其他业务接口**: 根据功能命名，保持简洁

#### 需要重命名的接口
- `Module` → 保持不变 (DI框架接口，简洁性优先)
- `TransactionManager` → 保持不变 (已经有Manager后缀)

### Phase 2: 清理重复的Exercise定义

#### 策略
1. **保留**: `domain/exercise/entity/exercise.go` 作为主要业务实体
2. **简化**: `models/exercise.go` 只保留数据库映射
3. **移除**: `domain/learning/entity/exercise.go` 中的重复定义
4. **添加**: 转换方法在repository层

### Phase 3: 清理重复的Lesson定义

#### 策略
1. **保留**: `domain/lesson/entity/lesson.go` 作为主要业务实体
2. **简化**: `models/lesson.go` 只保留数据库映射
3. **移除**: `domain/learning/entity/lesson.go` 中的重复定义

### Phase 4: 清理重复的Notification定义

#### 策略
1. **保留**: `domain/notification/entity/notification.go` 作为主要业务实体
2. **移除**: `models/notification.go` 中的重复定义
3. **更新**: 所有引用使用domain entity

## 📝 实施步骤

### Step 1: 分析依赖关系
- 检查哪些文件引用了重复的定义
- 确定转换的优先级

### Step 2: 创建转换方法
- 在repository层添加model <-> entity转换方法
- 确保类型安全

### Step 3: 逐步迁移
- 从最少依赖的开始
- 更新所有引用
- 运行测试确保功能正常

### Step 4: 清理未使用的定义
- 删除重复的定义
- 更新import语句

## 🎯 预期结果

### 清理后的结构
```
models/
├── exercise.go          # 仅数据库映射
├── lesson.go           # 仅数据库映射
├── user.go             # 仅数据库映射
└── ...

domain/
├── exercise/entity/exercise.go     # 主要业务实体
├── lesson/entity/lesson.go         # 主要业务实体
├── user/entity/user.go             # 主要业务实体
├── notification/entity/notification.go  # 主要业务实体
└── ...
```

### 统一的接口命名
- 所有Service接口以 `Service` 结尾
- 所有Repository接口以 `Repository` 结尾
- 保持命名的一致性和可预测性
