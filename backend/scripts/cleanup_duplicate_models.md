# 清理重复模型定义的计划

## 当前问题
1. `models/` 和 `domain/*/entity/` 中存在重复的模型定义
2. 一些服务层直接使用GORM而不是repository模式

## 清理策略

### 1. 保留结构
- **Domain Entities** (`domain/*/entity/`): 纯粹的业务实体，包含业务逻辑
- **Models** (`models/`): 数据库映射层，包含GORM标签，主要用于持久化

### 2. 转换规则
- 所有业务逻辑使用domain entities
- Repository层负责models和entities之间的转换
- Controllers接收/返回DTOs，内部使用domain entities

### 3. 需要清理的重复定义

#### User相关
- [x] `models/user.go` - 保留作为数据库模型
- [x] `domain/user/entity/user.go` - 保留作为业务实体
- [x] 确保转换方法正确

#### LearningPath相关
- [ ] `models/learning_path.go` - 需要简化
- [ ] `domain/learning/path/entity/learning_path.go` - 保留作为业务实体
- [ ] 添加转换方法

#### Lesson相关
- [ ] 检查是否存在重复
- [ ] 如果存在，应用相同的清理策略

#### Exercise相关
- [ ] 检查是否存在重复
- [ ] 如果存在，应用相同的清理策略

### 4. 实施步骤

1. **完成GORM清理** ✅
   - [x] 移除domain services中的直接GORM调用
   - [x] 添加TransactionManager接口
   - [x] 更新UserService使用repository模式

2. **清理重复定义**
   - [ ] 分析所有重复的模型定义
   - [ ] 为每个模型添加转换方法
   - [ ] 更新repository实现使用转换方法
   - [ ] 验证所有引用都正确

3. **验证和测试**
   - [ ] 运行现有测试
   - [ ] 确保所有功能正常工作
   - [ ] 检查没有遗漏的GORM直接调用

## 当前进度

### ✅ 已完成
1. 移除了 `domain/user/service/impl/user_service_impl.go` 中的直接GORM调用
2. 添加了 `TransactionManager` 接口
3. 更新了用户注册和删除方法使用事务

### 🔄 进行中
1. 清理重复的模型定义

### ⏳ 待完成
1. 检查其他domain services中的GORM使用
2. 验证所有转换方法
3. 运行测试确保功能正常
