package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// 定义各种数据结构
type Word struct {
	Word        string   `json:"word"`
	Translation string   `json:"translation"`
	Level       string   `json:"level"`
	Example     string   `json:"example"`
	Tags        []string `json:"tags"`
}

type Category struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Words       []Word `json:"words"`
}

type Vocabulary struct {
	VocabularyCategories []Category `json:"vocabularyCategories"`
}

// 定义词汇类别和每个类别的描述 - 30个类别
var categories = map[string]string{
	"Basic Vocabulary":   "Essential words for everyday communication",
	"Food and Drinks":    "Words related to eating and drinking",
	"Travel":             "Vocabulary for travel and transportation",
	"Business":           "Business and professional terms",
	"Technology":         "Words related to computers and technology",
	"Health":             "Health and medical vocabulary",
	"Education":          "Words related to learning and schools",
	"Entertainment":      "Vocabulary for entertainment and leisure",
	"Nature":             "Words describing the natural world",
	"Sports":             "Sports and fitness vocabulary",
	"Emotions":           "Words describing feelings and emotions",
	"Time":               "Vocabulary related to time and schedules",
	"Weather":            "Weather and climate vocabulary",
	"Clothing":           "Words for clothes and fashion",
	"Home":               "Vocabulary for household items and living spaces",
	"Work":               "Words related to jobs and workplaces",
	"Body Parts":         "Words describing human body parts",
	"Transportation":     "Words for different modes of transportation",
	"Communication":      "Vocabulary for communicating with others",
	"Shopping":           "Words related to buying goods and services",
	"Colors and Shapes":  "Words describing visual properties",
	"Numbers and Math":   "Numerical and mathematical vocabulary",
	"Family":             "Words for family relationships",
	"Arts and Culture":   "Vocabulary related to creative expression",
	"Science":            "Scientific terms and concepts",
	"Daily Activities":   "Words related to everyday routines",
	"Geography":          "Words related to places and landforms",
	"Animals":            "Words for different animals and creatures",
	"Plants":             "Words for various types of vegetation",
	"Personality Traits": "Words describing character and behavior",
}

// 设置难度级别和对应的标签
var levels = []string{"beginner", "intermediate", "advanced"}
var levelTags = map[string][]string{
	"beginner":     {"basic", "beginner", "essential"},
	"intermediate": {"intermediate", "common", "regular"},
	"advanced":     {"advanced", "complex", "academic"},
}

// 词汇分类标签
var categoryTags = map[string][]string{
	"Basic Vocabulary":   {"daily", "common", "essential"},
	"Food and Drinks":    {"food", "cuisine", "meal"},
	"Travel":             {"journey", "tourism", "vacation"},
	"Business":           {"work", "corporate", "professional"},
	"Technology":         {"tech", "digital", "electronic"},
	"Health":             {"medical", "wellness", "healthcare"},
	"Education":          {"school", "learning", "academic"},
	"Entertainment":      {"leisure", "fun", "recreation"},
	"Nature":             {"environment", "outdoor", "natural"},
	"Sports":             {"fitness", "athletic", "game"},
	"Emotions":           {"feeling", "mood", "mental"},
	"Time":               {"period", "duration", "schedule"},
	"Weather":            {"climate", "atmosphere", "meteorology"},
	"Clothing":           {"fashion", "apparel", "wear"},
	"Home":               {"house", "domestic", "living"},
	"Work":               {"job", "career", "profession"},
	"Body Parts":         {"anatomy", "physical", "bodily"},
	"Transportation":     {"travel", "vehicle", "transit"},
	"Communication":      {"language", "message", "interaction"},
	"Shopping":           {"retail", "purchase", "consumption"},
	"Colors and Shapes":  {"visual", "geometric", "appearance"},
	"Numbers and Math":   {"numerical", "calculation", "quantity"},
	"Family":             {"relative", "relationship", "kinship"},
	"Arts and Culture":   {"creative", "artistic", "cultural"},
	"Science":            {"scientific", "research", "study"},
	"Daily Activities":   {"routine", "habits", "everyday"},
	"Geography":          {"location", "place", "region"},
	"Animals":            {"wildlife", "pets", "species"},
	"Plants":             {"botanical", "vegetation", "flora"},
	"Personality Traits": {"character", "behavior", "attitude"},
}

// 扩展的中英文翻译映射
var translationMap = map[string]string{
	// Basic verbs
	"be": "是", "have": "有", "do": "做", "say": "说", "get": "得到",
	"make": "制作", "go": "去", "know": "知道", "take": "拿", "see": "看",
	"come": "来", "think": "思考", "look": "看", "want": "想要", "give": "给予",
	"use": "使用", "find": "找到", "tell": "告诉", "ask": "问", "work": "工作",
	"seem": "似乎", "feel": "感觉", "try": "尝试", "leave": "离开", "call": "打电话",

	// Basic nouns
	"time": "时间", "year": "年", "people": "人们", "way": "方式", "day": "日",
	"man": "男人", "thing": "事物", "woman": "女人", "life": "生活", "child": "孩子",
	"world": "世界", "school": "学校", "state": "状态", "family": "家庭", "student": "学生",
	"group": "群体", "country": "国家", "problem": "问题", "hand": "手", "part": "部分",

	// Basic adjectives
	"good": "好的", "new": "新的", "first": "第一", "last": "最后", "long": "长的",
	"great": "很棒的", "little": "小的", "own": "自己的", "other": "其他的", "old": "老的",
	"right": "正确的", "big": "大的", "high": "高的", "different": "不同的", "small": "小的",

	// Food
	"apple": "苹果", "banana": "香蕉", "orange": "橙子", "bread": "面包", "rice": "米饭",
	"meat": "肉", "vegetable": "蔬菜", "fruit": "水果", "coffee": "咖啡", "tea": "茶",

	// Greetings
	"hello": "你好", "goodbye": "再见", "welcome": "欢迎", "please": "请", "thank you": "谢谢",
	"sorry": "对不起", "excuse me": "打扰了", "good morning": "早上好", "good afternoon": "下午好", "good evening": "晚上好",
}

// 扩展常用英语单词库 - 用于随机生成词汁
var commonEnglishWords = []string{
	// Basic verbs - 100 words
	"be", "have", "do", "say", "get", "make", "go", "know", "take", "see",
	"come", "think", "look", "want", "give", "use", "find", "tell", "ask", "work",
	"seem", "feel", "try", "leave", "call", "play", "need", "move", "put", "mean",
	"keep", "let", "begin", "help", "talk", "turn", "start", "show", "hear", "run",
	"bring", "write", "sit", "stand", "lose", "pay", "meet", "include", "continue", "set",
	"learn", "change", "lead", "understand", "watch", "follow", "stop", "create", "speak", "read",
	"allow", "add", "spend", "grow", "open", "walk", "win", "offer", "remember", "love",
	"consider", "appear", "buy", "wait", "serve", "die", "send", "expect", "build", "stay",
	"fall", "cut", "reach", "kill", "remain", "suggest", "raise", "pass", "sell", "require",
	"report", "decide", "pull", "return", "explain", "develop", "carry", "drive", "break", "thank",

	// Basic nouns - 100 words
	"time", "year", "people", "way", "day", "man", "thing", "woman", "life", "child",
	"world", "school", "state", "family", "student", "group", "country", "problem", "hand", "part",
	"place", "case", "week", "company", "system", "program", "question", "work", "government", "number",
	"night", "point", "home", "water", "room", "mother", "area", "money", "story", "fact",
	"month", "lot", "right", "study", "book", "eye", "job", "word", "business", "issue",
	"side", "kind", "head", "house", "service", "friend", "father", "power", "hour", "game",
	"line", "end", "member", "law", "car", "city", "community", "name", "president", "team",
	"minute", "idea", "kid", "body", "information", "back", "parent", "face", "others", "level",
	"office", "door", "health", "person", "art", "war", "history", "party", "result", "change",
	"morning", "reason", "research", "girl", "guy", "moment", "air", "teacher", "force", "education",

	// Basic adjectives - 100 words
	"good", "new", "first", "last", "long", "great", "little", "own", "other", "old",
	"right", "big", "high", "different", "small", "large", "next", "early", "young", "important",
	"few", "public", "bad", "same", "able", "best", "better", "low", "late", "hard",
	"major", "sure", "clear", "ready", "short", "easy", "strong", "human", "simple", "black",
	"various", "white", "free", "military", "possible", "political", "real", "recent", "total", "whole",
	"least", "true", "available", "open", "social", "special", "economic", "international", "national", "final",
	"main", "green", "top", "nice", "serious", "live", "happy", "red", "certain", "wrong",
	"present", "personal", "current", "dark", "single", "past", "poor", "natural", "particular", "common",
	"necessary", "basic", "successful", "full", "dead", "beautiful", "popular", "medical", "specific", "middle",
	"blue", "financial", "future", "positive", "local", "foreign", "legal", "cold", "hot", "deep",

	// Food and Drink - 50 words
	"food", "water", "meal", "dinner", "lunch", "breakfast", "meat", "fruit", "vegetable", "drink",
	"coffee", "tea", "milk", "juice", "wine", "beer", "bread", "cheese", "egg", "chicken",
	"fish", "beef", "pork", "rice", "pasta", "potato", "tomato", "apple", "banana", "orange",
	"cake", "chocolate", "sugar", "salt", "pepper", "soup", "salad", "sandwich", "hamburger", "pizza",
	"restaurant", "cook", "bake", "boil", "grill", "fry", "taste", "flavor", "hungry", "thirsty",

	// Travel - 50 words
	"travel", "trip", "journey", "tour", "vacation", "holiday", "map", "hotel", "airport", "flight",
	"plane", "train", "bus", "car", "ship", "boat", "ticket", "luggage", "suitcase", "passport",
	"visa", "destination", "tourist", "guide", "souvenir", "foreign", "beach", "mountain", "resort", "camping",
	"adventure", "explore", "discover", "departure", "arrival", "border", "customs", "reservation", "check-in", "check-out",
	"distance", "direction", "route", "highway", "path", "road", "bridge", "tunnel", "compass", "gps",

	// Technology - 50 words
	"technology", "computer", "internet", "website", "software", "hardware", "program", "application", "data", "file",
	"system", "network", "server", "cloud", "digital", "electronic", "device", "gadget", "machine", "robot",
	"mobile", "phone", "tablet", "laptop", "desktop", "keyboard", "mouse", "screen", "monitor", "printer",
	"camera", "video", "audio", "password", "username", "account", "email", "download", "upload", "install",
	"update", "backup", "storage", "memory", "processor", "battery", "wireless", "bluetooth", "wifi", "online",

	// Health - 50 words
	"health", "doctor", "nurse", "patient", "hospital", "clinic", "medicine", "drug", "pill", "treatment",
	"therapy", "surgery", "operation", "disease", "illness", "infection", "symptom", "pain", "fever", "cough",
	"cold", "flu", "headache", "stomachache", "injury", "wound", "blood", "heart", "lung", "brain",
	"dental", "vision", "hearing", "diet", "nutrition", "vitamin", "exercise", "fitness", "rest", "sleep",
	"allergy", "stress", "anxiety", "depression", "mental", "physical", "healthy", "sick", "recovery", "emergency",
}

// 预定义的单词映射 - 为每个类别增加更多单词
var predefinedWords = map[string][]map[string]string{
	"Basic Vocabulary": {
		{"word": "hello", "translation": "你好", "example": "Hello, how are you today?"},
		{"word": "goodbye", "translation": "再见", "example": "Goodbye, see you tomorrow."},
		{"word": "yes", "translation": "是", "example": "Yes, I agree with you."},
		{"word": "no", "translation": "不", "example": "No, I don't want to go."},
		{"word": "please", "translation": "请", "example": "Please help me with this."},
		{"word": "thank you", "translation": "谢谢", "example": "Thank you for your help."},
		{"word": "sorry", "translation": "对不起", "example": "I'm sorry for being late."},
		{"word": "excuse me", "translation": "打扰一下", "example": "Excuse me, can you tell me the time?"},
		{"word": "welcome", "translation": "欢迎", "example": "Welcome to our home."},
		{"word": "name", "translation": "名字", "example": "My name is John."},
	},
	"Food and Drinks": {
		{"word": "water", "translation": "水", "example": "I drink water every day."},
		{"word": "bread", "translation": "面包", "example": "I eat bread for breakfast."},
		{"word": "rice", "translation": "米饭", "example": "Rice is a staple food in many countries."},
		{"word": "meat", "translation": "肉", "example": "I don't eat meat."},
		{"word": "vegetable", "translation": "蔬菜", "example": "You should eat more vegetables."},
		{"word": "fruit", "translation": "水果", "example": "Fruit is healthy for you."},
		{"word": "coffee", "translation": "咖啡", "example": "I drink coffee in the morning."},
		{"word": "tea", "translation": "茶", "example": "Would you like some tea?"},
		{"word": "milk", "translation": "牛奶", "example": "Children should drink milk."},
		{"word": "juice", "translation": "果汁", "example": "Orange juice is my favorite."},
	},
	"Travel": {
		{"word": "airport", "translation": "机场", "example": "We'll meet you at the airport."},
		{"word": "ticket", "translation": "票", "example": "I bought a ticket for the concert."},
		{"word": "passport", "translation": "护照", "example": "Don't forget your passport."},
		{"word": "hotel", "translation": "酒店", "example": "We're staying at a hotel downtown."},
		{"word": "map", "translation": "地图", "example": "Let me check the map."},
		{"word": "suitcase", "translation": "行李箱", "example": "My suitcase is too heavy."},
		{"word": "tourist", "translation": "游客", "example": "The city is full of tourists in summer."},
		{"word": "vacation", "translation": "假期", "example": "We're going on vacation next month."},
		{"word": "guide", "translation": "导游", "example": "The guide showed us around the city."},
		{"word": "souvenir", "translation": "纪念品", "example": "I bought some souvenirs for my friends."},
	},
}

// 更丰富的例句模板
var exampleTemplates = []string{
	"I use the word '%s' every day.",
	"Can you use '%s' in a sentence?",
	"The word '%s' is very useful.",
	"'%s' is an important word to know.",
	"She doesn't know the word '%s'.",
	"He explained what '%s' means.",
	"We learned about '%s' in class.",
	"The definition of '%s' is complex.",
	"You should practice using '%s'.",
	"'%s' is commonly used in conversation.",
	"The word '%s' has multiple meanings.",
	"Children learn '%s' at an early age.",
	"The meaning of '%s' has changed over time.",
	"'%s' is similar to several other words.",
	"Many people misuse the word '%s'.",
	"I've been studying the word '%s' recently.",
	"The teacher wrote '%s' on the board.",
	"Can you pronounce '%s' correctly?",
	"What's another word for '%s'?",
	"She's trying to remember how to say '%s'.",
	"'%s' is a word that foreigners often struggle with.",
	"The opposite of '%s' is difficult to explain.",
	"I looked up '%s' in the dictionary yesterday.",
	"We need to understand the concept of '%s'.",
	"The word '%s' appears frequently in this text.",
	"Let me tell you what '%s' means in English.",
	"My student asked about the word '%s' today.",
	"'%s' is a term used mainly in professional contexts.",
	"I always forget how to spell '%s'.",
	"The origin of the word '%s' is interesting.",
	"We're going to discuss the meaning of '%s' today.",
	"I'm not sure if '%s' is the right word to use here.",
	"The most common translation for '%s' is not always accurate.",
	"The word '%s' can be used in many different situations.",
	"'%s' is one of the first words I learned in this language.",
}

// 新增 - 语境化的例句模板
var contextualExampleTemplates = map[string][]string{
	"Food and Drinks": {
		"I really enjoy eating %s for breakfast.",
		"This restaurant serves the best %s in town.",
		"My grandmother taught me how to make %s.",
		"I'm allergic to %s, so I can't eat it.",
		"Would you like some %s with your meal?",
		"The %s tastes delicious with this sauce.",
		"She's learning how to cook %s properly.",
		"In my country, %s is a traditional food.",
		"I bought some fresh %s at the market today.",
		"Children usually don't like eating %s.",
	},
	"Travel": {
		"I need to book a %s for my trip next month.",
		"The %s was delayed due to bad weather.",
		"Don't forget to bring your %s when traveling abroad.",
		"We saw many interesting %s during our vacation.",
		"The %s to the island takes about two hours.",
		"I lost my %s at the airport yesterday.",
		"You should visit the %s when you're in Paris.",
		"The %s was included in our tour package.",
		"They offer a discount on %s during the off-season.",
		"The best way to explore the city is by %s.",
	},
	"Technology": {
		"I need to update the %s on my computer.",
		"My new %s has many advanced features.",
		"Can you help me fix this %s problem?",
		"The company is developing a new type of %s.",
		"You should protect your %s with a strong password.",
		"I can't imagine life without %s nowadays.",
		"She's an expert in %s development.",
		"The %s needs to be charged regularly.",
		"This %s makes our work much easier.",
		"There's a tutorial about %s online.",
	},
}

// 生成更自然的随机中文翻译
func generateRandomTranslation(word string) string {
	// 查看是否有预定义的翻译
	if translation, ok := translationMap[word]; ok {
		return translation
	}

	// 如果没有预定义翻译，生成一个模拟翻译
	return fmt.Sprintf("%s的中文", word)
}

// 生成更自然的随机例句
func generateRandomExample(word string, category string) string {
	rand.Seed(time.Now().UnixNano())

	// 查看是否有特定类别的例句模板
	if templates, ok := contextualExampleTemplates[category]; ok && len(templates) > 0 && rand.Intn(100) < 70 {
		// 70%的概率使用特定类别的例句
		template := templates[rand.Intn(len(templates))]
		return fmt.Sprintf(template, word)
	}

	// 使用通用例句模板
	template := exampleTemplates[rand.Intn(len(exampleTemplates))]
	return fmt.Sprintf(template, word)
}

// 获取单词的标签
func getWordTags(word string, level string, category string) []string {
	// 基本标签 - 类别的第一个词
	categoryTag := strings.ToLower(strings.Split(category, " ")[0])
	tags := []string{categoryTag}

	// 添加类别相关标签
	if categoryTagOptions, ok := categoryTags[category]; ok && len(categoryTagOptions) > 0 {
		// 随机选择1-2个类别标签
		rand.Seed(time.Now().UnixNano())
		numCategoryTags := rand.Intn(2) + 1

		// 洗牌标签选项
		rand.Shuffle(len(categoryTagOptions), func(i, j int) {
			categoryTagOptions[i], categoryTagOptions[j] = categoryTagOptions[j], categoryTagOptions[i]
		})

		for i := 0; i < numCategoryTags && i < len(categoryTagOptions); i++ {
			tags = append(tags, categoryTagOptions[i])
		}
	}

	// 添加难度级别相关的标签
	levelTagOptions := levelTags[level]
	rand.Seed(time.Now().UnixNano())
	numLevelTags := rand.Intn(2) + 1 // 随机选择1-2个标签

	// 洗牌标签选项
	rand.Shuffle(len(levelTagOptions), func(i, j int) {
		levelTagOptions[i], levelTagOptions[j] = levelTagOptions[j], levelTagOptions[i]
	})

	for i := 0; i < numLevelTags && i < len(levelTagOptions); i++ {
		tags = append(tags, levelTagOptions[i])
	}

	return tags
}

// 生成单词列表 - 改进随机词汇生成逻辑
func generateWords(category string, count int) []Word {
	words := []Word{}

	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	// 获取预定义的单词（如果存在）
	predefined := predefinedWords[category]

	// 先添加预定义的单词
	for _, w := range predefined {
		// 随机分配难度级别
		level := levels[rand.Intn(len(levels))]

		words = append(words, Word{
			Word:        w["word"],
			Translation: w["translation"],
			Level:       level,
			Example:     w["example"],
			Tags:        getWordTags(w["word"], level, category),
		})
	}

	// 如果预定义单词不够，则生成随机单词
	if len(words) < count {
		// 创建已有单词的集合，避免重复
		existingWords := make(map[string]bool)
		for _, w := range words {
			existingWords[w.Word] = true
		}

		// 洗牌常用单词库
		randomizedWords := make([]string, len(commonEnglishWords))
		copy(randomizedWords, commonEnglishWords)
		rand.Shuffle(len(randomizedWords), func(i, j int) {
			randomizedWords[i], randomizedWords[j] = randomizedWords[j], randomizedWords[i]
		})

		// 添加随机单词直到达到目标数量
		for _, word := range randomizedWords {
			if len(words) >= count {
				break
			}

			// 跳过已经添加的单词
			if existingWords[word] {
				continue
			}

			// 随机分配难度级别 - 更加平衡的分布
			// 初学者: 40%, 中级: 40%, 高级: 20%
			levelRand := rand.Intn(100)
			var level string
			if levelRand < 40 {
				level = "beginner"
			} else if levelRand < 80 {
				level = "intermediate"
			} else {
				level = "advanced"
			}

			// 生成翻译和例句
			translation := generateRandomTranslation(word)
			example := generateRandomExample(word, category)

			// 添加到结果中
			words = append(words, Word{
				Word:        word,
				Translation: translation,
				Level:       level,
				Example:     example,
				Tags:        getWordTags(word, level, category),
			})

			existingWords[word] = true
		}
	}

	return words
}

func main() {
	// 设置随机数种子
	rand.Seed(time.Now().UnixNano())

	// 创建最终的词汇数据结构
	vocabulary := Vocabulary{
		VocabularyCategories: []Category{},
	}

	// 定义每个类别要获取的单词数量 - 增加到200个单词/类别
	wordsPerCategory := 200 // 30个类别 × 200 = 6000个单词

	// 逐个类别生成单词
	for categoryName, description := range categories {
		log.Printf("Generating words for category: %s", categoryName)

		words := generateWords(categoryName, wordsPerCategory)

		category := Category{
			Name:        categoryName,
			Description: description,
			Words:       words,
		}

		vocabulary.VocabularyCategories = append(vocabulary.VocabularyCategories, category)
		log.Printf("Added %d words to category %s", len(words), categoryName)
	}

	// 将结果保存为JSON文件
	jsonData, err := json.MarshalIndent(vocabulary, "", "  ")
	if err != nil {
		log.Fatalf("Error encoding vocabulary to JSON: %v", err)
	}

	// 确保正确的文件路径
	currentDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("Error getting current directory: %v", err)
	}

	// 通常脚本在scripts目录中，所以需要回到上级目录
	seedsDir := filepath.Join(filepath.Dir(currentDir), "seeds")

	// 确保目录存在
	err = os.MkdirAll(seedsDir, 0755)
	if err != nil {
		log.Fatalf("Error creating seeds directory: %v", err)
	}

	outputFilePath := filepath.Join(seedsDir, "extended_vocabulary.json")
	err = ioutil.WriteFile(outputFilePath, jsonData, 0644)
	if err != nil {
		log.Fatalf("Error writing vocabulary to file: %v", err)
	}

	// 计算总词汇量
	totalWords := 0
	for _, category := range vocabulary.VocabularyCategories {
		totalWords += len(category.Words)
	}

	log.Printf("Successfully generated vocabulary file at: %s", outputFilePath)
	log.Printf("Total categories: %d, Total words: %d", len(vocabulary.VocabularyCategories), totalWords)
}
