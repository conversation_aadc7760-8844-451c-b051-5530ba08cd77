package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
)

func main() {
	fmt.Println("🧹 开始Exercise重复定义清理...")
	
	tasks := []struct {
		name   string
		action func() error
	}{
		{"检查编译状态", checkCompilation},
		{"分析Exercise重复定义", analyzeExerciseDuplicates},
		{"创建Exercise转换方法", createExerciseConversionMethods},
		{"验证清理结果", verifyCleanup},
	}
	
	for i, task := range tasks {
		fmt.Printf("\n📋 步骤 %d: %s\n", i+1, task.name)
		
		if err := task.action(); err != nil {
			log.Fatalf("❌ 任务失败: %v", err)
		}
		
		fmt.Printf("✅ 完成: %s\n", task.name)
	}
	
	fmt.Println("\n🎉 Exercise重复定义清理完成！")
}

func checkCompilation() error {
	fmt.Println("   检查当前编译状态...")
	cmd := exec.Command("go", "build", "./cmd/api")
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("API编译失败: %v\n%s", err, output)
	}
	return nil
}

func analyzeExerciseDuplicates() error {
	fmt.Println("   分析Exercise重复定义...")
	
	// 检查Exercise定义文件
	exerciseFiles := []string{
		"models/exercise.go",
		"domain/exercise/entity/exercise.go",
	}
	
	fmt.Println("   发现的Exercise定义:")
	for _, file := range exerciseFiles {
		if _, err := os.Stat(file); err == nil {
			fmt.Printf("     ✓ %s\n", file)
		} else {
			fmt.Printf("     - %s (不存在)\n", file)
		}
	}
	
	// 检查ExerciseType常量重复
	fmt.Println("   检查ExerciseType常量重复...")
	cmd := exec.Command("grep", "-n", "type ExerciseType", "models/exercise.go", "domain/exercise/entity/exercise.go")
	output, err := cmd.CombinedOutput()
	if err == nil {
		fmt.Printf("     发现ExerciseType定义:\n%s", output)
	}
	
	return nil
}

func createExerciseConversionMethods() error {
	fmt.Println("   创建Exercise转换方法...")
	
	// 检查是否已有转换方法
	content, err := os.ReadFile("models/exercise.go")
	if err != nil {
		return err
	}
	
	if contains(string(content), "ToEntity") {
		fmt.Println("     转换方法已存在，跳过创建")
		return nil
	}
	
	fmt.Println("     添加转换方法到models/exercise.go...")
	
	conversionMethods := `
// ToExerciseEntity converts the database model to a domain entity
func (e *Exercise) ToExerciseEntity() *exerciseEntity.Exercise {
	return &exerciseEntity.Exercise{
		ID:            e.ID,
		Type:          exerciseEntity.ExerciseType(e.Type),
		Title:         e.Title,
		Description:   e.Description,
		Content:       e.Content,
		Question:      e.Question,
		Instructions:  e.Instruction,
		Options:       e.Options,
		CorrectAnswer: e.CorrectAnswer,
		Explanation:   e.Explanation,
		Points:        e.Points,
		MediaURL:      e.AudioURL, // Map AudioURL to MediaURL
		MediaType:     "audio",    // Default media type
		Tags:          e.Tags,
		AuthorID:      e.AuthorID,
		IsPublished:   e.IsPublished,
		CreatedAt:     e.CreatedAt,
		UpdatedAt:     e.UpdatedAt,
	}
}

// FromExerciseEntity converts a domain entity to the database model
func (e *Exercise) FromExerciseEntity(entity *exerciseEntity.Exercise) {
	e.ID = entity.ID
	e.Type = ExerciseType(entity.Type)
	e.Title = entity.Title
	e.Description = entity.Description
	e.Content = entity.Content
	e.Question = entity.Question
	e.Instruction = entity.Instructions
	e.Options = entity.Options
	e.CorrectAnswer = entity.CorrectAnswer
	e.Explanation = entity.Explanation
	e.Points = entity.Points
	e.AudioURL = entity.MediaURL
	e.Tags = entity.Tags
	e.AuthorID = entity.AuthorID
	e.IsPublished = entity.IsPublished
	e.CreatedAt = entity.CreatedAt
	e.UpdatedAt = entity.UpdatedAt
}
`
	
	// 添加import
	importLine := `import (
	"time"

	exerciseEntity "languagelearning/domain/exercise/entity"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/gorm"
)`
	
	// 读取当前内容
	currentContent := string(content)
	
	// 替换import部分
	newContent := replaceImport(currentContent, importLine)
	
	// 添加转换方法
	newContent += conversionMethods
	
	// 写回文件
	err = os.WriteFile("models/exercise.go", []byte(newContent), 0644)
	if err != nil {
		return fmt.Errorf("写入转换方法失败: %v", err)
	}
	
	fmt.Println("     ✓ 转换方法已添加")
	return nil
}

func verifyCleanup() error {
	fmt.Println("   验证清理结果...")
	
	// 检查编译
	cmd := exec.Command("go", "build", "./models/")
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("models包编译失败: %v\n%s", err, output)
	}
	
	cmd = exec.Command("go", "build", "./cmd/api")
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("API编译失败: %v\n%s", err, output)
	}
	
	fmt.Println("     ✓ 编译验证通过")
	return nil
}

// Helper functions
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) && 
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || 
		 containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func replaceImport(content, newImport string) string {
	// 简单的import替换逻辑
	lines := splitLines(content)
	var result []string
	inImport := false
	importReplaced := false
	
	for _, line := range lines {
		if !importReplaced && (startsWith(line, "import (") || startsWith(line, "import")) {
			result = append(result, newImport)
			inImport = true
			importReplaced = true
			continue
		}
		
		if inImport && startsWith(line, ")") {
			inImport = false
			continue
		}
		
		if !inImport {
			result = append(result, line)
		}
	}
	
	return joinLines(result)
}

func splitLines(s string) []string {
	var lines []string
	start := 0
	for i, c := range s {
		if c == '\n' {
			lines = append(lines, s[start:i])
			start = i + 1
		}
	}
	if start < len(s) {
		lines = append(lines, s[start:])
	}
	return lines
}

func joinLines(lines []string) string {
	result := ""
	for i, line := range lines {
		result += line
		if i < len(lines)-1 {
			result += "\n"
		}
	}
	return result
}

func startsWith(s, prefix string) bool {
	return len(s) >= len(prefix) && s[:len(prefix)] == prefix
}
