.PHONY: dev build run test clean migrate seed reset docker-up docker-down docker-build docker-simple-up docker-simple-down swagger

# Development
dev:
	go run cmd/api/main.go

# Run worker
worker:
	go run cmd/worker/main.go

# Build the application
build:
	go build -o bin/api cmd/api/main.go
	go build -o bin/migrate cmd/migrate/main.go
	go build -o bin/seed cmd/seed/main.go
	go build -o bin/reset cmd/reset/main.go
	go build -o bin/worker cmd/worker/main.go

# Run the built application
run: build
	./bin/api

# Run tests
test:
	go test -v ./...

# Clean build artifacts
clean:
	rm -rf bin/ tmp/

# Database operations
migrate:
	go run cmd/migrate/main.go

seed:
	go run cmd/seed/main.go

reset:
	go run cmd/reset/main.go

# Docker operations with Air hot reloading
docker-up:
	docker-compose up -d

docker-down:
	docker-compose down

docker-build:
	docker-compose build

# Docker operations with simple setup (no Air)
docker-simple-up:
	docker-compose -f docker-compose.simple.yml up -d

docker-simple-down:
	docker-compose -f docker-compose.simple.yml down

# Start development environment with Docker
dev-docker: docker-build docker-up
	docker-compose logs -f api

# Start simple development environment with Docker
dev-docker-simple: docker-simple-up
	docker-compose -f docker-compose.simple.yml logs -f api

# Initialize the development environment
init: docker-up
	sleep 5  # Wait for container to be fully started
	docker exec languagelearning-api go run cmd/seed/main.go

# Initialize the simple development environment
init-simple: docker-simple-up
	sleep 5  # Wait for container to be fully started
	docker exec languagelearning-api go run cmd/seed/main.go

# Run migrations manually if needed
migrate-docker:
	docker exec languagelearning-api go run cmd/migrate/main.go

# Seed database manually
seed-docker:
	docker exec languagelearning-api go run cmd/seed/main.go

# Show logs
logs:
	docker-compose logs -f

# Show logs for simple setup
logs-simple:
	docker-compose -f docker-compose.simple.yml logs -f

# Help
help:
	@echo "Available commands:"
	@echo "  make dev               - Run the application locally"
	@echo "  make build             - Build the application"
	@echo "  make run               - Run the built application"
	@echo "  make test              - Run tests"
	@echo "  make clean             - Clean build artifacts"
	@echo "  make migrate           - Run database migrations"
	@echo "  make seed              - Seed the database"
	@echo "  make reset             - Reset the database"
	@echo ""
	@echo "Docker with Air (hot reloading):"
	@echo "  make docker-up         - Start Docker containers with Air"
	@echo "  make docker-down       - Stop Docker containers with Air"
	@echo "  make docker-build      - Build Docker images with Air"
	@echo "  make dev-docker        - Start development environment with Air"
	@echo "  make init              - Initialize the development environment with Air"
	@echo "  make logs              - Show logs from Docker containers with Air"
	@echo ""
	@echo "Simple Docker (without Air):"
	@echo "  make docker-simple-up  - Start simple Docker containers"
	@echo "  make docker-simple-down - Stop simple Docker containers"
	@echo "  make dev-docker-simple - Start simple development environment"
	@echo "  make init-simple       - Initialize the simple development environment"
	@echo "  make logs-simple       - Show logs from simple Docker containers"
	@echo ""
	@echo "Documentation:"
	@echo "  make swagger          - Generate Swagger documentation"

# Generate Swagger documentation
swagger:
	go install github.com/swaggo/swag/cmd/swag@latest
	swag init -g main.go -o ./cmd/docs/swagger