package utils

import (
	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// ExtractValidationErrors extracts validation errors from the validator
func ExtractValidationErrors(err error) map[string]string {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		errors := make(map[string]string)
		for _, e := range validationErrors {
			errors[e.Field()] = getErrorMsg(e)
		}
		return errors
	}
	return nil
}

// getErrorMsg returns a human-readable error message for a validation error
func getErrorMsg(e validator.FieldError) string {
	switch e.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Invalid email format"
	case "min":
		return "Value is too short"
	case "max":
		return "Value is too long"
	default:
		return "Invalid value"
	}
}

// HandleValidationErrors is a helper function to handle validation errors
func HandleValidationErrors(c *gin.Context, err error, message string) {
	validationErrors := ExtractValidationErrors(err)
	response.ValidationError(c, message, validationErrors)
}

// Legacy response functions for backward compatibility
// These will be deprecated in favor of the new response package

// RespondWithSuccess responds with a success message
func RespondWithSuccess(c *gin.Context, statusCode int, data interface{}, message string) {
	response.Success(c, statusCode, data, message)
}

// RespondWithUnauthorized responds with an unauthorized error
func RespondWithUnauthorized(c *gin.Context, message string) {
	response.Unauthorized(c, message)
}

// RespondWithServerError responds with an internal server error
func RespondWithServerError(c *gin.Context, message string) {
	response.InternalError(c, message)
}

// RespondWithNotFound responds with a not found error
func RespondWithNotFound(c *gin.Context, message string) {
	response.NotFound(c, message)
}

// RespondWithValidationError responds with a validation error
func RespondWithValidationError(c *gin.Context, message string, details map[string]string) {
	response.ValidationError(c, message, details)
}

// RespondWithError responds with a generic error
func RespondWithError(c *gin.Context, statusCode int, err interface{}, message string) {
	// For backward compatibility, use the message parameter
	if message != "" {
		response.BadRequest(c, message)
	} else {
		response.BadRequest(c, "Request error")
	}
}

// NewErrorResponse creates a new error response (legacy compatibility)
func NewErrorResponse(code, message string) map[string]string {
	return map[string]string{
		"code":    code,
		"message": message,
	}
}
