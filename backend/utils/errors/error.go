package errors

import (
	"fmt"
	"net/http"
)

// ErrorType 是错误类型的枚举
type ErrorType string

const (
	// ValidationError 表示输入验证错误
	ValidationError ErrorType = "VALIDATION_ERROR"

	// ResourceNotFound 表示请求的资源不存在
	ResourceNotFound ErrorType = "RESOURCE_NOT_FOUND"

	// Unauthorized 表示用户没有权限
	Unauthorized ErrorType = "UNAUTHORIZED"

	// Forbidden 表示用户禁止访问
	Forbidden ErrorType = "FORBIDDEN"

	// InternalError 表示内部服务器错误
	InternalError ErrorType = "INTERNAL_ERROR"

	// BadRequest 表示请求有错误
	BadRequest ErrorType = "BAD_REQUEST"

	// ConflictError 表示资源冲突错误
	ConflictError ErrorType = "CONFLICT_ERROR"
)

// AppError 是应用程序中使用的统一错误类型
type AppError struct {
	Type       ErrorType         `json:"type"`
	Message    string            `json:"message"`
	Details    map[string]string `json:"details,omitempty"`
	StatusCode int               `json:"-"`
	Err        error             `json:"-"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s (%s)", e.Type, e.Message, e.Err.Error())
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap 实现errors.Unwrap接口
func (e *AppError) Unwrap() error {
	return e.Err
}

// New 创建一个新的AppError
func New(errType ErrorType, message string, err error) *AppError {
	statusCode := getStatusCodeForErrorType(errType)

	return &AppError{
		Type:       errType,
		Message:    message,
		StatusCode: statusCode,
		Err:        err,
	}
}

// NewWithDetails 创建一个带有详细信息的AppError
func NewWithDetails(errType ErrorType, message string, details map[string]string, err error) *AppError {
	appErr := New(errType, message, err)
	appErr.Details = details
	return appErr
}

// getStatusCodeForErrorType 根据错误类型返回对应的HTTP状态码
func getStatusCodeForErrorType(errType ErrorType) int {
	switch errType {
	case ValidationError:
		return http.StatusBadRequest
	case ResourceNotFound:
		return http.StatusNotFound
	case Unauthorized:
		return http.StatusUnauthorized
	case Forbidden:
		return http.StatusForbidden
	case ConflictError:
		return http.StatusConflict
	case BadRequest:
		return http.StatusBadRequest
	default:
		return http.StatusInternalServerError
	}
}

// 一些便捷的创建特定类型错误的函数

// NewValidationError 创建一个验证错误
func NewValidationError(message string, details map[string]string) *AppError {
	return NewWithDetails(ValidationError, message, details, nil)
}

// NewNotFoundError 创建一个资源不存在错误
func NewNotFoundError(message string) *AppError {
	return New(ResourceNotFound, message, nil)
}

// NewUnauthorizedError 创建一个未授权错误
func NewUnauthorizedError(message string) *AppError {
	return New(Unauthorized, message, nil)
}

// NewForbiddenError 创建一个禁止访问错误
func NewForbiddenError(message string) *AppError {
	return New(Forbidden, message, nil)
}

// NewInternalError 创建一个内部服务器错误
func NewInternalError(message string, err error) *AppError {
	return New(InternalError, message, err)
}

// NewBadRequestError 创建一个请求错误
func NewBadRequestError(message string) *AppError {
	return New(BadRequest, message, nil)
}

// NewConflictError 创建一个资源冲突错误
func NewConflictError(message string) *AppError {
	return New(ConflictError, message, nil)
}
