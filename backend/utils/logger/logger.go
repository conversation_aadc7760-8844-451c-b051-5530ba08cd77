package logger

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"runtime"
	"strings"
	"time"
)

// LogLevel 日志级别类型
type LogLevel int

const (
	// DEBUG 调试级别
	DEBUG LogLevel = iota
	// INFO 信息级别
	INFO
	// WARNING 警告级别
	WARNING
	// ERROR 错误级别
	ERROR
	// FATAL 致命错误级别
	FATAL
)

// String 返回日志级别的字符串表示
func (l LogLevel) String() string {
	return [...]string{"DEBUG", "INFO", "WARNING", "ERROR", "FATAL"}[l]
}

// Logger 结构化日志记录器
type Logger struct {
	level     LogLevel
	formatter *Formatter
}

// Formatter 用于格式化日志条目
type Formatter struct {
	TimeFormat string
}

// LogEntry 单条日志条目
type LogEntry struct {
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Timestamp string                 `json:"timestamp"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
	Caller    string                 `json:"caller,omitempty"`
	TraceID   string                 `json:"traceId,omitempty"`
}

// New 创建一个新的日志记录器
func New(level LogLevel) *Logger {
	return &Logger{
		level: level,
		formatter: &Formatter{
			TimeFormat: time.RFC3339,
		},
	}
}

// DefaultLogger 返回默认的日志记录器
func DefaultLogger() *Logger {
	return New(INFO)
}

// log 记录日志
func (l *Logger) log(ctx context.Context, level LogLevel, message string, fields map[string]interface{}) {
	if level < l.level {
		return
	}

	entry := LogEntry{
		Level:     level.String(),
		Message:   message,
		Timestamp: time.Now().Format(l.formatter.TimeFormat),
		Fields:    fields,
	}

	// 添加调用者信息
	_, file, line, ok := runtime.Caller(2)
	if ok {
		parts := strings.Split(file, "/")
		entry.Caller = fmt.Sprintf("%s:%d", parts[len(parts)-1], line)
	}

	// 从上下文中提取跟踪ID（如果有）
	if ctx != nil {
		if traceID, ok := ctx.Value("traceID").(string); ok {
			entry.TraceID = traceID
		}
	}

	// 编码为JSON并输出
	jsonBytes, err := json.Marshal(entry)
	if err != nil {
		log.Printf("Unable to marshal log entry: %v", err)
		return
	}

	fmt.Fprintln(os.Stdout, string(jsonBytes))

	// 如果是致命错误，退出程序
	if level == FATAL {
		os.Exit(1)
	}
}

// Debug 记录调试级别的日志
func (l *Logger) Debug(ctx context.Context, message string, fields map[string]interface{}) {
	l.log(ctx, DEBUG, message, fields)
}

// Info 记录信息级别的日志
func (l *Logger) Info(ctx context.Context, message string, fields map[string]interface{}) {
	l.log(ctx, INFO, message, fields)
}

// Warn 记录警告级别的日志
func (l *Logger) Warn(ctx context.Context, message string, fields map[string]interface{}) {
	l.log(ctx, WARNING, message, fields)
}

// Error 记录错误级别的日志
func (l *Logger) Error(ctx context.Context, message string, fields map[string]interface{}) {
	l.log(ctx, ERROR, message, fields)
}

// Fatal 记录致命错误级别的日志
func (l *Logger) Fatal(ctx context.Context, message string, fields map[string]interface{}) {
	l.log(ctx, FATAL, message, fields)
}

// WithFields 创建一个带有字段的新日志条目
func WithFields(fields map[string]interface{}) map[string]interface{} {
	return fields
}

// DefaultLoggerWithContext 从上下文中获取或创建一个日志记录器
func DefaultLoggerWithContext(ctx context.Context) *Logger {
	if ctx == nil {
		return DefaultLogger()
	}

	if logger, ok := ctx.Value("logger").(*Logger); ok {
		return logger
	}

	return DefaultLogger()
}
