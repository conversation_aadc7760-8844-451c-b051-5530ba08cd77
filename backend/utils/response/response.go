package response

import (
	"languagelearning/utils/errors"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// APIResponse 是标准化的API响应结构
type APIResponse struct {
	Success bool       `json:"success"`
	Message string     `json:"message,omitempty"`
	Data    any        `json:"data,omitempty"`
	Error   *ErrorInfo `json:"error,omitempty"`
	TraceID string     `json:"traceId,omitempty"`
}

// ErrorInfo 错误信息结构
type ErrorInfo struct {
	Type    string            `json:"type"`
	Code    string            `json:"code"`
	Message string            `json:"message"`
	Details map[string]string `json:"details,omitempty"`
}

// getTraceID 獲取追蹤ID
func getTraceID(ctx *gin.Context) string {
	if traceID, exists := ctx.Get("traceID"); exists {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return ""
}

// Success 返回成功响应
func Success(ctx *gin.Context, statusCode int, data any, message string) {
	traceID := getTraceID(ctx)
	ctx.JSON(statusCode, APIResponse{
		Success: true,
		Message: message,
		Data:    data,
		TraceID: traceID,
	})
}

// Error 返回错误响应
func Error(ctx *gin.Context, appErr *errors.AppError) {
	traceID := getTraceID(ctx)
	ctx.JSON(appErr.StatusCode, APIResponse{
		Success: false,
		Message: appErr.Message,
		Error: &ErrorInfo{
			Type:    string(appErr.Type),
			Code:    string(appErr.Type),
			Message: appErr.Message,
			Details: appErr.Details,
		},
		TraceID: traceID,
	})
}

// InternalError 返回内部服务器错误响应
func InternalError(ctx *gin.Context, message string) {
	appErr := errors.NewInternalError(message, nil)
	Error(ctx, appErr)
}

// ValidationError 返回验证错误响应
func ValidationError(ctx *gin.Context, message string, details map[string]string) {
	appErr := errors.NewValidationError(message, details)
	Error(ctx, appErr)
}

// NotFound 返回资源不存在错误响应
func NotFound(ctx *gin.Context, message string) {
	appErr := errors.NewNotFoundError(message)
	Error(ctx, appErr)
}

// Unauthorized 返回未授权错误响应
func Unauthorized(ctx *gin.Context, message string) {
	appErr := errors.NewUnauthorizedError(message)
	Error(ctx, appErr)
}

// Forbidden 返回禁止访问错误响应
func Forbidden(ctx *gin.Context, message string) {
	appErr := errors.NewForbiddenError(message)
	Error(ctx, appErr)
}

// BadRequest 返回请求错误响应
func BadRequest(ctx *gin.Context, message string) {
	appErr := errors.NewBadRequestError(message)
	Error(ctx, appErr)
}

// Conflict 返回资源冲突错误响应
func Conflict(ctx *gin.Context, message string) {
	appErr := errors.NewConflictError(message)
	Error(ctx, appErr)
}

// HandleValidationErrors 处理验证错误
func HandleValidationErrors(ctx *gin.Context, err error, message string) {
	validationErrors := make(map[string]string)

	if errs, ok := err.(validator.ValidationErrors); ok {
		for _, e := range errs {
			field := e.Field()
			tag := e.Tag()
			validationErrors[field] = "Field validation for '" + field + "' failed on '" + tag + "'"
		}
	} else {
		// 如果不是validator.ValidationErrors，可能是JSON绑定错误
		validationErrors["body"] = err.Error()
	}

	ValidationError(ctx, message, validationErrors)
}
