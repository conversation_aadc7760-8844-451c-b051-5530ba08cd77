# 安全加固計劃 - 語言學習系統

## 🎯 安全目標

將系統安全等級從當前的5/10提升到8.5+/10，實施企業級安全控制，確保生產環境安全。

## 🔍 當前安全狀況評估

### 現有安全機制
- ✅ **基本JWT認證**: 實現了基本的令牌認證
- ✅ **密碼哈希**: 使用bcrypt進行密碼加密
- ✅ **HTTPS支持**: 基礎的TLS配置
- ⚠️ **輸入驗證**: 部分實現，需要加強
- ❌ **API限流**: 缺失
- ❌ **RBAC權限**: 缺失
- ❌ **安全頭**: 缺失
- ❌ **審計日誌**: 缺失

### 安全風險評估
```
Critical Risks:
├── API濫用攻擊 (無限流控制)
├── 權限提升 (缺乏RBAC)
└── 會話劫持 (JWT安全性不足)

High Risks:
├── SQL注入 (輸入驗證不完整)
├── XSS攻擊 (響應頭缺失)
└── CSRF攻擊 (缺乏保護機制)

Medium Risks:
├── 信息洩露 (錯誤信息過詳細)
├── 暴力破解 (缺乏賬戶鎖定)
└── 會話固定 (令牌管理不當)
```

## 📋 Week 2: 安全機制加固計劃

### Day 8-10: API安全中間件

#### 1. 限流中間件實現
創建 `middleware/rate_limit.go`:

```go
package middleware

import (
    "net/http"
    "sync"
    "time"
    "github.com/gin-gonic/gin"
    "golang.org/x/time/rate"
)

type RateLimiter struct {
    visitors map[string]*rate.Limiter
    mu       sync.RWMutex
    rate     rate.Limit
    burst    int
}

func NewRateLimiter(r rate.Limit, b int) *RateLimiter {
    return &RateLimiter{
        visitors: make(map[string]*rate.Limiter),
        rate:     r,
        burst:    b,
    }
}

func (rl *RateLimiter) GetLimiter(ip string) *rate.Limiter {
    rl.mu.Lock()
    defer rl.mu.Unlock()
    
    limiter, exists := rl.visitors[ip]
    if !exists {
        limiter = rate.NewLimiter(rl.rate, rl.burst)
        rl.visitors[ip] = limiter
    }
    
    return limiter
}

func RateLimitMiddleware(rps int, burst int) gin.HandlerFunc {
    limiter := NewRateLimiter(rate.Limit(rps), burst)
    
    return func(c *gin.Context) {
        ip := c.ClientIP()
        if !limiter.GetLimiter(ip).Allow() {
            c.JSON(http.StatusTooManyRequests, gin.H{
                "error": "Rate limit exceeded",
                "retry_after": "60s",
            })
            c.Abort()
            return
        }
        c.Next()
    }
}
```

#### 2. 安全頭中間件
創建 `middleware/security_headers.go`:

```go
package middleware

import "github.com/gin-gonic/gin"

func SecurityHeadersMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 防止XSS攻擊
        c.Header("X-Content-Type-Options", "nosniff")
        c.Header("X-Frame-Options", "DENY")
        c.Header("X-XSS-Protection", "1; mode=block")
        
        // 強制HTTPS
        c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        
        // 內容安全策略
        c.Header("Content-Security-Policy", "default-src 'self'")
        
        // 隱藏服務器信息
        c.Header("Server", "")
        
        c.Next()
    }
}
```

#### 3. 請求大小限制
創建 `middleware/request_size_limit.go`:

```go
package middleware

import (
    "net/http"
    "github.com/gin-gonic/gin"
)

func RequestSizeLimitMiddleware(maxSize int64) gin.HandlerFunc {
    return func(c *gin.Context) {
        if c.Request.ContentLength > maxSize {
            c.JSON(http.StatusRequestEntityTooLarge, gin.H{
                "error": "Request body too large",
                "max_size": maxSize,
            })
            c.Abort()
            return
        }
        
        c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxSize)
        c.Next()
    }
}
```

### Day 11-12: JWT安全增強

#### 1. 刷新令牌機制
更新 `middleware/jwt.go`:

```go
// RefreshTokenResponse JWT刷新響應
type RefreshTokenResponse struct {
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
    ExpiresIn    int    `json:"expires_in"`
}

// TokenPair 令牌對
type TokenPair struct {
    AccessToken  string
    RefreshToken string
}

// GenerateTokenPair 生成訪問令牌和刷新令牌
func GenerateTokenPair(userID uuid.UUID) (*TokenPair, error) {
    accessToken, err := GenerateToken(userID)
    if err != nil {
        return nil, err
    }
    
    refreshToken, err := GenerateRefreshToken(userID)
    if err != nil {
        return nil, err
    }
    
    return &TokenPair{
        AccessToken:  accessToken,
        RefreshToken: refreshToken,
    }, nil
}
```

#### 2. 令牌黑名單機制
創建 `middleware/token_blacklist.go`:

```go
package middleware

import (
    "context"
    "time"
    "github.com/go-redis/redis/v8"
)

type TokenBlacklist struct {
    redis *redis.Client
}

func NewTokenBlacklist(redisClient *redis.Client) *TokenBlacklist {
    return &TokenBlacklist{redis: redisClient}
}

func (tb *TokenBlacklist) AddToken(token string, expiry time.Duration) error {
    ctx := context.Background()
    return tb.redis.Set(ctx, "blacklist:"+token, "1", expiry).Err()
}

func (tb *TokenBlacklist) IsBlacklisted(token string) bool {
    ctx := context.Background()
    result := tb.redis.Get(ctx, "blacklist:"+token)
    return result.Err() == nil
}
```

#### 3. 密碼強度驗證
創建 `utils/password_validator.go`:

```go
package utils

import (
    "errors"
    "regexp"
    "unicode"
)

type PasswordPolicy struct {
    MinLength    int
    RequireUpper bool
    RequireLower bool
    RequireDigit bool
    RequireSpecial bool
}

func DefaultPasswordPolicy() *PasswordPolicy {
    return &PasswordPolicy{
        MinLength:    8,
        RequireUpper: true,
        RequireLower: true,
        RequireDigit: true,
        RequireSpecial: true,
    }
}

func (p *PasswordPolicy) Validate(password string) error {
    if len(password) < p.MinLength {
        return errors.New("password too short")
    }
    
    var hasUpper, hasLower, hasDigit, hasSpecial bool
    
    for _, char := range password {
        switch {
        case unicode.IsUpper(char):
            hasUpper = true
        case unicode.IsLower(char):
            hasLower = true
        case unicode.IsDigit(char):
            hasDigit = true
        case unicode.IsPunct(char) || unicode.IsSymbol(char):
            hasSpecial = true
        }
    }
    
    if p.RequireUpper && !hasUpper {
        return errors.New("password must contain uppercase letter")
    }
    if p.RequireLower && !hasLower {
        return errors.New("password must contain lowercase letter")
    }
    if p.RequireDigit && !hasDigit {
        return errors.New("password must contain digit")
    }
    if p.RequireSpecial && !hasSpecial {
        return errors.New("password must contain special character")
    }
    
    return nil
}
```

### Day 13-14: RBAC權限控制

#### 1. 權限模型設計
創建 `domain/auth/entity/permission.go`:

```go
package entity

import (
    "github.com/google/uuid"
    "time"
)

// Permission 權限實體
type Permission struct {
    ID          uuid.UUID `json:"id"`
    Name        string    `json:"name"`
    Resource    string    `json:"resource"`
    Action      string    `json:"action"`
    Description string    `json:"description"`
    CreatedAt   time.Time `json:"createdAt"`
}

// Role 角色實體
type Role struct {
    ID          uuid.UUID    `json:"id"`
    Name        string       `json:"name"`
    Description string       `json:"description"`
    Permissions []Permission `json:"permissions"`
    CreatedAt   time.Time    `json:"createdAt"`
    UpdatedAt   time.Time    `json:"updatedAt"`
}

// UserRole 用戶角色關聯
type UserRole struct {
    UserID    uuid.UUID `json:"userId"`
    RoleID    uuid.UUID `json:"roleId"`
    AssignedAt time.Time `json:"assignedAt"`
    AssignedBy uuid.UUID `json:"assignedBy"`
}
```

#### 2. 權限檢查中間件
創建 `middleware/rbac.go`:

```go
package middleware

import (
    "net/http"
    "strings"
    "github.com/gin-gonic/gin"
    "languagelearning/domain/auth/service"
)

func RequirePermission(resource, action string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID, exists := c.Get("userID")
        if !exists {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "User not authenticated",
            })
            c.Abort()
            return
        }
        
        // 檢查用戶權限
        authService := getAuthService(c) // 從DI容器獲取
        hasPermission, err := authService.CheckPermission(
            userID.(string), resource, action)
        
        if err != nil || !hasPermission {
            c.JSON(http.StatusForbidden, gin.H{
                "error": "Insufficient permissions",
                "required": map[string]string{
                    "resource": resource,
                    "action":   action,
                },
            })
            c.Abort()
            return
        }
        
        c.Next()
    }
}

func RequireRole(roleName string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID, exists := c.Get("userID")
        if !exists {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "User not authenticated",
            })
            c.Abort()
            return
        }
        
        authService := getAuthService(c)
        hasRole, err := authService.CheckRole(userID.(string), roleName)
        
        if err != nil || !hasRole {
            c.JSON(http.StatusForbidden, gin.H{
                "error": "Insufficient role",
                "required_role": roleName,
            })
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

## 📋 Week 3: 高級安全功能

### Day 15-17: 輸入驗證和SQL注入防護

#### 1. 增強輸入驗證
創建 `utils/input_validator.go`:

```go
package utils

import (
    "regexp"
    "strings"
    "html"
)

type InputSanitizer struct {
    allowedTags map[string]bool
}

func NewInputSanitizer() *InputSanitizer {
    return &InputSanitizer{
        allowedTags: map[string]bool{
            "b": true, "i": true, "em": true, "strong": true,
        },
    }
}

func (s *InputSanitizer) SanitizeHTML(input string) string {
    // 移除危險的HTML標籤
    input = html.EscapeString(input)
    return input
}

func (s *InputSanitizer) SanitizeSQL(input string) string {
    // 移除SQL注入風險字符
    dangerous := []string{"'", "\"", ";", "--", "/*", "*/", "xp_", "sp_"}
    for _, d := range dangerous {
        input = strings.ReplaceAll(input, d, "")
    }
    return input
}

func ValidateEmail(email string) bool {
    pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
    matched, _ := regexp.MatchString(pattern, email)
    return matched
}

func ValidateUsername(username string) bool {
    // 只允許字母、數字和下劃線，3-20個字符
    pattern := `^[a-zA-Z0-9_]{3,20}$`
    matched, _ := regexp.MatchString(pattern, username)
    return matched
}
```

#### 2. 參數化查詢檢查
更新Repository實現，確保所有查詢都使用參數化：

```go
// 正確的參數化查詢示例
func (r *userRepository) FindByEmail(ctx context.Context, email string) (*entity.User, error) {
    var model models.User
    // 使用GORM的參數化查詢，自動防止SQL注入
    err := r.db.WithContext(ctx).Where("email = ?", email).First(&model).Error
    if err != nil {
        return nil, err
    }
    return model.ToEntity(), nil
}
```

### Day 18-19: 審計日誌和監控

#### 1. 安全審計日誌
創建 `utils/audit_logger.go`:

```go
package utils

import (
    "context"
    "encoding/json"
    "time"
    "languagelearning/utils/logger"
)

type AuditEvent struct {
    UserID    string                 `json:"user_id"`
    Action    string                 `json:"action"`
    Resource  string                 `json:"resource"`
    IP        string                 `json:"ip"`
    UserAgent string                 `json:"user_agent"`
    Success   bool                   `json:"success"`
    Details   map[string]interface{} `json:"details"`
    Timestamp time.Time              `json:"timestamp"`
}

type AuditLogger struct {
    logger *logger.Logger
}

func NewAuditLogger() *AuditLogger {
    return &AuditLogger{
        logger: logger.DefaultLogger(),
    }
}

func (al *AuditLogger) LogSecurityEvent(ctx context.Context, event *AuditEvent) {
    event.Timestamp = time.Now()
    
    eventJSON, _ := json.Marshal(event)
    
    fields := logger.WithFields(map[string]interface{}{
        "audit_event": true,
        "user_id":     event.UserID,
        "action":      event.Action,
        "resource":    event.Resource,
        "success":     event.Success,
    })
    
    if event.Success {
        al.logger.Info(ctx, "Security audit event", fields)
    } else {
        al.logger.Warn(ctx, "Security audit event - FAILED", fields)
    }
}

// 預定義的安全事件
func (al *AuditLogger) LogLogin(ctx context.Context, userID, ip, userAgent string, success bool) {
    al.LogSecurityEvent(ctx, &AuditEvent{
        UserID:    userID,
        Action:    "login",
        Resource:  "auth",
        IP:        ip,
        UserAgent: userAgent,
        Success:   success,
    })
}

func (al *AuditLogger) LogPermissionDenied(ctx context.Context, userID, resource, action, ip string) {
    al.LogSecurityEvent(ctx, &AuditEvent{
        UserID:   userID,
        Action:   "permission_denied",
        Resource: resource,
        IP:       ip,
        Success:  false,
        Details: map[string]interface{}{
            "denied_action": action,
        },
    })
}
```

### Day 20-21: 安全配置和部署

#### 1. 安全配置管理
更新 `config/config.go`:

```go
type SecurityConfig struct {
    RateLimit struct {
        RequestsPerSecond int `json:"requests_per_second"`
        BurstSize         int `json:"burst_size"`
    } `json:"rate_limit"`
    
    JWT struct {
        AccessTokenExpiry  time.Duration `json:"access_token_expiry"`
        RefreshTokenExpiry time.Duration `json:"refresh_token_expiry"`
        SecretRotationDays int           `json:"secret_rotation_days"`
    } `json:"jwt"`
    
    Password struct {
        MinLength      int  `json:"min_length"`
        RequireUpper   bool `json:"require_upper"`
        RequireLower   bool `json:"require_lower"`
        RequireDigit   bool `json:"require_digit"`
        RequireSpecial bool `json:"require_special"`
        MaxAttempts    int  `json:"max_attempts"`
        LockoutMinutes int  `json:"lockout_minutes"`
    } `json:"password"`
    
    CORS struct {
        AllowedOrigins []string `json:"allowed_origins"`
        AllowedMethods []string `json:"allowed_methods"`
        AllowedHeaders []string `json:"allowed_headers"`
    } `json:"cors"`
}
```

#### 2. 安全中間件集成
更新路由配置：

```go
func (r *APIRouter) RegisterRoutes(router *gin.Engine) {
    // 全局安全中間件
    router.Use(middleware.SecurityHeadersMiddleware())
    router.Use(middleware.RequestSizeLimitMiddleware(10 * 1024 * 1024)) // 10MB
    router.Use(middleware.RateLimitMiddleware(100, 200)) // 100 RPS, burst 200
    
    // API路由
    apiV1 := router.Group("/api/v1")
    
    // 公開路由（較嚴格的限流）
    public := apiV1.Group("")
    public.Use(middleware.RateLimitMiddleware(10, 20)) // 10 RPS for public endpoints
    
    // 認證路由
    authenticated := apiV1.Group("")
    authenticated.Use(middleware.JWTAuth())
    authenticated.Use(middleware.AuditMiddleware())
    
    // 管理員路由
    admin := apiV1.Group("/admin")
    admin.Use(middleware.JWTAuth())
    admin.Use(middleware.RequireRole("admin"))
    admin.Use(middleware.AuditMiddleware())
}
```

## 🎯 安全指標和監控

### 安全KPI
- **認證成功率**: > 99%
- **權限檢查響應時間**: < 10ms
- **安全事件檢測時間**: < 1分鐘
- **密碼策略合規率**: 100%

### 安全監控告警
- 異常登錄嘗試（地理位置、時間）
- API濫用檢測（超過限流閾值）
- 權限提升嘗試
- SQL注入嘗試
- XSS攻擊嘗試

### 定期安全檢查
- 每週安全掃描
- 每月滲透測試
- 每季度安全審計
- 年度安全評估

這個安全加固計劃將顯著提升系統的安全防護能力，確保生產環境的安全運行。
