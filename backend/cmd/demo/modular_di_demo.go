package main

import (
	"fmt"
	"log"

	"languagelearning/di"
	coredi "languagelearning/domain/core/di"
)

func main() {
	fmt.Println("🚀 依賴注入模塊化架構演示")
	fmt.Println("====================================================")

	// 創建模塊化容器
	container := di.NewModularContainer()

	// 啟用進階功能（可選）
	container.EnableFeature(coredi.FeatureAdvanced)

	// 構建容器
	fmt.Println("📦 構建模塊化容器...")
	if err := container.Build(); err != nil {
		log.Fatalf("❌ 構建容器失敗: %v", err)
	}

	// 顯示模塊信息
	fmt.Println("\n📋 模塊註冊信息:")
	registry := container.GetRegistry()

	// 顯示加載順序
	loadOrder := container.GetLoadOrder()
	fmt.Printf("🔄 模塊加載順序: %v\n", loadOrder)

	// 顯示啟用的模塊
	enabledModules := container.GetEnabledModules()
	fmt.Printf("\n✅ 啟用的模塊 (%d 個):\n", len(enabledModules))
	for i, module := range enabledModules {
		fmt.Printf("  %d. %s\n", i+1, module.Name())
		fmt.Printf("     依賴: %v\n", module.Dependencies())
		fmt.Printf("     功能: %v\n", module.Features())
		fmt.Println()
	}

	// 顯示功能狀態
	fmt.Println("🎛️  功能狀態:")
	features := []coredi.ModuleFeature{
		coredi.FeatureBasic,
		coredi.FeatureAdvanced,
		coredi.FeatureExperimental,
	}

	for _, feature := range features {
		status := "❌ 禁用"
		if registry.IsFeatureEnabled(feature) {
			status = "✅ 啟用"
		}
		fmt.Printf("  %s: %s\n", feature, status)
	}

	// 測試依賴注入
	fmt.Println("\n🔧 測試依賴注入...")
	err := container.Invoke(func() {
		fmt.Println("✅ 依賴注入成功！所有服務都已正確註冊。")
	})

	if err != nil {
		log.Printf("❌ 依賴注入測試失敗: %v", err)
	}

	fmt.Println("\n🎉 模塊化架構演示完成！")
	fmt.Println("\n📝 總結:")
	fmt.Println("  • 成功實現了模塊化依賴注入架構")
	fmt.Println("  • 支持依賴關係解析和循環依賴檢測")
	fmt.Println("  • 支持功能特性管理")
	fmt.Println("  • 支持按需模塊加載")
	fmt.Println("  • 提供了清晰的模塊邊界和自治性")
}
