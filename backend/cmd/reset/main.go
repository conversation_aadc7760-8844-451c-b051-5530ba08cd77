package main

import (
	"context"
	"log"

	"languagelearning/migrations"
	"languagelearning/models"
	appLogger "languagelearning/utils/logger"

	"github.com/joho/godotenv"
)

func main() {
	// 创建应用级上下文
	ctx := context.Background()
	logger := appLogger.DefaultLogger()

	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		logger.Info(ctx, "No .env file found, using environment variables", nil)
	}

	// 初始化数据库
	db := models.ConnectDatabase()

	// 运行数据库重置
	log.Println("WARNING: Starting database reset (this will delete all data)...")
	migrations.Reset(db)
	log.Println("Database reset completed successfully!")
}
