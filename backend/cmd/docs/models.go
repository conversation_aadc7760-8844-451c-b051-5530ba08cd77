package docs

// Generic response format
type APIResponse struct {
	Success bool        `json:"success" example:"true"`
	Message string      `json:"message" example:"Operation successful"`
	Data    interface{} `json:"data,omitempty"`
}

// Error response format
type ErrorResponse struct {
	Success bool   `json:"success" example:"false"`
	Message string `json:"message" example:"Error message"`
	Error   string `json:"error,omitempty" example:"Detailed error information"`
}

// Auth models
type LoginRequest struct {
	Email    string `json:"email" example:"<EMAIL>"`
	Password string `json:"password" example:"password123"`
}

type RegisterRequest struct {
	Username string `json:"username" example:"johndoe"`
	Email    string `json:"email" example:"<EMAIL>"`
	Password string `json:"password" example:"password123"`
}

type AuthResponse struct {
	ID                    string       `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Username              string       `json:"username" example:"johndoe"`
	Email                 string       `json:"email" example:"<EMAIL>"`
	CurrentStreak         int          `json:"currentStreak" example:"5"`
	VocabularyCount       int          `json:"vocabularyCount" example:"120"`
	ListeningExerciseCount int         `json:"listeningExerciseCount" example:"45"`
	SpeakingExerciseCount int          `json:"speakingExerciseCount" example:"30"`
	Points                int          `json:"points" example:"750"`
	CompletedChallenges   int          `json:"completedChallenges" example:"12"`
	HelpedUsers           int          `json:"helpedUsers" example:"3"`
	Token                 string       `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	CreatedAt             string       `json:"created_at" example:"2023-01-15T14:30:45Z"`
	LastLoginAt           string       `json:"last_login_at" example:"2023-05-20T09:15:22Z"`
	Settings              UserSettings `json:"settings"`
}

type UserSettings struct {
	NotificationsEnabled bool   `json:"notificationsEnabled" example:"true"`
	DarkModeEnabled      bool   `json:"darkModeEnabled" example:"false"`
	DailyGoal            int    `json:"dailyGoal" example:"3"`
	PreferredLanguage    string `json:"preferredLanguage" example:"en-US"`
}

// User profile models
type UserProfile struct {
	ID              string `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Username        string `json:"username" example:"johndoe"`
	Email           string `json:"email" example:"<EMAIL>"`
	CurrentStreak   int    `json:"currentStreak" example:"5"`
	VocabularyCount int    `json:"vocabularyCount" example:"120"`
	Points          int    `json:"points" example:"750"`
	AvatarURL       string `json:"avatarUrl,omitempty" example:"https://example.com/avatars/user123.jpg"`
}

// Exercise models
type GrammarExercise struct {
	ID          string   `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Title       string   `json:"title" example:"Past Simple Tense"`
	Description string   `json:"description" example:"Practice using the past simple tense in English"`
	Content     string   `json:"content" example:"Fill in the blanks with the correct past tense form of the verb."`
	Options     []string `json:"options,omitempty" example:"['walked', 'walking', 'walks']"`
	Difficulty  string   `json:"difficulty" example:"intermediate"`
	Category    string   `json:"category" example:"verbs"`
	LanguageID  string   `json:"languageId" example:"550e8400-e29b-41d4-a716-446655440000"`
}

type ExerciseSubmission struct {
	Answer string `json:"answer" example:"walked"`
}

type ExerciseResult struct {
	Correct  bool   `json:"correct" example:"true"`
	Feedback string `json:"feedback,omitempty" example:"Great job! 'Walked' is the correct past tense form."`
	Points   int    `json:"points" example:"10"`
}

// Language models
type Language struct {
	ID          string `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Code        string `json:"code" example:"en-US"`
	Name        string `json:"name" example:"English"`
	NativeName  string `json:"nativeName" example:"English"`
	Description string `json:"description" example:"American English"`
	Flag        string `json:"flag" example:"🇺🇸"`
	IsActive    bool   `json:"isActive" example:"true"`
}

// Lesson models
type Lesson struct {
	ID          string `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Title       string `json:"title" example:"Introduction to Spanish Greetings"`
	Description string `json:"description" example:"Learn common greetings and introductions in Spanish"`
	Content     string `json:"content" example:"<h1>Greetings</h1><p>In this lesson, we'll learn...</p>"`
	Category    string `json:"category" example:"conversation"`
	Level       string `json:"level" example:"beginner"`
	Difficulty  int    `json:"difficulty" example:"1"`
	Duration    int    `json:"duration" example:"15"` // in minutes
	LanguageID  string `json:"languageId" example:"550e8400-e29b-41d4-a716-446655440000"`
}

// Learning path models
type LearningPath struct {
	ID          string        `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Title       string        `json:"title" example:"Spanish for Beginners"`
	Description string        `json:"description" example:"A comprehensive path to learn Spanish from scratch"`
	Level       string        `json:"level" example:"beginner"`
	Status      string        `json:"status" example:"in_progress"`
	Progress    int           `json:"progress" example:"35"` // percentage
	UserID      string        `json:"userId" example:"550e8400-e29b-41d4-a716-446655440000"`
	Lessons     []PathLesson  `json:"lessons,omitempty"`
	CreatedAt   string        `json:"createdAt" example:"2023-01-15T14:30:45Z"`
	UpdatedAt   string        `json:"updatedAt" example:"2023-05-20T09:15:22Z"`
}

type PathLesson struct {
	ID          string `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	LessonID    string `json:"lessonId" example:"550e8400-e29b-41d4-a716-446655440000"`
	Title       string `json:"title" example:"Introduction to Spanish Greetings"`
	Order       int    `json:"order" example:"1"`
	IsCompleted bool   `json:"isCompleted" example:"true"`
}
