// Package docs provides the Swagger documentation for the API.
//
// The following annotations are used by <PERSON><PERSON><PERSON> to generate the Swagger documentation.
package docs

// @title           Language Learning API
// @version         1.0
// @description     API for the Language Learning Application
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.languagelearningapp.com/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8080
// @BasePath  /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and the JWT token.

// @tag.name Auth
// @tag.description Authentication operations

// @tag.name User
// @tag.description User operations

// @tag.name Grammar
// @tag.description Grammar exercises

// @tag.name Speaking
// @tag.description Speaking exercises

// @tag.name Listening
// @tag.description Listening exercises

// @tag.name Words
// @tag.description Vocabulary words

// @tag.name Lessons
// @tag.description Lessons and learning materials

// @tag.name Practice
// @tag.description Practice sessions

// @tag.name Evaluations
// @tag.description User evaluations and assessments

// @tag.name LearningPaths
// @tag.description Learning paths and progress

// @tag.name Achievements
// @tag.description User achievements and rewards

// @tag.name Notifications
// @tag.description User notifications
