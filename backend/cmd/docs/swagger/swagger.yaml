definitions:
  controllers.ChangePasswordRequest:
    properties:
      newPassword:
        example: newpassword123
        minLength: 6
        type: string
      oldPassword:
        example: oldpassword123
        type: string
    required:
    - newPassword
    - oldPassword
    type: object
  controllers.CreateRelationRequest:
    properties:
      description:
        example: Grammar concept needed to understand this vocabulary
        type: string
      relationType:
        description: prerequisite, related, similar
        example: prerequisite
        type: string
      sourceId:
        example: 550e8400-e29b-41d4-a716-446655440000
        type: string
      sourceType:
        description: grammar, vocabulary, listening, speaking
        example: grammar
        type: string
      strength:
        description: 1=weak, 2=medium, 3=strong
        example: 2
        maximum: 3
        minimum: 1
        type: integer
      targetId:
        example: 550e8400-e29b-41d4-a716-446655440001
        type: string
      targetType:
        description: grammar, vocabulary, listening, speaking
        example: vocabulary
        type: string
    required:
    - relationType
    - sourceId
    - sourceType
    - strength
    - targetId
    - targetType
    type: object
  controllers.HealthResponse:
    properties:
      database:
        example: true
        type: boolean
      status:
        example: ok
        type: string
      timestamp:
        example: "2023-05-17T14:30:45Z"
        type: string
      uptime:
        example: 3h25m10s
        type: string
      version:
        example: 1.0.0
        type: string
    type: object
  controllers.LoginRequest:
    properties:
      password:
        example: password123
        type: string
      username:
        example: johndoe
        type: string
    required:
    - password
    - username
    type: object
  controllers.RegisterRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: password123
        minLength: 6
        type: string
      username:
        example: johndoe
        type: string
    required:
    - email
    - password
    - username
    type: object
  controllers.ResetPasswordRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  controllers.SavePracticeSessionRequest:
    properties:
      duration:
        description: in seconds
        example: 300
        minimum: 1
        type: integer
      score:
        description: percentage score
        example: 85
        maximum: 100
        minimum: 0
        type: integer
      type:
        description: vocabulary, grammar, listening, speaking, mixed
        example: vocabulary
        type: string
    required:
    - duration
    - score
    - type
    type: object
  controllers.SubmitGrammarAnswerRequest:
    properties:
      answer:
        example: have been living
        type: string
    required:
    - answer
    type: object
  controllers.SubmitListeningAnswerRequest:
    properties:
      answerIndex:
        example: 2
        type: integer
    required:
    - answerIndex
    type: object
  controllers.SubmitSpeakingAnswerRequest:
    properties:
      recordingPath:
        example: /uploads/recordings/user123_exercise456.mp3
        type: string
    required:
    - recordingPath
    type: object
  controllers.SubmitWordAnswerRequest:
    properties:
      isCorrect:
        example: true
        type: boolean
    required:
    - isCorrect
    type: object
  controllers.ToggleFavoriteLessonRequest:
    properties:
      isFavorite:
        example: true
        type: boolean
    required:
    - isFavorite
    type: object
  controllers.UpdateDifficultyMetadataRequest:
    properties:
      complexityScore:
        description: 1-10 scale
        example: 7.5
        maximum: 10
        minimum: 1
        type: number
      tags:
        example:
        - '["grammar"'
        - ' "verbs"'
        - ' "past-tense"]'
        items:
          type: string
        type: array
      timeToComplete:
        description: in seconds
        example: 300
        minimum: 1
        type: integer
    required:
    - complexityScore
    - timeToComplete
    type: object
  controllers.UpdateLessonProgressRequest:
    properties:
      completed:
        example: false
        type: boolean
      progress:
        example: 0.75
        maximum: 1
        minimum: 0
        type: number
    required:
    - completed
    - progress
    type: object
  controllers.UpdateRelationRequest:
    properties:
      description:
        example: Updated relationship description
        type: string
      relationType:
        description: prerequisite, related, similar
        example: related
        type: string
      strength:
        description: 1=weak, 2=medium, 3=strong
        example: 3
        maximum: 3
        minimum: 1
        type: integer
    required:
    - relationType
    - strength
    type: object
  controllers.UpdateUserProfileRequest:
    properties:
      avatar:
        description: Base64 encoded image
        example: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
        type: string
      email:
        example: <EMAIL>
        type: string
      name:
        example: John Doe
        type: string
    type: object
  controllers.UpdateUserSettingsRequest:
    properties:
      dailyGoal:
        example: 5
        type: integer
      darkModeEnabled:
        example: false
        type: boolean
      notificationsEnabled:
        example: true
        type: boolean
      preferredLanguage:
        example: en-US
        type: string
    type: object
  models.AchievementType:
    enum:
    - 连续学习
    - 词汇量
    - 听力练习
    - 口语练习
    - 课程完成
    - 积分
    - 挑战
    - 社交
    type: string
    x-enum-varnames:
    - AchStreak
    - AchVocabulary
    - AchListening
    - AchSpeaking
    - AchLessons
    - AchPoints
    - AchChallenges
    - AchSocial
  models.Difficulty:
    enum:
    - easy
    - medium
    - hard
    type: string
    x-enum-varnames:
    - Easy
    - Medium
    - Hard
  models.Language:
    properties:
      code:
        type: string
      createdAt:
        type: string
      description:
        type: string
      flag:
        type: string
      id:
        type: string
      isActive:
        type: boolean
      name:
        type: string
      nativeName:
        type: string
      updatedAt:
        type: string
    type: object
  models.NotificationPreference:
    properties:
      achievementNotifications:
        type: boolean
      dailyReminderTime:
        description: 'Format: "HH:MM"'
        type: string
      emailNotificationsEnabled:
        type: boolean
      lessonNotifications:
        type: boolean
      pushNotificationsEnabled:
        type: boolean
      reminderNotifications:
        type: boolean
      streakNotifications:
        type: boolean
      userId:
        type: string
    type: object
  models.ProgressReport:
    properties:
      averageScore:
        type: number
      completedExercises:
        type: integer
      completedLessons:
        type: integer
      dailyActivity:
        description: activity level for each day
        items:
          type: integer
        type: array
      endDate:
        type: string
      newWords:
        type: integer
      period:
        type: string
      pointsEarned:
        type: integer
      startDate:
        type: string
      totalPracticeTime:
        description: in seconds
        type: integer
    type: object
  models.User:
    properties:
      avatar_url:
        type: string
      completedChallenges:
        type: integer
      created_at:
        type: string
      currentStreak:
        type: integer
      email:
        type: string
      helpedUsers:
        type: integer
      id:
        type: string
      last_login_at:
        type: string
      listeningExerciseCount:
        type: integer
      points:
        type: integer
      settings:
        $ref: '#/definitions/models.UserSettings'
      speakingExerciseCount:
        type: integer
      username:
        type: string
      vocabularyCount:
        type: integer
    type: object
  models.UserAchievementResponse:
    properties:
      color:
        type: string
      description:
        type: string
      icon:
        type: string
      id:
        type: string
      isUnlocked:
        type: boolean
      progress:
        type: integer
      requirement:
        type: integer
      reward:
        type: integer
      rewardClaimed:
        type: boolean
      title:
        type: string
      type:
        $ref: '#/definitions/models.AchievementType'
      unlockedDate:
        type: string
    type: object
  models.UserResponse:
    properties:
      avatar_url:
        type: string
      completedChallenges:
        type: integer
      created_at:
        type: string
      currentStreak:
        type: integer
      email:
        type: string
      helpedUsers:
        type: integer
      id:
        type: string
      last_login_at:
        type: string
      listeningExerciseCount:
        type: integer
      points:
        type: integer
      settings:
        $ref: '#/definitions/models.UserSettings'
      speakingExerciseCount:
        type: integer
      token:
        type: string
      username:
        type: string
      vocabularyCount:
        type: integer
    type: object
  models.UserSettings:
    properties:
      dailyGoal:
        type: integer
      darkModeEnabled:
        type: boolean
      notificationsEnabled:
        type: boolean
      preferredLanguage:
        type: string
    type: object
  models.UserWordResponse:
    properties:
      isFavorite:
        type: boolean
      isLearned:
        type: boolean
      word:
        $ref: '#/definitions/models.Word'
    type: object
  models.Word:
    properties:
      audioURL:
        type: string
      category:
        type: string
      definition:
        type: string
      difficulty:
        $ref: '#/definitions/models.Difficulty'
      exampleSentence:
        type: string
      id:
        type: string
      imageURL:
        type: string
      language:
        allOf:
        - $ref: '#/definitions/models.Language'
        description: Relationships
      languageId:
        type: string
      pronunciation:
        type: string
      translation:
        type: string
      word:
        type: string
    type: object
  utils.ErrorResponse:
    properties:
      code:
        type: string
      message:
        type: string
    type: object
  utils.Response:
    properties:
      data: {}
      error:
        $ref: '#/definitions/utils.ErrorResponse'
      message:
        type: string
      success:
        type: boolean
      validation_errors:
        additionalProperties:
          type: string
        type: object
    type: object
info:
  contact: {}
paths:
  /api/v1/achievements:
    get:
      description: Get all achievements with user progress information
      produces:
      - application/json
      responses:
        "200":
          description: Achievements retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get achievements
      tags:
      - Achievements
  /api/v1/achievements/{id}/claim:
    patch:
      description: Claim the reward points for an unlocked achievement
      parameters:
      - description: Achievement ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Achievement reward claimed successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid achievement ID or achievement not unlocked or reward
            already claimed
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Achievement not found or user achievement not found
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Claim achievement reward
      tags:
      - Achievements
  /api/v1/exercise-relations:
    post:
      consumes:
      - application/json
      description: Create a relationship between two exercises (e.g., prerequisite,
        related)
      parameters:
      - description: Relation details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.CreateRelationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Relation created successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body or validation errors
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Create exercise relation
      tags:
      - Exercise Relations
  /api/v1/exercise-relations/{id}:
    delete:
      description: Delete an existing relationship between two exercises
      parameters:
      - description: Relation ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Relation deleted successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid relation ID
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Delete exercise relation
      tags:
      - Exercise Relations
    get:
      description: Get all relationships for a specific exercise
      parameters:
      - description: Exercise ID
        in: path
        name: id
        required: true
        type: string
      - description: Exercise type (grammar, vocabulary, listening, speaking)
        in: query
        name: type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Relations retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid exercise ID or type
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get exercise relations
      tags:
      - Exercise Relations
    put:
      consumes:
      - application/json
      description: Update an existing relationship between two exercises
      parameters:
      - description: Relation ID
        in: path
        name: id
        required: true
        type: integer
      - description: Updated relation details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.UpdateRelationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Relation updated successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body or validation errors
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Update exercise relation
      tags:
      - Exercise Relations
  /api/v1/exercise-relations/{id}/difficulty:
    put:
      consumes:
      - application/json
      description: Update the difficulty metadata for a specific exercise
      parameters:
      - description: Exercise ID
        in: path
        name: id
        required: true
        type: string
      - description: Exercise type (grammar, vocabulary, listening, speaking)
        in: query
        name: type
        required: true
        type: string
      - description: Difficulty metadata
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.UpdateDifficultyMetadataRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Difficulty metadata updated successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid exercise ID, type, or request body
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Update exercise difficulty metadata
      tags:
      - Exercise Relations
  /api/v1/exercise-relations/recommend:
    get:
      description: Get personalized exercise recommendations based on user's learning
        history and preferences
      parameters:
      - description: 'Number of recommendations to return (default: 5)'
        in: query
        name: count
        type: integer
      - collectionFormat: multi
        description: Preferred exercise types (can specify multiple)
        in: query
        items:
          type: string
        name: type
        type: array
      - description: 'Preferred difficulty level (easy, medium, hard) (default: medium)'
        in: query
        name: difficulty
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Recommendations retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Recommend exercises
      tags:
      - Exercise Relations
  /api/v1/exercises/grammar:
    get:
      description: Get all grammar exercises with optional filtering by category and
        difficulty
      parameters:
      - description: Filter by category (e.g., verbs, nouns, adjectives)
        in: query
        name: category
        type: string
      - description: Filter by difficulty (e.g., beginner, intermediate, advanced)
        in: query
        name: difficulty
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Grammar exercises retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get grammar exercises
      tags:
      - Grammar
  /api/v1/exercises/grammar/{id}/submit:
    post:
      consumes:
      - application/json
      description: Submit an answer for a grammar exercise and get feedback
      parameters:
      - description: Exercise ID
        in: path
        name: id
        required: true
        type: string
      - description: Answer submission
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.SubmitGrammarAnswerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Grammar answer submitted successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body or Exercise ID
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Exercise not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Submit grammar answer
      tags:
      - Grammar
  /api/v1/exercises/listening:
    get:
      description: Get all listening exercises with optional filtering by category
        and difficulty
      parameters:
      - description: Filter by category (e.g., conversations, news, stories)
        in: query
        name: category
        type: string
      - description: Filter by difficulty (e.g., beginner, intermediate, advanced)
        in: query
        name: difficulty
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Listening exercises retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get listening exercises
      tags:
      - Listening
  /api/v1/exercises/listening/{id}/submit:
    post:
      consumes:
      - application/json
      description: Submit an answer for a listening exercise and get feedback
      parameters:
      - description: Exercise ID
        in: path
        name: id
        required: true
        type: string
      - description: Answer submission
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.SubmitListeningAnswerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Listening answer submitted successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body or Exercise ID
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Exercise not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Submit listening answer
      tags:
      - Listening
  /api/v1/exercises/speaking:
    get:
      description: Get all speaking exercises with optional filtering by category
        and difficulty
      parameters:
      - description: Filter by category (e.g., pronunciation, conversation, presentation)
        in: query
        name: category
        type: string
      - description: Filter by difficulty (e.g., beginner, intermediate, advanced)
        in: query
        name: difficulty
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Speaking exercises retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get speaking exercises
      tags:
      - Speaking
  /api/v1/exercises/speaking/{id}/submit:
    post:
      consumes:
      - application/json
      description: Submit a recording for a speaking exercise and get feedback
      parameters:
      - description: Exercise ID
        in: path
        name: id
        required: true
        type: string
      - description: Recording submission
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.SubmitSpeakingAnswerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Speaking answer submitted successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body or Exercise ID
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Exercise not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Submit speaking answer
      tags:
      - Speaking
  /api/v1/exercises/word:
    get:
      description: Get vocabulary word exercises with optional filtering by category
        and difficulty
      parameters:
      - description: Filter by category (e.g., food, travel, business)
        in: query
        name: category
        type: string
      - description: Filter by difficulty (e.g., beginner, intermediate, advanced)
        in: query
        name: difficulty
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Word exercises retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get word exercises
      tags:
      - Words
  /api/v1/exercises/word/{id}/submit:
    post:
      consumes:
      - application/json
      description: Submit an answer for a vocabulary word exercise and get feedback
      parameters:
      - description: Word ID
        in: path
        name: id
        required: true
        type: string
      - description: Answer submission
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.SubmitWordAnswerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Word answer submitted successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body or Word ID
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Word not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Submit word answer
      tags:
      - Words
  /api/v1/lessons:
    get:
      description: Get all lessons with optional filtering by category, level, and
        difficulty
      parameters:
      - description: Filter by category
        in: query
        name: category
        type: string
      - description: Filter by level (beginner, intermediate, advanced)
        in: query
        name: level
        type: string
      - description: Filter by difficulty (1-10)
        in: query
        name: difficulty
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Lessons retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get all lessons
      tags:
      - Lessons
  /api/v1/lessons/{id}:
    get:
      description: Get detailed information about a specific lesson
      parameters:
      - description: Lesson ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Lesson retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Lesson ID is required
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Lesson not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get lesson detail
      tags:
      - Lessons
  /api/v1/lessons/{id}/favorite:
    patch:
      consumes:
      - application/json
      description: Mark or unmark a lesson as favorite for the current user
      parameters:
      - description: Lesson ID
        in: path
        name: id
        required: true
        type: string
      - description: Favorite status
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.ToggleFavoriteLessonRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Lesson favorite status updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  additionalProperties:
                    type: boolean
                  type: object
              type: object
        "400":
          description: Invalid request body or Lesson ID is required
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Lesson not found
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Toggle favorite lesson
      tags:
      - Lessons
  /api/v1/lessons/{id}/progress:
    get:
      description: Get the current user's progress for a specific lesson
      parameters:
      - description: Lesson ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Lesson progress retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Lesson ID is required
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get lesson progress
      tags:
      - Lessons
    put:
      consumes:
      - application/json
      description: Update the current user's progress for a specific lesson
      parameters:
      - description: Lesson ID
        in: path
        name: id
        required: true
        type: string
      - description: Progress information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.UpdateLessonProgressRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Lesson progress updated successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body or Lesson ID is required
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Lesson not found
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Update lesson progress
      tags:
      - Lessons
  /api/v1/lessons/favorites:
    get:
      description: Get the current user's favorite lessons
      produces:
      - application/json
      responses:
        "200":
          description: Favorite lessons retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get favorite lessons
      tags:
      - Lessons
  /api/v1/notifications:
    get:
      description: Get all notifications for the current user with optional filtering
        by status
      parameters:
      - description: Filter by status (unread, read, archived)
        in: query
        name: status
        type: string
      - description: 'Number of notifications to return (default: 10)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Notifications retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get user notifications
      tags:
      - Notifications
  /api/v1/notifications/{id}/archive:
    patch:
      description: Archive a specific notification for the current user
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Notification archived
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid notification ID
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Archive notification
      tags:
      - Notifications
  /api/v1/notifications/{id}/read:
    patch:
      description: Mark a specific notification as read for the current user
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Notification marked as read
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid notification ID
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Mark notification as read
      tags:
      - Notifications
  /api/v1/notifications/preferences:
    get:
      description: Get the notification preferences for the current user
      produces:
      - application/json
      responses:
        "200":
          description: Notification preferences retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get notification preferences
      tags:
      - Notifications
    put:
      consumes:
      - application/json
      description: Update the notification preferences for the current user
      parameters:
      - description: Notification preferences
        in: body
        name: preferences
        required: true
        schema:
          $ref: '#/definitions/models.NotificationPreference'
      produces:
      - application/json
      responses:
        "200":
          description: Notification preferences updated
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid notification preferences
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Update notification preferences
      tags:
      - Notifications
  /api/v1/notifications/read-all:
    patch:
      description: Mark all notifications for the current user as read
      produces:
      - application/json
      responses:
        "200":
          description: All notifications marked as read
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Mark all notifications as read
      tags:
      - Notifications
  /api/v1/personalized-learning/initiate:
    post:
      description: Start the personalized learning process for a new user by creating
        an initial assessment
      produces:
      - application/json
      responses:
        "200":
          description: Initial assessment created successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Initiate personalized learning
      tags:
      - Personalized Learning
  /api/v1/personalized-learning/status:
    get:
      description: Get the current status of a user's personalized learning journey
      produces:
      - application/json
      responses:
        "200":
          description: Personalized learning status retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get personalized learning status
      tags:
      - Personalized Learning
  /api/v1/practice/history:
    get:
      description: Get the current user's practice session history
      produces:
      - application/json
      responses:
        "200":
          description: Practice history retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get practice history
      tags:
      - Practice
  /api/v1/practice/recommended:
    get:
      description: Get personalized practice recommendations for the current user
      produces:
      - application/json
      responses:
        "200":
          description: Recommended practice retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get recommended practice
      tags:
      - Practice
  /api/v1/practice/session:
    post:
      consumes:
      - application/json
      description: Save a completed practice session for the current user
      parameters:
      - description: Practice session details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.SavePracticeSessionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Practice session saved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Save practice session
      tags:
      - Practice
  /api/v1/user/achievements:
    get:
      description: Get the current user's achievements
      produces:
      - application/json
      responses:
        "200":
          description: User achievements retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.UserAchievementResponse'
                  type: array
              type: object
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get user achievements
      tags:
      - User
  /api/v1/user/profile:
    get:
      description: Get the current user's profile information
      produces:
      - application/json
      responses:
        "200":
          description: User profile retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.User'
              type: object
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - User
    put:
      consumes:
      - application/json
      description: Update the current user's profile information
      parameters:
      - description: User profile information to update
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.UpdateUserProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User profile updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.User'
              type: object
        "400":
          description: Invalid request body or validation errors
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Update user profile
      tags:
      - User
  /api/v1/user/progress/{period}:
    get:
      description: Get the current user's progress report for a specific period (week,
        month, year)
      parameters:
      - description: Period (week, month, year)
        enum:
        - week
        - month
        - year
        in: path
        name: period
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Progress report retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.ProgressReport'
              type: object
        "400":
          description: Invalid period
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get progress report
      tags:
      - User
  /api/v1/user/settings:
    get:
      description: Get the current user's settings
      produces:
      - application/json
      responses:
        "200":
          description: User settings retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserSettings'
              type: object
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: User settings not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get user settings
      tags:
      - User
    put:
      consumes:
      - application/json
      description: Update the current user's settings
      parameters:
      - description: User settings to update
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.UpdateUserSettingsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User settings updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserSettings'
              type: object
        "400":
          description: Invalid request body
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Update user settings
      tags:
      - User
  /api/v1/user/stats:
    get:
      description: Get the current user's learning statistics
      produces:
      - application/json
      responses:
        "200":
          description: User stats retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get user statistics
      tags:
      - User
  /api/v1/user/streak:
    get:
      description: Get the current user's learning streak
      produces:
      - application/json
      responses:
        "200":
          description: Learning streak retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  additionalProperties:
                    type: integer
                  type: object
              type: object
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get learning streak
      tags:
      - User
  /api/v1/user/words:
    get:
      description: Get the current user's vocabulary word list
      produces:
      - application/json
      responses:
        "200":
          description: User word list retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.UserWordResponse'
                  type: array
              type: object
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get user word list
      tags:
      - User
  /api/v1/words:
    get:
      description: Get all vocabulary words with optional filtering by category and
        difficulty
      parameters:
      - description: Filter by category (e.g., food, travel, business)
        in: query
        name: category
        type: string
      - description: Filter by difficulty (e.g., beginner, intermediate, advanced)
        in: query
        name: difficulty
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Words retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get vocabulary words
      tags:
      - Words
  /api/v1/words/{id}:
    get:
      description: Get detailed information about a specific vocabulary word
      parameters:
      - description: Word ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Word retrieved successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid word ID
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Word not found
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Get word detail
      tags:
      - Words
  /api/v1/words/{id}/learned:
    put:
      description: Mark a vocabulary word as learned for the current user
      parameters:
      - description: Word ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Word marked as learned successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid word ID
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: Word not found
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Mark word as learned
      tags:
      - Words
  /auth/change-password:
    put:
      consumes:
      - application/json
      description: Change the current user's password
      parameters:
      - description: Old and new password
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password changed successfully
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated or invalid old password
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Change password
      tags:
      - Auth
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate a user and return a JWT token
      parameters:
      - description: Login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "400":
          description: Invalid request body
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: Invalid username or password
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      summary: Login user
      tags:
      - Auth
  /auth/logout:
    delete:
      description: Logout the current user (client-side token removal)
      produces:
      - application/json
      responses:
        "200":
          description: Logout successful
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: Logout user
      tags:
      - Auth
  /auth/register:
    post:
      consumes:
      - application/json
      description: Register a new user and return a JWT token
      parameters:
      - description: Registration information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration successful
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "400":
          description: Invalid request body or validation errors
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/utils.Response'
      summary: Register user
      tags:
      - Auth
  /auth/reset-password:
    post:
      consumes:
      - application/json
      description: Send a password reset link to the user's email
      parameters:
      - description: Email for password reset
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controllers.ResetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: If your email is registered, you will receive a password reset
            link
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: Invalid request body
          schema:
            $ref: '#/definitions/utils.Response'
      summary: Reset password
      tags:
      - Auth
  /health:
    get:
      description: Check the health status of the service
      produces:
      - application/json
      responses:
        "200":
          description: Health check successful
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/controllers.HealthResponse'
              type: object
      summary: Get service health
      tags:
      - Health
  /liveness:
    get:
      description: Check if the service is alive
      produces:
      - application/json
      responses:
        "200":
          description: Service is alive
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
      summary: Check service liveness
      tags:
      - Health
  /readiness:
    get:
      description: Check if the service is ready to accept traffic
      produces:
      - application/json
      responses:
        "200":
          description: Service is ready
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
        "503":
          description: Service is not ready
          schema:
            $ref: '#/definitions/utils.ErrorResponse'
      summary: Check service readiness
      tags:
      - Health
swagger: "2.0"
