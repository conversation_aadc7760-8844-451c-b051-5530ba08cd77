// Package swagger Code generated by swaggo/swag. DO NOT EDIT
package swagger

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/achievements": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all achievements with user progress information",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Achievements"
                ],
                "summary": "Get achievements",
                "responses": {
                    "200": {
                        "description": "Achievements retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/achievements/{id}/claim": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Claim the reward points for an unlocked achievement",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Achievements"
                ],
                "summary": "Claim achievement reward",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Achievement ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Achievement reward claimed successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid achievement ID or achievement not unlocked or reward already claimed",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Achievement not found or user achievement not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercise-relations": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a relationship between two exercises (e.g., prerequisite, related)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exercise Relations"
                ],
                "summary": "Create exercise relation",
                "parameters": [
                    {
                        "description": "Relation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.CreateRelationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Relation created successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or validation errors",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercise-relations/recommend": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get personalized exercise recommendations based on user's learning history and preferences",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exercise Relations"
                ],
                "summary": "Recommend exercises",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of recommendations to return (default: 5)",
                        "name": "count",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Preferred exercise types (can specify multiple)",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Preferred difficulty level (easy, medium, hard) (default: medium)",
                        "name": "difficulty",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Recommendations retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercise-relations/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all relationships for a specific exercise",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exercise Relations"
                ],
                "summary": "Get exercise relations",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Exercise ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Exercise type (grammar, vocabulary, listening, speaking)",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Relations retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid exercise ID or type",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update an existing relationship between two exercises",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exercise Relations"
                ],
                "summary": "Update exercise relation",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Relation ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Updated relation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.UpdateRelationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Relation updated successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or validation errors",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete an existing relationship between two exercises",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exercise Relations"
                ],
                "summary": "Delete exercise relation",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Relation ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Relation deleted successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid relation ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercise-relations/{id}/difficulty": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the difficulty metadata for a specific exercise",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exercise Relations"
                ],
                "summary": "Update exercise difficulty metadata",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Exercise ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Exercise type (grammar, vocabulary, listening, speaking)",
                        "name": "type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "Difficulty metadata",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.UpdateDifficultyMetadataRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Difficulty metadata updated successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid exercise ID, type, or request body",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercises/grammar": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all grammar exercises with optional filtering by category and difficulty",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Grammar"
                ],
                "summary": "Get grammar exercises",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by category (e.g., verbs, nouns, adjectives)",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by difficulty (e.g., beginner, intermediate, advanced)",
                        "name": "difficulty",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Grammar exercises retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercises/grammar/{id}/submit": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Submit an answer for a grammar exercise and get feedback",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Grammar"
                ],
                "summary": "Submit grammar answer",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Exercise ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Answer submission",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.SubmitGrammarAnswerRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Grammar answer submitted successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or Exercise ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Exercise not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercises/listening": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all listening exercises with optional filtering by category and difficulty",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Listening"
                ],
                "summary": "Get listening exercises",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by category (e.g., conversations, news, stories)",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by difficulty (e.g., beginner, intermediate, advanced)",
                        "name": "difficulty",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Listening exercises retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercises/listening/{id}/submit": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Submit an answer for a listening exercise and get feedback",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Listening"
                ],
                "summary": "Submit listening answer",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Exercise ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Answer submission",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.SubmitListeningAnswerRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Listening answer submitted successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or Exercise ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Exercise not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercises/speaking": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all speaking exercises with optional filtering by category and difficulty",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Speaking"
                ],
                "summary": "Get speaking exercises",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by category (e.g., pronunciation, conversation, presentation)",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by difficulty (e.g., beginner, intermediate, advanced)",
                        "name": "difficulty",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Speaking exercises retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercises/speaking/{id}/submit": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Submit a recording for a speaking exercise and get feedback",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Speaking"
                ],
                "summary": "Submit speaking answer",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Exercise ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Recording submission",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.SubmitSpeakingAnswerRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Speaking answer submitted successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or Exercise ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Exercise not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercises/word": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get vocabulary word exercises with optional filtering by category and difficulty",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Words"
                ],
                "summary": "Get word exercises",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by category (e.g., food, travel, business)",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by difficulty (e.g., beginner, intermediate, advanced)",
                        "name": "difficulty",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Word exercises retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/exercises/word/{id}/submit": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Submit an answer for a vocabulary word exercise and get feedback",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Words"
                ],
                "summary": "Submit word answer",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Word ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Answer submission",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.SubmitWordAnswerRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Word answer submitted successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or Word ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Word not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/lessons": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all lessons with optional filtering by category, level, and difficulty",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Lessons"
                ],
                "summary": "Get all lessons",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by category",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by level (beginner, intermediate, advanced)",
                        "name": "level",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by difficulty (1-10)",
                        "name": "difficulty",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Lessons retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/lessons/favorites": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's favorite lessons",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Lessons"
                ],
                "summary": "Get favorite lessons",
                "responses": {
                    "200": {
                        "description": "Favorite lessons retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/lessons/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get detailed information about a specific lesson",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Lessons"
                ],
                "summary": "Get lesson detail",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Lesson ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Lesson retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Lesson ID is required",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Lesson not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/lessons/{id}/favorite": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Mark or unmark a lesson as favorite for the current user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Lessons"
                ],
                "summary": "Toggle favorite lesson",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Lesson ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Favorite status",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.ToggleFavoriteLessonRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Lesson favorite status updated successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "boolean"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body or Lesson ID is required",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Lesson not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/lessons/{id}/progress": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's progress for a specific lesson",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Lessons"
                ],
                "summary": "Get lesson progress",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Lesson ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Lesson progress retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Lesson ID is required",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the current user's progress for a specific lesson",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Lessons"
                ],
                "summary": "Update lesson progress",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Lesson ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Progress information",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.UpdateLessonProgressRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Lesson progress updated successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body or Lesson ID is required",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Lesson not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all notifications for the current user with optional filtering by status",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notifications"
                ],
                "summary": "Get user notifications",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by status (unread, read, archived)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Number of notifications to return (default: 10)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination (default: 0)",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Notifications retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/preferences": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the notification preferences for the current user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notifications"
                ],
                "summary": "Get notification preferences",
                "responses": {
                    "200": {
                        "description": "Notification preferences retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the notification preferences for the current user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notifications"
                ],
                "summary": "Update notification preferences",
                "parameters": [
                    {
                        "description": "Notification preferences",
                        "name": "preferences",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NotificationPreference"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Notification preferences updated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid notification preferences",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/read-all": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Mark all notifications for the current user as read",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notifications"
                ],
                "summary": "Mark all notifications as read",
                "responses": {
                    "200": {
                        "description": "All notifications marked as read",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/{id}/archive": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Archive a specific notification for the current user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notifications"
                ],
                "summary": "Archive notification",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Notification ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Notification archived",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid notification ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/{id}/read": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Mark a specific notification as read for the current user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notifications"
                ],
                "summary": "Mark notification as read",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Notification ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Notification marked as read",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid notification ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/personalized-learning/initiate": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Start the personalized learning process for a new user by creating an initial assessment",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Personalized Learning"
                ],
                "summary": "Initiate personalized learning",
                "responses": {
                    "200": {
                        "description": "Initial assessment created successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/personalized-learning/status": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current status of a user's personalized learning journey",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Personalized Learning"
                ],
                "summary": "Get personalized learning status",
                "responses": {
                    "200": {
                        "description": "Personalized learning status retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/practice/history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's practice session history",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Practice"
                ],
                "summary": "Get practice history",
                "responses": {
                    "200": {
                        "description": "Practice history retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/practice/recommended": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get personalized practice recommendations for the current user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Practice"
                ],
                "summary": "Get recommended practice",
                "responses": {
                    "200": {
                        "description": "Recommended practice retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/practice/session": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Save a completed practice session for the current user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Practice"
                ],
                "summary": "Save practice session",
                "parameters": [
                    {
                        "description": "Practice session details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.SavePracticeSessionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Practice session saved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/achievements": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's achievements",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get user achievements",
                "responses": {
                    "200": {
                        "description": "User achievements retrieved successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.UserAchievementResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's profile information",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get user profile",
                "responses": {
                    "200": {
                        "description": "User profile retrieved successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the current user's profile information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Update user profile",
                "parameters": [
                    {
                        "description": "User profile information to update",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.UpdateUserProfileRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User profile updated successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body or validation errors",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/progress/{period}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's progress report for a specific period (week, month, year)",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get progress report",
                "parameters": [
                    {
                        "enum": [
                            "week",
                            "month",
                            "year"
                        ],
                        "type": "string",
                        "description": "Period (week, month, year)",
                        "name": "period",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Progress report retrieved successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ProgressReport"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid period",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/settings": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's settings",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get user settings",
                "responses": {
                    "200": {
                        "description": "User settings retrieved successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserSettings"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "User settings not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the current user's settings",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Update user settings",
                "parameters": [
                    {
                        "description": "User settings to update",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.UpdateUserSettingsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User settings updated successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserSettings"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's learning statistics",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get user statistics",
                "responses": {
                    "200": {
                        "description": "User stats retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/streak": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's learning streak",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get learning streak",
                "responses": {
                    "200": {
                        "description": "Learning streak retrieved successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "integer"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/user/words": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current user's vocabulary word list",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get user word list",
                "responses": {
                    "200": {
                        "description": "User word list retrieved successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.UserWordResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/words": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all vocabulary words with optional filtering by category and difficulty",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Words"
                ],
                "summary": "Get vocabulary words",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by category (e.g., food, travel, business)",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by difficulty (e.g., beginner, intermediate, advanced)",
                        "name": "difficulty",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Words retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/words/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get detailed information about a specific vocabulary word",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Words"
                ],
                "summary": "Get word detail",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Word ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Word retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid word ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Word not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/words/{id}/learned": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Mark a vocabulary word as learned for the current user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Words"
                ],
                "summary": "Mark word as learned",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Word ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Word marked as learned successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid word ID",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "Word not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/auth/change-password": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Change the current user's password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Change password",
                "parameters": [
                    {
                        "description": "Old and new password",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.ChangePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Password changed successfully",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated or invalid old password",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/auth/login": {
            "post": {
                "description": "Authenticate a user and return a JWT token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Login user",
                "parameters": [
                    {
                        "description": "Login credentials",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Login successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "Invalid username or password",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/auth/logout": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Logout the current user (client-side token removal)",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Logout user",
                "responses": {
                    "200": {
                        "description": "Logout successful",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "401": {
                        "description": "User not authenticated",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/auth/register": {
            "post": {
                "description": "Register a new user and return a JWT token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Register user",
                "parameters": [
                    {
                        "description": "Registration information",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.RegisterRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Registration successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request body or validation errors",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/auth/reset-password": {
            "post": {
                "description": "Send a password reset link to the user's email",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Reset password",
                "parameters": [
                    {
                        "description": "Email for password reset",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/controllers.ResetPasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "If your email is registered, you will receive a password reset link",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "$ref": "#/definitions/utils.Response"
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "Check the health status of the service",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "Get service health",
                "responses": {
                    "200": {
                        "description": "Health check successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/controllers.HealthResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/liveness": {
            "get": {
                "description": "Check if the service is alive",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "Check service liveness",
                "responses": {
                    "200": {
                        "description": "Service is alive",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "string"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/readiness": {
            "get": {
                "description": "Check if the service is ready to accept traffic",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "Check service readiness",
                "responses": {
                    "200": {
                        "description": "Service is ready",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "string"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "503": {
                        "description": "Service is not ready",
                        "schema": {
                            "$ref": "#/definitions/utils.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "controllers.ChangePasswordRequest": {
            "type": "object",
            "required": [
                "newPassword",
                "oldPassword"
            ],
            "properties": {
                "newPassword": {
                    "type": "string",
                    "minLength": 6,
                    "example": "newpassword123"
                },
                "oldPassword": {
                    "type": "string",
                    "example": "oldpassword123"
                }
            }
        },
        "controllers.CreateRelationRequest": {
            "type": "object",
            "required": [
                "relationType",
                "sourceId",
                "sourceType",
                "strength",
                "targetId",
                "targetType"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Grammar concept needed to understand this vocabulary"
                },
                "relationType": {
                    "description": "prerequisite, related, similar",
                    "type": "string",
                    "example": "prerequisite"
                },
                "sourceId": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "sourceType": {
                    "description": "grammar, vocabulary, listening, speaking",
                    "type": "string",
                    "example": "grammar"
                },
                "strength": {
                    "description": "1=weak, 2=medium, 3=strong",
                    "type": "integer",
                    "maximum": 3,
                    "minimum": 1,
                    "example": 2
                },
                "targetId": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440001"
                },
                "targetType": {
                    "description": "grammar, vocabulary, listening, speaking",
                    "type": "string",
                    "example": "vocabulary"
                }
            }
        },
        "controllers.HealthResponse": {
            "type": "object",
            "properties": {
                "database": {
                    "type": "boolean",
                    "example": true
                },
                "status": {
                    "type": "string",
                    "example": "ok"
                },
                "timestamp": {
                    "type": "string",
                    "example": "2023-05-17T14:30:45Z"
                },
                "uptime": {
                    "type": "string",
                    "example": "3h25m10s"
                },
                "version": {
                    "type": "string",
                    "example": "1.0.0"
                }
            }
        },
        "controllers.LoginRequest": {
            "type": "object",
            "required": [
                "password",
                "username"
            ],
            "properties": {
                "password": {
                    "type": "string",
                    "example": "password123"
                },
                "username": {
                    "type": "string",
                    "example": "johndoe"
                }
            }
        },
        "controllers.RegisterRequest": {
            "type": "object",
            "required": [
                "email",
                "password",
                "username"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "password": {
                    "type": "string",
                    "minLength": 6,
                    "example": "password123"
                },
                "username": {
                    "type": "string",
                    "example": "johndoe"
                }
            }
        },
        "controllers.ResetPasswordRequest": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                }
            }
        },
        "controllers.SavePracticeSessionRequest": {
            "type": "object",
            "required": [
                "duration",
                "score",
                "type"
            ],
            "properties": {
                "duration": {
                    "description": "in seconds",
                    "type": "integer",
                    "minimum": 1,
                    "example": 300
                },
                "score": {
                    "description": "percentage score",
                    "type": "integer",
                    "maximum": 100,
                    "minimum": 0,
                    "example": 85
                },
                "type": {
                    "description": "vocabulary, grammar, listening, speaking, mixed",
                    "type": "string",
                    "example": "vocabulary"
                }
            }
        },
        "controllers.SubmitGrammarAnswerRequest": {
            "type": "object",
            "required": [
                "answer"
            ],
            "properties": {
                "answer": {
                    "type": "string",
                    "example": "have been living"
                }
            }
        },
        "controllers.SubmitListeningAnswerRequest": {
            "type": "object",
            "required": [
                "answerIndex"
            ],
            "properties": {
                "answerIndex": {
                    "type": "integer",
                    "example": 2
                }
            }
        },
        "controllers.SubmitSpeakingAnswerRequest": {
            "type": "object",
            "required": [
                "recordingPath"
            ],
            "properties": {
                "recordingPath": {
                    "type": "string",
                    "example": "/uploads/recordings/user123_exercise456.mp3"
                }
            }
        },
        "controllers.SubmitWordAnswerRequest": {
            "type": "object",
            "required": [
                "isCorrect"
            ],
            "properties": {
                "isCorrect": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "controllers.ToggleFavoriteLessonRequest": {
            "type": "object",
            "required": [
                "isFavorite"
            ],
            "properties": {
                "isFavorite": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "controllers.UpdateDifficultyMetadataRequest": {
            "type": "object",
            "required": [
                "complexityScore",
                "timeToComplete"
            ],
            "properties": {
                "complexityScore": {
                    "description": "1-10 scale",
                    "type": "number",
                    "maximum": 10,
                    "minimum": 1,
                    "example": 7.5
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"grammar\"",
                        " \"verbs\"",
                        " \"past-tense\"]"
                    ]
                },
                "timeToComplete": {
                    "description": "in seconds",
                    "type": "integer",
                    "minimum": 1,
                    "example": 300
                }
            }
        },
        "controllers.UpdateLessonProgressRequest": {
            "type": "object",
            "required": [
                "completed",
                "progress"
            ],
            "properties": {
                "completed": {
                    "type": "boolean",
                    "example": false
                },
                "progress": {
                    "type": "number",
                    "maximum": 1,
                    "minimum": 0,
                    "example": 0.75
                }
            }
        },
        "controllers.UpdateRelationRequest": {
            "type": "object",
            "required": [
                "relationType",
                "strength"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Updated relationship description"
                },
                "relationType": {
                    "description": "prerequisite, related, similar",
                    "type": "string",
                    "example": "related"
                },
                "strength": {
                    "description": "1=weak, 2=medium, 3=strong",
                    "type": "integer",
                    "maximum": 3,
                    "minimum": 1,
                    "example": 3
                }
            }
        },
        "controllers.UpdateUserProfileRequest": {
            "type": "object",
            "properties": {
                "avatar": {
                    "description": "Base64 encoded image",
                    "type": "string",
                    "example": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "name": {
                    "type": "string",
                    "example": "John Doe"
                }
            }
        },
        "controllers.UpdateUserSettingsRequest": {
            "type": "object",
            "properties": {
                "dailyGoal": {
                    "type": "integer",
                    "example": 5
                },
                "darkModeEnabled": {
                    "type": "boolean",
                    "example": false
                },
                "notificationsEnabled": {
                    "type": "boolean",
                    "example": true
                },
                "preferredLanguage": {
                    "type": "string",
                    "example": "en-US"
                }
            }
        },
        "models.AchievementType": {
            "type": "string",
            "enum": [
                "连续学习",
                "词汇量",
                "听力练习",
                "口语练习",
                "课程完成",
                "积分",
                "挑战",
                "社交"
            ],
            "x-enum-varnames": [
                "AchStreak",
                "AchVocabulary",
                "AchListening",
                "AchSpeaking",
                "AchLessons",
                "AchPoints",
                "AchChallenges",
                "AchSocial"
            ]
        },
        "models.Difficulty": {
            "type": "string",
            "enum": [
                "easy",
                "medium",
                "hard"
            ],
            "x-enum-varnames": [
                "Easy",
                "Medium",
                "Hard"
            ]
        },
        "models.Language": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "flag": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "isActive": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "nativeName": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.NotificationPreference": {
            "type": "object",
            "properties": {
                "achievementNotifications": {
                    "type": "boolean"
                },
                "dailyReminderTime": {
                    "description": "Format: \"HH:MM\"",
                    "type": "string"
                },
                "emailNotificationsEnabled": {
                    "type": "boolean"
                },
                "lessonNotifications": {
                    "type": "boolean"
                },
                "pushNotificationsEnabled": {
                    "type": "boolean"
                },
                "reminderNotifications": {
                    "type": "boolean"
                },
                "streakNotifications": {
                    "type": "boolean"
                },
                "userId": {
                    "type": "string"
                }
            }
        },
        "models.ProgressReport": {
            "type": "object",
            "properties": {
                "averageScore": {
                    "type": "number"
                },
                "completedExercises": {
                    "type": "integer"
                },
                "completedLessons": {
                    "type": "integer"
                },
                "dailyActivity": {
                    "description": "activity level for each day",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "endDate": {
                    "type": "string"
                },
                "newWords": {
                    "type": "integer"
                },
                "period": {
                    "type": "string"
                },
                "pointsEarned": {
                    "type": "integer"
                },
                "startDate": {
                    "type": "string"
                },
                "totalPracticeTime": {
                    "description": "in seconds",
                    "type": "integer"
                }
            }
        },
        "models.User": {
            "type": "object",
            "properties": {
                "avatar_url": {
                    "type": "string"
                },
                "completedChallenges": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "currentStreak": {
                    "type": "integer"
                },
                "email": {
                    "type": "string"
                },
                "helpedUsers": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "last_login_at": {
                    "type": "string"
                },
                "listeningExerciseCount": {
                    "type": "integer"
                },
                "points": {
                    "type": "integer"
                },
                "settings": {
                    "$ref": "#/definitions/models.UserSettings"
                },
                "speakingExerciseCount": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                },
                "vocabularyCount": {
                    "type": "integer"
                }
            }
        },
        "models.UserAchievementResponse": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "icon": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "isUnlocked": {
                    "type": "boolean"
                },
                "progress": {
                    "type": "integer"
                },
                "requirement": {
                    "type": "integer"
                },
                "reward": {
                    "type": "integer"
                },
                "rewardClaimed": {
                    "type": "boolean"
                },
                "title": {
                    "type": "string"
                },
                "type": {
                    "$ref": "#/definitions/models.AchievementType"
                },
                "unlockedDate": {
                    "type": "string"
                }
            }
        },
        "models.UserResponse": {
            "type": "object",
            "properties": {
                "avatar_url": {
                    "type": "string"
                },
                "completedChallenges": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "currentStreak": {
                    "type": "integer"
                },
                "email": {
                    "type": "string"
                },
                "helpedUsers": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "last_login_at": {
                    "type": "string"
                },
                "listeningExerciseCount": {
                    "type": "integer"
                },
                "points": {
                    "type": "integer"
                },
                "settings": {
                    "$ref": "#/definitions/models.UserSettings"
                },
                "speakingExerciseCount": {
                    "type": "integer"
                },
                "token": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                },
                "vocabularyCount": {
                    "type": "integer"
                }
            }
        },
        "models.UserSettings": {
            "type": "object",
            "properties": {
                "dailyGoal": {
                    "type": "integer"
                },
                "darkModeEnabled": {
                    "type": "boolean"
                },
                "notificationsEnabled": {
                    "type": "boolean"
                },
                "preferredLanguage": {
                    "type": "string"
                }
            }
        },
        "models.UserWordResponse": {
            "type": "object",
            "properties": {
                "isFavorite": {
                    "type": "boolean"
                },
                "isLearned": {
                    "type": "boolean"
                },
                "word": {
                    "$ref": "#/definitions/models.Word"
                }
            }
        },
        "models.Word": {
            "type": "object",
            "properties": {
                "audioURL": {
                    "type": "string"
                },
                "category": {
                    "type": "string"
                },
                "definition": {
                    "type": "string"
                },
                "difficulty": {
                    "$ref": "#/definitions/models.Difficulty"
                },
                "exampleSentence": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "imageURL": {
                    "type": "string"
                },
                "language": {
                    "description": "Relationships",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.Language"
                        }
                    ]
                },
                "languageId": {
                    "type": "string"
                },
                "pronunciation": {
                    "type": "string"
                },
                "translation": {
                    "type": "string"
                },
                "word": {
                    "type": "string"
                }
            }
        },
        "utils.ErrorResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "utils.Response": {
            "type": "object",
            "properties": {
                "data": {},
                "error": {
                    "$ref": "#/definitions/utils.ErrorResponse"
                },
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                },
                "validation_errors": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
