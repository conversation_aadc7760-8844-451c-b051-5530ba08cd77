package main

import (
	"context"
	"log"

	"languagelearning/migrations"
	"languagelearning/models"
	appLogger "languagelearning/utils/logger"

	"github.com/joho/godotenv"
)

func main() {
	// 创建应用级上下文
	ctx := context.Background()
	logger := appLogger.DefaultLogger()

	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		logger.Info(ctx, "No .env file found, using environment variables", nil)
	}

	// 初始化数据库
	db := models.ConnectDatabase()

	// 运行数据库迁移
	log.Println("Starting database migrations...")
	migrations.Migrate(db)
	migrations.CreateIndexes(db)
	log.Println("Database migrations completed successfully!")
}
