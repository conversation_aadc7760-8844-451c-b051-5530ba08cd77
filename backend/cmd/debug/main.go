package main

import (
	"log"
	"languagelearning/di"
)

func main() {
	log.Printf("Starting debug test...")

	// 創建模塊化容器
	container := di.NewModularContainerWrapper()

	log.Printf("Container created, is modular: %v", container.IsModular())

	// 構建容器
	log.Printf("Building container...")
	err := container.Build()
	if err != nil {
		log.Fatalf("Failed to build container: %v", err)
	}

	log.Printf("Container built successfully!")

	// 檢查模塊加載順序
	loadOrder := container.GetModuleLoadOrder()
	log.Printf("Module load order: %v", loadOrder)

	// 檢查啟用的模塊
	enabledModules := container.GetEnabledModules()
	log.Printf("Enabled modules: %d", len(enabledModules))

	log.Printf("Debug test completed successfully!")
}
