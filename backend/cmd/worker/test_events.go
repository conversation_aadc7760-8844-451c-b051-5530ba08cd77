package main

import (
	"context"
	"log"
	"time"

	"languagelearning/config"
	"languagelearning/di"
	"languagelearning/domain/core/event"
	learningevent "languagelearning/domain/learning/event"
	"languagelearning/models"

	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

// testEventPublishing tests that events can be published and processed
func testEventPublishing() {
	log.Println("Testing event publishing...")

	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Load configuration
	cfg := config.LoadConfig()

	// Initialize database
	models.ConnectDatabase()

	// Initialize DI container
	container := di.NewContainer()
	if err := container.Build(); err != nil {
		log.Fatalf("Failed to build DI container: %v", err)
	}

	// Get EventBus from DI container
	var eventBus event.EventBus
	if err := container.Invoke(func(bus event.EventBus) {
		eventBus = bus
	}); err != nil {
		log.Fatalf("Failed to get EventBus: %v", err)
	}

	ctx := context.Background()

	// Test publishing a learning progress event
	userID := uuid.New()
	progressEvent := learningevent.NewLearningProgressUpdatedEvent(
		userID, nil, 85, 10, 8, 2,
	)

	log.Printf("Publishing learning progress event for user: %s", userID)
	if err := eventBus.Publish(ctx, progressEvent); err != nil {
		log.Printf("Failed to publish event: %v", err)
	} else {
		log.Println("Event published successfully!")
	}

	// Test publishing a lesson completed event
	lessonID := uuid.New()
	lessonEvent := learningevent.NewLessonCompletedEvent(
		lessonID, userID, nil, nil, "completed",
	)

	log.Printf("Publishing lesson completed event for lesson: %s", lessonID)
	if err := eventBus.Publish(ctx, lessonEvent); err != nil {
		log.Printf("Failed to publish event: %v", err)
	} else {
		log.Println("Event published successfully!")
	}

	// Wait a bit for event processing
	time.Sleep(time.Second * 2)
	log.Println("Event publishing test completed")
}

// Uncomment the main function below to run the test
// func main() {
// 	testEventPublishing()
// }
