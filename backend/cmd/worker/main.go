package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"languagelearning/config"
	"languagelearning/di"
	achievementevent "languagelearning/domain/achievement/event"
	"languagelearning/domain/core/event"
	exerciseevent "languagelearning/domain/exercise/event"
	"languagelearning/domain/learning/event/handlers"
	lessonevent "languagelearning/domain/learning/lesson/event"
	notificationevent "languagelearning/domain/notification/event"
	notificationservice "languagelearning/domain/notification/service"
	userevent "languagelearning/domain/user/event"
	"languagelearning/models"

	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Load configuration
	cfg := config.LoadConfig()

	// Initialize database
	models.ConnectDatabase()

	// Initialize DI container (using modular architecture)
	container := di.NewModularContainerWrapper()
	if err := container.Build(); err != nil {
		log.Fatalf("Failed to build DI container: %v", err)
	}

	// Log modular information
	if container.IsModular() {
		loadOrder := container.GetModuleLoadOrder()
		log.Printf("Worker loaded modules in order: %v", loadOrder)
	}

	// Create context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle OS signals for graceful shutdown
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-signals
		log.Printf("Received signal: %v", sig)
		cancel()
	}()

	// Start event processing
	if err := startEventProcessing(ctx, container, cfg); err != nil {
		log.Fatalf("Failed to start event processing: %v", err)
	}

	// Wait for context cancellation
	<-ctx.Done()
	log.Println("Shutting down worker...")
	time.Sleep(2 * time.Second) // Give consumers time to finish processing
}

// startEventProcessing initializes and starts the event processing system
func startEventProcessing(ctx context.Context, container *di.Container, cfg *config.Config) error {
	log.Println("Starting event processing system...")

	// Get EventBus from DI container
	var eventBus event.EventBus
	if err := container.Invoke(func(bus event.EventBus) {
		eventBus = bus
	}); err != nil {
		return err
	}

	// Get NotificationService from DI container
	var notificationService notificationservice.NotificationService
	if err := container.Invoke(func(service notificationservice.NotificationService) {
		notificationService = service
	}); err != nil {
		return err
	}

	// Register all event handlers
	if err := registerEventHandlers(eventBus, notificationService); err != nil {
		return err
	}

	// Start event consumers based on eventbus type
	go startEventConsumers(ctx, eventBus)

	log.Println("Event processing system started successfully")
	return nil
}

// registerEventHandlers registers all domain event handlers
func registerEventHandlers(eventBus event.EventBus, notificationService notificationservice.NotificationService) error {
	log.Println("Registering event handlers...")

	// Register learning event handlers
	if err := handlers.RegisterAllHandlers(eventBus, notificationService); err != nil {
		return err
	}

	// Register lesson event handlers
	if err := lessonevent.RegisterLessonEventHandlers(eventBus); err != nil {
		return err
	}

	// Register exercise event handlers
	if err := exerciseevent.RegisterExerciseEventHandlers(eventBus); err != nil {
		return err
	}

	// Register achievement event handlers
	if err := achievementevent.RegisterAchievementEventHandlers(eventBus); err != nil {
		return err
	}

	// Register user event handlers
	if err := userevent.RegisterUserEventHandlers(eventBus); err != nil {
		return err
	}

	// Register notification event handlers
	if err := notificationevent.RegisterNotificationEventHandlers(eventBus); err != nil {
		return err
	}

	log.Println("All event handlers registered successfully")
	return nil
}

// startEventConsumers starts event consumers based on the eventbus implementation
func startEventConsumers(ctx context.Context, eventBus event.EventBus) {
	log.Println("Starting event consumers...")

	// Check if the eventbus supports background consumption
	// For RabbitMQ, Redis, and Kafka eventbuses, consumption is handled automatically
	// For in-memory eventbus, events are processed synchronously

	// Keep the consumer running
	<-ctx.Done()
	log.Println("Event consumers stopped")
}
