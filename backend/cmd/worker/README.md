# Event Worker

The Event Worker is a background service that processes domain events using the pluggable EventBus system.

## Features

- **Pluggable EventBus**: Supports multiple backends (In-Memory, Redis, RabbitMQ, Kafka)
- **Domain Event Processing**: Handles all domain events from learning, lesson, exercise, achievement, user, and notification domains
- **Dependency Injection**: Uses the same DI container as the API for consistent configuration
- **Graceful Shutdown**: Handles OS signals for clean shutdown
- **Auto-Registration**: Automatically registers all domain event handlers

## Supported Event Types

### Learning Events
- `learning_progress.updated` - Learning progress updates
- `learning_path.updated` - Learning path changes
- `lesson.completed` - Lesson completion
- `exercise.attempted` - Exercise attempts

### Lesson Events
- `lesson.created` - New lesson creation
- `lesson.updated` - Lesson modifications
- `lesson.deleted` - Lesson removal

### Exercise Events
- `exercise.attempted` - Exercise attempts
- `exercise.difficulty_changed` - Difficulty adjustments

### Achievement Events
- `achievement.unlocked` - Achievement unlocks
- `achievement.progress_updated` - Achievement progress

### User Events
- `user.registered` - User registration
- `user.updated` - User profile updates

### Notification Events
- `notification.created` - New notifications
- `email.queued` - Email queue events

## Configuration

The worker uses the same configuration as the API server. Configure the EventBus type in your environment:

```bash
# In-Memory EventBus (default, no persistence)
EVENT_BUS_TYPE=memory

# Redis EventBus
EVENT_BUS_TYPE=redis
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# RabbitMQ EventBus
EVENT_BUS_TYPE=rabbitmq
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest

# Kafka EventBus
EVENT_BUS_TYPE=kafka
KAFKA_BROKERS=localhost:9092
```

## Running the Worker

### Development
```bash
# Run directly
go run cmd/worker/main.go

# Or use make
make worker
```

### Production
```bash
# Build first
make build

# Run the binary
./bin/worker
```

### Docker
The worker can be run alongside the API in Docker Compose. Add it to your `docker-compose.yml`:

```yaml
services:
  worker:
    build: .
    command: ./bin/worker
    environment:
      - EVENT_BUS_TYPE=rabbitmq
      - RABBITMQ_HOST=rabbitmq
    depends_on:
      - db
      - rabbitmq
```

## Event Flow

1. **API Services** publish domain events to the EventBus
2. **Worker** subscribes to all event types and processes them
3. **Event Handlers** perform side effects like:
   - Sending notifications
   - Updating learning analytics
   - Triggering achievement checks
   - Sending emails
   - Updating user statistics

## Monitoring

The worker logs all event processing activities. Monitor the logs for:
- Event handler registration
- Event processing success/failures
- Consumer status
- Graceful shutdown events

## Scaling

- **In-Memory**: Single instance only
- **Redis**: Multiple workers can run, events are distributed
- **RabbitMQ**: Multiple workers with load balancing
- **Kafka**: Multiple workers with partition-based scaling
