package main

import (
	"context"
	"log"

	"languagelearning/models"
	"languagelearning/seeds"
	appLogger "languagelearning/utils/logger"

	"github.com/joho/godotenv"
)

func main() {
	// 创建应用级上下文
	ctx := context.Background()
	logger := appLogger.DefaultLogger()

	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		logger.Info(ctx, "No .env file found, using environment variables", nil)
	}

	// 初始化数据库
	models.ConnectDatabase()

	// 运行数据库种子
	log.Println("Starting database seeding...")
	seeds.SeedAll()
	log.Println("Database seeding completed successfully!")
}
