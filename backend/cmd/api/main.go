package main

import (
	"context"
	"log"
	"os"

	"languagelearning/config"
	v1 "languagelearning/controllers/v1"
	"languagelearning/di"
	"languagelearning/middleware"
	"languagelearning/models"
	appLogger "languagelearning/utils/logger"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func initializeApp() (*gin.Engine, error) {
	// 创建应用级上下文
	ctx := context.Background()
	logger := appLogger.DefaultLogger()

	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		logger.Info(ctx, "No .env file found, using environment variables", nil)
	}

	// 加载配置
	cfg := config.LoadConfig()

	// 设置Gin模式
	gin.SetMode(cfg.Server.GinMode)

	// 初始化数据库
	models.ConnectDatabase()

	// 初始化路由
	r := gin.Default()

	// 添加中间件
	r.Use(middleware.LoggingMiddleware())

	// 初始化DI容器（使用模塊化架構）
	container := di.NewModularContainerWrapper()

	// 可選：啟用進階功能
	// container.EnableFeature(coredi.FeatureAdvanced)

	if err := container.Build(); err != nil {
		log.Fatalf("Failed to build DI container: %v", err)
	}

	// 顯示模塊化信息（可選，用於調試）
	if container.IsModular() {
		loadOrder := container.GetModuleLoadOrder()
		log.Printf("Loaded modules in order: %v", loadOrder)
	}

	// 使用DI容器设置路由
	var apiRouter *v1.APIRouter
	if err := container.Invoke(func(router *v1.APIRouter) {
		apiRouter = router
	}); err != nil {
		log.Fatalf("无法从DI容器获取API路由: %v", err)
	}
	apiRouter.RegisterRoutes(r)

	return r, nil
}

func main() {
	r, err := initializeApp()
	if err != nil {
		log.Fatalf("Failed to initialize app: %v", err)
	}

	// Start the server
	port := ":8080"
	if envPort := os.Getenv("PORT"); envPort != "" {
		port = ":" + envPort
	}

	log.Printf("Server starting on port %s", port)
	if err := r.Run(port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
