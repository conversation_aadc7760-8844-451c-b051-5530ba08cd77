package seeds

import (
	"languagelearning/models"
	"log"

	"github.com/google/uuid"
)

// seedEnglishWords creates sample English words
func seedEnglishWords() []models.Word {
	// Check if words already exist
	var count int64
	models.DB.Model(&models.Word{}).Count(&count)
	if count > 0 {
		log.Println("Words already exist, skipping English word seed")
		var words []models.Word
		models.DB.Find(&words)
		return words
	}

	// Get English language ID
	var englishLanguage models.Language
	if err := models.DB.Where("code = ?", "en").First(&englishLanguage).Error; err != nil {
		log.Println("English language not found, creating a default one")
		englishLanguage = models.Language{
			ID:          uuid.New(),
			Code:        "en",
			Name:        "English",
			NativeName:  "English",
			Description: "English language",
			IsActive:    true,
		}
		if err := models.DB.Create(&englishLanguage).Error; err != nil {
			log.Printf("Error creating English language: %v", err)
		}
	}

	// Create sample words
	words := []models.Word{
		// Beginner level words
		{
			ID:              uuid.New(),
			Word:            "hello",
			Translation:     "你好",
			Pronunciation:   "həˈloʊ",
			Definition:      "Used as a greeting or to begin a phone conversation",
			ExampleSentence: "Hello, how are you today?",
			Category:        "greeting",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/hello.mp3",
			ImageURL:        "https://example.com/images/hello.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "thank you",
			Translation:     "謝謝",
			Pronunciation:   "θæŋk juː",
			Definition:      "A polite expression used when acknowledging a gift, service, or compliment",
			ExampleSentence: "Thank you for your help.",
			Category:        "greeting",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/thank_you.mp3",
			ImageURL:        "https://example.com/images/thank_you.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "goodbye",
			Translation:     "再見",
			Pronunciation:   "ɡʊdˈbaɪ",
			Definition:      "A conventional expression used at parting",
			ExampleSentence: "Goodbye, see you tomorrow.",
			Category:        "greeting",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/goodbye.mp3",
			ImageURL:        "https://example.com/images/goodbye.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "friend",
			Translation:     "朋友",
			Pronunciation:   "frend",
			Definition:      "A person with whom one has a bond of mutual affection",
			ExampleSentence: "He is my best friend.",
			Category:        "people",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/friend.mp3",
			ImageURL:        "https://example.com/images/friend.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "student",
			Translation:     "學生",
			Pronunciation:   "ˈstuːdnt",
			Definition:      "A person who is studying at a school or college",
			ExampleSentence: "I am a university student.",
			Category:        "people",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/student.mp3",
			ImageURL:        "https://example.com/images/student.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "teacher",
			Translation:     "老師",
			Pronunciation:   "ˈtiːtʃər",
			Definition:      "A person who teaches, especially in a school",
			ExampleSentence: "She is my English teacher.",
			Category:        "people",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/teacher.mp3",
			ImageURL:        "https://example.com/images/teacher.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "water",
			Translation:     "水",
			Pronunciation:   "ˈwɔːtər",
			Definition:      "A colorless, transparent, odorless liquid that forms the seas, lakes, rivers, and rain",
			ExampleSentence: "I would like a glass of water.",
			Category:        "food",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/water.mp3",
			ImageURL:        "https://example.com/images/water.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "food",
			Translation:     "食物",
			Pronunciation:   "fuːd",
			Definition:      "Any nutritious substance that people or animals eat or drink to maintain life and growth",
			ExampleSentence: "I need to buy some food for dinner.",
			Category:        "food",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/food.mp3",
			ImageURL:        "https://example.com/images/food.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "book",
			Translation:     "書",
			Pronunciation:   "bʊk",
			Definition:      "A written or printed work consisting of pages glued or sewn together along one side",
			ExampleSentence: "This book is very interesting.",
			Category:        "objects",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/book.mp3",
			ImageURL:        "https://example.com/images/book.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "time",
			Translation:     "時間",
			Pronunciation:   "taɪm",
			Definition:      "The indefinite continued progress of existence and events in the past, present, and future",
			ExampleSentence: "What time is it?",
			Category:        "concepts",
			Difficulty:      models.Easy,
			AudioURL:        "https://example.com/audio/time.mp3",
			ImageURL:        "https://example.com/images/time.jpg",
			LanguageID:      englishLanguage.ID,
		},

		// Intermediate level words
		{
			ID:              uuid.New(),
			Word:            "environment",
			Translation:     "環境",
			Pronunciation:   "ɪnˈvaɪrənmənt",
			Definition:      "The surroundings or conditions in which a person, animal, or plant lives or operates",
			ExampleSentence: "We should protect the environment.",
			Category:        "society",
			Difficulty:      models.Medium,
			AudioURL:        "https://example.com/audio/environment.mp3",
			ImageURL:        "https://example.com/images/environment.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "economy",
			Translation:     "經濟",
			Pronunciation:   "ɪˈkɑːnəmi",
			Definition:      "The state of a country or region in terms of the production and consumption of goods and services",
			ExampleSentence: "The economy is growing rapidly.",
			Category:        "society",
			Difficulty:      models.Medium,
			AudioURL:        "https://example.com/audio/economy.mp3",
			ImageURL:        "https://example.com/images/economy.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "culture",
			Translation:     "文化",
			Pronunciation:   "ˈkʌltʃər",
			Definition:      "The arts and other manifestations of human intellectual achievement regarded collectively",
			ExampleSentence: "Chinese culture has a long history.",
			Category:        "society",
			Difficulty:      models.Medium,
			AudioURL:        "https://example.com/audio/culture.mp3",
			ImageURL:        "https://example.com/images/culture.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "technology",
			Translation:     "科技",
			Pronunciation:   "tekˈnɑːlədʒi",
			Definition:      "The application of scientific knowledge for practical purposes, especially in industry",
			ExampleSentence: "Technology has changed the way we live.",
			Category:        "society",
			Difficulty:      models.Medium,
			AudioURL:        "https://example.com/audio/technology.mp3",
			ImageURL:        "https://example.com/images/technology.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "education",
			Translation:     "教育",
			Pronunciation:   "ˌedʒuˈkeɪʃn",
			Definition:      "The process of receiving or giving systematic instruction, especially at a school or university",
			ExampleSentence: "Education is important for a country's development.",
			Category:        "society",
			Difficulty:      models.Medium,
			AudioURL:        "https://example.com/audio/education.mp3",
			ImageURL:        "https://example.com/images/education.jpg",
			LanguageID:      englishLanguage.ID,
		},

		// Advanced level words
		{
			ID:              uuid.New(),
			Word:            "sustainable development",
			Translation:     "可持續發展",
			Pronunciation:   "səˈsteɪnəbl dɪˈveləpmənt",
			Definition:      "Economic development that is conducted without depletion of natural resources",
			ExampleSentence: "Sustainable development is an important issue in modern society.",
			Category:        "academic",
			Difficulty:      models.Hard,
			AudioURL:        "https://example.com/audio/sustainable_development.mp3",
			ImageURL:        "https://example.com/images/sustainable_development.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "globalization",
			Translation:     "全球化",
			Pronunciation:   "ˌɡloʊbəlɪˈzeɪʃn",
			Definition:      "The process by which businesses or other organizations develop international influence or start operating on an international scale",
			ExampleSentence: "Globalization has made the world more connected.",
			Category:        "academic",
			Difficulty:      models.Hard,
			AudioURL:        "https://example.com/audio/globalization.mp3",
			ImageURL:        "https://example.com/images/globalization.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "multiculturalism",
			Translation:     "多元文化",
			Pronunciation:   "ˌmʌltiˈkʌltʃərəlɪzəm",
			Definition:      "The presence of, or support for the presence of, several distinct cultural or ethnic groups within a society",
			ExampleSentence: "Multiculturalism is a characteristic of modern society.",
			Category:        "academic",
			Difficulty:      models.Hard,
			AudioURL:        "https://example.com/audio/multiculturalism.mp3",
			ImageURL:        "https://example.com/images/multiculturalism.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "artificial intelligence",
			Translation:     "人工智能",
			Pronunciation:   "ˌɑːrtɪˈfɪʃl ɪnˈtelɪdʒəns",
			Definition:      "The theory and development of computer systems able to perform tasks that normally require human intelligence",
			ExampleSentence: "Artificial intelligence technology is developing rapidly.",
			Category:        "academic",
			Difficulty:      models.Hard,
			AudioURL:        "https://example.com/audio/artificial_intelligence.mp3",
			ImageURL:        "https://example.com/images/artificial_intelligence.jpg",
			LanguageID:      englishLanguage.ID,
		},
		{
			ID:              uuid.New(),
			Word:            "biodiversity",
			Translation:     "生物多樣性",
			Pronunciation:   "ˌbaɪoʊdaɪˈvɜːrsəti",
			Definition:      "The variety of plant and animal life in the world or in a particular habitat",
			ExampleSentence: "Protecting biodiversity is important for ecological balance.",
			Category:        "academic",
			Difficulty:      models.Hard,
			AudioURL:        "https://example.com/audio/biodiversity.mp3",
			ImageURL:        "https://example.com/images/biodiversity.jpg",
			LanguageID:      englishLanguage.ID,
		},
	}

	// Save words to database
	for _, word := range words {
		if err := models.DB.Create(&word).Error; err != nil {
			log.Printf("Error creating English word: %v", err)
		}
	}

	log.Println("Sample English words created successfully")
	return words
}
