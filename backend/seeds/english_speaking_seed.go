package seeds

import (
	"languagelearning/models"
	"languagelearning/seeds/helpers"
	"log"

	"github.com/google/uuid"
)

// seedEnglishSpeakingExercises creates sample English speaking exercises
func seedEnglishSpeakingExercises() []models.SpeakingExercise {
	// Check if speaking exercises already exist
	var count int64
	models.DB.Model(&models.SpeakingExercise{}).Count(&count)
	if count > 0 {
		log.Println("Speaking exercises already exist, skipping English speaking seed")
		var exercises []models.SpeakingExercise
		models.DB.Find(&exercises)
		return exercises
	}

	// Get English language ID
	var englishLanguage models.Language
	if err := models.DB.Where("code = ?", "en").First(&englishLanguage).Error; err != nil {
		log.Println("English language not found, creating a default one")
		englishLanguage = models.Language{
			ID:          uuid.New(),
			Code:        "en",
			Name:        "English",
			NativeName:  "English",
			Description: "English language",
			IsActive:    true,
		}
		if err := models.DB.Create(&englishLanguage).Error; err != nil {
			log.Printf("Error creating English language: %v", err)
		}
	}

	// Create sample speaking exercises using helper functions
	exercises := []models.SpeakingExercise{
		// Beginner level exercises
		helpers.NewSpeakingExercise(
			"Basic Introduction",
			"Hello, my name is John.",
			"speaking",
			models.Easy,
			30, // 30 seconds expected duration
			englishLanguage.ID,
		),
	}

	// Save exercises to database
	for _, exercise := range exercises {
		if err := models.DB.Create(&exercise).Error; err != nil {
			log.Printf("Error creating English speaking exercise: %v", err)
		}
	}

	log.Println("Sample English speaking exercises created successfully")
	return exercises
}
