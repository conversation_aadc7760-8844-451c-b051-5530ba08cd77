package seeds

import (
	"log"
	"time"

	"languagelearning/models"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// SeedEvaluations creates sample evaluations
func SeedEvaluations() []models.Evaluation {
	// Check if evaluations already exist
	var count int64
	models.DB.Model(&models.Evaluation{}).Count(&count)
	if count > 0 {
		log.Println("Evaluations already exist, skipping evaluation seed")
		var evaluations []models.Evaluation
		models.DB.Find(&evaluations)
		return evaluations
	}

	// Create sample evaluations
	evaluations := []models.Evaluation{
		// Placement evaluation
		{
			ID:             uuid.New(),
			Type:           models.EvalPlacement,
			Title:          "初级水平测试",
			Description:    "这个测试将评估您的中文水平，并帮助我们为您推荐合适的学习内容。",
			TotalQuestions: 20,
			PassingScore:   60,
			Duration:       30, // 30 minutes
			CreatedAt:      time.Now().AddDate(0, -1, 0),
			UpdatedAt:      time.Now().AddDate(0, -1, 0),
		},
		// Progress evaluation
		{
			ID:             uuid.New(),
			Type:           models.EvalProgress,
			Title:          "初级进度评估",
			Description:    "这个评估将测试您在初级课程中学到的内容。",
			TotalQuestions: 15,
			PassingScore:   70,
			Duration:       20, // 20 minutes
			CreatedAt:      time.Now().AddDate(0, 0, -15),
			UpdatedAt:      time.Now().AddDate(0, 0, -15),
		},
		// Skill evaluation
		{
			ID:             uuid.New(),
			Type:           models.EvalSkill,
			Title:          "听力技能评估",
			Description:    "这个评估将测试您的中文听力理解能力。",
			TotalQuestions: 10,
			PassingScore:   65,
			Duration:       15, // 15 minutes
			CreatedAt:      time.Now().AddDate(0, 0, -7),
			UpdatedAt:      time.Now().AddDate(0, 0, -7),
		},
		// Certificate evaluation
		{
			ID:             uuid.New(),
			Type:           models.EvalCertificate,
			Title:          "初级中文证书考试",
			Description:    "通过这个考试可以获得初级中文水平证书。",
			TotalQuestions: 30,
			PassingScore:   75,
			Duration:       45, // 45 minutes
			CreatedAt:      time.Now().AddDate(0, 0, -3),
			UpdatedAt:      time.Now().AddDate(0, 0, -3),
		},
	}

	// Create sections and questions for each evaluation
	for i := range evaluations {
		// Create sections
		var sections []models.EvalSection
		
		// Vocabulary section
		vocabSection := models.EvalSection{
			Title:  "词汇",
			Skill:  "vocabulary",
			Weight: 30,
		}
		
		// Create vocabulary questions
		var vocabQuestions []models.EvalQuestion
		vocabQuestions = append(vocabQuestions, models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       "\"你好\" 的英文是什么?",
			Options:       pq.StringArray{"Hello", "Goodbye", "Thank you", "Sorry"},
			CorrectAnswer: "Hello",
			Points:        5,
		})
		vocabQuestions = append(vocabQuestions, models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       "\"谢谢\" 的英文是什么?",
			Options:       pq.StringArray{"Hello", "Goodbye", "Thank you", "Sorry"},
			CorrectAnswer: "Thank you",
			Points:        5,
		})
		vocabQuestions = append(vocabQuestions, models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       "\"再见\" 的英文是什么?",
			Options:       pq.StringArray{"Hello", "Goodbye", "Thank you", "Sorry"},
			CorrectAnswer: "Goodbye",
			Points:        5,
		})
		
		vocabSection.Questions = vocabQuestions
		sections = append(sections, vocabSection)
		
		// Grammar section
		grammarSection := models.EvalSection{
			Title:  "语法",
			Skill:  "grammar",
			Weight: 40,
		}
		
		// Create grammar questions
		var grammarQuestions []models.EvalQuestion
		grammarQuestions = append(grammarQuestions, models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       "选择正确的句子: \"我 ___ 中文。\"",
			Options:       pq.StringArray{"学习", "学习了", "学习着", "在学习"},
			CorrectAnswer: "学习",
			Points:        5,
		})
		grammarQuestions = append(grammarQuestions, models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       "选择正确的句子: \"他 ___ 去北京。\"",
			Options:       pq.StringArray{"想", "想要", "想着", "想到"},
			CorrectAnswer: "想要",
			Points:        5,
		})
		grammarQuestions = append(grammarQuestions, models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       "选择正确的句子: \"我 ___ 吃饭了。\"",
			Options:       pq.StringArray{"已经", "已", "已经有", "有已经"},
			CorrectAnswer: "已经",
			Points:        5,
		})
		
		grammarSection.Questions = grammarQuestions
		sections = append(sections, grammarSection)
		
		// Listening section
		listeningSection := models.EvalSection{
			Title:  "听力",
			Skill:  "listening",
			Weight: 30,
		}
		
		// Create listening questions
		var listeningQuestions []models.EvalQuestion
		listeningQuestions = append(listeningQuestions, models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       "听录音，选择正确的回答: \"你好，你叫什么名字?\"",
			Options:       pq.StringArray{"我叫李明。", "我很好。", "我是学生。", "我喜欢中文。"},
			CorrectAnswer: "我叫李明。",
			Points:        5,
		})
		listeningQuestions = append(listeningQuestions, models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       "听录音，选择正确的回答: \"你今年多大了?\"",
			Options:       pq.StringArray{"我20岁了。", "我是中国人。", "我住在北京。", "我是学生。"},
			CorrectAnswer: "我20岁了。",
			Points:        5,
		})
		
		listeningSection.Questions = listeningQuestions
		sections = append(sections, listeningSection)
		
		evaluations[i].Sections = sections
	}

	// Save evaluations to database
	for _, evaluation := range evaluations {
		if err := models.DB.Create(&evaluation).Error; err != nil {
			log.Printf("Error creating evaluation: %v", err)
		}
	}

	log.Println("Sample evaluations created successfully")
	return evaluations
}
