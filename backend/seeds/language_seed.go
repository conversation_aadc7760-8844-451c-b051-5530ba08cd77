package seeds

import (
	"log"
	"time"

	"languagelearning/models"

	"github.com/google/uuid"
)

// seedLanguages creates sample languages
func seedLanguages() []models.Language {
	// Check if languages already exist
	var count int64
	models.DB.Model(&models.Language{}).Count(&count)
	if count > 0 {
		log.Println("Languages already exist, skipping language seed")
		var languages []models.Language
		models.DB.Find(&languages)
		return languages
	}

	// Create sample languages
	languages := []models.Language{
		{
			ID:          uuid.New(),
			Code:        "en",
			Name:        "English",
			NativeName:  "English",
			Description: "English is a West Germanic language that arose in the Anglo-Saxon kingdoms of England and spread into what was to become south-east Scotland under the influence of the Anglian medieval kingdom of Northumbria.",
			Flag:        "https://example.com/flags/en.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "zh",
			Name:        "Chinese",
			NativeName:  "中文",
			Description: "Chinese is a group of language varieties that form the Sinitic branch of the Sino-Tibetan languages, spoken by the ethnic Han Chinese majority and many minority ethnic groups in Greater China.",
			Flag:        "https://example.com/flags/zh.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "es",
			Name:        "Spanish",
			NativeName:  "Español",
			Description: "Spanish is a Romance language that originated in the Iberian Peninsula and today has over 483 million native speakers in Spain and the Americas.",
			Flag:        "https://example.com/flags/es.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "fr",
			Name:        "French",
			NativeName:  "Français",
			Description: "French is a Romance language of the Indo-European family. It descended from the Vulgar Latin of the Roman Empire, as did all Romance languages.",
			Flag:        "https://example.com/flags/fr.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "de",
			Name:        "German",
			NativeName:  "Deutsch",
			Description: "German is a West Germanic language that is mainly spoken in Central Europe. It is the most widely spoken and official or co-official language in Germany, Austria, Switzerland, South Tyrol in Italy, the German-speaking Community of Belgium, and Liechtenstein.",
			Flag:        "https://example.com/flags/de.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "ja",
			Name:        "Japanese",
			NativeName:  "日本語",
			Description: "Japanese is an East Asian language spoken by about 128 million people, primarily in Japan, where it is the national language.",
			Flag:        "https://example.com/flags/ja.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "ko",
			Name:        "Korean",
			NativeName:  "한국어",
			Description: "Korean is an East Asian language spoken by about 77 million people. It is the official and national language of both Koreas: North Korea and South Korea.",
			Flag:        "https://example.com/flags/ko.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "ru",
			Name:        "Russian",
			NativeName:  "Русский",
			Description: "Russian is an East Slavic language, which is an official language in Russia, Belarus, Kazakhstan, Kyrgyzstan, as well as being widely used throughout Eastern Europe, the Baltic states, the Caucasus and Central Asia.",
			Flag:        "https://example.com/flags/ru.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "ar",
			Name:        "Arabic",
			NativeName:  "العربية",
			Description: "Arabic is a Semitic language that first emerged in the 1st to 4th centuries CE. It is now the lingua franca of the Arab world.",
			Flag:        "https://example.com/flags/ar.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Code:        "pt",
			Name:        "Portuguese",
			NativeName:  "Português",
			Description: "Portuguese is a Romance language originating in the Iberian Peninsula of Europe. It is the sole official language of Portugal, Brazil, Angola, Mozambique, Guinea-Bissau, Cape Verde, São Tomé and Príncipe, and East Timor.",
			Flag:        "https://example.com/flags/pt.png",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	// Save languages to database
	for _, language := range languages {
		if err := models.DB.Create(&language).Error; err != nil {
			log.Printf("Error creating language: %v", err)
		}
	}

	log.Println("Sample languages created successfully")
	return languages
}
