package seeds

import (
	"log"
	"languagelearning/models"

	"github.com/google/uuid"
)

// seedEnhancedWords creates enhanced sample words
func seedEnhancedWords() []models.Word {
	// Check if words already exist
	var count int64
	models.DB.Model(&models.Word{}).Count(&count)
	if count > 0 {
		log.Println("Words already exist, skipping word seed")
		var words []models.Word
		models.DB.Find(&words)
		return words
	}

	// Create sample words
	words := []models.Word{
		// Beginner level words
		{
    ID:              uuid.New(),
    Word:            "你好",
    Translation:     "hello",
    Pronunciation:   "nǐ hǎo",
    Definition:      "hello",
    ExampleSentence: "你好，我叫小明。",
    Category:        "greeting",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/你好.mp3",
    ImageURL:        "https://example.com/images/你好.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "謝謝",
    Translation:     "thank you",
    Pronunciation:   "xiè xiè",
    Definition:      "thank you",
    ExampleSentence: "謝謝你的幫助。",
    Category:        "greeting",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/謝謝.mp3",
    ImageURL:        "https://example.com/images/謝謝.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "再見",
    Translation:     "goodbye",
    Pronunciation:   "zài jiàn",
    Definition:      "goodbye",
    ExampleSentence: "再見，明天見。",
    Category:        "greeting",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/再見.mp3",
    ImageURL:        "https://example.com/images/再見.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "朋友",
    Translation:     "friend",
    Pronunciation:   "péng yǒu",
    Definition:      "friend",
    ExampleSentence: "他是我的好朋友。",
    Category:        "people",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/朋友.mp3",
    ImageURL:        "https://example.com/images/朋友.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "學生",
    Translation:     "student",
    Pronunciation:   "xué shēng",
    Definition:      "student",
    ExampleSentence: "我是一名大學生。",
    Category:        "people",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/學生.mp3",
    ImageURL:        "https://example.com/images/學生.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "老師",
    Translation:     "teacher",
    Pronunciation:   "lǎo shī",
    Definition:      "teacher",
    ExampleSentence: "她是我的中文老師。",
    Category:        "people",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/老師.mp3",
    ImageURL:        "https://example.com/images/老師.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "水",
    Translation:     "water",
    Pronunciation:   "shuǐ",
    Definition:      "water",
    ExampleSentence: "我想喝水。",
    Category:        "food",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/水.mp3",
    ImageURL:        "https://example.com/images/水.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "飯",
    Translation:     "rice/meal",
    Pronunciation:   "fàn",
    Definition:      "rice/meal",
    ExampleSentence: "我想吃飯。",
    Category:        "food",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/飯.mp3",
    ImageURL:        "https://example.com/images/飯.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "茶",
    Translation:     "tea",
    Pronunciation:   "chá",
    Definition:      "tea",
    ExampleSentence: "我喜歡喝綠茶。",
    Category:        "food",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/茶.mp3",
    ImageURL:        "https://example.com/images/茶.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "書",
    Translation:     "book",
    Pronunciation:   "shū",
    Definition:      "book",
    ExampleSentence: "這本書很有趣。",
    Category:        "objects",
    Difficulty:      models.Easy,
    AudioURL:        "https://example.com/audio/書.mp3",
    ImageURL:        "https://example.com/images/書.jpg",
},

		// Intermediate level words
		{
    ID:              uuid.New(),
    Word:            "環境",
    Translation:     "environment",
    Pronunciation:   "huán jìng",
    Definition:      "environment",
    ExampleSentence: "我們應該保護環境。",
    Category:        "society",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/環境.mp3",
    ImageURL:        "https://example.com/images/環境.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "經濟",
    Translation:     "economy",
    Pronunciation:   "jīng jì",
    Definition:      "economy",
    ExampleSentence: "中國的經濟發展很快。",
    Category:        "society",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/經濟.mp3",
    ImageURL:        "https://example.com/images/經濟.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "文化",
    Translation:     "culture",
    Pronunciation:   "wén huà",
    Definition:      "culture",
    ExampleSentence: "中國有悠久的文化歷史。",
    Category:        "society",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/文化.mp3",
    ImageURL:        "https://example.com/images/文化.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "科技",
    Translation:     "technology",
    Pronunciation:   "kē jì",
    Definition:      "technology",
    ExampleSentence: "科技的發展改變了我們的生活。",
    Category:        "society",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/科技.mp3",
    ImageURL:        "https://example.com/images/科技.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "教育",
    Translation:     "education",
    Pronunciation:   "jiào yù",
    Definition:      "education",
    ExampleSentence: "教育對一個國家的發展很重要。",
    Category:        "society",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/教育.mp3",
    ImageURL:        "https://example.com/images/教育.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "健康",
    Translation:     "health",
    Pronunciation:   "jiàn kāng",
    Definition:      "health",
    ExampleSentence: "健康比金錢更重要。",
    Category:        "life",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/健康.mp3",
    ImageURL:        "https://example.com/images/健康.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "幸福",
    Translation:     "happiness",
    Pronunciation:   "xìng fú",
    Definition:      "happiness",
    ExampleSentence: "家庭幸福是最重要的。",
    Category:        "life",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/幸福.mp3",
    ImageURL:        "https://example.com/images/幸福.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "成功",
    Translation:     "success",
    Pronunciation:   "chéng gōng",
    Definition:      "success",
    ExampleSentence: "努力工作是成功的關鍵。",
    Category:        "life",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/成功.mp3",
    ImageURL:        "https://example.com/images/成功.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "挑戰",
    Translation:     "challenge",
    Pronunciation:   "tiǎo zhàn",
    Definition:      "challenge",
    ExampleSentence: "學習一門新語言是一個挑戰。",
    Category:        "life",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/挑戰.mp3",
    ImageURL:        "https://example.com/images/挑戰.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "機會",
    Translation:     "opportunity",
    Pronunciation:   "jī huì",
    Definition:      "opportunity",
    ExampleSentence: "每個挑戰都是一個機會。",
    Category:        "life",
    Difficulty:      models.Medium,
    AudioURL:        "https://example.com/audio/機會.mp3",
    ImageURL:        "https://example.com/images/機會.jpg",
},

		// Advanced level words
		{
    ID:              uuid.New(),
    Word:            "可持續發展",
    Translation:     "sustainable development",
    Pronunciation:   "kě chí xù fā zhǎn",
    Definition:      "sustainable development",
    ExampleSentence: "可持續發展是現代社會的重要課題。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/可持續發展.mp3",
    ImageURL:        "https://example.com/images/可持續發展.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "全球化",
    Translation:     "globalization",
    Pronunciation:   "quán qiú huà",
    Definition:      "globalization",
    ExampleSentence: "全球化使世界各國的聯繫更加緊密。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/全球化.mp3",
    ImageURL:        "https://example.com/images/全球化.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "多元文化",
    Translation:     "multiculturalism",
    Pronunciation:   "duō yuán wén huà",
    Definition:      "multiculturalism",
    ExampleSentence: "多元文化是現代社會的特點。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/多元文化.mp3",
    ImageURL:        "https://example.com/images/多元文化.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "人工智能",
    Translation:     "artificial intelligence",
    Pronunciation:   "rén gōng zhì néng",
    Definition:      "artificial intelligence",
    ExampleSentence: "人工智能技術正在快速發展。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/人工智能.mp3",
    ImageURL:        "https://example.com/images/人工智能.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "生物多樣性",
    Translation:     "biodiversity",
    Pronunciation:   "shēng wù duō yàng xìng",
    Definition:      "biodiversity",
    ExampleSentence: "保護生物多樣性對生態平衡很重要。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/生物多樣性.mp3",
    ImageURL:        "https://example.com/images/生物多樣性.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "社會公正",
    Translation:     "social justice",
    Pronunciation:   "shè huì gōng zhèng",
    Definition:      "social justice",
    ExampleSentence: "社會公正是和諧社會的基礎。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/社會公正.mp3",
    ImageURL:        "https://example.com/images/社會公正.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "批判性思維",
    Translation:     "critical thinking",
    Pronunciation:   "pī pàn xìng sī wéi",
    Definition:      "critical thinking",
    ExampleSentence: "批判性思維是現代教育的重要目標。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/批判性思維.mp3",
    ImageURL:        "https://example.com/images/批判性思維.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "創新精神",
    Translation:     "innovative spirit",
    Pronunciation:   "chuàng xīn jīng shén",
    Definition:      "innovative spirit",
    ExampleSentence: "創新精神是企業成功的關鍵。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/創新精神.mp3",
    ImageURL:        "https://example.com/images/創新精神.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "國際關係",
    Translation:     "international relations",
    Pronunciation:   "guó jì guān xì",
    Definition:      "international relations",
    ExampleSentence: "良好的國際關係有利於世界和平。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/國際關係.mp3",
    ImageURL:        "https://example.com/images/國際關係.jpg",
},
		{
    ID:              uuid.New(),
    Word:            "文化遺產",
    Translation:     "cultural heritage",
    Pronunciation:   "wén huà yí chǎn",
    Definition:      "cultural heritage",
    ExampleSentence: "保護文化遺產是我們的責任。",
    Category:        "academic",
    Difficulty:      models.Hard,
    AudioURL:        "https://example.com/audio/文化遺產.mp3",
    ImageURL:        "https://example.com/images/文化遺產.jpg",
},
	}

	// Save words to database
	for _, word := range words {
		if err := models.DB.Create(&word).Error; err != nil {
			log.Printf("Error creating word: %v", err)
		}
	}

	log.Println("Sample words created successfully")
	return words
}
