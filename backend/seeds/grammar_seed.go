package seeds

import (
	"languagelearning/models"
	"languagelearning/seeds/helpers"
	"log"
)

// seedEnhancedGrammarExercises creates enhanced sample grammar exercises
func seedEnhancedGrammarExercises() []models.GrammarExercise {
	// Check if grammar exercises already exist
	var count int64
	models.DB.Model(&models.GrammarExercise{}).Count(&count)
	if count > 0 {
		log.Println("Grammar exercises already exist, skipping grammar seed")
		var exercises []models.GrammarExercise
		models.DB.Find(&exercises)
		return exercises
	}

	// Get Chinese language
	var chineseLanguage models.Language
	if err := models.DB.Where("code = ?", "zh").First(&chineseLanguage).Error; err != nil {
		log.Printf("Error finding Chinese language: %v", err)
		return nil
	}

	// Create sample grammar exercises using helper functions
	exercises := []models.GrammarExercise{
		// Beginner level exercises
		helpers.NewGrammarExercise(
			"動詞時態",
			"我 ____ 中文。",
			"學習",
			"在中文中，表達「我學習中文」時，直接使用動詞「學習」即可，不需要額外的時態標記。",
			"grammar",
			"選擇正確的動詞形式完成句子",
			"我學習中文。",
			[]string{"學習", "學習了", "學習著", "在學習"},
			models.Easy,
			chineseLanguage.ID,
		),
		helpers.NewGrammarExercise(
			"語法練習",
			"他 ____ 去北京。",
			"想要",
			"「想要」表示意願或願望，適合用於「他想要去北京」這個句子。",
			"grammar",
			"選擇正確的選項完成句子",
			"他想要去北京。",
			[]string{"想", "想要", "想著", "想到"},
			models.Easy,
			chineseLanguage.ID,
		),
	}

	// Save exercises to database
	for _, exercise := range exercises {
		if err := models.DB.Create(&exercise).Error; err != nil {
			log.Printf("Error creating grammar exercise: %v", err)
		}
	}

	log.Println("Sample grammar exercises created successfully")
	return exercises
}
