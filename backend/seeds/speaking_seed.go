package seeds

import (
	"languagelearning/models"
	"languagelearning/seeds/helpers"
	"log"
)

// seedEnhancedSpeakingExercises creates enhanced sample speaking exercises
func seedEnhancedSpeakingExercises() []models.SpeakingExercise {
	// Check if speaking exercises already exist
	var count int64
	models.DB.Model(&models.SpeakingExercise{}).Count(&count)
	if count > 0 {
		log.Println("Speaking exercises already exist, skipping speaking seed")
		var exercises []models.SpeakingExercise
		models.DB.Find(&exercises)
		return exercises
	}

	// Get Chinese language
	var chineseLanguage models.Language
	if err := models.DB.Where("code = ?", "zh").First(&chineseLanguage).Error; err != nil {
		log.Printf("Error finding Chinese language: %v", err)
		return nil
	}

	// Create sample speaking exercises using helper functions
	exercises := []models.SpeakingExercise{
		// Beginner level exercises
		helpers.NewSpeakingExercise(
			"口語練習",
			"你好，我叫小明。",
			"speaking",
			models.Easy,
			30, // 30 seconds expected duration
			chineseLanguage.ID,
		),
	}

	// Save exercises to database
	for _, exercise := range exercises {
		if err := models.DB.Create(&exercise).Error; err != nil {
			log.Printf("Error creating speaking exercise: %v", err)
		}
	}

	log.Println("Sample speaking exercises created successfully")
	return exercises
}
