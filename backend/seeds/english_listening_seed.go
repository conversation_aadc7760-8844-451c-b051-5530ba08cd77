package seeds

import (
	"languagelearning/models"
	"languagelearning/seeds/helpers"
	"log"

	"github.com/google/uuid"
)

// seedEnglishListeningExercises creates sample English listening exercises
func seedEnglishListeningExercises() []models.ListeningExercise {
	// Check if listening exercises already exist
	var count int64
	models.DB.Model(&models.ListeningExercise{}).Count(&count)
	if count > 0 {
		log.Println("Listening exercises already exist, skipping English listening seed")
		var exercises []models.ListeningExercise
		models.DB.Preload("Questions").Find(&exercises)
		return exercises
	}

	// Get English language ID
	var englishLanguage models.Language
	if err := models.DB.Where("code = ?", "en").First(&englishLanguage).Error; err != nil {
		log.Println("English language not found, creating a default one")
		englishLanguage = models.Language{
			ID:          uuid.New(),
			Code:        "en",
			Name:        "English",
			NativeName:  "English",
			Description: "English language",
			IsActive:    true,
		}
		if err := models.DB.Create(&englishLanguage).Error; err != nil {
			log.Printf("Error creating English language: %v", err)
		}
	}

	// Create sample listening exercises using helper functions
	exercises := []models.ListeningExercise{
		// Beginner level exercises
		helpers.NewListeningExercise(
			"Basic Greetings",
			"https://example.com/audio/basic_greetings.mp3",
			"Hello! My name is John. Nice to meet you. What's your name?",
			"listening",
			models.Easy,
			englishLanguage.ID,
			[]models.ListeningQuestion{
				helpers.NewListeningQuestion(
					"What is the speaker's name?",
					[]string{"John", "James", "Jack", "Jim"},
					0, // John
					1,
				),
				helpers.NewListeningQuestion(
					"What does the speaker ask?",
					[]string{"What's your name?", "How old are you?", "Where are you from?", "What do you do?"},
					0, // What's your name?
					1,
				),
			},
		),
	}

	// Save exercises to database
	for _, exercise := range exercises {
		if err := models.DB.Create(&exercise).Error; err != nil {
			log.Printf("Error creating English listening exercise: %v", err)
		}
	}

	log.Println("Sample English listening exercises created successfully")
	return exercises
}
