package seeds

import (
	"languagelearning/models"
	"log"

	"gorm.io/gorm"
)

// SeedTags 创建基础标签
func SeedTags(db *gorm.DB) error {
	log.Println("Seeding tags...")

	// 检查标签是否已存在
	var count int64
	db.Model(&models.Tag{}).Count(&count)
	if count > 0 {
		log.Println("Tags already exist, skipping tag seed")
		return nil
	}

	// 创建基础标签
	tags := []models.Tag{
		{Name: "basic"},
		{Name: "beginner"},
		{Name: "intermediate"},
		{Name: "advanced"},
		{Name: "greeting"},
		{Name: "farewell"},
		{Name: "polite"},
		{Name: "number"},
		{Name: "counting"},
		{Name: "color"},
		{Name: "description"},
		{Name: "family"},
		{Name: "relationship"},
		{Name: "food"},
		{Name: "fruit"},
		{Name: "vegetable"},
		{Name: "meat"},
		{Name: "drink"},
		{Name: "verb"},
		{Name: "action"},
		{Name: "movement"},
		{Name: "time"},
		{Name: "calendar"},
		{Name: "measurement"},
		{Name: "business"},
		{Name: "travel"},
		{Name: "education"},
		{Name: "health"},
		{Name: "technology"},
		{Name: "science"},
		{Name: "art"},
	}

	// 保存标签
	for _, tag := range tags {
		if err := db.Create(&tag).Error; err != nil {
			log.Printf("Failed to seed tag %s: %v", tag.Name, err)
			return err
		}
	}

	log.Printf("Seeded %d tags", len(tags))
	return nil
}
