package seeds

import (
	"languagelearning/models"
	"languagelearning/seeds/helpers"
	"log"
)

// seedEnhancedListeningExercises creates enhanced sample listening exercises
func seedEnhancedListeningExercises() []models.ListeningExercise {
	// Check if listening exercises already exist
	var count int64
	models.DB.Model(&models.ListeningExercise{}).Count(&count)
	if count > 0 {
		log.Println("Listening exercises already exist, skipping listening seed")
		var exercises []models.ListeningExercise
		models.DB.Preload("Questions").Find(&exercises)
		return exercises
	}

	// Get Chinese language
	var chineseLanguage models.Language
	if err := models.DB.Where("code = ?", "zh").First(&chineseLanguage).Error; err != nil {
		log.Printf("Error finding Chinese language: %v", err)
		return nil
	}

	// Create sample listening exercises using helper functions
	exercises := []models.ListeningExercise{
		// Beginner level exercises
		helpers.NewListeningExercise(
			"基本問候",
			"https://example.com/audio/basic_greetings.mp3",
			"你好！我叫李明。很高興認識你。你叫什麼名字？",
			"listening",
			models.Easy,
			chineseLanguage.ID,
			[]models.ListeningQuestion{
				helpers.NewListeningQuestion(
					"說話者的名字是什麼？",
					[]string{"李明", "王華", "張偉", "劉強"},
					0, // 李明
					1,
				),
				helpers.NewListeningQuestion(
					"說話者問了什麼問題？",
					[]string{"你叫什麼名字？", "你幾歲了？", "你是哪國人？", "你住在哪裡？"},
					0, // 你叫什麼名字？
					1,
				),
			},
		),
	}

	// Save exercises to database
	for _, exercise := range exercises {
		if err := models.DB.Create(&exercise).Error; err != nil {
			log.Printf("Error creating listening exercise: %v", err)
		}
	}

	log.Println("Sample listening exercises created successfully")
	return exercises
}
