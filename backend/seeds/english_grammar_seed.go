package seeds

import (
	"languagelearning/models"
	"languagelearning/seeds/helpers"
	"log"

	"github.com/google/uuid"
)

// seedEnglishGrammarExercises creates sample English grammar exercises
func seedEnglishGrammarExercises() []models.GrammarExercise {
	// Check if grammar exercises already exist
	var count int64
	models.DB.Model(&models.GrammarExercise{}).Count(&count)
	if count > 0 {
		log.Println("Grammar exercises already exist, skipping English grammar seed")
		var exercises []models.GrammarExercise
		models.DB.Find(&exercises)
		return exercises
	}

	// Get English language ID
	var englishLanguage models.Language
	if err := models.DB.Where("code = ?", "en").First(&englishLanguage).Error; err != nil {
		log.Println("English language not found, creating a default one")
		englishLanguage = models.Language{
			ID:          uuid.New(),
			Code:        "en",
			Name:        "English",
			NativeName:  "English",
			Description: "English language",
			IsActive:    true,
		}
		if err := models.DB.Create(&englishLanguage).Error; err != nil {
			log.Printf("Error creating English language: %v", err)
		}
	}

	// Create sample grammar exercises using helper functions
	exercises := []models.GrammarExercise{
		// Beginner level exercises
		helpers.NewGrammarExercise(
			"Present Simple Tense",
			"She ____ to school every day.",
			"goes",
			"In the present simple tense, when the subject is third person singular (he, she, it), we add -s or -es to the verb.",
			"grammar",
			"Choose the correct verb form to complete the sentence",
			"She goes to school every day.",
			[]string{"go", "goes", "going", "went"},
			models.Easy,
			englishLanguage.ID,
		),
		helpers.NewGrammarExercise(
			"Present Continuous Tense",
			"They ____ football now.",
			"are playing",
			"The present continuous tense is formed with 'be' (am/is/are) + the -ing form of the verb. It's used to talk about actions happening now.",
			"grammar",
			"Choose the correct verb form to complete the sentence",
			"They are playing football now.",
			[]string{"play", "plays", "are playing", "played"},
			models.Easy,
			englishLanguage.ID,
		),
		helpers.NewGrammarExercise(
			"Past Simple Tense",
			"I ____ a movie yesterday.",
			"watched",
			"The past simple tense is used to talk about completed actions in the past. Regular verbs add -ed to form the past tense.",
			"grammar",
			"Choose the correct verb form to complete the sentence",
			"I watched a movie yesterday.",
			[]string{"watch", "watches", "watching", "watched"},
			models.Easy,
			englishLanguage.ID,
		),
		helpers.NewGrammarExercise(
			"Articles",
			"I saw ____ elephant at the zoo.",
			"an",
			"We use 'an' before words that begin with a vowel sound. 'Elephant' begins with the vowel sound /e/, so we use 'an'.",
			"grammar",
			"Choose the correct article to complete the sentence",
			"I saw an elephant at the zoo.",
			[]string{"a", "an", "the", "no article"},
			models.Easy,
			englishLanguage.ID,
		),
		helpers.NewGrammarExercise(
			"Prepositions of Place",
			"The book is ____ the table.",
			"on",
			"We use 'on' for surfaces. The table is a surface, so we say 'on the table'.",
			"grammar",
			"Choose the correct preposition to complete the sentence",
			"The book is on the table.",
			[]string{"on", "in", "at", "by"},
			models.Easy,
			englishLanguage.ID,
		),
	}

	// Save exercises to database
	for _, exercise := range exercises {
		if err := models.DB.Create(&exercise).Error; err != nil {
			log.Printf("Error creating English grammar exercise: %v", err)
		}
	}

	log.Println("Sample English grammar exercises created successfully")
	return exercises
}
