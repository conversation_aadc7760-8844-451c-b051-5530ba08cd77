package helpers

import (
	"languagelearning/models"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// NewGrammarExercise 创建语法练习
func NewGrammarExercise(
	title, question, correctAnswer, explanation, category, instruction, exampleSentence string,
	options []string,
	difficulty models.Difficulty,
	languageID uuid.UUID,
) models.GrammarExercise {
	return models.GrammarExercise{
		Exercise: models.Exercise{
			ID:              uuid.New(),
			Type:            models.ExGrammar,
			Title:           title,
			Question:        question,
			Options:         pq.StringArray(options),
			CorrectAnswer:   correctAnswer,
			Explanation:     explanation,
			Category:        category,
			Difficulty:      difficulty,
			Instruction:     instruction,
			ExampleSentence: exampleSentence,
			LanguageID:      languageID,
			Points:          10,
			IsPublished:     true,
		},
		GrammarRule: "", // 可以根据需要设置
	}
}

// NewListeningExercise 创建听力练习
func NewListeningExercise(
	title, audioURL, transcript, category string,
	difficulty models.Difficulty,
	languageID uuid.UUID,
	questions []models.ListeningQuestion,
) models.ListeningExercise {
	return models.ListeningExercise{
		Exercise: models.Exercise{
			ID:          uuid.New(),
			Type:        models.ExListening,
			Title:       title,
			AudioURL:    audioURL,
			Content:     transcript, // 使用Content字段存储transcript
			Category:    category,
			Difficulty:  difficulty,
			LanguageID:  languageID,
			Points:      10,
			IsPublished: true,
		},
		AudioDuration: 0, // 可以根据需要设置
		Questions:     questions,
	}
}

// NewSpeakingExercise 创建口语练习
func NewSpeakingExercise(
	title, targetPhrase, category string,
	difficulty models.Difficulty,
	expectedDuration int,
	languageID uuid.UUID,
) models.SpeakingExercise {
	return models.SpeakingExercise{
		Exercise: models.Exercise{
			ID:          uuid.New(),
			Type:        models.ExSpeaking,
			Title:       title,
			Category:    category,
			Difficulty:  difficulty,
			LanguageID:  languageID,
			Points:      15,
			IsPublished: true,
		},
		TargetPhrase:     targetPhrase,
		ExpectedDuration: expectedDuration,
	}
}

// NewListeningQuestion 创建听力问题
func NewListeningQuestion(
	question string,
	options []string,
	correctAnswerIndex int,
	points int,
) models.ListeningQuestion {
	return models.ListeningQuestion{
		ID:            uuid.New(),
		Question:      question,
		Options:       pq.StringArray(options),
		CorrectAnswer: correctAnswerIndex,
		Points:        points,
	}
}

// NewListeningExerciseWithQuestions 创建带问题的听力练习
func NewListeningExerciseWithQuestions(
	title, audioURL, transcript, category string,
	difficulty models.Difficulty,
	languageID uuid.UUID,
	questionData []QuestionData,
) models.ListeningExercise {
	questions := make([]models.ListeningQuestion, len(questionData))
	for i, qd := range questionData {
		questions[i] = NewListeningQuestion(qd.Question, qd.Options, qd.CorrectAnswerIndex, qd.Points)
	}

	return NewListeningExercise(title, audioURL, transcript, category, difficulty, languageID, questions)
}

// QuestionData 用于简化听力问题创建的辅助结构
type QuestionData struct {
	Question           string
	Options            []string
	CorrectAnswerIndex int
	Points             int
}
