package seeds

import (
	"encoding/json"
	"io/ioutil"
	"languagelearning/models"
	"languagelearning/seeds/helpers"
	"log"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// VocabularyData 表示词汇JSON数据结构
type VocabularyData struct {
	VocabularyCategories []struct {
		Name        string `json:"name"`
		Description string `json:"description"`
		Words       []struct {
			Word        string   `json:"word"`
			Translation string   `json:"translation"`
			Level       string   `json:"level"`
			Example     string   `json:"example"`
			Tags        []string `json:"tags"`
		} `json:"words"`
	} `json:"vocabularyCategories"`
}

// SeedVocabulary 从JSON文件生成词汇数据
func SeedVocabulary(db *gorm.DB) error {
	log.Println("Seeding vocabulary from JSON...")

	// 读取JSON文件 - 改为使用扩展词汇文件
	data, err := ioutil.ReadFile("seeds/extended_vocabulary.json")
	if err != nil {
		log.Printf("Error reading extended_vocabulary.json, falling back to vocabulary.json")
		data, err = ioutil.ReadFile("seeds/vocabulary.json")
		if err != nil {
			log.Printf("Error reading vocabulary.json: %v", err)
			return err
		}
	}

	// 解析JSON数据
	var vocabData VocabularyData
	if err := json.Unmarshal(data, &vocabData); err != nil {
		log.Printf("Error parsing vocabulary.json: %v", err)
		return err
	}

	// 获取英语语言ID
	var englishLanguage models.Language
	if err := db.Where("code = ?", "en").First(&englishLanguage).Error; err != nil {
		log.Printf("Error finding English language: %v", err)
		return err
	}

	// 获取所有标签
	var tags []models.Tag
	if err := db.Find(&tags).Error; err != nil {
		log.Printf("Error finding tags: %v", err)
		return err
	}

	// 创建标签映射以便快速查找
	tagMap := make(map[string]uint)
	for _, tag := range tags {
		tagMap[tag.Name] = tag.ID
	}

	totalWords := 0
	totalCategories := 0

	// 导入词汇
	for _, category := range vocabData.VocabularyCategories {
		if len(category.Words) == 0 {
			continue // 跳过没有单词的类别
		}

		log.Printf("Processing category: %s (%d words)", category.Name, len(category.Words))
		totalCategories++

		// 为每个类别创建一个课程
		lesson := models.Lesson{
			ID:          uuid.New(),
			Title:       "English " + category.Name,
			Description: category.Description,
			Category:    models.LessonVocabulary,
			Level:       models.LessonBeginner,
			Difficulty:  models.Easy,
			Duration:    15,
			Points:      100,
		}

		if err := db.Create(&lesson).Error; err != nil {
			log.Printf("Error creating lesson for category %s: %v", category.Name, err)
			continue
		}

		// 为每个单词创建练习
		for _, word := range category.Words {
			exercise := helpers.NewGrammarExercise(
				"Vocabulary: "+word.Word,
				"What is the meaning of '"+word.Word+"'?",
				word.Translation,
				"The word '"+word.Word+"' means '"+word.Translation+"' in Chinese.",
				"Vocabulary",
				"Select the correct translation for the word.",
				word.Example,
				[]string{word.Translation, "Wrong option 1", "Wrong option 2", "Wrong option 3"},
				models.Easy, // Default to Easy for vocabulary
				englishLanguage.ID,
			)

			if err := db.Create(&exercise).Error; err != nil {
				log.Printf("Error creating exercise for word %s: %v", word.Word, err)
				continue
			}

			// 创建课程与练习的关联
			lessonExercise := models.LessonExercise{
				LessonID:   lesson.ID,
				ExerciseID: exercise.ID,
			}

			if err := db.Create(&lessonExercise).Error; err != nil {
				log.Printf("Error creating lesson-exercise relation: %v", err)
				continue
			}

			// 添加标签
			for _, tagName := range word.Tags {
				if tagID, ok := tagMap[tagName]; ok {
					exerciseTag := models.ExerciseTag{
						ExerciseID: exercise.ID,
						TagID:      tagID,
					}

					if err := db.Create(&exerciseTag).Error; err != nil {
						log.Printf("Error creating exercise-tag relation: %v", err)
					}
				}
			}

			totalWords++
		}
	}

	log.Printf("Vocabulary seeding completed! Added %d words in %d categories", totalWords, totalCategories)
	return nil
}
