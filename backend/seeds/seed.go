package seeds

import (
	"log"
	"time"

	"languagelearning/models"
	"languagelearning/seeds/helpers"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// SeedAll seeds all data
func SeedAll() {
	log.Println("Seeding data...")

	// Seed languages
	languages := seedLanguages()

	// Seed tags
	if err := SeedTags(models.DB); err != nil {
		log.Printf("Error seeding tags: %v", err)
	}

	// Seed vocabulary
	if err := SeedVocabulary(models.DB); err != nil {
		log.Printf("Error seeding vocabulary: %v", err)
	}

	// Seed users
	users := seedUsers()

	// Seed exercises
	grammarExercises := seedGrammarExercises()
	listeningExercises := seedListeningExercises()
	speakingExercises := seedSpeakingExercises()

	// Seed lessons
	lessons := seedLessons()

	// Seed achievements
	achievements := seedAchievements()

	// Seed words
	words := seedWords()

	// Seed evaluations
	evaluations := SeedEvaluations()

	// Seed user progress
	seedUserProgress(users, grammarExercises, listeningExercises, speakingExercises, lessons, achievements, words)

	// Assign evaluations to the first user
	assignEvaluationsToUser(users[0], evaluations)

	// Seed user languages
	seedUserLanguages(users, languages)

	// 新增：批量分配 lesson-exercise 关系
	seedLessonExercises(lessons, grammarExercises, listeningExercises, speakingExercises)

	log.Println("Data seeding completed!")
}

// seedUsers creates sample users
func seedUsers() []models.User {
	// Check if users already exist
	var count int64
	models.DB.Model(&models.User{}).Count(&count)
	if count > 0 {
		log.Println("Users already exist, skipping user seed")
		var users []models.User
		models.DB.Find(&users)
		return users
	}

	// Create sample users
	users := []models.User{
		{
			Username:    "testuser",
			Email:       "<EMAIL>",
			Password:    "password123",
			AvatarURL:   "https://api.languagelearningapp.com/avatars/default.png",
			CreatedAt:   time.Now().AddDate(0, -1, 0),
			LastLoginAt: time.Now().AddDate(0, 0, -1),
			IsActive:    true,
			Stats: &models.UserStats{
				CurrentStreak:       3,
				VocabularyCount:     50,
				ListeningCount:      30,
				SpeakingCount:       20,
				TotalPoints:         500,
				ChallengesCompleted: 3,
				HelpedUsers:         5,
				LastActive:          time.Now().AddDate(0, 0, -1),
				UpdatedAt:           time.Now(),
			},
		},
		{
			Username:    "beginner",
			Email:       "<EMAIL>",
			Password:    "password123",
			AvatarURL:   "https://api.languagelearningapp.com/avatars/default.png",
			CreatedAt:   time.Now().AddDate(0, 0, -7),
			LastLoginAt: time.Now().AddDate(0, 0, -1),
			IsActive:    true,
			Stats: &models.UserStats{
				CurrentStreak:       1,
				VocabularyCount:     10,
				ListeningCount:      5,
				SpeakingCount:       3,
				TotalPoints:         100,
				ChallengesCompleted: 0,
				HelpedUsers:         0,
				LastActive:          time.Now().AddDate(0, 0, -1),
				UpdatedAt:           time.Now(),
			},
		},
		{
			Username:    "advanced",
			Email:       "<EMAIL>",
			Password:    "password123",
			AvatarURL:   "https://api.languagelearningapp.com/avatars/default.png",
			CreatedAt:   time.Now().AddDate(0, -3, 0),
			LastLoginAt: time.Now(),
			IsActive:    true,
			Stats: &models.UserStats{
				CurrentStreak:       30,
				VocabularyCount:     200,
				ListeningCount:      100,
				SpeakingCount:       80,
				TotalPoints:         2000,
				ChallengesCompleted: 10,
				HelpedUsers:         15,
				LastActive:          time.Now(),
				UpdatedAt:           time.Now(),
			},
		},
	}

	// Hash passwords
	for i := range users {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(users[i].Password), bcrypt.DefaultCost)
		if err != nil {
			log.Fatalf("Failed to hash password: %v", err)
		}
		users[i].Password = string(hashedPassword)
	}

	// Save users
	for i := range users {
		if err := models.DB.Create(&users[i]).Error; err != nil {
			log.Fatalf("Failed to seed user: %v", err)
		}

		// Create user settings
		settings := models.UserSettings{
			UserID:               users[i].ID,
			NotificationsEnabled: true,
			DarkModeEnabled:      false,
			DailyGoal:            3,
			PreferredLanguage:    "zh-CN",
		}
		if err := models.DB.Create(&settings).Error; err != nil {
			log.Fatalf("Failed to seed user settings: %v", err)
		}
	}

	log.Printf("Seeded %d users", len(users))
	return users
}

// seedGrammarExercises creates sample grammar exercises
func seedGrammarExercises() []models.GrammarExercise {
	// Use the enhanced implementation
	return seedEnhancedGrammarExercises()
}

// seedListeningExercises creates sample listening exercises
func seedListeningExercises() []models.ListeningExercise {
	// Use the enhanced implementation
	return seedEnhancedListeningExercises()
}

// seedSpeakingExercises creates sample speaking exercises
func seedSpeakingExercises() []models.SpeakingExercise {
	// Use the enhanced implementation
	return seedEnhancedSpeakingExercises()
}

// seedLessons creates sample lessons
func seedLessons() []models.Lesson {
	// Check if lessons already exist
	var count int64
	models.DB.Model(&models.Lesson{}).Count(&count)
	if count > 0 {
		log.Println("Lessons already exist, skipping lesson seed")
		var lessons []models.Lesson
		models.DB.Find(&lessons)
		return lessons
	}

	// Create sample lessons
	lessons := []models.Lesson{
		{
			ID:          uuid.New(),
			Title:       "基础问候语",
			Description: "学习基本的问候和自我介绍",
			Category:    models.LessonVocabulary,
			Level:       models.LessonBeginner,
			Difficulty:  models.Easy,
			Duration:    15,
			Points:      100,
			Tags:        pq.StringArray{"beginner", "greetings", "basic"},
			CreatedAt:   time.Now().AddDate(0, -2, 0),
			UpdatedAt:   time.Now().AddDate(0, -2, 0),
		},
		{
			ID:          uuid.New(),
			Title:       "数字学习",
			Description: "学习中文数字1-10",
			Category:    models.LessonVocabulary,
			Level:       models.LessonBeginner,
			Difficulty:  models.Easy,
			Duration:    20,
			Points:      150,
			Tags:        pq.StringArray{"beginner", "numbers", "basic"},
			CreatedAt:   time.Now().AddDate(0, -2, 0),
			UpdatedAt:   time.Now().AddDate(0, -2, 0),
		},
		{
			ID:          uuid.New(),
			Title:       "日常对话",
			Description: "学习常见的日常对话场景",
			Category:    models.LessonSpeaking,
			Level:       models.LessonIntermediate,
			Difficulty:  models.Medium,
			Duration:    25,
			Points:      200,
			Tags:        pq.StringArray{"intermediate", "conversation", "dining"},
			CreatedAt:   time.Now().AddDate(0, -1, 0),
			UpdatedAt:   time.Now().AddDate(0, -1, 0),
		},
		{
			ID:          uuid.New(),
			Title:       "商务谈判",
			Description: "学习商务谈判中的专业术语和表达方式",
			Category:    models.LessonSpeaking,
			Level:       models.LessonAdvanced,
			Difficulty:  models.Hard,
			Duration:    40,
			Points:      300,
			Tags:        pq.StringArray{"advanced", "business", "negotiation"},
			CreatedAt:   time.Now().AddDate(0, 0, -15),
			UpdatedAt:   time.Now().AddDate(0, 0, -15),
		},
		{
			ID:          uuid.New(),
			Title:       "商务邮件写作",
			Description: "学习如何撰写专业的商务邮件",
			Category:    models.LessonWriting,
			Level:       models.LessonIntermediate,
			Difficulty:  models.Medium,
			Duration:    30,
			Points:      250,
			Tags:        pq.StringArray{"business", "email", "writing"},
			CreatedAt:   time.Now().AddDate(0, 0, -10),
			UpdatedAt:   time.Now().AddDate(0, 0, -10),
		},
	}

	// Save lessons
	for i := range lessons {
		if err := models.DB.Create(&lessons[i]).Error; err != nil {
			log.Fatalf("Failed to seed lesson: %v", err)
		}
	}

	log.Printf("Seeded %d lessons", len(lessons))
	return lessons
}

// seedAchievements creates sample achievements
func seedAchievements() []models.Achievement {
	// Check if achievements already exist
	var count int64
	models.DB.Model(&models.Achievement{}).Count(&count)
	if count > 0 {
		log.Println("Achievements already exist, skipping achievement seed")
		var achievements []models.Achievement
		models.DB.Find(&achievements)
		return achievements
	}

	// Create sample achievements
	achievements := []models.Achievement{
		{
			ID:          uuid.New(),
			Type:        models.AchStreak,
			Title:       "连续学习3天",
			Description: "连续学习3天不间断",
			Icon:        "flame.fill",
			Color:       "FF9500",
			Requirement: 3,
			Reward:      100,
		},
		{
			ID:          uuid.New(),
			Type:        models.AchVocabulary,
			Title:       "词汇达人",
			Description: "掌握100个单词",
			Icon:        "textformat.abc",
			Color:       "007AFF",
			Requirement: 100,
			Reward:      200,
		},
		{
			ID:          uuid.New(),
			Type:        models.AchListening,
			Title:       "听力高手",
			Description: "完成50个听力练习",
			Icon:        "ear.fill",
			Color:       "34C759",
			Requirement: 50,
			Reward:      150,
		},
		{
			ID:          uuid.New(),
			Type:        models.AchSpeaking,
			Title:       "口语达人",
			Description: "完成30个口语练习",
			Icon:        "mic.fill",
			Color:       "FF3B30",
			Requirement: 30,
			Reward:      150,
		},
		{
			ID:          uuid.New(),
			Type:        models.AchLessons,
			Title:       "课程完成者",
			Description: "完成10个课程",
			Icon:        "book.fill",
			Color:       "AF52DE",
			Requirement: 10,
			Reward:      300,
		},
	}

	// Save achievements
	for i := range achievements {
		if err := models.DB.Create(&achievements[i]).Error; err != nil {
			log.Fatalf("Failed to seed achievement: %v", err)
		}
	}

	log.Printf("Seeded %d achievements", len(achievements))
	return achievements
}

// seedWords creates sample words
func seedWords() []models.Word {
	// Use the enhanced implementation
	return seedEnhancedWords()
}

// assignEvaluationsToUser assigns evaluations to a user
func assignEvaluationsToUser(user models.User, evaluations []models.Evaluation) {
	// Check if user already has evaluations
	var count int64
	models.DB.Model(&models.Evaluation{}).Where("user_id = ?", user.ID).Count(&count)
	if count > 0 {
		log.Println("User already has evaluations, skipping evaluation assignment")
		return
	}

	// Assign evaluations to user
	for _, evaluation := range evaluations {
		// Create a copy of the evaluation for the user
		userEvaluation := models.Evaluation{
			ID:             uuid.New(),
			UserID:         user.ID,
			Type:           evaluation.Type,
			Title:          evaluation.Title,
			Description:    evaluation.Description,
			TotalQuestions: evaluation.TotalQuestions,
			PassingScore:   evaluation.PassingScore,
			Duration:       evaluation.Duration,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}

		// Create sections and questions
		var sections []models.EvalSection
		for _, section := range evaluation.Sections {
			userSection := models.EvalSection{
				Title:  section.Title,
				Skill:  section.Skill,
				Weight: section.Weight,
			}

			// Create questions
			var questions []models.EvalQuestion
			for _, question := range section.Questions {
				userQuestion := models.EvalQuestion{
					Type:          question.Type,
					Content:       question.Content,
					Options:       question.Options,
					CorrectAnswer: question.CorrectAnswer,
					Points:        question.Points,
				}
				questions = append(questions, userQuestion)
			}

			userSection.Questions = questions
			sections = append(sections, userSection)
		}

		userEvaluation.Sections = sections

		// Save the user evaluation
		if err := models.DB.Create(&userEvaluation).Error; err != nil {
			log.Printf("Error assigning evaluation to user: %v", err)
		}
	}

	log.Printf("Assigned %d evaluations to user %s", len(evaluations), user.Username)
}

// seedUserProgress creates sample user progress
func seedUserProgress(users []models.User, grammarExercises []models.GrammarExercise, listeningExercises []models.ListeningExercise, speakingExercises []models.SpeakingExercise, lessons []models.Lesson, achievements []models.Achievement, words []models.Word) {
	// Create progress for the first user
	user := users[0]

	// Lesson progress
	for i, lesson := range lessons {
		if i < 2 { // Complete first two lessons
			progress := models.LessonProgress{
				UserID:        user.ID,
				LessonID:      lesson.ID,
				CurrentScore:  85,
				Completed:     true,
				CompletedDate: time.Now().AddDate(0, 0, -i-1),
				Favorited:     i == 0, // Favorite the first lesson
			}
			if err := models.DB.Create(&progress).Error; err != nil {
				log.Fatalf("Failed to seed lesson progress: %v", err)
			}

			// Seed UserLessonExercise for completed lessons
			// Assuming each lesson has a couple of exercises, and we'll mark some as completed.
			// For simplicity, we'll use placeholder ExerciseIDs. In a real scenario, these would be actual exercise IDs.
			if progress.Completed {
				exerciseIDsToSeed := []uuid.UUID{uuid.New(), uuid.New()} // Example exercise IDs
				for _, exID := range exerciseIDsToSeed {
					userLessonExercise := models.UserLessonExercise{
						UserID:        user.ID,
						LessonID:      lesson.ID,
						ExerciseID:    exID,
						IsCompleted:   true,
						Score:         90, // Example score
						CompletedDate: time.Now().AddDate(0, 0, -i-1),
						CreatedAt:     time.Now().AddDate(0, 0, -i-1),
						UpdatedAt:     time.Now().AddDate(0, 0, -i-1),
					}
					if err := models.DB.Create(&userLessonExercise).Error; err != nil {
						log.Fatalf("Failed to seed user lesson exercise: %v", err)
					}
				}
			}

		} else { // In progress for other lessons
			progress := models.LessonProgress{
				UserID:       user.ID,
				LessonID:     lesson.ID,
				CurrentScore: 50,
				Completed:    false,
				Favorited:    false,
			}
			if err := models.DB.Create(&progress).Error; err != nil {
				log.Fatalf("Failed to seed lesson progress: %v", err)
			}
			// Optionally, seed one UserLessonExercise as completed for an in-progress lesson
			userLessonExercise := models.UserLessonExercise{
				UserID:        user.ID,
				LessonID:      lesson.ID,
				ExerciseID:    uuid.New(), // Example exercise ID
				IsCompleted:   true,       // User completed one exercise but not the whole lesson
				Score:         80,
				CompletedDate: time.Now().AddDate(0, 0, -i-1),
				CreatedAt:     time.Now().AddDate(0, 0, -i-1),
				UpdatedAt:     time.Now().AddDate(0, 0, -i-1),
			}
			if err := models.DB.Create(&userLessonExercise).Error; err != nil {
				log.Fatalf("Failed to seed user lesson exercise for in-progress lesson: %v", err)
			}
		}
	}

	// User achievements
	for i, achievement := range achievements {
		progress := i * 30  // Vary progress
		isUnlocked := i < 2 // First two achievements unlocked
		userAchievement := models.UserAchievement{
			ID:              uuid.New(),
			UserID:          user.ID,
			AchievementID:   achievement.ID,
			AchievementType: achievement.Type,
			Progress:        progress,
			IsUnlocked:      isUnlocked,
			RewardClaimed:   i == 0, // First achievement reward claimed
			Reward:          achievement.Reward,
		}
		if isUnlocked {
			userAchievement.UnlockedDate = time.Now().AddDate(0, 0, -i-1)
		}
		if err := models.DB.Create(&userAchievement).Error; err != nil {
			log.Fatalf("Failed to seed user achievement: %v", err)
		}
	}

	// User words
	for i, word := range words {
		isLearned := i < 3 // First three words learned
		userWord := models.UserWord{
			UserID:     user.ID,
			WordID:     word.ID,
			IsLearned:  isLearned,
			IsFavorite: i == 0, // First word favorited
		}
		if err := models.DB.Create(&userWord).Error; err != nil {
			log.Fatalf("Failed to seed user word: %v", err)
		}
	}

	// Practice sessions
	for i := 0; i < 10; i++ {
		types := []string{"grammar", "listening", "speaking", "vocabulary"}
		session := models.PracticeSession{
			UserID:    user.ID,
			Type:      types[i%4],
			Duration:  (i + 1) * 60, // Vary duration
			Score:     70 + i%30,    // Vary score
			CreatedAt: time.Now().AddDate(0, 0, -i),
		}
		if err := models.DB.Create(&session).Error; err != nil {
			log.Fatalf("Failed to seed practice session: %v", err)
		}
	}

	log.Println("Seeded user progress")
}

// seedUserLanguages creates sample user languages
func seedUserLanguages(users []models.User, languages []models.Language) {
	// Check if user languages already exist
	var count int64
	models.DB.Model(&models.UserLanguage{}).Count(&count)
	if count > 0 {
		log.Println("User languages already exist, skipping user language seed")
		return
	}

	// Create user languages for each user
	for _, user := range users {
		// Assign English as a learning language for all users
		var englishLanguage models.Language
		for _, lang := range languages {
			if lang.Code == "en" {
				englishLanguage = lang
				break
			}
		}

		if englishLanguage.ID != uuid.Nil {
			userLanguage := models.UserLanguage{
				ID:         uuid.New(),
				UserID:     user.ID,
				LanguageID: englishLanguage.ID,
				Level:      models.Beginner,
				IsLearning: true,
				IsNative:   false,
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			}
			if err := models.DB.Create(&userLanguage).Error; err != nil {
				log.Printf("Error creating user language: %v", err)
			}
		}

		// Assign Chinese as a native language for all users
		var chineseLanguage models.Language
		for _, lang := range languages {
			if lang.Code == "zh" {
				chineseLanguage = lang
				break
			}
		}

		if chineseLanguage.ID != uuid.Nil {
			userLanguage := models.UserLanguage{
				ID:         uuid.New(),
				UserID:     user.ID,
				LanguageID: chineseLanguage.ID,
				Level:      models.Native,
				IsLearning: false,
				IsNative:   true,
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			}
			if err := models.DB.Create(&userLanguage).Error; err != nil {
				log.Printf("Error creating user language: %v", err)
			}
		}
	}

	log.Println("Seeded user languages")
}

// seedLessonExercises 批量为每个 lesson 分配一批练习
func seedLessonExercises(lessons []models.Lesson, grammarExercises []models.GrammarExercise, listeningExercises []models.ListeningExercise, speakingExercises []models.SpeakingExercise) {
	// 这里只做简单分配：按 lesson 的 category 分配对应类型的练习
	for _, lesson := range lessons {
		switch lesson.Category {
		case models.LessonGrammar:
			for _, ex := range grammarExercises {
				le := models.LessonExercise{
					LessonID:   lesson.ID,
					ExerciseID: ex.ID,
				}
				models.DB.Create(&le)
			}
		case models.LessonListening:
			for _, ex := range listeningExercises {
				le := models.LessonExercise{
					LessonID:   lesson.ID,
					ExerciseID: ex.ID,
				}
				models.DB.Create(&le)
			}
		case models.LessonSpeaking:
			for _, ex := range speakingExercises {
				le := models.LessonExercise{
					LessonID:   lesson.ID,
					ExerciseID: ex.ID,
				}
				models.DB.Create(&le)
			}
			// 其他类型可按需扩展
		}
	}
}

// SeedBasicEnglishVocabulary 生成基础的英文常用词汇练习和课程
func SeedBasicEnglishVocabulary(db *gorm.DB) error {
	log.Println("Seeding basic English vocabulary exercises and lessons...")

	// Get English language
	var englishLanguage models.Language
	if err := db.Where("code = ?", "en").First(&englishLanguage).Error; err != nil {
		log.Printf("Error finding English language: %v", err)
		return err
	}

	// 创建基础词汇练习 using helper functions
	exercises := []models.GrammarExercise{
		helpers.NewGrammarExercise(
			"Common Greetings",
			"What does 'Hello' mean?",
			"A greeting",
			"Hello is a common greeting used to say hi.",
			"Vocabulary",
			"Match the greeting with its meaning.",
			"Hello, how are you?",
			[]string{"A greeting", "A farewell", "A question", "A statement"},
			models.Easy,
			englishLanguage.ID,
		),
		helpers.NewGrammarExercise(
			"Numbers 1-10",
			"What comes after 5?",
			"6",
			"The number after 5 is 6.",
			"Vocabulary",
			"Fill in the missing number.",
			"1, 2, 3, 4, 5, 6",
			[]string{"4", "6", "7", "8"},
			models.Easy,
			englishLanguage.ID,
		),
		helpers.NewGrammarExercise(
			"Colors",
			"What color is the sky on a clear day?",
			"Blue",
			"The sky is blue on a clear day.",
			"Vocabulary",
			"Identify the color.",
			"The sky is blue.",
			[]string{"Red", "Blue", "Green", "Yellow"},
			models.Easy,
			englishLanguage.ID,
		),
	}

	// 创建基础词汇课程
	lessons := []models.Lesson{
		{
			ID:          uuid.New(),
			Title:       "Basic Greetings",
			Description: "Learn common greetings in English.",
			Content:     "Learn common greetings and introductions in English.",
			Language:    "English",
			Level:       models.LessonBeginner,
			Category:    models.LessonVocabulary,
			Difficulty:  models.Easy,
			Duration:    15,
			Points:      10,
			Status:      "published",
		},
		{
			ID:          uuid.New(),
			Title:       "Numbers and Colors",
			Description: "Learn numbers and colors in English.",
			Content:     "Learn basic numbers and colors in English.",
			Language:    "English",
			Level:       models.LessonBeginner,
			Category:    models.LessonVocabulary,
			Difficulty:  models.Easy,
			Duration:    20,
			Points:      15,
			Status:      "published",
		},
	}

	// 保存练习和课程到数据库
	for _, exercise := range exercises {
		if err := db.Create(&exercise).Error; err != nil {
			return err
		}
	}

	for _, lesson := range lessons {
		if err := db.Create(&lesson).Error; err != nil {
			return err
		}
	}

	log.Println("Basic English vocabulary exercises and lessons seeded successfully.")
	return nil
}
