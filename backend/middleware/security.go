package middleware

import (
	"github.com/gin-gonic/gin"
)

// SecurityHeaders 添加安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 防止XSS攻击
		c.Header("X-Content-Type-Options", "nosniff")
		c.<PERSON><PERSON>("X-Frame-Options", "DENY")
		c.<PERSON><PERSON>("X-XSS-Protection", "1; mode=block")
		
		// HSTS (仅在HTTPS时)
		if c.Request.TLS != nil {
			c.<PERSON>er("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}
		
		// CSP (根据需要调整)
		c.<PERSON><PERSON>("Content-Security-Policy", "default-src 'self'")
		
		c.Next()
	}
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*") // 生产环境应该限制具体域名
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	}
}
