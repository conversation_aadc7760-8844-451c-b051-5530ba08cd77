package middleware

import (
	"context"
	"languagelearning/utils/logger"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LoggingMiddleware 为每个请求添加日志记录器和跟踪ID
func LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成跟踪ID
		traceID := uuid.New().String()

		// 创建带有跟踪ID的上下文
		ctx := context.WithValue(c.Request.Context(), "traceID", traceID)

		// 创建日志记录器并添加到上下文
		log := logger.DefaultLogger()
		ctx = context.WithValue(ctx, "logger", log)

		// 更新请求上下文
		c.Request = c.Request.WithContext(ctx)

		// 设置跟踪ID响应头
		c.Header("X-Trace-ID", traceID)

		// 记录请求开始
		startTime := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()
		userAgent := c.Request.UserAgent()

		fields := logger.WithFields(map[string]interface{}{
			"method":    method,
			"path":      path,
			"clientIP":  clientIP,
			"userAgent": userAgent,
			"requestId": traceID,
		})

		log.Info(ctx, "Request received", fields)

		// 处理请求
		c.Next()

		// 计算请求处理时间
		latency := time.Since(startTime)
		statusCode := c.Writer.Status()

		fields["statusCode"] = statusCode
		fields["latency"] = latency.String()

		// 根据状态码记录不同级别的日志
		if statusCode >= 500 {
			log.Error(ctx, "Request failed", fields)
		} else if statusCode >= 400 {
			log.Warn(ctx, "Request failed", fields)
		} else {
			log.Info(ctx, "Request completed", fields)
		}
	}
}

// GetLogger 从Gin上下文中获取日志记录器
func GetLogger(c *gin.Context) *logger.Logger {
	ctx := c.Request.Context()
	return logger.DefaultLoggerWithContext(ctx)
}

// GetTraceID 从Gin上下文中获取跟踪ID
func GetTraceID(c *gin.Context) string {
	ctx := c.Request.Context()
	if traceID, ok := ctx.Value("traceID").(string); ok {
		return traceID
	}
	return ""
}
