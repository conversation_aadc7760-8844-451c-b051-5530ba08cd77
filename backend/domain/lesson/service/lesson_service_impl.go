package service

import (
	"context"

	"languagelearning/domain/core"
	"languagelearning/domain/learning/entity"
	"languagelearning/domain/lesson/repository"

	"github.com/google/uuid"
)

// lessonServiceImpl implements LessonService
type lessonServiceImpl struct {
	repo repository.LessonRepository
}

// NewLessonService creates a new lesson service
func NewLessonService(repo repository.LessonRepository) LessonService {
	return &lessonServiceImpl{
		repo: repo,
	}
}

// CreateLesson implements LessonService
func (s *lessonServiceImpl) CreateLesson(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error) {
	return s.repo.Create(ctx, lesson)
}

// UpdateLesson implements LessonService
func (s *lessonServiceImpl) UpdateLesson(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error) {
	return s.repo.Update(ctx, lesson)
}

// DeleteLesson implements LessonService
func (s *lessonServiceImpl) DeleteLesson(ctx context.Context, id uuid.UUID) error {
	return s.repo.Delete(ctx, id)
}

// GetLessonByID implements LessonService
func (s *lessonServiceImpl) GetLessonByID(ctx context.Context, id uuid.UUID) (*entity.Lesson, error) {
	return s.repo.GetByID(ctx, id)
}

// GetLessons implements LessonService
func (s *lessonServiceImpl) GetLessons(ctx context.Context, pageable core.Pageable, filters repository.LessonFilters) (*core.Page[entity.Lesson], error) {
	return s.repo.GetLessons(ctx, pageable, filters)
}

// SearchLessons implements LessonService
func (s *lessonServiceImpl) SearchLessons(ctx context.Context, query string, pageable core.Pageable) (*core.Page[entity.Lesson], error) {
	return s.repo.SearchLessons(ctx, query, pageable)
}

// GetLessonsByLanguage implements LessonService
func (s *lessonServiceImpl) GetLessonsByLanguage(ctx context.Context, language string) ([]*entity.Lesson, error) {
	return s.repo.GetLessonsByLanguage(ctx, language)
}

// GetLessonsByLevel implements LessonService
func (s *lessonServiceImpl) GetLessonsByLevel(ctx context.Context, level entity.LessonLevel) ([]*entity.Lesson, error) {
	return s.repo.GetLessonsByLevel(ctx, level)
}

// GetLessonsByCategory implements LessonService
func (s *lessonServiceImpl) GetLessonsByCategory(ctx context.Context, category entity.LessonCategory) ([]*entity.Lesson, error) {
	return s.repo.GetLessonsByCategory(ctx, category)
}

// GetRelatedLessons implements LessonService
func (s *lessonServiceImpl) GetRelatedLessons(ctx context.Context, lessonID uuid.UUID) ([]*entity.Lesson, error) {
	return s.repo.GetRelatedLessons(ctx, lessonID)
}

// GetFavoriteLessons implements LessonService
func (s *lessonServiceImpl) GetFavoriteLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error) {
	return s.repo.GetFavoriteLessons(ctx, userID)
}

// ToggleFavoriteLesson implements LessonService
func (s *lessonServiceImpl) ToggleFavoriteLesson(ctx context.Context, userID, lessonID uuid.UUID, isFavorite bool) error {
	return s.repo.ToggleFavorite(ctx, userID, lessonID, isFavorite)
}

// GetUserLessonProgress implements LessonService
func (s *lessonServiceImpl) GetUserLessonProgress(ctx context.Context, userID, lessonID uuid.UUID) (*entity.LessonProgress, error) {
	return s.repo.GetUserProgress(ctx, userID, lessonID)
}

// UpdateLessonProgress implements LessonService
func (s *lessonServiceImpl) UpdateLessonProgress(ctx context.Context, progress *entity.LessonProgress) error {
	return s.repo.UpdateUserProgress(ctx, progress)
}

// GetUserCompletedLessons implements LessonService
func (s *lessonServiceImpl) GetUserCompletedLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error) {
	return s.repo.GetUserCompletedLessons(ctx, userID)
}

// GetUserInProgressLessons implements LessonService
func (s *lessonServiceImpl) GetUserInProgressLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error) {
	return s.repo.GetUserInProgressLessons(ctx, userID)
}

// CompleteLessonProgress implements LessonService
func (s *lessonServiceImpl) CompleteLessonProgress(ctx context.Context, userID, lessonID uuid.UUID) error {
	progress, err := s.repo.GetUserProgress(ctx, userID, lessonID)
	if err != nil {
		return err
	}

	progress.Complete()
	return s.repo.UpdateUserProgress(ctx, progress)
}

// PublishLesson implements LessonService
func (s *lessonServiceImpl) PublishLesson(ctx context.Context, lessonID uuid.UUID) error {
	return s.repo.Publish(ctx, lessonID)
}

// UnpublishLesson implements LessonService
func (s *lessonServiceImpl) UnpublishLesson(ctx context.Context, lessonID uuid.UUID) error {
	return s.repo.Unpublish(ctx, lessonID)
}
