package service

import (
	"context"

	"languagelearning/domain/core"
	"languagelearning/domain/learning/entity"
	"languagelearning/domain/lesson/repository"

	"github.com/google/uuid"
)

// LessonService defines the interface for lesson business logic
type LessonService interface {
	// Basic CRUD operations
	CreateLesson(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error)
	UpdateLesson(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error)
	DeleteLesson(ctx context.Context, id uuid.UUID) error
	GetLessonByID(ctx context.Context, id uuid.UUID) (*entity.Lesson, error)

	// Query operations
	GetLessons(ctx context.Context, pageable core.Pageable, filters repository.LessonFilters) (*core.Page[entity.Lesson], error)
	SearchLessons(ctx context.Context, query string, pageable core.Pageable) (*core.Page[entity.Lesson], error)
	GetLessonsByLanguage(ctx context.Context, language string) ([]*entity.Lesson, error)
	GetLessonsByLevel(ctx context.Context, level entity.LessonLevel) ([]*entity.Lesson, error)
	GetLessonsByCategory(ctx context.Context, category entity.LessonCategory) ([]*entity.Lesson, error)
	GetRelatedLessons(ctx context.Context, lessonID uuid.UUID) ([]*entity.Lesson, error)

	// User-related operations
	GetFavoriteLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error)
	ToggleFavoriteLesson(ctx context.Context, userID, lessonID uuid.UUID, isFavorite bool) error
	GetUserLessonProgress(ctx context.Context, userID, lessonID uuid.UUID) (*entity.LessonProgress, error)
	UpdateLessonProgress(ctx context.Context, progress *entity.LessonProgress) error
	GetUserCompletedLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error)
	GetUserInProgressLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error)
	CompleteLessonProgress(ctx context.Context, userID, lessonID uuid.UUID) error

	// Publishing operations
	PublishLesson(ctx context.Context, lessonID uuid.UUID) error
	UnpublishLesson(ctx context.Context, lessonID uuid.UUID) error
}
