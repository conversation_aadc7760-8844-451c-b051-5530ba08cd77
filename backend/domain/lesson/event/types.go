package event

import (
	"time"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/learning/entity"

	"github.com/google/uuid"
)

// LessonEvent 課程領域事件接口
type LessonEvent interface {
	coreevent.Event
	// LessonEventType 返回課程事件類型
	LessonEventType() string
}

// LessonEventHandler 課程領域事件處理器接口
type LessonEventHandler interface {
	coreevent.EventHandler
	// HandleLessonEvent 處理課程領域事件
	HandleLessonEvent(event LessonEvent) error
}

// BaseLessonEvent 課程領域基礎事件實現
type BaseLessonEvent struct {
	*coreevent.BaseEvent
	LessonType string `json:"lessonType"`
}

// NewBaseLessonEvent 創建課程領域基礎事件
func NewBaseLessonEvent(eventType string, aggregateID uuid.UUID, aggregateType string, lessonType string, data interface{}) *BaseLessonEvent {
	return &BaseLessonEvent{
		BaseEvent:  coreevent.NewBaseEvent(eventType, aggregateID, aggregateType, data),
		LessonType: lessonType,
	}
}

// LessonEventType 實現 LessonEvent 接口
func (e *BaseLessonEvent) LessonEventType() string {
	return e.LessonType
}

// LessonCompletedEvent 課程完成事件
type LessonCompletedEvent struct {
	*coreevent.BaseEvent
	LessonID     uuid.UUID
	UserID       uuid.UUID
	Score        *entity.Score
	Duration     *entity.Duration
	CompletedAt  time.Time
	MasteryLevel entity.MasteryLevel
}

// NewLessonCompletedEvent 創建課程完成事件
func NewLessonCompletedEvent(lessonID, userID uuid.UUID, score *entity.Score, duration *entity.Duration, masteryLevel entity.MasteryLevel) *LessonCompletedEvent {
	now := time.Now()
	return &LessonCompletedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.completed",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":     lessonID,
				"userID":       userID,
				"score":        score,
				"duration":     duration,
				"completedAt":  now,
				"masteryLevel": masteryLevel,
			},
		),
		LessonID:     lessonID,
		UserID:       userID,
		Score:        score,
		Duration:     duration,
		CompletedAt:  now,
		MasteryLevel: masteryLevel,
	}
}

// LessonStartedEvent 課程開始事件
type LessonStartedEvent struct {
	*coreevent.BaseEvent
	LessonID   uuid.UUID
	UserID     uuid.UUID
	StartedAt  time.Time
	Difficulty *entity.ExerciseDifficulty
}

// NewLessonStartedEvent 創建課程開始事件
func NewLessonStartedEvent(lessonID, userID uuid.UUID, difficulty *entity.ExerciseDifficulty) *LessonStartedEvent {
	now := time.Now()
	return &LessonStartedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.started",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":   lessonID,
				"userID":     userID,
				"startedAt":  now,
				"difficulty": difficulty,
			},
		),
		LessonID:   lessonID,
		UserID:     userID,
		StartedAt:  now,
		Difficulty: difficulty,
	}
}

// LessonEvaluatedEvent 課程評估事件
type LessonEvaluatedEvent struct {
	*coreevent.BaseEvent
	LessonID    uuid.UUID
	UserID      uuid.UUID
	EvaluatedAt time.Time
	Score       *entity.Score
	Feedback    string
}

// NewLessonEvaluatedEvent 創建課程評估事件
func NewLessonEvaluatedEvent(lessonID, userID uuid.UUID, score *entity.Score, feedback string) *LessonEvaluatedEvent {
	now := time.Now()
	return &LessonEvaluatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.evaluated",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":    lessonID,
				"userID":      userID,
				"evaluatedAt": now,
				"score":       score,
				"feedback":    feedback,
			},
		),
		LessonID:    lessonID,
		UserID:      userID,
		EvaluatedAt: now,
		Score:       score,
		Feedback:    feedback,
	}
}
