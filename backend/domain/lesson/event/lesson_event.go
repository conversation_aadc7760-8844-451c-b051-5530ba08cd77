package event

import (
	"time"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/learning/entity"

	"github.com/google/uuid"
)

// LessonCreatedEvent is emitted when a lesson is created
type LessonCreatedEvent struct {
	*coreevent.BaseEvent
	LessonID   uuid.UUID
	Title      string
	CreatedAt  time.Time
	CreatedBy  uuid.UUID
	Language   string
	Level      string
	Category   string
	Difficulty string
}

// NewLessonCreatedEvent creates a new lesson created event
func NewLessonCreatedEvent(lessonID, createdBy uuid.UUID, title, language, level, category, difficulty string) *LessonCreatedEvent {
	now := time.Now()
	return &LessonCreatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.created",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":   lessonID,
				"title":      title,
				"createdAt":  now,
				"createdBy":  createdBy,
				"language":   language,
				"level":      level,
				"category":   category,
				"difficulty": difficulty,
			},
		),
		LessonID:   lessonID,
		Title:      title,
		CreatedAt:  now,
		CreatedBy:  createdBy,
		Language:   language,
		Level:      level,
		Category:   category,
		Difficulty: difficulty,
	}
}

// LessonUpdatedEvent is emitted when a lesson is updated
type LessonUpdatedEvent struct {
	*coreevent.BaseEvent
	LessonID   uuid.UUID
	Title      string
	UpdatedAt  time.Time
	UpdatedBy  uuid.UUID
	Language   string
	Level      string
	Category   string
	Difficulty string
}

// NewLessonUpdatedEvent creates a new lesson updated event
func NewLessonUpdatedEvent(lessonID, updatedBy uuid.UUID, title, language, level, category, difficulty string) *LessonUpdatedEvent {
	now := time.Now()
	return &LessonUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.updated",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":   lessonID,
				"title":      title,
				"updatedAt":  now,
				"updatedBy":  updatedBy,
				"language":   language,
				"level":      level,
				"category":   category,
				"difficulty": difficulty,
			},
		),
		LessonID:   lessonID,
		Title:      title,
		UpdatedAt:  now,
		UpdatedBy:  updatedBy,
		Language:   language,
		Level:      level,
		Category:   category,
		Difficulty: difficulty,
	}
}

// LessonPublishedEvent is emitted when a lesson is published
type LessonPublishedEvent struct {
	*coreevent.BaseEvent
	LessonID    uuid.UUID
	Title       string
	PublishedAt time.Time
	PublishedBy uuid.UUID
	Language    string
	Level       string
	Category    string
	Difficulty  string
}

// NewLessonPublishedEvent creates a new lesson published event
func NewLessonPublishedEvent(lessonID, publishedBy uuid.UUID, title, language, level, category, difficulty string) *LessonPublishedEvent {
	now := time.Now()
	return &LessonPublishedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.published",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":    lessonID,
				"title":       title,
				"publishedAt": now,
				"publishedBy": publishedBy,
				"language":    language,
				"level":       level,
				"category":    category,
				"difficulty":  difficulty,
			},
		),
		LessonID:    lessonID,
		Title:       title,
		PublishedAt: now,
		PublishedBy: publishedBy,
		Language:    language,
		Level:       level,
		Category:    category,
		Difficulty:  difficulty,
	}
}

// LessonUnpublishedEvent is emitted when a lesson is unpublished
type LessonUnpublishedEvent struct {
	*coreevent.BaseEvent
	LessonID      uuid.UUID
	Title         string
	UnpublishedAt time.Time
	UnpublishedBy uuid.UUID
	Language      string
	Level         string
	Category      string
	Difficulty    string
}

// NewLessonUnpublishedEvent creates a new lesson unpublished event
func NewLessonUnpublishedEvent(lessonID, unpublishedBy uuid.UUID, title, language, level, category, difficulty string) *LessonUnpublishedEvent {
	now := time.Now()
	return &LessonUnpublishedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.unpublished",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":      lessonID,
				"title":         title,
				"unpublishedAt": now,
				"unpublishedBy": unpublishedBy,
				"language":      language,
				"level":         level,
				"category":      category,
				"difficulty":    difficulty,
			},
		),
		LessonID:      lessonID,
		Title:         title,
		UnpublishedAt: now,
		UnpublishedBy: unpublishedBy,
		Language:      language,
		Level:         level,
		Category:      category,
		Difficulty:    difficulty,
	}
}

// LessonProgressUpdatedEvent is emitted when a user's lesson progress is updated
type LessonProgressUpdatedEvent struct {
	*coreevent.BaseEvent
	LessonID   uuid.UUID
	UserID     uuid.UUID
	UpdatedAt  time.Time
	Progress   int
	Score      *entity.Score
	Language   string
	Level      string
	Category   string
	Difficulty string
}

// NewLessonProgressUpdatedEvent creates a new lesson progress updated event
func NewLessonProgressUpdatedEvent(lessonID, userID uuid.UUID, progress int, score *entity.Score, language, level, category, difficulty string) *LessonProgressUpdatedEvent {
	now := time.Now()
	return &LessonProgressUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.progress.updated",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":   lessonID,
				"userID":     userID,
				"updatedAt":  now,
				"progress":   progress,
				"score":      score,
				"language":   language,
				"level":      level,
				"category":   category,
				"difficulty": difficulty,
			},
		),
		LessonID:   lessonID,
		UserID:     userID,
		UpdatedAt:  now,
		Progress:   progress,
		Score:      score,
		Language:   language,
		Level:      level,
		Category:   category,
		Difficulty: difficulty,
	}
}

// LessonFavoritedEvent is emitted when a user favorites a lesson
type LessonFavoritedEvent struct {
	*coreevent.BaseEvent
	LessonID    uuid.UUID
	UserID      uuid.UUID
	FavoritedAt time.Time
	Language    string
	Level       string
	Category    string
	Difficulty  string
}

// NewLessonFavoritedEvent creates a new lesson favorited event
func NewLessonFavoritedEvent(lessonID, userID uuid.UUID, language, level, category, difficulty string) *LessonFavoritedEvent {
	now := time.Now()
	return &LessonFavoritedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.favorited",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":    lessonID,
				"userID":      userID,
				"favoritedAt": now,
				"language":    language,
				"level":       level,
				"category":    category,
				"difficulty":  difficulty,
			},
		),
		LessonID:    lessonID,
		UserID:      userID,
		FavoritedAt: now,
		Language:    language,
		Level:       level,
		Category:    category,
		Difficulty:  difficulty,
	}
}

// LessonUnfavoritedEvent is emitted when a user unfavorites a lesson
type LessonUnfavoritedEvent struct {
	*coreevent.BaseEvent
	LessonID      uuid.UUID
	UserID        uuid.UUID
	UnfavoritedAt time.Time
	Language      string
	Level         string
	Category      string
	Difficulty    string
}

// NewLessonUnfavoritedEvent creates a new lesson unfavorited event
func NewLessonUnfavoritedEvent(lessonID, userID uuid.UUID, language, level, category, difficulty string) *LessonUnfavoritedEvent {
	now := time.Now()
	return &LessonUnfavoritedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.unfavorited",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":      lessonID,
				"userID":        userID,
				"unfavoritedAt": now,
				"language":      language,
				"level":         level,
				"category":      category,
				"difficulty":    difficulty,
			},
		),
		LessonID:      lessonID,
		UserID:        userID,
		UnfavoritedAt: now,
		Language:      language,
		Level:         level,
		Category:      category,
		Difficulty:    difficulty,
	}
}
