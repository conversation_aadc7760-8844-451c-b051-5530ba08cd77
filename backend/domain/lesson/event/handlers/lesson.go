package handlers

import (
	"context"
	"fmt"
	"log"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/lesson/event"
	notificationentity "languagelearning/domain/notification/entity"
)

// LessonHandler 處理課程相關事件
type LessonHandler struct {
	*BaseHandler
}

// NewLessonHandler 創建課程處理器
func NewLessonHandler(base *BaseHandler) *LessonHandler {
	return &LessonHandler{
		BaseHandler: base,
	}
}

// Handle 實現 EventHandler 接口
func (h *LessonHandler) Handle(e coreevent.Event) error {
	switch evt := e.(type) {
	case *event.LessonCompletedEvent:
		return h.handleLessonCompleted(evt)
	case *event.LessonStartedEvent:
		return h.handleLessonStarted(evt)
	case *event.LessonEvaluatedEvent:
		return h.handleLessonEvaluated(evt)
	default:
		return fmt.Errorf("unsupported event type: %T", e)
	}
}

// handleLessonCompleted 處理課程完成事件
func (h *LessonHandler) handleLessonCompleted(evt *event.LessonCompletedEvent) error {
	log.Printf("處理課程完成事件：LessonID=%s, UserID=%s, Time=%s",
		evt.LessonID, evt.UserID, evt.CompletedAt)

	// 發送課程完成通知
	ctx := context.Background()
	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeAchievement,
		"課程完成",
		fmt.Sprintf("恭喜你完成了課程！掌握程度：%s", evt.MasteryLevel),
		map[string]interface{}{
			"lessonID":     evt.LessonID,
			"score":        evt.Score,
			"duration":     evt.Duration,
			"masteryLevel": evt.MasteryLevel,
		},
	)

	return err
}

// handleLessonStarted 處理課程開始事件
func (h *LessonHandler) handleLessonStarted(evt *event.LessonStartedEvent) error {
	log.Printf("處理課程開始事件：LessonID=%s, UserID=%s, Time=%s",
		evt.LessonID, evt.UserID, evt.StartedAt)

	// 發送課程開始通知
	ctx := context.Background()
	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLesson,
		"課程開始",
		fmt.Sprintf("你開始了一個新的課程，難度：%s", evt.Difficulty),
		map[string]interface{}{
			"lessonID":   evt.LessonID,
			"difficulty": evt.Difficulty,
		},
	)

	return err
}

// handleLessonEvaluated 處理課程評估事件
func (h *LessonHandler) handleLessonEvaluated(evt *event.LessonEvaluatedEvent) error {
	log.Printf("處理課程評估事件：LessonID=%s, UserID=%s, Score=%d",
		evt.LessonID, evt.UserID, evt.Score.Value())

	// 發送課程評估通知
	ctx := context.Background()
	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLesson,
		"課程評估",
		fmt.Sprintf("課程評估完成！得分：%d，反饋：%s", evt.Score.Value(), evt.Feedback),
		map[string]interface{}{
			"lessonID": evt.LessonID,
			"score":    evt.Score,
			"feedback": evt.Feedback,
		},
	)

	return err
}
