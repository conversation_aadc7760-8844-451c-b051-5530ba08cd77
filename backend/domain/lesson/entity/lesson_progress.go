package entity

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

// UserLessonProgress 用户课程进度实体
type UserLessonProgress struct {
	ID            uuid.UUID `json:"id"`
	UserID        uuid.UUID `json:"userId"`
	LessonID      uuid.UUID `json:"lessonId"`
	Score         int       `json:"score"` // 0-100
	Completed     bool      `json:"completed"`
	Favorited     bool      `json:"favorited"`
	CompletedDate time.Time `json:"completedDate,omitempty"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// NewUserLessonProgress 创建新的用户课程进度
func NewUserLessonProgress(userID, lessonID uuid.UUID) *UserLessonProgress {
	now := time.Now()
	return &UserLessonProgress{
		ID:        uuid.New(),
		UserID:    userID,
		LessonID:  lessonID,
		Score:     0,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// UpdateScore 更新分数
func (p *UserLessonProgress) UpdateScore(score int) error {
	if score < 0 || score > 100 {
		return errors.New("score must be between 0 and 100")
	}
	p.Score = score
	p.UpdatedAt = time.Now()
	return nil
}

// MarkAsCompleted 标记为已完成
func (p *UserLessonProgress) MarkAsCompleted() {
	if !p.Completed {
		p.Completed = true
		now := time.Now()
		p.CompletedDate = now
		p.UpdatedAt = now
	}
}

// ToggleFavorite 切换收藏状态
func (p *UserLessonProgress) ToggleFavorite() {
	p.Favorited = !p.Favorited
	p.UpdatedAt = time.Now()
}

// SetFavorite 设置收藏状态
func (p *UserLessonProgress) SetFavorite(favorited bool) {
	p.Favorited = favorited
	p.UpdatedAt = time.Now()
}

// GetProgressPercentage 获取进度百分比
func (p *UserLessonProgress) GetProgressPercentage() float64 {
	return float64(p.Score) / 100.0
}
