package entity

import (
	"time"

	"github.com/google/uuid"
)

// ProgressValue represents a progress value between 0 and 100
type ProgressValue struct {
	value int
}

// NewProgress creates a new progress value
func NewProgress(value int) (*ProgressValue, error) {
	if value < 0 || value > 100 {
		return nil, ErrInvalidProgressValue
	}
	return &ProgressValue{value: value}, nil
}

// Value returns the progress value
func (p *ProgressValue) Value() int {
	return p.value
}

// SetValue sets the progress value
func (p *ProgressValue) SetValue(value int) error {
	if value < 0 || value > 100 {
		return ErrInvalidProgressValue
	}
	p.value = value
	return nil
}

// LessonProgress represents a user's progress in a lesson
type LessonProgress struct {
	UserID        uuid.UUID
	LessonID      uuid.UUID
	Progress      *ProgressValue
	Completed     bool
	Favorited     bool
	CurrentScore  int
	CompletedDate *time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// NewLessonProgress creates a new lesson progress
func NewLessonProgress(userID, lessonID uuid.UUID) *LessonProgress {
	now := time.Now()
	progress, _ := NewProgress(0)
	return &LessonProgress{
		UserID:    userID,
		LessonID:  lessonID,
		Progress:  progress,
		Completed: false,
		Favorited: false,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// UpdateProgress updates the progress value
func (p *LessonProgress) UpdateProgress(value int) error {
	if err := p.Progress.SetValue(value); err != nil {
		return err
	}
	p.UpdatedAt = time.Now()
	return nil
}

// Complete marks the lesson as completed
func (p *LessonProgress) Complete() {
	if !p.Completed {
		now := time.Now()
		p.Completed = true
		p.CompletedDate = &now
		p.Progress.SetValue(100)
		p.UpdatedAt = now
	}
}

// ToggleFavorite toggles the favorite status
func (p *LessonProgress) ToggleFavorite(isFavorite bool) {
	p.Favorited = isFavorite
	p.UpdatedAt = time.Now()
}

// UpdateScore updates the current score
func (p *LessonProgress) UpdateScore(score int) {
	if score < 0 {
		score = 0
	} else if score > 100 {
		score = 100
	}
	p.CurrentScore = score
	p.UpdatedAt = time.Now()
}

// IsCompleted returns whether the lesson is completed
func (p *LessonProgress) IsCompleted() bool {
	return p.Completed
}

// IsFavorite returns whether the lesson is favorited
func (p *LessonProgress) IsFavorite() bool {
	return p.Favorited
}

// GetProgress returns the current progress value
func (p *LessonProgress) GetProgress() int {
	return p.Progress.Value()
}

// GetScore returns the current score
func (p *LessonProgress) GetScore() int {
	return p.CurrentScore
}
