package entity

import "errors"

var (
	// ErrLessonAlreadyPublished is returned when trying to publish an already published lesson
	ErrLessonAlreadyPublished = errors.New("lesson is already published")
	// ErrLessonNotPublished is returned when trying to unpublish a lesson that is not published
	ErrLessonNotPublished = errors.New("lesson is not published")
	// ErrLessonAlreadyArchived is returned when trying to archive an already archived lesson
	ErrLessonAlreadyArchived = errors.New("lesson is already archived")
	// ErrLessonNotFound is returned when a lesson is not found
	ErrLessonNotFound = errors.New("lesson not found")
	// ErrInvalidLessonData is returned when lesson data is invalid
	ErrInvalidLessonData = errors.New("invalid lesson data")
	// ErrLessonInUse is returned when trying to delete a lesson that is in use
	ErrLessonInUse = errors.New("lesson is in use and cannot be deleted")

	// Progress errors
	ErrInvalidProgressValue = errors.New("progress value must be between 0 and 100")
	ErrProgressNotFound     = errors.New("progress not found")
	ErrInvalidProgressData  = errors.New("invalid progress data")
)
