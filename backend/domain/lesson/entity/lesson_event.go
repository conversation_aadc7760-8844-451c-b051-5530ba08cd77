package entity

import (
	"time"

	"github.com/google/uuid"
)

// LessonEventType 课程事件类型
type LessonEventType string

const (
	// LessonCreated 课程创建事件
	LessonCreated LessonEventType = "LESSON_CREATED"
	// LessonUpdated 课程更新事件
	LessonUpdated LessonEventType = "LESSON_UPDATED"
	// LessonDeleted 课程删除事件
	LessonDeleted LessonEventType = "LESSON_DELETED"
	// LessonPublished 课程发布事件
	LessonPublished LessonEventType = "LESSON_PUBLISHED"
	// LessonUnpublished 课程取消发布事件
	LessonUnpublished LessonEventType = "LESSON_UNPUBLISHED"
	// LessonCompleted 课程完成事件
	LessonCompleted LessonEventType = "LESSON_COMPLETED"
	// LessonProgressUpdated 课程进度更新事件
	LessonProgressUpdated LessonEventType = "LESSON_PROGRESS_UPDATED"
	// LessonFavorited 课程收藏事件
	LessonFavorited LessonEventType = "LESSON_FAVORITED"
	// LessonUnfavorited 课程取消收藏事件
	LessonUnfavorited LessonEventType = "LESSON_UNFAVORITED"
)

// LessonEvent 课程事件
type LessonEvent struct {
	ID        uuid.UUID       `json:"id"`
	Type      LessonEventType `json:"type"`
	LessonID  uuid.UUID       `json:"lessonId"`
	UserID    uuid.UUID       `json:"userId,omitempty"`
	Data      interface{}     `json:"data,omitempty"`
	CreatedAt time.Time       `json:"createdAt"`
}

// NewLessonEvent 创建新的课程事件
func NewLessonEvent(eventType LessonEventType, lessonID uuid.UUID, userID uuid.UUID, data interface{}) *LessonEvent {
	return &LessonEvent{
		ID:        uuid.New(),
		Type:      eventType,
		LessonID:  lessonID,
		UserID:    userID,
		Data:      data,
		CreatedAt: time.Now(),
	}
}

// LessonEventData 课程事件数据接口
type LessonEventData interface {
	GetEventType() LessonEventType
	GetLessonID() uuid.UUID
	GetUserID() uuid.UUID
}

// LessonProgressEventData 课程进度事件数据
type LessonProgressEventData struct {
	LessonID  uuid.UUID `json:"lessonId"`
	UserID    uuid.UUID `json:"userId"`
	Score     int       `json:"score"`
	Completed bool      `json:"completed"`
}

func (d *LessonProgressEventData) GetEventType() LessonEventType {
	return LessonProgressUpdated
}

func (d *LessonProgressEventData) GetLessonID() uuid.UUID {
	return d.LessonID
}

func (d *LessonProgressEventData) GetUserID() uuid.UUID {
	return d.UserID
}

// LessonFavoriteEventData 课程收藏事件数据
type LessonFavoriteEventData struct {
	LessonID  uuid.UUID `json:"lessonId"`
	UserID    uuid.UUID `json:"userId"`
	Favorited bool      `json:"favorited"`
}

func (d *LessonFavoriteEventData) GetEventType() LessonEventType {
	if d.Favorited {
		return LessonFavorited
	}
	return LessonUnfavorited
}

func (d *LessonFavoriteEventData) GetLessonID() uuid.UUID {
	return d.LessonID
}

func (d *LessonFavoriteEventData) GetUserID() uuid.UUID {
	return d.UserID
}
