package entity

import (
	"errors"
	"time"
)

// LessonFilters 课程查询过滤器
type LessonFilters struct {
	Language   string    `json:"language,omitempty"`
	Level      string    `json:"level,omitempty"`
	Category   string    `json:"category,omitempty"`
	Status     string    `json:"status,omitempty"`
	Tags       []string  `json:"tags,omitempty"`
	StartDate  time.Time `json:"startDate,omitempty"`
	EndDate    time.Time `json:"endDate,omitempty"`
	SearchTerm string    `json:"searchTerm,omitempty"`
}

// Pageable 分页参数
type Pageable struct {
	Page     int `json:"page"`     // 从1开始
	PageSize int `json:"pageSize"` // 每页大小
}

// PageResult 分页结果
type PageResult[T any] struct {
	Content       []T   `json:"content"`
	TotalElements int64 `json:"totalElements"`
	TotalPages    int   `json:"totalPages"`
	Page          int   `json:"page"`
	PageSize      int   `json:"pageSize"`
	HasNext       bool  `json:"hasNext"`
	HasPrevious   bool  `json:"hasPrevious"`
}

// NewPageResult 创建分页结果
func NewPageResult[T any](content []T, totalElements int64, pageable Pageable) *PageResult[T] {
	totalPages := int(totalElements) / pageable.PageSize
	if int(totalElements)%pageable.PageSize > 0 {
		totalPages++
	}

	return &PageResult[T]{
		Content:       content,
		TotalElements: totalElements,
		TotalPages:    totalPages,
		Page:          pageable.Page,
		PageSize:      pageable.PageSize,
		HasNext:       pageable.Page < totalPages,
		HasPrevious:   pageable.Page > 1,
	}
}

// Validate 验证分页参数
func (p *Pageable) Validate() error {
	if p.Page < 1 {
		return errors.New("page must be greater than 0")
	}
	if p.PageSize < 1 || p.PageSize > 100 {
		return errors.New("pageSize must be between 1 and 100")
	}
	return nil
}

// GetOffset 获取偏移量
func (p *Pageable) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// GetLimit 获取限制
func (p *Pageable) GetLimit() int {
	return p.PageSize
}
