package repository

import (
	"context"

	"languagelearning/domain/core"
	"languagelearning/domain/learning/entity"
	"languagelearning/models"

	"github.com/google/uuid"
)

// LessonFilters represents the filters for lesson queries
type LessonFilters struct {
	Language   string
	Level      entity.LessonLevel
	Category   entity.LessonCategory
	Difficulty models.Difficulty
	Status     entity.LessonStatus
}

// LessonRepository defines the interface for lesson data access
type LessonRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error)
	Update(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error)
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*entity.Lesson, error)

	// Query operations
	GetLessons(ctx context.Context, pageable core.Pageable, filters LessonFilters) (*core.Page[entity.Lesson], error)
	SearchLessons(ctx context.Context, query string, pageable core.Pageable) (*core.Page[entity.Lesson], error)
	GetLessonsByLanguage(ctx context.Context, language string) ([]*entity.Lesson, error)
	GetLessonsByLevel(ctx context.Context, level entity.LessonLevel) ([]*entity.Lesson, error)
	GetLessonsByCategory(ctx context.Context, category entity.LessonCategory) ([]*entity.Lesson, error)
	GetRelatedLessons(ctx context.Context, lessonID uuid.UUID) ([]*entity.Lesson, error)

	// User-related operations
	GetFavoriteLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error)
	ToggleFavorite(ctx context.Context, userID, lessonID uuid.UUID, isFavorite bool) error
	GetUserProgress(ctx context.Context, userID, lessonID uuid.UUID) (*entity.LessonProgress, error)
	UpdateUserProgress(ctx context.Context, progress *entity.LessonProgress) error
	GetUserCompletedLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error)
	GetUserInProgressLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error)

	// Publishing operations
	Publish(ctx context.Context, lessonID uuid.UUID) error
	Unpublish(ctx context.Context, lessonID uuid.UUID) error
}
