package service

import (
	"languagelearning/models"

	"github.com/google/uuid"
)

// EvaluationService defines the interface for evaluation-related operations.
type EvaluationService interface {
	GetEvaluations(userID uuid.UUID, evalType string) ([]models.Evaluation, error)
	GetEvaluationDetails(userID, evalID uuid.UUID) (*models.Evaluation, error)
	CreateEvaluation(userID uuid.UUID, evalType models.EvaluationType, title, description string, passingScore, duration int, sections []models.EvalSection) (*models.Evaluation, error)
	StartEvaluation(userID, evalID uuid.UUID, sessionToken string) (*models.Evaluation, *models.AssessmentProgressResponse, error)
	SubmitEvaluationAnswer(userID, evalID uuid.UUID, questionID uint, answer, sessionToken string, currentSection, currentQuestion int) (bool, *models.AssessmentProgressResponse, error)
	CompleteEvaluation(userID, evalID uuid.UUID) (*models.EvaluationResult, models.EvaluationType, error)
	GetEvaluationResult(userID, evalID uuid.UUID) (*models.EvaluationResult, error)
	GetUserEvaluationHistory(userID uuid.UUID) ([]models.EvaluationResult, error)
} 