package impl

import (
	"context"
	"errors"
	"fmt"
	"languagelearning/domain/core/event"
	evaluationevent "languagelearning/domain/evaluation/event"
	evaluationSvc "languagelearning/domain/evaluation/service"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"
	"log"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type EvaluationService struct {
	evaluationRepo       repository.EvaluationRepository
	evaluationResultRepo repository.EvaluationResultRepository
	progressRepo         repository.AssessmentProgressRepository
	questionRepo         repository.EvalQuestionRepository
	sectionRepo          repository.EvalSectionRepository
	eventBus             event.EventBus
	db                   *gorm.DB // Temporary for complex operations
}

// Ensure that EvaluationService implements the EvaluationService interface.
var _ evaluationSvc.EvaluationService = (*EvaluationService)(nil)

func NewEvaluationService(
	evaluationRepo repository.EvaluationRepository,
	evaluationResultRepo repository.EvaluationResultRepository,
	progressRepo repository.AssessmentProgressRepository,
	questionRepo repository.EvalQuestionRepository,
	sectionRepo repository.EvalSectionRepository,
	eventBus event.EventBus,
	db *gorm.DB,
) evaluationSvc.EvaluationService {
	return &EvaluationService{
		evaluationRepo:       evaluationRepo,
		evaluationResultRepo: evaluationResultRepo,
		progressRepo:         progressRepo,
		questionRepo:         questionRepo,
		sectionRepo:          sectionRepo,
		eventBus:             eventBus,
		db:                   db,
	}
}

func (s *EvaluationService) GetEvaluations(userID uuid.UUID, evalType string) ([]models.Evaluation, error) {
	ctx := context.Background()
	if evalType != "" {
		// Convert string to EvaluationType
		evalTypeEnum := models.EvaluationType(evalType)
		return s.evaluationRepo.FindByUserIDAndType(ctx, userID, evalTypeEnum)
	}
	return s.evaluationRepo.FindByUserID(ctx, userID)
}

func (s *EvaluationService) GetEvaluationDetails(userID, evalID uuid.UUID) (*models.Evaluation, error) {
	ctx := context.Background()
	return s.evaluationRepo.FindByIDAndUserIDWithDetails(ctx, evalID, userID)
}

// CreateEvaluation creates a new evaluation
func (s *EvaluationService) CreateEvaluation(userID uuid.UUID, evalType models.EvaluationType, title, description string, passingScore, duration int, sections []models.EvalSection) (*models.Evaluation, error) {
	// Create the evaluation
	evaluation := models.Evaluation{
		UserID:         userID,
		Type:           evalType,
		Title:          title,
		Description:    description,
		PassingScore:   passingScore,
		Duration:       duration,
		TotalQuestions: 0, // Will be calculated
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Calculate total questions and set section relationships
	totalQuestions := 0
	for i := range sections {
		sections[i].EvaluationID = evaluation.ID // Set EvaluationID
		totalQuestions += len(sections[i].Questions)
	}
	evaluation.TotalQuestions = totalQuestions
	evaluation.Sections = sections

	// Save the evaluation and its sections/questions
	ctx := context.Background()
	createdEvaluation, err := s.evaluationRepo.Create(ctx, evaluation)
	if err != nil {
		return nil, fmt.Errorf("failed to create evaluation: %w", err)
	}

	return createdEvaluation, nil
}

// StartEvaluation starts an evaluation for a user
func (s *EvaluationService) StartEvaluation(userID, evalID uuid.UUID, sessionToken string) (*models.Evaluation, *models.AssessmentProgressResponse, error) {
	ctx := context.Background()

	// Find the evaluation
	evaluation, err := s.evaluationRepo.FindByIDAndUserIDWithDetails(ctx, evalID, userID)
	if err != nil {
		return nil, nil, err
	}

	// Check if the evaluation is already completed
	if evaluation.IsCompleted {
		return evaluation, nil, errors.New("evaluation already completed")
	}

	// Check if there's an existing progress record
	progress, err := s.progressRepo.FindByUserEvaluationAndSession(ctx, userID, evalID, sessionToken)

	var progressResponse *models.AssessmentProgressResponse

	// If there's an existing progress record, use it
	if err == nil {
		response := progress.ToResponse()
		progressResponse = &response
	} else {
		// Create a new progress record
		newProgress := models.AssessmentProgress{
			UserID:          userID,
			EvaluationID:    evalID,
			CurrentSection:  0,
			CurrentQuestion: 0,
			SessionToken:    sessionToken,
			LastUpdated:     time.Now(),
			CreatedAt:       time.Now(),
		}

		createdProgress, err := s.progressRepo.Create(ctx, newProgress)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to create assessment progress: %w", err)
		}

		response := createdProgress.ToResponse()
		progressResponse = &response
	}

	// Update the evaluation if it hasn't been started yet
	if evaluation.StartedAt.IsZero() {
		evaluation.StartedAt = time.Now()
		evaluation.UpdatedAt = time.Now()

		// Save the evaluation
		updatedEvaluation, err := s.evaluationRepo.Update(ctx, *evaluation)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to start evaluation: %w", err)
		}
		evaluation = updatedEvaluation
	}

	return evaluation, progressResponse, nil
}

// SubmitEvaluationAnswer submits an answer for an evaluation question
func (s *EvaluationService) SubmitEvaluationAnswer(userID, evalID uuid.UUID, questionID uint, answer, sessionToken string, currentSection, currentQuestion int) (bool, *models.AssessmentProgressResponse, error) {
	ctx := context.Background()

	// Find the evaluation
	evaluation, err := s.evaluationRepo.FindByID(ctx, evalID)
	if err != nil {
		return false, nil, err
	}

	// Check if the evaluation is already completed
	if evaluation.IsCompleted {
		return false, nil, errors.New("evaluation already completed")
	}

	// Find the question
	question, err := s.questionRepo.FindByID(ctx, questionID)
	if err != nil {
		return false, nil, err
	}

	// Update the question with the user's answer
	question.UserAnswer = answer
	question.IsCorrect = answer == question.CorrectAnswer

	// Save the question
	_, err = s.questionRepo.Update(ctx, *question)
	if err != nil {
		return false, nil, fmt.Errorf("failed to submit answer: %w", err)
	}

	// Update the assessment progress
	progress, err := s.progressRepo.FindByUserEvaluationAndSession(ctx, userID, evalID, sessionToken)

	var progressResponse *models.AssessmentProgressResponse

	if err == nil {
		// Update existing progress
		progress.CurrentSection = currentSection
		progress.CurrentQuestion = currentQuestion
		progress.LastUpdated = time.Now()

		updatedProgress, err := s.progressRepo.Update(ctx, *progress)
		if err != nil {
			return false, nil, fmt.Errorf("failed to update assessment progress: %w", err)
		}
		response := updatedProgress.ToResponse()
		progressResponse = &response
	} else {
		// Create new progress record
		newProgress := models.AssessmentProgress{
			UserID:          userID,
			EvaluationID:    evalID,
			CurrentSection:  currentSection,
			CurrentQuestion: currentQuestion,
			SessionToken:    sessionToken,
			LastUpdated:     time.Now(),
			CreatedAt:       time.Now(),
		}

		createdProgress, err := s.progressRepo.Create(ctx, newProgress)
		if err != nil {
			return false, nil, fmt.Errorf("failed to create assessment progress: %w", err)
		}
		response := createdProgress.ToResponse()
		progressResponse = &response
	}

	return question.IsCorrect, progressResponse, nil
}

// CompleteEvaluation completes an evaluation and generates results
func (s *EvaluationService) CompleteEvaluation(userID, evalID uuid.UUID) (*models.EvaluationResult, models.EvaluationType, error) {
	ctx := context.Background()

	// Find the evaluation with sections and questions
	evaluation, err := s.evaluationRepo.FindByIDAndUserIDWithDetails(ctx, evalID, userID)
	if err != nil {
		return nil, "", err
	}

	// Check if the evaluation is already completed
	if evaluation.IsCompleted {
		return nil, "", errors.New("evaluation already completed")
	}

	// Calculate scores for each section and overall score
	var totalScore int
	var totalWeight int
	var sectionScores []models.SectionScore

	for _, section := range evaluation.Sections {
		var sectionPoints int
		var totalPoints int

		for _, question := range section.Questions {
			totalPoints += question.Points
			if question.IsCorrect {
				sectionPoints += question.Points
			}
		}

		// Calculate section score as percentage
		sectionScore := 0
		if totalPoints > 0 {
			sectionScore = (sectionPoints * 100) / totalPoints
		}

		// Update section score
		section.Score = sectionScore
		_, err := s.sectionRepo.Update(ctx, section)
		if err != nil {
			log.Printf("Failed to update section score: %v", err)
			// Continue processing, don't fail the entire operation
		}

		// Add to total score (weighted)
		totalScore += sectionScore * section.Weight
		totalWeight += section.Weight

		// Create section score for result
		sectionScoreObj := models.SectionScore{
			SectionTitle: section.Title,
			Skill:        section.Skill,
			Score:        sectionScore,
		}

		// Add strengths and weaknesses based on score
		if sectionScore >= 80 {
			sectionScoreObj.Strengths = append(sectionScoreObj.Strengths, "Strong understanding of "+section.Skill)
		} else if sectionScore <= 40 {
			sectionScoreObj.Weaknesses = append(sectionScoreObj.Weaknesses, "Needs improvement in "+section.Skill)
		}

		sectionScores = append(sectionScores, sectionScoreObj)
	}

	// Calculate overall score
	overallScore := 0
	if totalWeight > 0 {
		overallScore = totalScore / totalWeight
	}

	// Update evaluation
	evaluation.IsCompleted = true
	evaluation.CompletedAt = time.Now()
	evaluation.Score = overallScore
	evaluation.UpdatedAt = time.Now()

	// Save the evaluation
	_, err = s.evaluationRepo.Update(ctx, *evaluation)
	if err != nil {
		return nil, "", fmt.Errorf("failed to complete evaluation: %w", err)
	}

	// Generate recommendations based on scores
	var recommendations []string
	if overallScore < evaluation.PassingScore {
		recommendations = append(recommendations, "Review the material and try again")
	}

	for _, sectionScore := range sectionScores {
		if sectionScore.Score < 60 {
			recommendations = append(recommendations, "Focus on improving your "+sectionScore.Skill+" skills")
		}
	}

	// Create evaluation result
	evalResult := models.EvaluationResult{
		EvaluationID:    evalID,
		UserID:          userID,
		OverallScore:    overallScore,
		PassingScore:    evaluation.PassingScore,
		IsPassed:        overallScore >= evaluation.PassingScore,
		CompletedAt:     evaluation.CompletedAt,
		SectionScores:   sectionScores,
		Feedback:        generateFeedback(overallScore, evaluation.PassingScore),
		Recommendations: recommendations,
	}

	// Save the result
	createdResult, err := s.evaluationResultRepo.Create(ctx, evalResult)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create evaluation result: %w", err)
	}

	// Publish evaluation completed event
	if s.eventBus != nil {
		evalCompletedEvent := evaluationevent.NewEvaluationCompletedEvent(nil, userID)
		if err := s.eventBus.Publish(ctx, evalCompletedEvent); err != nil {
			log.Printf("Failed to publish evaluation completed event: %v", err)
			// Don't return error, as the main operation succeeded
		}
	}

	// Clean up assessment progress records for this evaluation
	err = s.progressRepo.DeleteByUserAndEvaluation(ctx, userID, evalID)
	if err != nil {
		log.Printf("Failed to clean up assessment progress: %v", err)
		// Don't fail the operation for cleanup errors
	}

	return createdResult, evaluation.Type, nil
}

// GetEvaluationResult returns results of a completed evaluation
func (s *EvaluationService) GetEvaluationResult(userID, evalID uuid.UUID) (*models.EvaluationResult, error) {
	ctx := context.Background()
	return s.evaluationResultRepo.FindByEvaluationIDAndUserIDWithDetails(ctx, evalID, userID)
}

// GetUserEvaluationHistory returns a user's evaluation history
func (s *EvaluationService) GetUserEvaluationHistory(userID uuid.UUID) ([]models.EvaluationResult, error) {
	ctx := context.Background()
	return s.evaluationResultRepo.FindByUserIDOrderedByDate(ctx, userID)
}

// Helper functions (moved from controller)

// generateFeedback generates feedback based on the score
func generateFeedback(score int, passingScore int) string {
	if score >= 90 {
		return "Excellent work! You have demonstrated a strong understanding of the material."
	} else if score >= 80 {
		return "Great job! You have a good grasp of the material."
	} else if score >= passingScore {
		return "Good work! You have passed the evaluation, but there is room for improvement."
	} else if score >= passingScore-10 {
		return "You were close to passing. With a little more practice, you'll be able to pass next time."
	} else {
		return "You need more practice before attempting this evaluation again."
	}
}

// calculatePointsForEvaluation calculates points based on evaluation type and score
func calculatePointsForEvaluation(evalType models.EvaluationType, score int) int {
	basePoints := 0

	switch evalType {
	case models.EvalPlacement:
		basePoints = 50
	case models.EvalProgress:
		basePoints = 100
	case models.EvalSkill:
		basePoints = 150
	case models.EvalCertificate:
		basePoints = 300
	}

	// Adjust points based on score
	return (basePoints * score) / 100
}
