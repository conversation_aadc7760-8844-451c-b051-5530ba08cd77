package entity

import (
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// EvaluationType 评估类型
type EvaluationType string

const (
	// EvalPlacement 分级评估
	EvalPlacement EvaluationType = "placement"
	// EvalProgress 进度评估
	EvalProgress EvaluationType = "progress"
	// EvalCertification 认证评估
	EvalCertification EvaluationType = "certification"
	// EvalSkill 技能评估
	EvalSkill EvaluationType = "skill"
)

// Evaluation 评估实体
type Evaluation struct {
	ID             uuid.UUID      `json:"id"`
	Type           EvaluationType `json:"type"`
	Title          string         `json:"title"`
	Description    string         `json:"description"`
	UserID         uuid.UUID      `json:"userId"`
	TotalQuestions int            `json:"totalQuestions"`
	PassingScore   int            `json:"passingScore"` // 百分比，如70表示70%
	Duration       int            `json:"duration"`     // 分钟
	IsCompleted    bool           `json:"isCompleted"`
	CompletedAt    *time.Time     `json:"completedAt,omitempty"`
	Sections       []EvalSection  `json:"sections,omitempty"`
	CreatedAt      time.Time      `json:"createdAt"`
	UpdatedAt      time.Time      `json:"updatedAt"`
}

// NewEvaluation 创建新评估
func NewEvaluation(evalType EvaluationType, title, description string, userID uuid.UUID, totalQuestions, passingScore, duration int) *Evaluation {
	now := time.Now()
	return &Evaluation{
		ID:             uuid.New(),
		Type:           evalType,
		Title:          title,
		Description:    description,
		UserID:         userID,
		TotalQuestions: totalQuestions,
		PassingScore:   passingScore,
		Duration:       duration,
		IsCompleted:    false,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
}

// EvalSection 评估部分
type EvalSection struct {
	ID        uuid.UUID      `json:"id"`
	EvalID    uuid.UUID      `json:"evalId"`
	Title     string         `json:"title"`
	Skill     string         `json:"skill"`
	Weight    int            `json:"weight"` // 百分比
	Questions []EvalQuestion `json:"questions,omitempty"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
}

// NewEvalSection 创建新评估部分
func NewEvalSection(evalID uuid.UUID, title, skill string, weight int) *EvalSection {
	now := time.Now()
	return &EvalSection{
		ID:        uuid.New(),
		EvalID:    evalID,
		Title:     title,
		Skill:     skill,
		Weight:    weight,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// EvalQuestion 评估问题
type EvalQuestion struct {
	ID            uuid.UUID      `json:"id"`
	SectionID     uuid.UUID      `json:"sectionId"`
	Question      string         `json:"question"`
	Type          string         `json:"type"` // multiple_choice, fill_in_blank, etc.
	Options       pq.StringArray `json:"options,omitempty"`
	CorrectAnswer string         `json:"correctAnswer"`
	Points        int            `json:"points"`
	Explanation   string         `json:"explanation,omitempty"`
	CreatedAt     time.Time      `json:"createdAt"`
	UpdatedAt     time.Time      `json:"updatedAt"`
}

// NewEvalQuestion 创建新评估问题
func NewEvalQuestion(sectionID uuid.UUID, question, questionType string, options []string, correctAnswer string, points int) *EvalQuestion {
	now := time.Now()
	return &EvalQuestion{
		ID:            uuid.New(),
		SectionID:     sectionID,
		Question:      question,
		Type:          questionType,
		Options:       options,
		CorrectAnswer: correctAnswer,
		Points:        points,
		CreatedAt:     now,
		UpdatedAt:     now,
	}
}

// EvaluationResult 评估结果
type EvaluationResult struct {
	ID            uuid.UUID      `json:"id"`
	EvaluationID  uuid.UUID      `json:"evaluationId"`
	UserID        uuid.UUID      `json:"userId"`
	Score         int            `json:"score"` // 百分比
	IsPassed      bool           `json:"isPassed"`
	TimeSpent     int            `json:"timeSpent"` // 分钟
	CompletedAt   time.Time      `json:"completedAt"`
	Feedback      string         `json:"feedback,omitempty"`
	SectionScores []SectionScore `json:"sectionScores,omitempty"`
	CreatedAt     time.Time      `json:"createdAt"`
	UpdatedAt     time.Time      `json:"updatedAt"`
}

// NewEvaluationResult 创建新评估结果
func NewEvaluationResult(evaluationID, userID uuid.UUID, score, timeSpent int, isPassed bool) *EvaluationResult {
	now := time.Now()
	return &EvaluationResult{
		ID:           uuid.New(),
		EvaluationID: evaluationID,
		UserID:       userID,
		Score:        score,
		IsPassed:     isPassed,
		TimeSpent:    timeSpent,
		CompletedAt:  now,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// SectionScore 部分分数
type SectionScore struct {
	ID           uuid.UUID `json:"id"`
	ResultID     uuid.UUID `json:"resultId"`
	SectionID    uuid.UUID `json:"sectionId"`
	SectionTitle string    `json:"sectionTitle"`
	Skill        string    `json:"skill"`
	Score        int       `json:"score"` // 百分比
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// NewSectionScore 创建新部分分数
func NewSectionScore(resultID, sectionID uuid.UUID, sectionTitle, skill string, score int) *SectionScore {
	now := time.Now()
	return &SectionScore{
		ID:           uuid.New(),
		ResultID:     resultID,
		SectionID:    sectionID,
		SectionTitle: sectionTitle,
		Skill:        skill,
		Score:        score,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// QuestionResponse 问题回答
type QuestionResponse struct {
	ID         uuid.UUID `json:"id"`
	ResultID   uuid.UUID `json:"resultId"`
	QuestionID uuid.UUID `json:"questionId"`
	UserAnswer string    `json:"userAnswer"`
	IsCorrect  bool      `json:"isCorrect"`
	Score      int       `json:"score"`
	TimeSpent  int       `json:"timeSpent"` // 秒
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

// NewQuestionResponse 创建新问题回答
func NewQuestionResponse(resultID, questionID uuid.UUID, userAnswer string, isCorrect bool, score, timeSpent int) *QuestionResponse {
	now := time.Now()
	return &QuestionResponse{
		ID:         uuid.New(),
		ResultID:   resultID,
		QuestionID: questionID,
		UserAnswer: userAnswer,
		IsCorrect:  isCorrect,
		Score:      score,
		TimeSpent:  timeSpent,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
}
