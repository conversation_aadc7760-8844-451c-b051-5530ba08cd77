package di

import (
	"go.uber.org/dig"
	coredi "languagelearning/domain/core/di"
	evaluationSvcImpl "languagelearning/domain/evaluation/service/impl"
)

// EvaluationModule 評估領域的DI模塊
type EvaluationModule struct {
	coredi.BaseModule
}

// NewEvaluationModule 創建評估領域模塊
func NewEvaluationModule() coredi.Module {
	return &EvaluationModule{
		BaseModule: coredi.NewBaseModule(
			"evaluation",
			[]string{"core", "learning"}, // 依賴核心模塊和學習模塊
			[]coredi.ModuleFeature{coredi.FeatureBasic},
		),
	}
}

// Register 註冊評估領域的所有依賴
func (m *EvaluationModule) Register(container *dig.Container) error {
	// 註冊評估服務
	if err := container.Provide(evaluationSvcImpl.NewEvaluationService); err != nil {
		return err
	}

	return nil
}
