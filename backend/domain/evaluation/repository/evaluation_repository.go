package repository

import (
	"context"
	"languagelearning/domain/evaluation/entity"
	baseRepo "languagelearning/domain/repository"

	"github.com/google/uuid"
)

// EvaluationRepository 评估仓库接口
type EvaluationRepository interface {
	baseRepo.Repository[entity.Evaluation, uuid.UUID]
	baseRepo.PageableRepository[entity.Evaluation, uuid.UUID]

	// FindByUser 查找用户的评估
	FindByUser(ctx context.Context, userID uuid.UUID) ([]*entity.Evaluation, error)

	// FindByUserAndType 查找用户指定类型的评估
	FindByUserAndType(ctx context.Context, userID uuid.UUID, evalType entity.EvaluationType) ([]*entity.Evaluation, error)

	// GetPendingEvaluations 获取用户的待完成评估
	GetPendingEvaluations(ctx context.Context, userID uuid.UUID) ([]*entity.Evaluation, error)

	// GetCompletedEvaluations 获取用户的已完成评估
	GetCompletedEvaluations(ctx context.Context, userID uuid.UUID) ([]*entity.Evaluation, error)

	// MarkAsCompleted 将评估标记为已完成
	MarkAsCompleted(ctx context.Context, evalID uuid.UUID) error

	// GetEvaluationWithSections 获取评估及其部分
	GetEvaluationWithSections(ctx context.Context, evalID uuid.UUID) (*entity.Evaluation, error)
}

// EvalSectionRepository 评估部分仓库接口
type EvalSectionRepository interface {
	baseRepo.Repository[entity.EvalSection, uuid.UUID]

	// FindByEvaluation 查找评估的部分
	FindByEvaluation(ctx context.Context, evalID uuid.UUID) ([]*entity.EvalSection, error)

	// GetSectionWithQuestions 获取部分及其问题
	GetSectionWithQuestions(ctx context.Context, sectionID uuid.UUID) (*entity.EvalSection, error)
}

// EvalQuestionRepository 评估问题仓库接口
type EvalQuestionRepository interface {
	baseRepo.Repository[entity.EvalQuestion, uuid.UUID]

	// FindBySection 查找部分的问题
	FindBySection(ctx context.Context, sectionID uuid.UUID) ([]*entity.EvalQuestion, error)

	// FindByType 查找指定类型的问题
	FindByType(ctx context.Context, questionType string) ([]*entity.EvalQuestion, error)
}

// EvaluationResultRepository 评估结果仓库接口
type EvaluationResultRepository interface {
	baseRepo.Repository[entity.EvaluationResult, uuid.UUID]

	// FindByEvaluation 查找评估的结果
	FindByEvaluation(ctx context.Context, evalID uuid.UUID) ([]*entity.EvaluationResult, error)

	// FindByUser 查找用户的评估结果
	FindByUser(ctx context.Context, userID uuid.UUID) ([]*entity.EvaluationResult, error)

	// FindByUserAndEvaluation 查找用户的特定评估结果
	FindByUserAndEvaluation(ctx context.Context, userID, evalID uuid.UUID) (*entity.EvaluationResult, error)

	// GetResultWithSectionScores 获取结果及其部分分数
	GetResultWithSectionScores(ctx context.Context, resultID uuid.UUID) (*entity.EvaluationResult, error)

	// GetUserLatestResult 获取用户的最新评估结果
	GetUserLatestResult(ctx context.Context, userID uuid.UUID) (*entity.EvaluationResult, error)
}

// QuestionResponseRepository 问题回答仓库接口
type QuestionResponseRepository interface {
	baseRepo.Repository[entity.QuestionResponse, uuid.UUID]

	// FindByResult 查找结果的问题回答
	FindByResult(ctx context.Context, resultID uuid.UUID) ([]*entity.QuestionResponse, error)

	// FindByResultAndQuestion 查找结果的特定问题回答
	FindByResultAndQuestion(ctx context.Context, resultID, questionID uuid.UUID) (*entity.QuestionResponse, error)

	// GetCorrectResponses 获取正确的回答
	GetCorrectResponses(ctx context.Context, resultID uuid.UUID) ([]*entity.QuestionResponse, error)

	// GetIncorrectResponses 获取错误的回答
	GetIncorrectResponses(ctx context.Context, resultID uuid.UUID) ([]*entity.QuestionResponse, error)
}
