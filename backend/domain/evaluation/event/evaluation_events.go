package event

import (
	"time"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/evaluation/entity"

	"github.com/google/uuid"
)

// EvaluationCompletedEvent 評估完成事件
type EvaluationCompletedEvent struct {
	*coreevent.BaseEvent
	Evaluation  *entity.Evaluation
	UserID      uuid.UUID
	CompletedAt time.Time
}

// NewEvaluationCompletedEvent 創建評估完成事件
func NewEvaluationCompletedEvent(evaluation *entity.Evaluation, userID uuid.UUID) *EvaluationCompletedEvent {
	return &EvaluationCompletedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"evaluation.completed",
			evaluation.ID,
			"evaluation",
			map[string]interface{}{
				"evaluation": evaluation,
				"userID":     userID,
			},
		),
		Evaluation:  evaluation,
		UserID:      userID,
		CompletedAt: time.Now(),
	}
}

// EvaluationStartedEvent 評估開始事件
type EvaluationStartedEvent struct {
	*coreevent.BaseEvent
	EvaluationID uuid.UUID
	UserID       uuid.UUID
	StartedAt    time.Time
}

// NewEvaluationStartedEvent 創建評估開始事件
func NewEvaluationStartedEvent(evaluationID, userID uuid.UUID) *EvaluationStartedEvent {
	return &EvaluationStartedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"evaluation.started",
			evaluationID,
			"evaluation",
			map[string]interface{}{
				"evaluationID": evaluationID,
				"userID":       userID,
			},
		),
		EvaluationID: evaluationID,
		UserID:       userID,
		StartedAt:    time.Now(),
	}
}

// EvaluationProgressUpdatedEvent 評估進度更新事件
type EvaluationProgressUpdatedEvent struct {
	*coreevent.BaseEvent
	EvaluationID uuid.UUID
	UserID       uuid.UUID
	Progress     int
	UpdatedAt    time.Time
}

// NewEvaluationProgressUpdatedEvent 創建評估進度更新事件
func NewEvaluationProgressUpdatedEvent(evaluationID, userID uuid.UUID, progress int) *EvaluationProgressUpdatedEvent {
	return &EvaluationProgressUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"evaluation.progress_updated",
			evaluationID,
			"evaluation",
			map[string]interface{}{
				"evaluationID": evaluationID,
				"userID":       userID,
				"progress":     progress,
			},
		),
		EvaluationID: evaluationID,
		UserID:       userID,
		Progress:     progress,
		UpdatedAt:    time.Now(),
	}
}
