package service

import (
	"context"
	"languagelearning/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AchievementService defines the interface for achievement-related operations
type AchievementService interface {
	GetAchievements(userID uuid.UUID) ([]models.UserAchievementResponse, error)
	ClaimAchievement(ctx context.Context, userID uuid.UUID, achievementID uuid.UUID) (gin.H, error)
	CheckAndUpdateAchievements(userID uuid.UUID) error
	ClaimAchievementReward(userID, achievementID uuid.UUID) (gin.H, error)
} 