package impl

import (
	"context"
	"fmt"
	"languagelearning/domain/achievement/service"
	learningRepo "languagelearning/domain/learning/repository"
	"languagelearning/domain/user/entity"
	domainUserRepo "languagelearning/domain/user/repository"
	"languagelearning/models"
	"log/slog"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	achRepoImpl "languagelearning/domain/achievement/repository/impl"
	learningRepoImpl "languagelearning/domain/learning/repository/impl"
	userRepositoryImpl "languagelearning/domain/user/repository/impl"
)

// AchievementService handles the achievement functionality
type AchievementService struct {
	achievementRepo     domainUserRepo.AchievementRepository
	userAchievementRepo domainUserRepo.UserAchievementRepository
	userRepository      domainUserRepo.UserRepository
	userStatsRepository domainUserRepo.UserStatsRepository
	lessonProgressRepo  learningRepo.LessonProgressRepository
	DB                  *gorm.DB // Keep DB for now for transaction management
}

// NewAchievementService creates a new achievement service
func NewAchievementService(db *gorm.DB) service.AchievementService {
	return &AchievementService{
		achievementRepo:     achRepoImpl.NewAchievementRepository(db),
		userAchievementRepo: achRepoImpl.NewUserAchievementRepository(db),
		userRepository:      userRepositoryImpl.NewUserRepository(db),
		userStatsRepository: userRepositoryImpl.NewUserStatsRepository(db),
		lessonProgressRepo:  learningRepoImpl.NewGormLessonProgressRepository(db),
		DB:                  db,
	}
}

// Helper function to map entity.AchievementType to models.AchievementType
// This is a placeholder - actual mapping might be more complex or defined elsewhere
func mapEntityTypeToModelType(entityType entity.AchievementType) models.AchievementType {
	// This is a naive mapping and assumes direct string conversion or a predefined map
	// For example, if entity.AchievementTypeStreak ("streak") should map to models.AchStreak ("连续学习")
	// A proper map should be used.
	switch entityType {
	case entity.AchievementTypeStreak: // "streak"
		return models.AchStreak // "连续学习"
	case entity.AchievementTypeVocabulary:
		return models.AchVocabulary
	case entity.AchievementTypeListening:
		return models.AchListening
	case entity.AchievementTypeSpeaking:
		return models.AchSpeaking
	// case entity.AchievementTypeGrammar: // model might not have direct equivalent, handle as needed
	// 	 return models.AchGrammar // If exists
	// case entity.AchievementTypeChallenge:
	// 	 return models.AchChallenges // If exists
	case entity.AchievementTypeSocial:
		return models.AchSocial
	// Add other mappings as needed
	default:
		// Fallback or error handling if no direct mapping
		// For now, try a direct cast, but this is likely wrong if values differ.
		return models.AchievementType(entityType) // This is risky if values aren't identical
	}
}

// GetAchievements retrieves all achievements with user progress information
func (s *AchievementService) GetAchievements(userID uuid.UUID) ([]models.UserAchievementResponse, error) {
	ctx := context.Background()

	achievements, err := s.achievementRepo.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	userAchievements, err := s.userAchievementRepo.FindByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	userAchievementMap := make(map[uuid.UUID]entity.UserAchievement)
	for _, ua := range userAchievements {
		userAchievementMap[ua.AchievementID] = ua
	}

	var response []models.UserAchievementResponse
	for _, achievement := range achievements {
		ua, exists := userAchievementMap[achievement.ID]
		if !exists {
			ua = entity.UserAchievement{
				UserID:        userID,
				AchievementID: achievement.ID,
				Progress:      0,
				IsUnlocked:    false,
				RewardClaimed: false,
			}
		}

		response = append(response, models.UserAchievementResponse{
			ID:            achievement.ID,
			Type:          mapEntityTypeToModelType(achievement.Type),
			Title:         achievement.Title,
			Description:   achievement.Description,
			Icon:          achievement.Icon,
			Color:         achievement.Color,
			Requirement:   achievement.Requirement,
			Reward:        achievement.Reward,
			Progress:      ua.Progress,
			IsUnlocked:    ua.IsUnlocked,
			RewardClaimed: ua.RewardClaimed,
			UnlockedDate:  ua.UnlockedDate,
		})
	}

	return response, nil
}

// ClaimAchievement allows a user to claim a reward for an achievement
func (s *AchievementService) ClaimAchievement(ctx context.Context, userID uuid.UUID, achievementID uuid.UUID) (gin.H, error) {
	userAchievement, err := s.userAchievementRepo.FindByUserAndAchievementID(ctx, userID, achievementID)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find user achievement", "userID", userID, "achievementID", achievementID, "error", err)
		return nil, fmt.Errorf("user achievement not found for user %s, achievement %s: %w", userID, achievementID, err)
	}

	if userAchievement.RewardClaimed {
		return nil, fmt.Errorf("reward already claimed for achievement %s by user %s", achievementID, userID)
	}
	if !userAchievement.IsUnlocked {
		return nil, fmt.Errorf("achievement %s not unlocked for user %s", achievementID, userID)
	}

	achievement, err := s.achievementRepo.FindByID(ctx, achievementID)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find achievement details", "achievementID", achievementID, "error", err)
		return nil, fmt.Errorf("failed to find achievement details for ID %s: %w", achievementID, err)
	}

	userAchievement.RewardClaimed = true
	updatedUserAchievement, err := s.userAchievementRepo.Update(ctx, userAchievement)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to save user achievement after claiming reward", "userID", userID, "achievementID", achievementID, "error", err)
		return nil, fmt.Errorf("failed to save user achievement for %s: %w", userID, err)
	}
	_ = updatedUserAchievement

	userStats, err := s.userStatsRepository.FindByUserID(ctx, userID)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find user stats for updating points", "userID", userID, "error", err)
		return nil, fmt.Errorf("failed to find user stats for %s: %w", userID, err)
	}

	userStats.TotalPoints += achievement.Reward
	userStats.UpdatedAt = time.Now()
	updatedUserStats, err := s.userStatsRepository.Update(ctx, userStats)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to save user stats after updating points", "userID", userID, "error", err)
		return nil, fmt.Errorf("failed to save user stats for %s: %w", userID, err)
	}
	_ = updatedUserStats

	return gin.H{"reward": achievement.Reward}, nil
}

// CheckAndUpdateAchievements checks and updates a user's achievements
func (s *AchievementService) CheckAndUpdateAchievements(userID uuid.UUID) error {
	ctx := context.Background()

	userStats, err := s.userStatsRepository.FindByUserID(ctx, userID)
	if err != nil {
		slog.WarnContext(ctx, "User stats not found for user when checking achievements. Stat-based achievements may not update.", "userID", userID, "error", err)
	}

	allAchievements, err := s.achievementRepo.FindAll(ctx)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to retrieve all achievements", "error", err)
		return fmt.Errorf("failed to retrieve all achievements: %w", err)
	}

	userAchievements, err := s.userAchievementRepo.FindByUserID(ctx, userID)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to retrieve user's existing achievements", "userID", userID, "error", err)
		return fmt.Errorf("failed to retrieve user achievements for %s: %w", userID, err)
	}

	userAchievementMap := make(map[uuid.UUID]*entity.UserAchievement)
	for i := range userAchievements {
		userAchievementMap[userAchievements[i].AchievementID] = &userAchievements[i]
	}

	tx := s.DB.Begin()
	if tx.Error != nil {
		slog.ErrorContext(ctx, "Failed to begin transaction for achievements update", "error", tx.Error)
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		} else if err := tx.Error; err != nil {
			slog.ErrorContext(ctx, "Rolling back transaction due to error", "error", err)
			tx.Rollback()
		}
	}()

	for _, achievement := range allAchievements {
		ua, exists := userAchievementMap[achievement.ID]
		if !exists {
			ua = &entity.UserAchievement{
				ID:            uuid.New(),
				UserID:        userID,
				AchievementID: achievement.ID,
				Progress:      0,
				IsUnlocked:    false,
				RewardClaimed: false,
			}
			createdUA, err := s.userAchievementRepo.Create(ctx, *ua)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to create new user achievement", "userID", userID, "achievementID", achievement.ID, "error", err)
				tx.Rollback()
				return fmt.Errorf("failed to create user achievement for %s: %w", userID, err)
			}
			*ua = createdUA
		} else {
			if ua.IsUnlocked {
				continue
			}
		}

		currentProgress := 0
		if !ua.IsUnlocked && userStats.ID != uuid.Nil {
			switch achievement.Type {
			case entity.AchievementTypeStreak:
				currentProgress = userStats.CurrentStreak
			case entity.AchievementTypeVocabulary:
				currentProgress = userStats.VocabularyCount
			case entity.AchievementTypeListening:
				currentProgress = userStats.ListeningCount
			case entity.AchievementTypeSpeaking:
				currentProgress = userStats.SpeakingCount
			case entity.AchievementTypeGrammar:
				currentProgress = userStats.GrammarCount
			case entity.AchievementTypeChallenge:
				currentProgress = userStats.ChallengesCompleted
			case entity.AchievementTypeSocial:
				currentProgress = userStats.HelpedUsers
			}
		}

		if achievement.Type == entity.AchievementTypeLessons {
			completedLessons, err := s.lessonProgressRepo.GetCompletedLessons(ctx, userID)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to get completed lessons", "userID", userID, "error", err)
				tx.Rollback()
				return fmt.Errorf("failed to get completed lessons for user %s: %w", userID, err)
			}
			currentProgress = len(completedLessons)
		}

		if currentProgress > ua.Progress {
			ua.Progress = currentProgress

			if ua.Progress >= achievement.Requirement {
				ua.IsUnlocked = true
				now := time.Now()
				ua.UnlockedDate = now
				ua.UpdatedAt = now
				slog.InfoContext(ctx, "Achievement unlocked!", "userID", userID, "achievementID", achievement.ID, "title", achievement.Title)
			}

			updatedUA, err := s.userAchievementRepo.Update(ctx, *ua)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to update user achievement progress", "userID", userID, "achievementID", achievement.ID, "error", err)
				tx.Rollback()
				return fmt.Errorf("failed to update user achievement for %s: %w", userID, err)
			}
			*ua = updatedUA
		}
	}

	if err := tx.Commit().Error; err != nil {
		slog.ErrorContext(ctx, "Failed to commit transaction for achievements update", "error", err)
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// ClaimAchievementReward is a helper function to claim a reward
func (s *AchievementService) ClaimAchievementReward(userID, achievementID uuid.UUID) (gin.H, error) {
	return s.ClaimAchievement(context.Background(), userID, achievementID)
}

// UpdateUserAchievementProgress updates the progress for a specific user achievement
func (s *AchievementService) UpdateUserAchievementProgress(ctx context.Context, userID, achievementID uuid.UUID, progress int) error {
	userAchievement, err := s.userAchievementRepo.FindByUserAndAchievementID(ctx, userID, achievementID)
	if err != nil {
		return err
	}
	userAchievement.Progress = progress
	if _, err := s.userAchievementRepo.Update(ctx, userAchievement); err != nil {
		return err
	}
	return nil
}

// UpdateUserAchievementsForType checks and updates all achievements of a specific type for a user
func (s *AchievementService) UpdateUserAchievementsForType(ctx context.Context, userID uuid.UUID, achievementType entity.AchievementType, currentProgress int) error {
	userAchievements, err := s.userAchievementRepo.FindByUserIDAndType(ctx, userID, achievementType)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get user achievements by type", "userID", userID, "type", achievementType, "error", err)
		return fmt.Errorf("failed to get user achievements for user %s, type %s: %w", userID, achievementType, err)
	}

	for _, ua := range userAchievements {
		if ua.IsUnlocked {
			continue
		}
		baseAchievement, err := s.achievementRepo.FindByID(ctx, ua.AchievementID)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to find base achievement for user achievement update", "achievementID", ua.AchievementID, "error", err)
			continue
		}

		if currentProgress > ua.Progress {
			uaToUpdate := ua
			uaToUpdate.Progress = currentProgress

			if uaToUpdate.Progress >= baseAchievement.Requirement {
				uaToUpdate.IsUnlocked = true
				now := time.Now()
				uaToUpdate.UnlockedDate = now
				uaToUpdate.UpdatedAt = now
				slog.InfoContext(ctx, "Achievement unlocked!", "userID", userID, "achievementID", baseAchievement.ID, "title", baseAchievement.Title)
			}

			if _, err := s.userAchievementRepo.Update(ctx, uaToUpdate); err != nil {
				slog.ErrorContext(ctx, "Failed to update user achievement progress", "userAchievementID", uaToUpdate.ID, "error", err)
				return fmt.Errorf("failed to update user achievement %s: %w", uaToUpdate.ID, err)
			}
		}
	}
	return nil
}
