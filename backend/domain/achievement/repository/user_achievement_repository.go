package repository

import (
	"context"

	"languagelearning/models"

	"github.com/google/uuid"
)

// UserAchievementRepository defines the interface for user achievement data operations
type UserAchievementRepository interface {
	// FindByUserID finds all user achievements for a specific user
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.UserAchievement, error)

	// FindByUserAndAchievementID finds a specific user achievement
	FindByUserAndAchievementID(ctx context.Context, userID, achievementID uuid.UUID) (*models.UserAchievement, error)

	// Create creates a new user achievement
	Create(ctx context.Context, userAchievement *models.UserAchievement) error

	// Update updates an existing user achievement
	Update(ctx context.Context, userAchievement *models.UserAchievement) error

	// FindByUserIDAndType finds user achievements for a user and achievement type
	FindByUserIDAndType(ctx context.Context, userID uuid.UUID, achievementType models.AchievementType) ([]models.UserAchievement, error)
} 