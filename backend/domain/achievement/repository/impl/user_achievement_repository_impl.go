package impl

import (
	"context"
	"languagelearning/domain/user/entity"
	domainUserRepo "languagelearning/domain/user/repository"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type userAchievementRepository struct {
	db *gorm.DB
}

// NewUserAchievementRepository creates a new UserAchievementRepository implementation
func NewUserAchievementRepository(db *gorm.DB) domainUserRepo.UserAchievementRepository {
	return &userAchievementRepository{db: db}
}

// FindByUserID finds all user achievements for a specific user
func (r *userAchievementRepository) FindByUserID(ctx context.Context, userID uuid.UUID) ([]entity.UserAchievement, error) {
	var userAchievements []entity.UserAchievement
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&userAchievements).Error; err != nil {
		return nil, err
	}
	return userAchievements, nil
}

// FindByUserAndAchievementID finds a specific user achievement
func (r *userAchievementRepository) FindByUserAndAchievementID(ctx context.Context, userID, achievementID uuid.UUID) (entity.UserAchievement, error) {
	var userAchievement entity.UserAchievement
	if result := r.db.WithContext(ctx).Where("user_id = ? AND achievement_id = ?", userID, achievementID).First(&userAchievement); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return entity.UserAchievement{}, result.Error
		}
		return entity.UserAchievement{}, result.Error
	}
	return userAchievement, nil
}

// Create creates a new user achievement
func (r *userAchievementRepository) Create(ctx context.Context, userAchievement entity.UserAchievement) (entity.UserAchievement, error) {
	if userAchievement.ID == uuid.Nil {
		userAchievement.ID = uuid.New()
	}
	if err := r.db.WithContext(ctx).Create(&userAchievement).Error; err != nil {
		return entity.UserAchievement{}, err
	}
	return userAchievement, nil
}

// Update updates an existing user achievement
func (r *userAchievementRepository) Update(ctx context.Context, userAchievement entity.UserAchievement) (entity.UserAchievement, error) {
	if err := r.db.WithContext(ctx).Save(&userAchievement).Error; err != nil {
		return entity.UserAchievement{}, err
	}
	return userAchievement, nil
}

// FindByUserIDAndType finds user achievements for a user and achievement type
func (r *userAchievementRepository) FindByUserIDAndType(ctx context.Context, userID uuid.UUID, achievementType entity.AchievementType) ([]entity.UserAchievement, error) {
	var userAchievements []entity.UserAchievement
	if err := r.db.WithContext(ctx).Where("user_id = ? AND achievement_type = ?", userID, achievementType).Find(&userAchievements).Error; err != nil {
		return nil, err
	}
	return userAchievements, nil
}

// FindByID finds a user achievement by its ID (newly added to conform to interface)
func (r *userAchievementRepository) FindByID(ctx context.Context, id uuid.UUID) (entity.UserAchievement, error) {
	var userAchievement entity.UserAchievement
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&userAchievement).Error; err != nil {
		return entity.UserAchievement{}, err
	}
	return userAchievement, nil
}

// Delete deletes a user achievement by its ID (newly added to conform to interface)
func (r *userAchievementRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&entity.UserAchievement{}, "id = ?", id).Error; err != nil {
		return err
	}
	return nil
}
