package impl

import (
	"context"
	"languagelearning/domain/user/entity"
	domainUserRepo "languagelearning/domain/user/repository"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type achievementRepository struct {
	db *gorm.DB
}

// NewAchievementRepository creates a new AchievementRepository implementation
func NewAchievementRepository(db *gorm.DB) domainUserRepo.AchievementRepository {
	return &achievementRepository{db: db}
}

// Create creates a new achievement in the database
func (r *achievementRepository) Create(ctx context.Context, achievement entity.Achievement) (entity.Achievement, error) {
	if err := r.db.WithContext(ctx).Create(&achievement).Error; err != nil {
		return entity.Achievement{}, err
	}
	return achievement, nil
}

// FindByID finds an achievement by its ID
func (r *achievementRepository) FindByID(ctx context.Context, id uuid.UUID) (entity.Achievement, error) {
	var achievementModel entity.Achievement
	if result := r.db.WithContext(ctx).Where("id = ?", id).First(&achievementModel); result.Error != nil {
		return entity.Achievement{}, result.Error
	}
	return achievementModel, nil
}

// FindAll finds all achievements
func (r *achievementRepository) FindAll(ctx context.Context) ([]entity.Achievement, error) {
	var achievements []entity.Achievement
	if err := r.db.WithContext(ctx).Find(&achievements).Error; err != nil {
		return nil, err
	}
	return achievements, nil
}

// Update updates an existing achievement in the database
func (r *achievementRepository) Update(ctx context.Context, achievement entity.Achievement) (entity.Achievement, error) {
	if err := r.db.WithContext(ctx).Save(&achievement).Error; err != nil {
		return entity.Achievement{}, err
	}
	return achievement, nil
}

// Delete deletes an achievement by its ID
func (r *achievementRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&entity.Achievement{}, "id = ?", id).Error; err != nil {
		return err
	}
	return nil
}
