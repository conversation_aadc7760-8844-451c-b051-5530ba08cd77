package di

import (
	"go.uber.org/dig"
	achievementSvcImpl "languagelearning/domain/achievement/service/impl"
	coredi "languagelearning/domain/core/di"
)

// AchievementModule 成就領域的DI模塊
type AchievementModule struct {
	coredi.BaseModule
}

// NewAchievementModule 創建成就領域模塊
func NewAchievementModule() coredi.Module {
	return &AchievementModule{
		BaseModule: coredi.NewBaseModule(
			"achievement",
			[]string{"core", "user"}, // 依賴核心模塊和用戶模塊
			[]coredi.ModuleFeature{coredi.FeatureBasic},
		),
	}
}

// Register 註冊成就領域的所有依賴
func (m *AchievementModule) Register(container *dig.Container) error {
	// 註冊成就服務
	if err := container.Provide(achievementSvcImpl.NewAchievementService); err != nil {
		return err
	}

	return nil
}
