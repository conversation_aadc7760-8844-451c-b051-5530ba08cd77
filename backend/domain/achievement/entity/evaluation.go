package entity

import (
	"time"

	"languagelearning/domain/learning/entity" // Import for Duration and Score types

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// EvaluationType 評估類型
type EvaluationType string

const (
	// EvalPlacement 分級評估
	EvalPlacement EvaluationType = "placement"
	// EvalProgress 進度評估
	EvalProgress EvaluationType = "progress"
	// EvalSkill 技能評估
	EvalSkill EvaluationType = "skill"
	// EvalCertificate 證書評估
	EvalCertificate EvaluationType = "certificate"
)

// Evaluation 評估實體
type Evaluation struct {
	ID             uuid.UUID        `json:"id"`
	Type           EvaluationType   `json:"type"`
	Title          string           `json:"title"`
	Description    string           `json:"description"`
	TotalQuestions int              `json:"totalQuestions"`
	PassingScore   int              `json:"passingScore"` // 百分比
	Duration       *entity.Duration `json:"duration"`     // 分鐘
	IsCompleted    bool             `json:"isCompleted"`
	StartedAt      time.Time        `json:"startedAt,omitempty"`
	CompletedAt    time.Time        `json:"completedAt,omitempty"`
	Score          *entity.Score    `json:"score,omitempty"`
	Sections       []EvalSection    `json:"sections"`
	CreatedAt      time.Time        `json:"createdAt"`
	UpdatedAt      time.Time        `json:"updatedAt"`
}

// NewEvaluation 創建新評估
func NewEvaluation(evalType EvaluationType, title, description string, totalQuestions, passingScore int, durationMinutes int) (*Evaluation, error) {
	duration, err := entity.NewDuration(durationMinutes)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	return &Evaluation{
		ID:             uuid.New(),
		Type:           evalType,
		Title:          title,
		Description:    description,
		TotalQuestions: totalQuestions,
		PassingScore:   passingScore,
		Duration:       duration,
		IsCompleted:    false,
		CreatedAt:      now,
		UpdatedAt:      now,
	}, nil
}

// Start 開始評估
func (e *Evaluation) Start() {
	if !e.IsCompleted {
		e.StartedAt = time.Now()
		e.UpdatedAt = time.Now()
	}
}

// Complete 完成評估
func (e *Evaluation) Complete(score *entity.Score) error {
	if e.IsCompleted {
		return nil
	}

	e.Score = score
	e.IsCompleted = true
	e.CompletedAt = time.Now()
	e.UpdatedAt = time.Now()
	return nil
}

// EvalSection 評估章節
type EvalSection struct {
	ID           uuid.UUID      `json:"id"`
	EvaluationID uuid.UUID      `json:"evaluationId"`
	Title        string         `json:"title"`
	Skill        string         `json:"skill"`  // vocabulary, grammar, etc.
	Weight       int            `json:"weight"` // 百分比權重
	Questions    []EvalQuestion `json:"questions"`
	Score        *entity.Score  `json:"score,omitempty"`
}

// NewEvalSection 創建新評估章節
func NewEvalSection(evaluationID uuid.UUID, title, skill string, weight int) *EvalSection {
	return &EvalSection{
		ID:           uuid.New(),
		EvaluationID: evaluationID,
		Title:        title,
		Skill:        skill,
		Weight:       weight,
	}
}

// UpdateScore 更新分數
func (s *EvalSection) UpdateScore(score *entity.Score) {
	s.Score = score
}

// EvalQuestion 評估問題
type EvalQuestion struct {
	ID            uuid.UUID      `json:"id"`
	SectionID     uuid.UUID      `json:"sectionId"`
	Type          string         `json:"type"` // multiple-choice, fill-in, etc.
	Content       string         `json:"content"`
	Options       pq.StringArray `json:"options,omitempty"`
	CorrectAnswer string         `json:"correctAnswer"`
	UserAnswer    string         `json:"userAnswer,omitempty"`
	IsCorrect     bool           `json:"isCorrect,omitempty"`
	Points        int            `json:"points"`
}

// NewEvalQuestion 創建新評估問題
func NewEvalQuestion(sectionID uuid.UUID, questionType, content, correctAnswer string, points int) *EvalQuestion {
	return &EvalQuestion{
		ID:            uuid.New(),
		SectionID:     sectionID,
		Type:          questionType,
		Content:       content,
		CorrectAnswer: correctAnswer,
		Points:        points,
	}
}

// SubmitAnswer 提交答案
func (q *EvalQuestion) SubmitAnswer(answer string) {
	q.UserAnswer = answer
	q.IsCorrect = answer == q.CorrectAnswer
}

// EvaluationResult 評估結果
type EvaluationResult struct {
	ID              uuid.UUID      `json:"id"`
	EvaluationID    uuid.UUID      `json:"evaluationId"`
	UserID          uuid.UUID      `json:"userId"`
	OverallScore    *entity.Score  `json:"overallScore"`
	PassingScore    int            `json:"passingScore"` // 百分比
	IsPassed        bool           `json:"isPassed"`
	CompletedAt     time.Time      `json:"completedAt"`
	SectionScores   []SectionScore `json:"sectionScores"`
	Feedback        string         `json:"feedback"`
	Recommendations pq.StringArray `json:"recommendations"`
	CreatedAt       time.Time      `json:"createdAt"`
	UpdatedAt       time.Time      `json:"updatedAt"`
}

// NewEvaluationResult 創建新評估結果
func NewEvaluationResult(evaluationID, userID uuid.UUID, passingScore int) *EvaluationResult {
	now := time.Now()
	return &EvaluationResult{
		ID:           uuid.New(),
		EvaluationID: evaluationID,
		UserID:       userID,
		PassingScore: passingScore,
		CompletedAt:  now,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// UpdateOverallScore 更新總分
func (r *EvaluationResult) UpdateOverallScore(score *entity.Score) {
	r.OverallScore = score
	r.IsPassed = score.Percentage() >= float64(r.PassingScore)
	r.UpdatedAt = time.Now()
}

// AddSectionScore 添加章節分數
func (r *EvaluationResult) AddSectionScore(sectionScore SectionScore) {
	r.SectionScores = append(r.SectionScores, sectionScore)
	r.UpdatedAt = time.Now()
}

// SectionScore 章節分數
type SectionScore struct {
	ID           uuid.UUID      `json:"id"`
	ResultID     uuid.UUID      `json:"resultId"`
	SectionTitle string         `json:"sectionTitle"`
	Skill        string         `json:"skill"`
	Score        *entity.Score  `json:"score"`
	Strengths    pq.StringArray `json:"strengths,omitempty"`
	Weaknesses   pq.StringArray `json:"weaknesses,omitempty"`
}

// NewSectionScore 創建新章節分數
func NewSectionScore(resultID uuid.UUID, sectionTitle, skill string, score *entity.Score) *SectionScore {
	return &SectionScore{
		ID:           uuid.New(),
		ResultID:     resultID,
		SectionTitle: sectionTitle,
		Skill:        skill,
		Score:        score,
	}
}
