package entity

import (
	"time"

	"github.com/google/uuid"
)

// AchievementType 成就類型
type AchievementType string

const (
	// AchStreak 連續學習成就
	AchStreak AchievementType = "streak"
	// AchVocabulary 詞彙量成就
	AchVocabulary AchievementType = "vocabulary"
	// AchListening 聽力練習成就
	AchListening AchievementType = "listening"
	// AchSpeaking 口語練習成就
	AchSpeaking AchievementType = "speaking"
	// AchLessons 課程完成成就
	AchLessons AchievementType = "lessons"
	// AchPoints 積分成就
	AchPoints AchievementType = "points"
	// AchChallenges 挑戰成就
	AchChallenges AchievementType = "challenges"
	// AchSocial 社交成就
	AchSocial AchievementType = "social"
)

// Achievement 成就實體
type Achievement struct {
	ID          uuid.UUID       `json:"id"`
	Type        AchievementType `json:"type"`
	Title       string          `json:"title"`
	Description string          `json:"description"`
	Icon        string          `json:"icon"`
	Color       string          `json:"color"`
	Requirement int             `json:"requirement"`
	Reward      int             `json:"reward"`
	CreatedAt   time.Time       `json:"createdAt"`
	UpdatedAt   time.Time       `json:"updatedAt"`
}

// NewAchievement 創建新成就
func NewAchievement(achievementType AchievementType, title, description, icon, color string, requirement, reward int) *Achievement {
	now := time.Now()
	return &Achievement{
		ID:          uuid.New(),
		Type:        achievementType,
		Title:       title,
		Description: description,
		Icon:        icon,
		Color:       color,
		Requirement: requirement,
		Reward:      reward,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// UserAchievement 用戶成就實體
type UserAchievement struct {
	ID            uuid.UUID       `json:"id"`
	UserID        uuid.UUID       `json:"userId"`
	AchievementID uuid.UUID       `json:"achievementId"`
	Type          AchievementType `json:"type"`
	Progress      int             `json:"progress"`
	IsUnlocked    bool            `json:"isUnlocked"`
	RewardClaimed bool            `json:"rewardClaimed"`
	UnlockedDate  time.Time       `json:"unlockedDate,omitempty"`
	Reward        int             `json:"reward"`
	CreatedAt     time.Time       `json:"createdAt"`
	UpdatedAt     time.Time       `json:"updatedAt"`
}

// NewUserAchievement 創建新用戶成就
func NewUserAchievement(userID, achievementID uuid.UUID, achievementType AchievementType, reward int) *UserAchievement {
	now := time.Now()
	return &UserAchievement{
		ID:            uuid.New(),
		UserID:        userID,
		AchievementID: achievementID,
		Type:          achievementType,
		Progress:      0,
		IsUnlocked:    false,
		RewardClaimed: false,
		Reward:        reward,
		CreatedAt:     now,
		UpdatedAt:     now,
	}
}

// UpdateProgress 更新進度
func (ua *UserAchievement) UpdateProgress(progress int) {
	ua.Progress = progress
	ua.UpdatedAt = time.Now()
}

// Unlock 解鎖成就
func (ua *UserAchievement) Unlock() {
	if !ua.IsUnlocked {
		ua.IsUnlocked = true
		ua.UnlockedDate = time.Now()
		ua.UpdatedAt = time.Now()
	}
}

// ClaimReward 領取獎勵
func (ua *UserAchievement) ClaimReward() {
	if ua.IsUnlocked && !ua.RewardClaimed {
		ua.RewardClaimed = true
		ua.UpdatedAt = time.Now()
	}
}

// IsCompleted 判斷是否完成
func (ua *UserAchievement) IsCompleted() bool {
	return ua.IsUnlocked && ua.RewardClaimed
}
