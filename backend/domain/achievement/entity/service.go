package entity

import (
	"context"

	"github.com/google/uuid"
)

// AchievementRepository 成就倉庫接口
type AchievementRepository interface {
	Create(ctx context.Context, achievement *Achievement) error
	FindByID(ctx context.Context, id uuid.UUID) (*Achievement, error)
	FindByType(ctx context.Context, achievementType AchievementType) ([]*Achievement, error)
	Update(ctx context.Context, achievement *Achievement) error
	Delete(ctx context.Context, id uuid.UUID) error
}

// UserAchievementRepository 用戶成就倉庫接口
type UserAchievementRepository interface {
	Create(ctx context.Context, userAchievement *UserAchievement) error
	FindByID(ctx context.Context, id uuid.UUID) (*UserAchievement, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]*UserAchievement, error)
	FindByUserIDAndType(ctx context.Context, userID uuid.UUID, achievementType AchievementType) ([]*UserAchievement, error)
	Update(ctx context.Context, userAchievement *UserAchievement) error
	Delete(ctx context.Context, id uuid.UUID) error
}

// AchievementService 成就服務接口
type AchievementService interface {
	UnlockAchievement(ctx context.Context, userID, achievementID uuid.UUID) error
	GetUserAchievements(ctx context.Context, userID uuid.UUID) ([]*UserAchievement, error)
	GetUserAchievementProgress(ctx context.Context, userID uuid.UUID, achievementType AchievementType) ([]*UserAchievement, error)
	ClaimAchievementReward(ctx context.Context, userID, achievementID uuid.UUID) error
	LevelUp(ctx context.Context, userID uuid.UUID, oldLevel, newLevel int) error
}
