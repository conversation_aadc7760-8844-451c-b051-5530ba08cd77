package event

import (
	"fmt"
	"time"

	"languagelearning/domain/achievement/entity"
	coreevent "languagelearning/domain/core/event"

	"github.com/google/uuid"
)

// AchievementEventReconstructor 成就事件重構器
type AchievementEventReconstructor struct{}

// NewAchievementEventReconstructor 創建成就事件重構器
func NewAchievementEventReconstructor() *AchievementEventReconstructor {
	return &AchievementEventReconstructor{}
}

// Reconstruct 重構事件
func (r *AchievementEventReconstructor) Reconstruct(eventType string, data map[string]interface{}) (coreevent.Event, error) {
	switch eventType {
	case "achievement.unlocked":
		return r.reconstructAchievementUnlocked(data)
	case "achievement.progress_updated":
		return r.reconstructAchievementProgressUpdated(data)
	case "achievement.reward_claimed":
		return r.reconstructAchievementRewardClaimed(data)
	default:
		return nil, fmt.Errorf("unsupported event type: %s", eventType)
	}
}

// reconstructAchievementUnlocked 重構成就解鎖事件
func (r *AchievementEventReconstructor) reconstructAchievementUnlocked(data map[string]interface{}) (*AchievementUnlockedEvent, error) {
	achievementData, ok := data["achievement"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid achievement data")
	}

	achievementID, err := uuid.Parse(achievementData["id"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid achievement ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	unlockedAt, err := time.Parse(time.RFC3339, data["unlockedAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid unlocked at time: %v", err)
	}

	achievement := &entity.Achievement{
		ID:          achievementID,
		Title:       achievementData["title"].(string),
		Description: achievementData["description"].(string),
		Type:        entity.AchievementType(achievementData["type"].(string)),
		Icon:        achievementData["icon"].(string),
		Color:       achievementData["color"].(string),
		Requirement: int(achievementData["requirement"].(float64)),
		Reward:      int(achievementData["reward"].(float64)),
		CreatedAt:   time.Now(), // 使用當前時間，因為這是重構的事件
		UpdatedAt:   time.Now(),
	}

	return &AchievementUnlockedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"achievement.unlocked",
			achievement.ID,
			"achievement",
			data,
		),
		Achievement: achievement,
		UserID:      userID,
		UnlockedAt:  unlockedAt,
	}, nil
}

// reconstructAchievementProgressUpdated 重構成就進度更新事件
func (r *AchievementEventReconstructor) reconstructAchievementProgressUpdated(data map[string]interface{}) (*AchievementProgressUpdatedEvent, error) {
	achievementID, err := uuid.Parse(data["achievementID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid achievement ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	updatedAt, err := time.Parse(time.RFC3339, data["updatedAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid updated at time: %v", err)
	}

	return &AchievementProgressUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"achievement.progress_updated",
			achievementID,
			"achievement",
			data,
		),
		AchievementID: achievementID,
		UserID:        userID,
		Progress:      int(data["progress"].(float64)),
		IsCompleted:   data["isCompleted"].(bool),
		UpdatedAt:     updatedAt,
	}, nil
}

// reconstructAchievementRewardClaimed 重構成就獎勵領取事件
func (r *AchievementEventReconstructor) reconstructAchievementRewardClaimed(data map[string]interface{}) (*AchievementRewardClaimedEvent, error) {
	achievementID, err := uuid.Parse(data["achievementID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid achievement ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	claimedAt, err := time.Parse(time.RFC3339, data["claimedAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid claimed at time: %v", err)
	}

	return &AchievementRewardClaimedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"achievement.reward_claimed",
			achievementID,
			"achievement",
			data,
		),
		AchievementID: achievementID,
		UserID:        userID,
		ClaimedAt:     claimedAt,
		RewardType:    data["rewardType"].(string),
		RewardAmount:  int(data["rewardAmount"].(float64)),
	}, nil
}
