package event

import (
	"time"

	"languagelearning/domain/achievement/entity"
	coreevent "languagelearning/domain/core/event"

	"github.com/google/uuid"
)

// AchievementUnlockedEvent 成就解鎖事件
type AchievementUnlockedEvent struct {
	*coreevent.BaseEvent
	Achievement *entity.Achievement
	UserID      uuid.UUID
	UnlockedAt  time.Time
}

// NewAchievementUnlockedEvent 創建成就解鎖事件
func NewAchievementUnlockedEvent(achievement *entity.Achievement, userID uuid.UUID) *AchievementUnlockedEvent {
	return &AchievementUnlockedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"achievement.unlocked",
			achievement.ID,
			"achievement",
			map[string]interface{}{
				"achievement": achievement,
				"userID":      userID,
			},
		),
		Achievement: achievement,
		UserID:      userID,
		UnlockedAt:  time.Now(),
	}
}

// AchievementProgressUpdatedEvent 成就進度更新事件
type AchievementProgressUpdatedEvent struct {
	*coreevent.BaseEvent
	AchievementID uuid.UUID
	UserID        uuid.UUID
	Progress      int
	IsCompleted   bool
	UpdatedAt     time.Time
}

// NewAchievementProgressUpdatedEvent 創建成就進度更新事件
func NewAchievementProgressUpdatedEvent(achievementID, userID uuid.UUID, progress int) *AchievementProgressUpdatedEvent {
	isCompleted := progress >= 100
	return &AchievementProgressUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"achievement.progress_updated",
			achievementID,
			"achievement",
			map[string]interface{}{
				"achievementID": achievementID,
				"userID":        userID,
				"progress":      progress,
				"isCompleted":   isCompleted,
			},
		),
		AchievementID: achievementID,
		UserID:        userID,
		Progress:      progress,
		IsCompleted:   isCompleted,
		UpdatedAt:     time.Now(),
	}
}

// AchievementRewardClaimedEvent 成就獎勵領取事件
type AchievementRewardClaimedEvent struct {
	*coreevent.BaseEvent
	AchievementID uuid.UUID
	UserID        uuid.UUID
	ClaimedAt     time.Time
	RewardType    string
	RewardAmount  int
}

// NewAchievementRewardClaimedEvent 創建成就獎勵領取事件
func NewAchievementRewardClaimedEvent(achievementID, userID uuid.UUID, rewardType string, rewardAmount int) *AchievementRewardClaimedEvent {
	return &AchievementRewardClaimedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"achievement.reward_claimed",
			achievementID,
			"achievement",
			map[string]interface{}{
				"achievementID": achievementID,
				"userID":        userID,
				"rewardType":    rewardType,
				"rewardAmount":  rewardAmount,
			},
		),
		AchievementID: achievementID,
		UserID:        userID,
		ClaimedAt:     time.Now(),
		RewardType:    rewardType,
		RewardAmount:  rewardAmount,
	}
}
