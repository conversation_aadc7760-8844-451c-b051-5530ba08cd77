package event

import (
	"fmt"
	"log"

	"languagelearning/domain/core/event"
)

// AchievementUnlockedHandler 成就解鎖事件處理器
type AchievementUnlockedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 通知服務
	// - 獎勵服務
}

// NewAchievementUnlockedHandler 創建成就解鎖事件處理器
func NewAchievementUnlockedHandler() *AchievementUnlockedHandler {
	return &AchievementUnlockedHandler{}
}

// Handle 處理成就解鎖事件
func (h *AchievementUnlockedHandler) Handle(e event.Event) error {
	unlockedEvent, ok := e.(*AchievementUnlockedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶成就統計
	// 2. 發送成就解鎖通知
	// 3. 檢查是否需要解鎖相關成就
	// 4. 更新用戶等級經驗

	log.Printf("Achievement unlocked: %s by user %s, requirement: %d",
		unlockedEvent.Achievement.ID,
		unlockedEvent.UserID,
		unlockedEvent.Achievement.Requirement,
	)
	return nil
}

// AchievementProgressUpdatedHandler 成就進度更新事件處理器
type AchievementProgressUpdatedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 通知服務
}

// NewAchievementProgressUpdatedHandler 創建成就進度更新事件處理器
func NewAchievementProgressUpdatedHandler() *AchievementProgressUpdatedHandler {
	return &AchievementProgressUpdatedHandler{}
}

// Handle 處理成就進度更新事件
func (h *AchievementProgressUpdatedHandler) Handle(e event.Event) error {
	updatedEvent, ok := e.(*AchievementProgressUpdatedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶成就進度
	// 2. 檢查是否需要發送進度通知
	// 3. 更新相關統計數據

	log.Printf("Achievement progress updated: %s for user %s, progress: %d, completed: %v",
		updatedEvent.AchievementID,
		updatedEvent.UserID,
		updatedEvent.Progress,
		updatedEvent.IsCompleted,
	)
	return nil
}

// AchievementRewardClaimedHandler 成就獎勵領取事件處理器
type AchievementRewardClaimedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 獎勵服務
	// - 通知服務
}

// NewAchievementRewardClaimedHandler 創建成就獎勵領取事件處理器
func NewAchievementRewardClaimedHandler() *AchievementRewardClaimedHandler {
	return &AchievementRewardClaimedHandler{}
}

// Handle 處理成就獎勵領取事件
func (h *AchievementRewardClaimedHandler) Handle(e event.Event) error {
	claimedEvent, ok := e.(*AchievementRewardClaimedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 發放獎勵給用戶
	// 2. 更新用戶獎勵統計
	// 3. 發送獎勵領取通知
	// 4. 記錄獎勵領取日誌

	log.Printf("Achievement reward claimed: %s by user %s, type: %s, amount: %d",
		claimedEvent.AchievementID,
		claimedEvent.UserID,
		claimedEvent.RewardType,
		claimedEvent.RewardAmount,
	)
	return nil
}

// RegisterAchievementEventHandlers 註冊所有成就相關的事件處理器
func RegisterAchievementEventHandlers(bus event.EventBus) error {
	// 註冊成就解鎖事件處理器
	unlockedHandler := NewAchievementUnlockedHandler()
	if err := bus.Subscribe("achievement.unlocked", unlockedHandler); err != nil {
		return fmt.Errorf("failed to register achievement unlocked handler: %w", err)
	}

	// 註冊成就進度更新事件處理器
	progressUpdatedHandler := NewAchievementProgressUpdatedHandler()
	if err := bus.Subscribe("achievement.progress_updated", progressUpdatedHandler); err != nil {
		return fmt.Errorf("failed to register achievement progress updated handler: %w", err)
	}

	// 註冊成就獎勵領取事件處理器
	rewardClaimedHandler := NewAchievementRewardClaimedHandler()
	if err := bus.Subscribe("achievement.reward_claimed", rewardClaimedHandler); err != nil {
		return fmt.Errorf("failed to register achievement reward claimed handler: %w", err)
	}

	return nil
}
