package event

import (
	"fmt"
	"log"

	"languagelearning/domain/core/event"
)

// UserRegisteredHandler 用戶註冊事件處理器
type UserRegisteredHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 通知服務
	// - 統計服務
}

// NewUserRegisteredHandler 創建用戶註冊事件處理器
func NewUserRegisteredHandler() *UserRegisteredHandler {
	return &UserRegisteredHandler{}
}

// Handle 處理用戶註冊事件
func (h *UserRegisteredHandler) Handle(e event.Event) error {
	registeredEvent, ok := e.(*UserRegisteredEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 發送歡迎郵件
	// 2. 創建用戶初始學習計劃
	// 3. 初始化用戶統計數據
	// 4. 發送系統通知

	log.Printf("User registered: %s (email: %s)",
		registeredEvent.User.Username,
		registeredEvent.User.Email,
	)
	return nil
}

// UserProfileUpdatedHandler 用戶資料更新事件處理器
type UserProfileUpdatedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 通知服務
	// - 統計服務
}

// NewUserProfileUpdatedHandler 創建用戶資料更新事件處理器
func NewUserProfileUpdatedHandler() *UserProfileUpdatedHandler {
	return &UserProfileUpdatedHandler{}
}

// Handle 處理用戶資料更新事件
func (h *UserProfileUpdatedHandler) Handle(e event.Event) error {
	updatedEvent, ok := e.(*UserProfileUpdatedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶緩存
	// 2. 記錄資料變更日誌
	// 3. 發送資料更新通知
	// 4. 更新相關統計數據

	log.Printf("User profile updated: %s, fields: %v",
		updatedEvent.GetAggregateID(),
		updatedEvent.UpdatedFields,
	)
	return nil
}

// UserLearningStatsUpdatedHandler 用戶學習統計更新事件處理器
type UserLearningStatsUpdatedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 成就服務
	// - 通知服務
}

// NewUserLearningStatsUpdatedHandler 創建用戶學習統計更新事件處理器
func NewUserLearningStatsUpdatedHandler() *UserLearningStatsUpdatedHandler {
	return &UserLearningStatsUpdatedHandler{}
}

// Handle 處理用戶學習統計更新事件
func (h *UserLearningStatsUpdatedHandler) Handle(e event.Event) error {
	updatedEvent, ok := e.(*UserLearningStatsUpdatedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶學習進度
	// 2. 檢查成就解鎖條件
	// 3. 更新學習路徑推薦
	// 4. 發送學習進度通知

	log.Printf("User learning stats updated: %s, stats: %v",
		updatedEvent.GetAggregateID(),
		updatedEvent.Stats,
	)
	return nil
}

// RegisterUserEventHandlers 註冊所有用戶相關的事件處理器
func RegisterUserEventHandlers(bus event.EventBus) error {
	// 註冊用戶註冊事件處理器
	registeredHandler := NewUserRegisteredHandler()
	if err := bus.Subscribe("user.registered", registeredHandler); err != nil {
		return fmt.Errorf("failed to register user registered handler: %w", err)
	}

	// 註冊用戶資料更新事件處理器
	profileUpdatedHandler := NewUserProfileUpdatedHandler()
	if err := bus.Subscribe("user.profile_updated", profileUpdatedHandler); err != nil {
		return fmt.Errorf("failed to register user profile updated handler: %w", err)
	}

	// 註冊用戶學習統計更新事件處理器
	statsUpdatedHandler := NewUserLearningStatsUpdatedHandler()
	if err := bus.Subscribe("user.learning_stats_updated", statsUpdatedHandler); err != nil {
		return fmt.Errorf("failed to register user learning stats updated handler: %w", err)
	}

	return nil
}
