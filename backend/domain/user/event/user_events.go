package event

import (
	"time"

	"languagelearning/domain/core/event"
	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/user/entity"

	"github.com/google/uuid"
)

// UserRegisteredEvent 用戶註冊事件
type UserRegisteredEvent struct {
	*coreevent.BaseEvent
	User      *entity.User
	CreatedAt time.Time
}

// NewUserRegisteredEvent 創建用戶註冊事件
func NewUserRegisteredEvent(user *entity.User) *UserRegisteredEvent {
	return &UserRegisteredEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.registered",
			user.ID,
			"user",
			map[string]interface{}{
				"username": user.Username,
				"email":    user.Email,
			},
		),
		User:      user,
		CreatedAt: time.Now(),
	}
}

// UserProfileUpdatedEvent 用戶資料更新事件
type UserProfileUpdatedEvent struct {
	event.BaseEvent
	UpdatedAt     time.Time
	UpdatedFields map[string]interface{}
}

// NewUserProfileUpdatedEvent 創建用戶資料更新事件
func NewUserProfileUpdatedEvent(userID uuid.UUID, updatedFields map[string]interface{}) *UserProfileUpdatedEvent {
	return &UserProfileUpdatedEvent{
		BaseEvent: *event.NewBaseEvent(
			"user.profile_updated",
			userID,
			"user",
			map[string]interface{}{
				"updatedFields": updatedFields,
				"updatedAt":     time.Now(),
			},
		),
		UpdatedAt:     time.Now(),
		UpdatedFields: updatedFields,
	}
}

// UserLearningStatsUpdatedEvent 用戶學習統計更新事件
type UserLearningStatsUpdatedEvent struct {
	event.BaseEvent
	UpdatedAt time.Time
	Stats     map[string]interface{}
}

// NewUserLearningStatsUpdatedEvent 創建用戶學習統計更新事件
func NewUserLearningStatsUpdatedEvent(userID uuid.UUID, stats map[string]interface{}) *UserLearningStatsUpdatedEvent {
	return &UserLearningStatsUpdatedEvent{
		BaseEvent: *event.NewBaseEvent(
			"user.learning_stats_updated",
			userID,
			"user",
			map[string]interface{}{
				"stats":     stats,
				"updatedAt": time.Now(),
			},
		),
		UpdatedAt: time.Now(),
		Stats:     stats,
	}
}

// UserLearningPreferenceUpdatedEvent 用戶學習偏好更新事件
type UserLearningPreferenceUpdatedEvent struct {
	*coreevent.BaseEvent
	UserID     uuid.UUID
	UpdatedAt  time.Time
	Preference *entity.LearningPreference
}

// NewUserLearningPreferenceUpdatedEvent 創建用戶學習偏好更新事件
func NewUserLearningPreferenceUpdatedEvent(userID uuid.UUID, preference *entity.LearningPreference) *UserLearningPreferenceUpdatedEvent {
	return &UserLearningPreferenceUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.learning_preference_updated",
			userID,
			"user",
			map[string]interface{}{
				"preference": preference,
			},
		),
		UserID:     userID,
		UpdatedAt:  time.Now(),
		Preference: preference,
	}
}

// UserLevelUpEvent 用戶等級提升事件
type UserLevelUpEvent struct {
	*coreevent.BaseEvent
	UserID    uuid.UUID
	OldLevel  int
	NewLevel  int
	UpdatedAt time.Time
}

// NewUserLevelUpEvent 創建用戶等級提升事件
func NewUserLevelUpEvent(userID uuid.UUID, oldLevel, newLevel int) *UserLevelUpEvent {
	return &UserLevelUpEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.level_up",
			userID,
			"user",
			map[string]interface{}{
				"oldLevel": oldLevel,
				"newLevel": newLevel,
			},
		),
		UserID:    userID,
		OldLevel:  oldLevel,
		NewLevel:  newLevel,
		UpdatedAt: time.Now(),
	}
}

// UserWordLearnedEvent 用戶詞彙學習事件
type UserWordLearnedEvent struct {
	*coreevent.BaseEvent
	UserID    uuid.UUID
	WordID    uuid.UUID
	LearnedAt time.Time
}

// NewUserWordLearnedEvent 創建用戶詞彙學習事件
func NewUserWordLearnedEvent(userID, wordID uuid.UUID) *UserWordLearnedEvent {
	return &UserWordLearnedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.word_learned",
			userID,
			"user",
			map[string]interface{}{
				"wordID":    wordID,
				"learnedAt": time.Now(),
			},
		),
		UserID:    userID,
		WordID:    wordID,
		LearnedAt: time.Now(),
	}
}

// UserWordFavoriteToggledEvent 用戶詞彙收藏狀態切換事件
type UserWordFavoriteToggledEvent struct {
	*coreevent.BaseEvent
	UserID     uuid.UUID
	WordID     uuid.UUID
	IsFavorite bool
	UpdatedAt  time.Time
}

// NewUserWordFavoriteToggledEvent 創建用戶詞彙收藏狀態切換事件
func NewUserWordFavoriteToggledEvent(userID, wordID uuid.UUID, isFavorite bool) *UserWordFavoriteToggledEvent {
	return &UserWordFavoriteToggledEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.word_favorite_toggled",
			userID,
			"user",
			map[string]interface{}{
				"wordID":     wordID,
				"isFavorite": isFavorite,
				"updatedAt":  time.Now(),
			},
		),
		UserID:     userID,
		WordID:     wordID,
		IsFavorite: isFavorite,
		UpdatedAt:  time.Now(),
	}
}

// PasswordResetEmailEvent 密碼重置郵件事件
type PasswordResetEmailEvent struct {
	*coreevent.BaseEvent
	UserID    uuid.UUID
	Email     string
	Username  string
	Data      map[string]interface{}
	CreatedAt time.Time
}

// NewPasswordResetEmailEvent 創建密碼重置郵件事件
func NewPasswordResetEmailEvent(userID uuid.UUID, email, username string, data map[string]interface{}) *PasswordResetEmailEvent {
	return &PasswordResetEmailEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.password_reset_email",
			userID,
			"user",
			map[string]interface{}{
				"email":     email,
				"username":  username,
				"data":      data,
				"createdAt": time.Now(),
			},
		),
		UserID:    userID,
		Email:     email,
		Username:  username,
		Data:      data,
		CreatedAt: time.Now(),
	}
}
