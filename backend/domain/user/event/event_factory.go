package event

import (
	"fmt"
	"time"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/user/entity"

	"github.com/google/uuid"
)

// UserEventReconstructor 用戶事件重構器
type UserEventReconstructor struct{}

// NewUserEventReconstructor 創建用戶事件重構器
func NewUserEventReconstructor() *UserEventReconstructor {
	return &UserEventReconstructor{}
}

// Reconstruct 重構事件
func (r *UserEventReconstructor) Reconstruct(eventType string, data map[string]interface{}) (coreevent.Event, error) {
	switch eventType {
	case "user.registered":
		return r.reconstructUserRegistered(data)
	case "user.learning_preference_updated":
		return r.reconstructUserLearningPreferenceUpdated(data)
	case "user.level_up":
		return r.reconstructUserLevelUp(data)
	default:
		return nil, fmt.Errorf("unsupported event type: %s", eventType)
	}
}

// reconstructUserRegistered 重構用戶註冊事件
func (r *UserEventReconstructor) reconstructUserRegistered(data map[string]interface{}) (*UserRegisteredEvent, error) {
	userData, ok := data["user"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid user data")
	}

	userID, err := uuid.Parse(userData["id"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	createdAt, err := time.Parse(time.RFC3339, userData["createdAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid created at time: %v", err)
	}

	user := &entity.User{
		ID:       userID,
		Username: userData["username"].(string),
		Email:    userData["email"].(string),
	}

	return &UserRegisteredEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.registered",
			user.ID,
			"user",
			data,
		),
		User:      user,
		CreatedAt: createdAt,
	}, nil
}

// reconstructUserLearningPreferenceUpdated 重構用戶學習偏好更新事件
func (r *UserEventReconstructor) reconstructUserLearningPreferenceUpdated(data map[string]interface{}) (*UserLearningPreferenceUpdatedEvent, error) {
	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	preferenceData, ok := data["preference"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid preference data")
	}

	updatedAt, err := time.Parse(time.RFC3339, preferenceData["updatedAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid updated at time: %v", err)
	}

	preference := &entity.LearningPreference{
		ID:              uuid.MustParse(preferenceData["id"].(string)),
		UserID:          userID,
		TargetLanguage:  preferenceData["targetLanguage"].(string),
		NativeLanguage:  preferenceData["nativeLanguage"].(string),
		DailyGoal:       int(preferenceData["dailyGoal"].(float64)),
		DifficultyLevel: preferenceData["difficultyLevel"].(string),
	}

	return &UserLearningPreferenceUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.learning_preference_updated",
			userID,
			"user",
			data,
		),
		UserID:     userID,
		Preference: preference,
		UpdatedAt:  updatedAt,
	}, nil
}

// reconstructUserLevelUp 重構用戶等級提升事件
func (r *UserEventReconstructor) reconstructUserLevelUp(data map[string]interface{}) (*UserLevelUpEvent, error) {
	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	updatedAt, err := time.Parse(time.RFC3339, data["updatedAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid updated at time: %v", err)
	}

	return &UserLevelUpEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"user.level_up",
			userID,
			"user",
			data,
		),
		UserID:    userID,
		OldLevel:  int(data["oldLevel"].(float64)),
		NewLevel:  int(data["newLevel"].(float64)),
		UpdatedAt: updatedAt,
	}, nil
}
