package service

import (
	"context"
	"languagelearning/domain/user/entity"

	"github.com/google/uuid"
)

// UserService defines the interface for user-related operations
type UserService interface {
	// Core user operations
	GetUserByID(ctx context.Context, id uuid.UUID) (*entity.User, error)
	GetUserByUsername(ctx context.Context, username string) (*entity.User, error)
	GetUserByEmail(ctx context.Context, email string) (*entity.User, error)
	CreateUser(ctx context.Context, username, email, password string) (*entity.User, error)
	UpdateUser(ctx context.Context, user *entity.User) error
	ChangePassword(ctx context.Context, userID uuid.UUID, currentPassword, newPassword string) error
	DeactivateUser(ctx context.Context, userID uuid.UUID) error
	ActivateUser(ctx context.Context, userID uuid.UUID) error
	VerifyEmail(ctx context.Context, userID uuid.UUID) error

	// Profile operations
	GetUserProfile(ctx context.Context, userID uuid.UUID) (*entity.UserProfile, error)
	UpdateUserProfile(ctx context.Context, userID uuid.UUID, profile *entity.UserProfile) (*entity.UserProfile, error)

	// Settings operations
	GetUserSettings(ctx context.Context, userID uuid.UUID) (*entity.UserSettings, error)
	UpdateUserSettings(ctx context.Context, userID uuid.UUID, settings *entity.UserSettings) (*entity.UserSettings, error)

	// Statistics operations
	GetUserStats(ctx context.Context, userID uuid.UUID) (*entity.UserStats, error)
	UpdateUserStats(ctx context.Context, userID uuid.UUID, stats *entity.UserStats) error
	GetLearningStreak(ctx context.Context, userID uuid.UUID) (int, error)
	GetProgressReport(ctx context.Context, userID uuid.UUID, period string) (*entity.UserStats, error)

	// Achievement operations
	GetUserAchievements(ctx context.Context, userID uuid.UUID) ([]*entity.UserAchievement, error)
	GetAchievementProgress(ctx context.Context, userID uuid.UUID, achievementID uuid.UUID) (*entity.UserAchievement, error)
	UpdateAchievementProgress(ctx context.Context, userID uuid.UUID, achievementID uuid.UUID, progress int) (*entity.UserAchievement, error)
	ClaimAchievementReward(ctx context.Context, userID uuid.UUID, achievementID uuid.UUID) error

	// Notification operations
	GetUserNotifications(ctx context.Context, userID uuid.UUID, status entity.NotificationStatus, limit, offset int) ([]*entity.Notification, error)
	GetNotificationPreferences(ctx context.Context, userID uuid.UUID) (*entity.NotificationPreference, error)
	UpdateNotificationPreferences(ctx context.Context, userID uuid.UUID, preferences *entity.NotificationPreference) (*entity.NotificationPreference, error)
	MarkNotificationAsRead(ctx context.Context, userID uuid.UUID, notificationID uuid.UUID) error
	ArchiveNotification(ctx context.Context, userID uuid.UUID, notificationID uuid.UUID) error
	CreateNotification(ctx context.Context, userID uuid.UUID, notificationType entity.NotificationType, title, message string, data map[string]interface{}) (*entity.Notification, error)

	// Search operations
	SearchUsers(ctx context.Context, query string, offset, limit int) ([]*entity.User, error)

	// GetUserWordList 獲取用戶的詞彙列表
	GetUserWordList(ctx context.Context, userID uuid.UUID) ([]*entity.UserWord, error)

	// GetUserWordStats 獲取用戶的詞彙統計
	GetUserWordStats(ctx context.Context, userID uuid.UUID) (int, int, error)

	// AddUserWord 添加用戶詞彙
	AddUserWord(ctx context.Context, userID, wordID uuid.UUID) (*entity.UserWord, error)

	// RemoveUserWord 移除用戶詞彙
	RemoveUserWord(ctx context.Context, userID, wordID uuid.UUID) error

	// MarkWordAsLearned 標記詞彙為已學習
	MarkWordAsLearned(ctx context.Context, userID, wordID uuid.UUID) error

	// ToggleWordFavorite 切換詞彙收藏狀態
	ToggleWordFavorite(ctx context.Context, userID, wordID uuid.UUID) error

	// GetFavoriteWords 獲取用戶收藏的詞彙
	GetFavoriteWords(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entity.UserWord, error)

	// GetLearnedWords 獲取用戶已學習的詞彙
	GetLearnedWords(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entity.UserWord, error)

	// SearchUserWords 搜索用戶詞彙
	SearchUserWords(ctx context.Context, userID uuid.UUID, query string, offset, limit int) ([]*entity.UserWord, error)

	// DeleteAccount 刪除賬戶
	DeleteAccount(ctx context.Context, userID uuid.UUID, password string) error

	// FollowUser 關注用戶
	FollowUser(ctx context.Context, followerID, followedID uuid.UUID) error

	// UnfollowUser 取消關注
	UnfollowUser(ctx context.Context, followerID, followedID uuid.UUID) error

	// GetFollowers 獲取粉絲列表
	GetFollowers(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entity.User, error)

	// GetFollowing 獲取關注列表
	GetFollowing(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entity.User, error)

	// GetUser 獲取用戶信息
	GetUser(ctx context.Context, userID uuid.UUID) (*entity.User, error)

	// Login 用戶登錄
	Login(ctx context.Context, email, password string) (*entity.User, error)

	// Logout 用戶登出
	Logout(ctx context.Context, userID uuid.UUID) error

	// RefreshToken 刷新訪問令牌
	RefreshToken(ctx context.Context, refreshToken string) (string, string, error)

	// Register 註冊新用戶
	Register(ctx context.Context, username, email, password string) (*entity.User, error)

	// ResetPassword 重置密碼
	ResetPassword(ctx context.Context, email string) error

	// UpdateAvatar 更新用戶頭像
	UpdateAvatar(ctx context.Context, userID uuid.UUID, avatarURL string) error

	// UpdatePassword 更新密碼
	UpdatePassword(ctx context.Context, userID uuid.UUID, newPassword string) error

	// UpdateSettings 更新用戶設置
	UpdateSettings(ctx context.Context, userID uuid.UUID, settings *entity.UserSettings) error

	// ValidateToken 驗證令牌
	ValidateToken(ctx context.Context, token string) (uuid.UUID, error)

	// VerifyResetToken 驗證重置令牌
	VerifyResetToken(ctx context.Context, token string) (uuid.UUID, error)
}
