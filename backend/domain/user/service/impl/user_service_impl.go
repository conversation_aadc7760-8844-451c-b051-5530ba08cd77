package service

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"

	"languagelearning/domain/core/event"
	"languagelearning/domain/user/entity"
	userevent "languagelearning/domain/user/event"
	"languagelearning/domain/user/repository"
	userService "languagelearning/domain/user/service"
	"languagelearning/middleware"

	"github.com/google/uuid"
)

type userServiceImpl struct {
	userRepo    repository.UserRepository
	profileRepo repository.UserProfileRepository
	statsRepo   repository.UserStatsRepository
	wordRepo    repository.UserWordRepository
	eventPub    event.EventPublisher
	txManager   repository.TransactionManager
}

func NewUserService(
	userRepo repository.UserRepository,
	profileRepo repository.UserProfileRepository,
	statsRepo repository.UserStatsRepository,
	wordRepo repository.UserWordRepository,
	eventPub event.EventPublisher,
	txManager repository.TransactionManager,
) userService.UserService {
	return &userServiceImpl{
		userRepo:    userRepo,
		profileRepo: profileRepo,
		statsRepo:   statsRepo,
		wordRepo:    wordRepo,
		eventPub:    eventPub,
		txManager:   txManager,
	}
}

// Register 註冊新用戶
func (s *userServiceImpl) Register(ctx context.Context, username, email, password string) (*entity.User, error) {
	// 基本输入校验
	if strings.TrimSpace(username) == "" || strings.TrimSpace(email) == "" || strings.TrimSpace(password) == "" {
		return nil, errors.New("username, email and password are required")
	}

	// 检查用户名和邮箱是否已存在（在事务外检查）
	if _, err := s.userRepo.FindByUsername(ctx, username); err == nil {
		return nil, errors.New("username already exists")
	}
	if _, err := s.userRepo.FindByEmail(ctx, email); err == nil {
		return nil, errors.New("email already exists")
	}

	// 创建新用户
	user := entity.NewUser(username, email, password)

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	user.Password = string(hashedPassword)

	var createdUser entity.User

	// 在事务中创建用户及相关数据
	err = s.txManager.WithTransaction(ctx, func(txCtx context.Context) error {
		// 创建用户
		var txErr error
		createdUser, txErr = s.userRepo.Create(txCtx, *user)
		if txErr != nil {
			return fmt.Errorf("failed to create user: %w", txErr)
		}

		// 保存用户资料
		_, txErr = s.profileRepo.Create(txCtx, *user.Profile)
		if txErr != nil {
			return fmt.Errorf("failed to create user profile: %w", txErr)
		}

		// 保存用户统计
		_, txErr = s.statsRepo.Create(txCtx, *user.Stats)
		if txErr != nil {
			return fmt.Errorf("failed to create user stats: %w", txErr)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 发布注册事件（不影响主流程）
	registeredEvent := userevent.NewUserRegisteredEvent(&createdUser)
	if err := s.eventPub.Publish(context.Background(), registeredEvent); err != nil {
		log.Printf("Warning: Failed to publish UserRegisteredEvent: %v", err)
	}

	return &createdUser, nil
}

// ActivateUser 激活用戶
func (s *userServiceImpl) ActivateUser(ctx context.Context, userID uuid.UUID) error {
	userVal, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	user := &userVal
	user.UpdateLastLogin()
	_, err = s.userRepo.Update(ctx, *user)
	return err
}

// DeactivateUser 激活用戶
func (s *userServiceImpl) DeactivateUser(ctx context.Context, userID uuid.UUID) error {
	userVal, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	user := &userVal
	user.UpdateLastLogin()
	_, err = s.userRepo.Update(ctx, *user)
	return err
}

// ChangePassword 修改密碼
func (s *userServiceImpl) ChangePassword(ctx context.Context, userID uuid.UUID, oldPassword, newPassword string) error {
	if strings.TrimSpace(oldPassword) == "" || strings.TrimSpace(newPassword) == "" {
		return errors.New("old and new password are required")
	}
	userVal, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	user := &userVal
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return errors.New("current password is incorrect")
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user.Password = string(hashedPassword)
	_, err = s.userRepo.Update(ctx, *user)
	return err
}

// UpdatePassword 更新密碼
func (s *userServiceImpl) UpdatePassword(ctx context.Context, userID uuid.UUID, newPassword string) error {
	if strings.TrimSpace(newPassword) == "" {
		return errors.New("new password is required")
	}
	userVal, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	user := &userVal
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user.Password = string(hashedPassword)
	_, err = s.userRepo.Update(ctx, *user)
	return err
}

// ResetPassword 重置密碼
func (s *userServiceImpl) ResetPassword(ctx context.Context, email string) error {
	// TODO: 生成重置令牌並發送郵件
	return nil
}

// VerifyResetToken 驗證重置令牌
func (s *userServiceImpl) VerifyResetToken(ctx context.Context, token string) (uuid.UUID, error) {
	// TODO: 實現實際的令牌驗證邏輯
	// 這裡需要實現：
	// 1. 驗證令牌的有效性
	// 2. 根據令牌找到對應的用戶
	// 3. 更新用戶狀態（如標記令牌已使用）

	// 暫時返回錯誤，提示需要實現
	return uuid.Nil, errors.New("VerifyResetToken not implemented yet")
}

// DeleteAccount 刪除賬戶
func (s *userServiceImpl) DeleteAccount(ctx context.Context, userID uuid.UUID, password string) error {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return errors.New("password is incorrect")
	}

	// 在事务中删除用户及相关数据
	return s.txManager.WithTransaction(ctx, func(txCtx context.Context) error {
		if err := s.statsRepo.Delete(txCtx, user.Stats.ID); err != nil {
			return fmt.Errorf("failed to delete user stats: %w", err)
		}
		if err := s.profileRepo.Delete(txCtx, user.Profile.ID); err != nil {
			return fmt.Errorf("failed to delete user profile: %w", err)
		}
		if err := s.userRepo.Delete(txCtx, user.ID); err != nil {
			return fmt.Errorf("failed to delete user: %w", err)
		}
		return nil
	})
}

// Login 用戶登錄
func (s *userServiceImpl) Login(ctx context.Context, email, password string) (*entity.User, error) {
	if strings.TrimSpace(email) == "" || strings.TrimSpace(password) == "" {
		return nil, errors.New("email and password are required")
	}
	userVal, err := s.userRepo.FindByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	user := &userVal
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, errors.New("invalid password")
	}
	user.UpdateLastLogin()
	_, err = s.userRepo.Update(ctx, *user)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// Logout 用戶登出
func (s *userServiceImpl) Logout(ctx context.Context, userID uuid.UUID) error {
	// TODO: 實現登出邏輯，例如清除會話等
	return nil
}

// RefreshToken 刷新訪問令牌
func (s *userServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (string, string, error) {
	// 驗證刷新令牌
	claims, err := middleware.ValidateRefreshToken(refreshToken)
	if err != nil {
		return "", "", fmt.Errorf("invalid refresh token: %w", err)
	}

	// 檢查用戶是否存在且處於活動狀態
	user, err := s.userRepo.FindByID(ctx, claims.UserID)
	if err != nil {
		return "", "", fmt.Errorf("user not found: %w", err)
	}

	if !user.IsActive {
		return "", "", errors.New("user account is not active")
	}

	// 生成新的訪問令牌
	newToken, err := middleware.GenerateToken(user.ID)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate new token: %w", err)
	}

	// 生成新的刷新令牌
	newRefreshToken, err := middleware.GenerateRefreshToken(user.ID)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate new refresh token: %w", err)
	}

	// 發佈令牌刷新事件
	event := userevent.NewUserProfileUpdatedEvent(user.ID, map[string]interface{}{
		"tokenRefreshedAt": time.Now(),
	})
	if err := s.eventPub.Publish(ctx, event); err != nil {
		log.Printf("Failed to publish token refresh event: %v", err)
	}

	// 返回新的訪問令牌和刷新令牌
	return newToken, newRefreshToken, nil
}

// ValidateToken 驗證令牌
func (s *userServiceImpl) ValidateToken(ctx context.Context, token string) (uuid.UUID, error) {
	// TODO: 實現令牌驗證邏輯
	return uuid.Nil, nil
}

// GetUser 獲取用戶信息
func (s *userServiceImpl) GetUser(ctx context.Context, userID uuid.UUID) (*entity.User, error) {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUser 更新用戶信息
func (s *userServiceImpl) UpdateUser(ctx context.Context, user *entity.User) error {
	user.UpdateLastLogin()
	_, err := s.userRepo.Update(ctx, *user)
	return err
}

// UpdateAvatar 更新頭像
func (s *userServiceImpl) UpdateAvatar(ctx context.Context, userID uuid.UUID, avatarURL string) error {
	userVal, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	user := &userVal
	user.AvatarURL = avatarURL
	user.UpdateLastLogin()
	updatedFields := map[string]interface{}{
		"avatar": avatarURL,
	}
	profileUpdatedEvent := userevent.NewUserProfileUpdatedEvent(user.ID, updatedFields)
	if err := s.eventPub.Publish(ctx, profileUpdatedEvent); err != nil {
		log.Printf("Failed to publish UserProfileUpdatedEvent: %v", err)
	}
	_, err = s.userRepo.Update(ctx, *user)
	return err
}

// UpdateSettings 更新用戶設置
func (s *userServiceImpl) UpdateSettings(ctx context.Context, userID uuid.UUID, settings *entity.UserSettings) error {
	userVal, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	user := &userVal
	user.UpdateSettings(settings)
	user.UpdateLastLogin()
	updatedFields := map[string]interface{}{
		"settings": settings,
	}
	profileUpdatedEvent := userevent.NewUserProfileUpdatedEvent(user.ID, updatedFields)
	if err := s.eventPub.Publish(ctx, profileUpdatedEvent); err != nil {
		log.Printf("Failed to publish UserProfileUpdatedEvent: %v", err)
	}
	_, err = s.userRepo.Update(ctx, *user)
	return err
}

// GetUserStats 獲取用戶統計
func (s *userServiceImpl) GetUserStats(ctx context.Context, userID uuid.UUID) (*entity.UserStats, error) {
	stats, err := s.statsRepo.FindByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// UpdateUserStats 更新用戶統計
func (s *userServiceImpl) UpdateUserStats(ctx context.Context, userID uuid.UUID, stats *entity.UserStats) error {
	userVal, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	user := &userVal
	user.UpdateStats(stats)
	statsUpdatedEvent := userevent.NewUserLearningStatsUpdatedEvent(user.ID, map[string]interface{}{})
	if err := s.eventPub.Publish(ctx, statsUpdatedEvent); err != nil {
		log.Printf("Failed to publish UserLearningStatsUpdatedEvent: %v", err)
	}
	_, err = s.statsRepo.Update(ctx, *stats)
	return err
}

// GetUserProfile 獲取用戶資料
func (s *userServiceImpl) GetUserProfile(ctx context.Context, userID uuid.UUID) (*entity.UserProfile, error) {
	profile, err := s.profileRepo.FindByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	return &profile, nil
}

// UpdateUserProfile 更新用戶資料
func (s *userServiceImpl) UpdateUserProfile(ctx context.Context, userID uuid.UUID, profile *entity.UserProfile) (*entity.UserProfile, error) {
	userVal, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	user := &userVal
	user.UpdateProfile(profile)
	updatedFields := map[string]interface{}{
		"bio":      profile.Bio,
		"location": profile.Location,
		"website":  profile.Website,
		"social":   profile.Social,
	}
	profileUpdatedEvent := userevent.NewUserProfileUpdatedEvent(user.ID, updatedFields)
	if err := s.eventPub.Publish(ctx, profileUpdatedEvent); err != nil {
		log.Printf("Failed to publish UserProfileUpdatedEvent: %v", err)
	}
	_, err = s.profileRepo.Update(ctx, *profile)
	return profile, err
}

// FollowUser 關注用戶
func (s *userServiceImpl) FollowUser(ctx context.Context, followerID, followedID uuid.UUID) error {
	// TODO: 實現關注用戶邏輯
	return nil
}

// UnfollowUser 取消關注
func (s *userServiceImpl) UnfollowUser(ctx context.Context, followerID, followedID uuid.UUID) error {
	// TODO: 實現取消關注邏輯
	return nil
}

// GetFollowers 獲取粉絲列表
func (s *userServiceImpl) GetFollowers(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entity.User, error) {
	// TODO: 實現獲取粉絲列表邏輯
	return nil, nil
}

// GetFollowing 獲取關注列表
func (s *userServiceImpl) GetFollowing(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entity.User, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

// AddUserWord 獲取關注列表
func (s *userServiceImpl) AddUserWord(ctx context.Context, userID uuid.UUID, wordID uuid.UUID) (*entity.UserWord, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

// SearchUsers 搜索用戶
func (s *userServiceImpl) SearchUsers(ctx context.Context, query string, offset, limit int) ([]*entity.User, error) {
	users, err := s.userRepo.Search(ctx, query)
	if err != nil {
		return nil, err
	}
	result := make([]*entity.User, len(users))
	for i := range users {
		result[i] = &users[i]
	}
	return result, nil
}

func (s *userServiceImpl) GetFavoriteWords(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entity.UserWord, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetLearnedWords(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entity.UserWord, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) SearchUserWords(ctx context.Context, userID uuid.UUID, query string, offset, limit int) ([]*entity.UserWord, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetUserWordList(ctx context.Context, userID uuid.UUID) ([]*entity.UserWord, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetUserWordStats(ctx context.Context, userID uuid.UUID) (int, int, error) {
	// TODO: 實現獲取關注列表邏輯
	return 0, 0, nil
}

func (s *userServiceImpl) RemoveUserWord(ctx context.Context, userID uuid.UUID, wordID uuid.UUID) error {
	// TODO: 實現獲取關注列表邏輯
	return nil
}

func (s *userServiceImpl) MarkWordAsLearned(ctx context.Context, userID uuid.UUID, wordID uuid.UUID) error {
	// TODO: 實現獲取關注列表邏輯
	return nil
}

func (s *userServiceImpl) ToggleWordFavorite(ctx context.Context, userID uuid.UUID, wordID uuid.UUID) error {
	// TODO: 實現獲取關注列表邏輯
	return nil
}

// Activate 激活用戶
func (s *userServiceImpl) Activate(ctx context.Context, userID uuid.UUID) error {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to find user: %w", err)
	}

	user.IsActive = true
	user.LastLoginAt = time.Now()

	if _, err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}

	// 發布用戶激活事件
	event := userevent.NewUserProfileUpdatedEvent(userID, map[string]interface{}{
		"isActive":    true,
		"lastLoginAt": user.LastLoginAt,
	})
	if err := s.eventPub.Publish(ctx, event); err != nil {
		log.Printf("failed to publish user activated event: %v", err)
	}

	return nil
}

// updateUserVocabularyStats 更新用戶詞彙統計
func (s *userServiceImpl) updateUserVocabularyStats(ctx context.Context, userID uuid.UUID) error {
	total, learned, err := s.wordRepo.GetUserWordStats(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user word stats: %w", err)
	}

	stats, err := s.statsRepo.FindByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user stats: %w", err)
	}

	stats.VocabularyCount = total
	stats.UpdatedAt = time.Now()

	if _, err := s.statsRepo.Update(ctx, stats); err != nil {
		return fmt.Errorf("failed to update user stats: %w", err)
	}

	// 發布統計更新事件
	event := userevent.NewUserLearningStatsUpdatedEvent(userID, map[string]interface{}{
		"vocabularyCount": total,
		"learnedWords":    learned,
	})
	if err := s.eventPub.Publish(ctx, event); err != nil {
		log.Printf("failed to publish stats update event: %v", err)
	}

	return nil
}

// sendResetPasswordEmail 發送密碼重置郵件
func (s *userServiceImpl) sendResetPasswordEmail(ctx context.Context, userID uuid.UUID, email, username string, data map[string]interface{}) error {
	// 這裡需要注入 EmailService，但為了不破壞現有的依賴注入結構，
	// 我們可以通過事件系統來處理郵件發送
	event := userevent.NewPasswordResetEmailEvent(userID, email, username, data)
	return s.eventPub.Publish(ctx, event)
}

// ArchiveNotification 归档通知
func (s *userServiceImpl) ArchiveNotification(ctx context.Context, userID uuid.UUID, notificationID uuid.UUID) error {
	// TODO: 實現獲取關注列表邏輯
	return nil
}

// ClaimAchievementReward 獲取成就獎勵
func (s *userServiceImpl) ClaimAchievementReward(ctx context.Context, userID uuid.UUID, achievementID uuid.UUID) error {
	// TODO: 實現獲取關注列表邏輯
	return nil
}

// CreateNotification 創建通知
func (s *userServiceImpl) CreateNotification(ctx context.Context, userID uuid.UUID, notificationType entity.NotificationType, title, message string, data map[string]interface{}) (*entity.Notification, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) CreateUser(ctx context.Context, username, email, password string) (*entity.User, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetAchievementProgress(ctx context.Context, userID uuid.UUID, achievementID uuid.UUID) (*entity.UserAchievement, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetLearningStreak(ctx context.Context, userID uuid.UUID) (int, error) {
	// TODO: 實現獲取關注列表邏輯
	return 0, nil
}

func (s *userServiceImpl) GetNotificationPreferences(ctx context.Context, userID uuid.UUID) (*entity.NotificationPreference, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetProgressReport(ctx context.Context, userID uuid.UUID, period string) (*entity.UserStats, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetUserAchievements(ctx context.Context, userID uuid.UUID) ([]*entity.UserAchievement, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetUserByEmail(ctx context.Context, email string) (*entity.User, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetUserByID(ctx context.Context, id uuid.UUID) (*entity.User, error) {
	user, err := s.userRepo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (s *userServiceImpl) GetUserByUsername(ctx context.Context, username string) (*entity.User, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetUserNotifications(ctx context.Context, userID uuid.UUID, status entity.NotificationStatus, limit, offset int) ([]*entity.Notification, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) GetUserSettings(ctx context.Context, userID uuid.UUID) (*entity.UserSettings, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) MarkNotificationAsRead(ctx context.Context, userID uuid.UUID, notificationID uuid.UUID) error {
	// TODO: 實現獲取關注列表邏輯
	return nil
}

func (s *userServiceImpl) UpdateAchievementProgress(ctx context.Context, userID uuid.UUID, achievementID uuid.UUID, progress int) (*entity.UserAchievement, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) UpdateNotificationPreferences(ctx context.Context, userID uuid.UUID, preferences *entity.NotificationPreference) (*entity.NotificationPreference, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) UpdateUserSettings(ctx context.Context, userID uuid.UUID, settings *entity.UserSettings) (*entity.UserSettings, error) {
	// TODO: 實現獲取關注列表邏輯
	return nil, nil
}

func (s *userServiceImpl) VerifyEmail(ctx context.Context, userID uuid.UUID) error {
	// TODO: 實現獲取關注列表邏輯
	return nil
}
