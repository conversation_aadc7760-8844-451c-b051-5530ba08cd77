package di

import (
	"go.uber.org/dig"
	coredi "languagelearning/domain/core/di"
	userRepoImpl "languagelearning/domain/user/repository/impl"
	userSvcImpl "languagelearning/domain/user/service/impl"
)

// UserModule 用戶領域的DI模塊
type UserModule struct {
	coredi.BaseModule
}

// NewUserModule 創建用戶領域模塊
func NewUserModule() coredi.Module {
	return &UserModule{
		BaseModule: coredi.NewBaseModule(
			"user",
			[]string{"core"}, // 依賴核心模塊（事件系統）
			[]coredi.ModuleFeature{coredi.FeatureBasic},
		),
	}
}

// Register 註冊用戶領域的所有依賴
func (m *UserModule) Register(container *dig.Container) error {
	// 註冊存儲庫
	if err := m.registerRepositories(container); err != nil {
		return err
	}

	// 註冊服務
	if err := m.registerServices(container); err != nil {
		return err
	}

	return nil
}

// registerRepositories 註冊用戶領域的存儲庫
func (m *UserModule) registerRepositories(container *dig.Container) error {
	// 註冊事務管理器
	if err := container.Provide(userRepoImpl.NewTransactionManager); err != nil {
		return err
	}

	// 註冊用戶存儲庫
	if err := container.Provide(userRepoImpl.NewUserRepository); err != nil {
		return err
	}

	// 註冊用戶配置存儲庫
	if err := container.Provide(userRepoImpl.NewUserProfileRepository); err != nil {
		return err
	}

	// 註冊用戶統計存儲庫
	if err := container.Provide(userRepoImpl.NewUserStatsRepository); err != nil {
		return err
	}

	// 註冊用戶詞彙存儲庫
	if err := container.Provide(userRepoImpl.NewUserWordRepository); err != nil {
		return err
	}

	return nil
}

// registerServices 註冊用戶領域的服務
func (m *UserModule) registerServices(container *dig.Container) error {
	// 註冊用戶服務
	if err := container.Provide(userSvcImpl.NewUserService); err != nil {
		return err
	}

	return nil
}
