package entity

import (
	"time"

	"github.com/google/uuid"
)

// User 用戶實體
type User struct {
	ID          uuid.UUID     `json:"id"`
	Username    string        `json:"username"`
	Email       string        `json:"email"`
	Password    string        `json:"-"`
	AvatarURL   string        `json:"avatarUrl,omitempty"`
	IsActive    bool          `json:"isActive"`
	CreatedAt   time.Time     `json:"createdAt"`
	LastLoginAt time.Time     `json:"lastLoginAt,omitempty"`
	Settings    *UserSettings `json:"settings,omitempty"`
	Profile     *UserProfile  `json:"profile,omitempty"`
	Stats       *UserStats    `json:"stats,omitempty"`
}

// UserSettings 用戶設置
type UserSettings struct {
	UserID uuid.UUID `json:"userId" gorm:"type:uuid"`
	NotificationsEnabled bool   `json:"notificationsEnabled"`
	DarkModeEnabled      bool   `json:"darkModeEnabled"`
	DailyGoal            int    `json:"dailyGoal"`
	PreferredLanguage    string `json:"preferredLanguage"`
}

// NewUser 創建新用戶
func NewUser(username, email, password string) *User {
	now := time.Now()
	return &User{
		ID:          uuid.New(),
		Username:    username,
		Email:       email,
		Password:    password,
		IsActive:    false,
		CreatedAt:   now,
		LastLoginAt: now,
		Settings: &UserSettings{
			NotificationsEnabled: true,
			DarkModeEnabled:      false,
			DailyGoal:            3,
			PreferredLanguage:    "zh-CN",
		},
		Profile: &UserProfile{
			ID:        uuid.New(),
			UserID:    uuid.New(), // 這個會在 Create 時更新
			UpdatedAt: now,
		},
		Stats: &UserStats{
			ID:         uuid.New(),
			UserID:     uuid.New(), // 這個會在 Create 時更新
			LastActive: now,
			UpdatedAt:  now,
		},
	}
}

// UpdateLastLogin 更新最後登錄時間
func (u *User) UpdateLastLogin() {
	u.LastLoginAt = time.Now()
	if u.Stats != nil {
		u.Stats.LastActive = time.Now()
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdateSettings 更新用戶設置
func (u *User) UpdateSettings(settings *UserSettings) {
	u.Settings = settings
}

// UpdateProfile 更新用戶資料
func (u *User) UpdateProfile(profile *UserProfile) {
	u.Profile = profile
	u.Profile.UpdatedAt = time.Now()
}

// UpdateStats 更新用戶統計
func (u *User) UpdateStats(stats *UserStats) {
	u.Stats = stats
	u.Stats.UpdatedAt = time.Now()
}

// AddPoints 增加積分
func (u *User) AddPoints(points int) {
	if u.Stats != nil {
		u.Stats.TotalPoints += points
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdateStreak 更新連續學習天數
func (u *User) UpdateStreak(streak int) {
	if u.Stats != nil {
		u.Stats.CurrentStreak = streak
		if streak > u.Stats.LongestStreak {
			u.Stats.LongestStreak = streak
		}
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdateExerciseCounts 更新練習計數
func (u *User) UpdateExerciseCounts(vocabulary, listening, speaking, grammar int) {
	if u.Stats != nil {
		u.Stats.VocabularyCount = vocabulary
		u.Stats.ListeningCount = listening
		u.Stats.SpeakingCount = speaking
		u.Stats.GrammarCount = grammar
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdatePracticeTime 更新練習時間
func (u *User) UpdatePracticeTime(duration int64) {
	if u.Stats != nil {
		u.Stats.TotalPracticeTime += duration
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdateLearningDays 更新學習天數
func (u *User) UpdateLearningDays(days int) {
	if u.Stats != nil {
		u.Stats.LearningDays = days
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdateCompletedLessons 更新完成課程數
func (u *User) UpdateCompletedLessons(count int) {
	if u.Stats != nil {
		u.Stats.CompletedLessons = count
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdateAverageScore 更新平均分數
func (u *User) UpdateAverageScore(score float64) {
	if u.Stats != nil {
		u.Stats.AverageScore = score
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdateChallengesCompleted 更新完成挑戰數
func (u *User) UpdateChallengesCompleted(count int) {
	if u.Stats != nil {
		u.Stats.ChallengesCompleted = count
		u.Stats.UpdatedAt = time.Now()
	}
}

// UpdateHelpedUsers 更新幫助用戶數
func (u *User) UpdateHelpedUsers(count int) {
	if u.Stats != nil {
		u.Stats.HelpedUsers = count
		u.Stats.UpdatedAt = time.Now()
	}
}
