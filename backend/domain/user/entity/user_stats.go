package entity

import (
	"time"

	"github.com/google/uuid"
)

// UserStats represents a user's learning statistics
type UserStats struct {
	ID                  uuid.UUID `json:"id"`
	UserID              uuid.UUID `json:"userId"`
	CurrentStreak       int       `json:"currentStreak"`
	LongestStreak       int       `json:"longestStreak"`
	VocabularyCount     int       `json:"vocabularyCount"`
	ListeningCount      int       `json:"listeningCount"`
	SpeakingCount       int       `json:"speakingCount"`
	GrammarCount        int       `json:"grammarCount"`
	TotalPoints         int       `json:"totalPoints"`
	ChallengesCompleted int       `json:"challengesCompleted"`
	HelpedUsers         int       `json:"helpedUsers"`
	TotalPracticeTime   int64     `json:"totalPracticeTime"` // in seconds
	AverageScore        float64   `json:"averageScore"`
	LearningDays        int       `json:"learningDays"`
	CompletedLessons    int       `json:"completedLessons"`
	LastActive          time.Time `json:"lastActive"`
	UpdatedAt           time.Time `json:"updatedAt"`
}

// NewUserStats creates new user statistics
func NewUserStats(userID uuid.UUID) *UserStats {
	now := time.Now()
	return &UserStats{
		ID:         uuid.New(),
		UserID:     userID,
		LastActive: now,
		UpdatedAt:  now,
	}
}

// UpdateStats updates the user statistics
func (s *UserStats) UpdateStats(stats *UserStats) {
	s.CurrentStreak = stats.CurrentStreak
	s.LongestStreak = stats.LongestStreak
	s.VocabularyCount = stats.VocabularyCount
	s.ListeningCount = stats.ListeningCount
	s.SpeakingCount = stats.SpeakingCount
	s.GrammarCount = stats.GrammarCount
	s.TotalPoints = stats.TotalPoints
	s.ChallengesCompleted = stats.ChallengesCompleted
	s.HelpedUsers = stats.HelpedUsers
	s.TotalPracticeTime = stats.TotalPracticeTime
	s.AverageScore = stats.AverageScore
	s.LearningDays = stats.LearningDays
	s.CompletedLessons = stats.CompletedLessons
	s.LastActive = time.Now()
	s.UpdatedAt = time.Now()
}
