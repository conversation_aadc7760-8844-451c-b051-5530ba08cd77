package entity

import (
	"time"

	"github.com/google/uuid"
)

// AchievementType represents the type of achievement
type AchievementType string

const (
	AchievementTypeStreak     AchievementType = "streak"
	AchievementTypeVocabulary AchievementType = "vocabulary"
	AchievementTypeListening  AchievementType = "listening"
	AchievementTypeSpeaking   AchievementType = "speaking"
	AchievementTypeGrammar    AchievementType = "grammar"
	AchievementTypeChallenge  AchievementType = "challenge"
	AchievementTypeSocial     AchievementType = "social"
	AchievementTypeLessons    AchievementType = "lessons"
)

// Achievement represents an achievement in the system
type Achievement struct {
	ID          uuid.UUID       `json:"id"`
	Type        AchievementType `json:"type"`
	Title       string          `json:"title"`
	Description string          `json:"description"`
	Icon        string          `json:"icon"`
	Color       string          `json:"color"`
	Requirement int             `json:"requirement"`
	Reward      int             `json:"reward"`
	CreatedAt   time.Time       `json:"createdAt"`
}

// UserAchievement represents a user's achievement progress
type UserAchievement struct {
	ID            uuid.UUID `json:"id"`
	UserID        uuid.UUID `json:"userId"`
	AchievementID uuid.UUID `json:"achievementId"`
	Progress      int       `json:"progress"`
	IsUnlocked    bool      `json:"isUnlocked"`
	RewardClaimed bool      `json:"rewardClaimed"`
	UnlockedDate  time.Time `json:"unlockedDate,omitempty"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// NewUserAchievement creates a new user achievement
func NewUserAchievement(userID, achievementID uuid.UUID) *UserAchievement {
	return &UserAchievement{
		ID:            uuid.New(),
		UserID:        userID,
		AchievementID: achievementID,
		Progress:      0,
		IsUnlocked:    false,
		RewardClaimed: false,
		UpdatedAt:     time.Now(),
	}
}

// UpdateProgress updates the achievement progress
func (ua *UserAchievement) UpdateProgress(progress int, requirement int) {
	ua.Progress = progress
	if progress >= requirement && !ua.IsUnlocked {
		ua.IsUnlocked = true
		ua.UnlockedDate = time.Now()
	}
	ua.UpdatedAt = time.Now()
}

// ClaimReward marks the achievement reward as claimed
func (ua *UserAchievement) ClaimReward() {
	ua.RewardClaimed = true
	ua.UpdatedAt = time.Now()
}
