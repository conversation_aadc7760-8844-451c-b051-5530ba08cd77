package entity

import (
	"time"

	"github.com/google/uuid"
)

// LearningPreference 表示用戶的學習偏好
type LearningPreference struct {
	ID              uuid.UUID
	UserID          uuid.UUID
	TargetLanguage  string
	NativeLanguage  string
	DailyGoal       int            // 每日學習目標（分鐘）
	PreferredTime   []time.Weekday // 偏好的學習時間
	DifficultyLevel string         // 偏好的難度級別
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

// NewLearningPreference 創建一個新的學習偏好實體
func NewLearningPreference(
	userID uuid.UUID,
	targetLanguage string,
	nativeLanguage string,
	dailyGoal int,
	preferredTime []time.Weekday,
	difficultyLevel string,
) *LearningPreference {
	return &LearningPreference{
		ID:              uuid.New(),
		UserID:          userID,
		TargetLanguage:  targetLanguage,
		NativeLanguage:  nativeLanguage,
		DailyGoal:       dailyGoal,
		PreferredTime:   preferredTime,
		DifficultyLevel: difficultyLevel,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}
