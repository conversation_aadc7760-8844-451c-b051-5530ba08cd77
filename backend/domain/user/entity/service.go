package entity

import (
	"context"

	"github.com/google/uuid"
)

// UserRepository 用戶倉儲接口
type UserRepository interface {
	Create(ctx context.Context, user *User) error
	FindByID(ctx context.Context, id uuid.UUID) (*User, error)
	FindByEmail(ctx context.Context, email string) (*User, error)
	FindByUsername(ctx context.Context, username string) (*User, error)
	Update(ctx context.Context, user *User) error
	Delete(ctx context.Context, id uuid.UUID) error
	Search(ctx context.Context, query string, offset, limit int) ([]*User, error)
}

// UserProfileRepository 用戶資料倉儲接口
type UserProfileRepository interface {
	Create(ctx context.Context, profile *UserProfile) error
	FindByID(ctx context.Context, id uuid.UUID) (*UserProfile, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) (*UserProfile, error)
	Update(ctx context.Context, profile *UserProfile) error
	Delete(ctx context.Context, id uuid.UUID) error
}

// UserStatsRepository 用戶統計倉儲接口
type UserStatsRepository interface {
	Create(ctx context.Context, stats *UserStats) error
	FindByID(ctx context.Context, id uuid.UUID) (*UserStats, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) (*UserStats, error)
	Update(ctx context.Context, stats *UserStats) error
	Delete(ctx context.Context, id uuid.UUID) error
}

// UserWordRepository 用戶詞彙倉儲接口
type UserWordRepository interface {
	Create(ctx context.Context, userWord *UserWord) error
	FindByID(ctx context.Context, id uuid.UUID) (*UserWord, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]UserWord, error)
	FindByUserAndWordID(ctx context.Context, userID, wordID uuid.UUID) (UserWord, error)
	Update(ctx context.Context, userWord *UserWord) error
	Delete(ctx context.Context, id uuid.UUID) error
	BatchCreate(ctx context.Context, userWords []*UserWord) error
	GetUserWordStats(ctx context.Context, userID uuid.UUID) (int, int, error)
	Search(ctx context.Context, userID uuid.UUID, query string, offset, limit int) ([]UserWord, error)
	GetFavorites(ctx context.Context, userID uuid.UUID, offset, limit int) ([]UserWord, error)
	GetLearned(ctx context.Context, userID uuid.UUID, offset, limit int) ([]UserWord, error)
}
