package entity

import (
	"time"

	"github.com/google/uuid"
)

// UserWord 表示用戶的詞彙學習記錄
type UserWord struct {
	ID         uuid.UUID `json:"id"`
	UserID     uuid.UUID `json:"userId"`
	WordID     uuid.UUID `json:"wordId"`
	Word       *Word     `json:"word,omitempty"`
	IsLearned  bool      `json:"isLearned"`
	IsFavorite bool      `json:"isFavorite"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

// Word 表示詞彙實體
type Word struct {
	ID              uuid.UUID `json:"id"`
	Word            string    `json:"word"`
	Translation     string    `json:"translation"`
	Pronunciation   string    `json:"pronunciation,omitempty"`
	Definition      string    `json:"definition"`
	ExampleSentence string    `json:"exampleSentence,omitempty"`
	Category        string    `json:"category,omitempty"`
	Difficulty      string    `json:"difficulty"`
	ImageURL        string    `json:"imageURL,omitempty"`
	AudioURL        string    `json:"audioURL,omitempty"`
	LanguageID      uuid.UUID `json:"languageId"`
}

// NewUserWord 創建新的用戶詞彙記錄
func NewUserWord(userID, wordID uuid.UUID) *UserWord {
	now := time.Now()
	return &UserWord{
		ID:         uuid.New(),
		UserID:     userID,
		WordID:     wordID,
		IsLearned:  false,
		IsFavorite: false,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
}

// MarkAsLearned 標記為已學習
func (uw *UserWord) MarkAsLearned() {
	uw.IsLearned = true
	uw.UpdatedAt = time.Now()
}

// ToggleFavorite 切換收藏狀態
func (uw *UserWord) ToggleFavorite() {
	uw.IsFavorite = !uw.IsFavorite
	uw.UpdatedAt = time.Now()
}

// UpdateWord 更新詞彙信息
func (uw *UserWord) UpdateWord(word *Word) {
	uw.Word = word
	uw.UpdatedAt = time.Now()
}
