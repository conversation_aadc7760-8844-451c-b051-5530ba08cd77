package entity

import (
	"time"

	"github.com/google/uuid"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	NotificationTypeAchievement NotificationType = "achievement"
	NotificationTypeLesson      NotificationType = "lesson"
	NotificationTypeStreak      NotificationType = "streak"
	NotificationTypeReminder    NotificationType = "reminder"
	NotificationTypeSystem      NotificationType = "system"
)

// NotificationStatus represents the status of a notification
type NotificationStatus string

const (
	NotificationStatusUnread   NotificationStatus = "unread"
	NotificationStatusRead     NotificationStatus = "read"
	NotificationStatusArchived NotificationStatus = "archived"
)

// Notification represents a notification in the system
type Notification struct {
	ID        uuid.UUID              `json:"id"`
	UserID    uuid.UUID              `json:"userId"`
	Type      NotificationType       `json:"type"`
	Title     string                 `json:"title"`
	Message   string                 `json:"message"`
	Status    NotificationStatus     `json:"status"`
	Data      map[string]interface{} `json:"data,omitempty"`
	CreatedAt time.Time              `json:"createdAt"`
	ReadAt    time.Time              `json:"readAt,omitempty"`
}

// NotificationPreference represents a user's notification preferences
type NotificationPreference struct {
	ID                        uuid.UUID `json:"id"`
	UserID                    uuid.UUID `json:"userId"`
	EmailNotificationsEnabled bool      `json:"emailNotificationsEnabled"`
	PushNotificationsEnabled  bool      `json:"pushNotificationsEnabled"`
	AchievementNotifications  bool      `json:"achievementNotifications"`
	LessonNotifications       bool      `json:"lessonNotifications"`
	StreakNotifications       bool      `json:"streakNotifications"`
	ReminderNotifications     bool      `json:"reminderNotifications"`
	DailyReminderTime         string    `json:"dailyReminderTime"`
	UpdatedAt                 time.Time `json:"updatedAt"`
}

// NewNotification creates a new notification
func NewNotification(userID uuid.UUID, notificationType NotificationType, title, message string) *Notification {
	return &Notification{
		ID:        uuid.New(),
		UserID:    userID,
		Type:      notificationType,
		Title:     title,
		Message:   message,
		Status:    NotificationStatusUnread,
		Data:      make(map[string]interface{}),
		CreatedAt: time.Now(),
	}
}

// NewNotificationPreference creates new notification preferences
func NewNotificationPreference(userID uuid.UUID) *NotificationPreference {
	return &NotificationPreference{
		ID:                        uuid.New(),
		UserID:                    userID,
		EmailNotificationsEnabled: true,
		PushNotificationsEnabled:  true,
		AchievementNotifications:  true,
		LessonNotifications:       true,
		StreakNotifications:       true,
		ReminderNotifications:     true,
		DailyReminderTime:         "09:00",
		UpdatedAt:                 time.Now(),
	}
}

// MarkAsRead marks the notification as read
func (n *Notification) MarkAsRead() {
	n.Status = NotificationStatusRead
	n.ReadAt = time.Now()
}

// Archive marks the notification as archived
func (n *Notification) Archive() {
	n.Status = NotificationStatusArchived
}

// UpdatePreference updates the notification preferences
func (p *NotificationPreference) UpdatePreference(pref *NotificationPreference) {
	p.EmailNotificationsEnabled = pref.EmailNotificationsEnabled
	p.PushNotificationsEnabled = pref.PushNotificationsEnabled
	p.AchievementNotifications = pref.AchievementNotifications
	p.LessonNotifications = pref.LessonNotifications
	p.StreakNotifications = pref.StreakNotifications
	p.ReminderNotifications = pref.ReminderNotifications
	p.DailyReminderTime = pref.DailyReminderTime
	p.UpdatedAt = time.Now()
}
