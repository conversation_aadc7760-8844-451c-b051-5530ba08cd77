package entity

import (
	"time"

	"github.com/google/uuid"
)

// UserProfile represents a user's profile information
type UserProfile struct {
	ID        uuid.UUID   `json:"id"`
	UserID    uuid.UUID   `json:"userId"`
	Bio       string      `json:"bio,omitempty"`
	Location  string      `json:"location,omitempty"`
	Website   string      `json:"website,omitempty"`
	Social    Social      `json:"social,omitempty"`
	Following []uuid.UUID `json:"following,omitempty"`
	Followers []uuid.UUID `json:"followers,omitempty"`
	UpdatedAt time.Time   `json:"updatedAt"`
}

// Social represents social media links
type Social struct {
	Twitter  string `json:"twitter,omitempty"`
	Facebook string `json:"facebook,omitempty"`
	LinkedIn string `json:"linkedIn,omitempty"`
}

// NewUserProfile creates a new user profile
func NewUserProfile(userID uuid.UUID) *UserProfile {
	return &UserProfile{
		ID:        uuid.New(),
		UserID:    userID,
		Following: make([]uuid.UUID, 0),
		Followers: make([]uuid.UUID, 0),
		UpdatedAt: time.Now(),
	}
}
