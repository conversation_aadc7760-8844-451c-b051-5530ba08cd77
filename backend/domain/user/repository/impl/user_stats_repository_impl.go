package impl

import (
	"context"
	"languagelearning/domain/user/entity"
	"languagelearning/domain/user/repository"
	"languagelearning/models"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type userStatsRepository struct {
	db *gorm.DB
}

// NewUserStatsRepository creates a new UserStatsRepository implementation
func NewUserStatsRepository(db *gorm.DB) repository.UserStatsRepository {
	return &userStatsRepository{db: db}
}

// Create creates new user stats in the database
func (r *userStatsRepository) Create(ctx context.Context, statsEntity entity.UserStats) (entity.UserStats, error) {
	statsModel := &models.UserStats{}
	// Assuming there's a FromEntity method on models.UserStats or convert manually
	statsModel.UserID = statsEntity.UserID
	statsModel.CurrentStreak = statsEntity.CurrentStreak
	statsModel.LongestStreak = statsEntity.LongestStreak
	statsModel.VocabularyCount = statsEntity.VocabularyCount
	statsModel.ListeningCount = statsEntity.ListeningCount
	statsModel.SpeakingCount = statsEntity.SpeakingCount
	statsModel.GrammarCount = statsEntity.GrammarCount
	statsModel.TotalPoints = statsEntity.TotalPoints
	statsModel.ChallengesCompleted = statsEntity.ChallengesCompleted
	statsModel.HelpedUsers = statsEntity.HelpedUsers
	statsModel.TotalPracticeTime = time.Duration(statsEntity.TotalPracticeTime) * time.Second
	statsModel.AverageScore = statsEntity.AverageScore
	statsModel.LearningDays = statsEntity.LearningDays
	statsModel.CompletedLessons = statsEntity.CompletedLessons
	statsModel.LastActive = statsEntity.LastActive
	statsModel.UpdatedAt = time.Now() // Update UpdatedAt on creation

	if err := r.db.WithContext(ctx).Create(statsModel).Error; err != nil {
		return entity.UserStats{}, err
	}

	// Assuming there's a ToEntity method on models.UserStats or convert manually
	return entity.UserStats{
		ID:                  statsModel.ID,
		UserID:              statsModel.UserID,
		CurrentStreak:       statsModel.CurrentStreak,
		LongestStreak:       statsModel.LongestStreak,
		VocabularyCount:     statsModel.VocabularyCount,
		ListeningCount:      statsModel.ListeningCount,
		SpeakingCount:       statsModel.SpeakingCount,
		GrammarCount:        statsModel.GrammarCount,
		TotalPoints:         statsModel.TotalPoints,
		ChallengesCompleted: statsModel.ChallengesCompleted,
		HelpedUsers:         statsModel.HelpedUsers,
		TotalPracticeTime:   int64(statsModel.TotalPracticeTime.Seconds()),
		AverageScore:        statsModel.AverageScore,
		LearningDays:        statsModel.LearningDays,
		CompletedLessons:    statsModel.CompletedLessons,
		LastActive:          statsModel.LastActive,
		UpdatedAt:           statsModel.UpdatedAt,
	}, nil
}

// FindByUserID finds user stats by user ID
func (r *userStatsRepository) FindByUserID(ctx context.Context, userID uuid.UUID) (entity.UserStats, error) {
	var statsModel models.UserStats
	if result := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&statsModel); result.Error != nil {
		return entity.UserStats{}, result.Error
	}
	// Assuming there's a ToEntity method on models.UserStats or convert manually
	return entity.UserStats{
		ID:                  statsModel.ID,
		UserID:              statsModel.UserID,
		CurrentStreak:       statsModel.CurrentStreak,
		LongestStreak:       statsModel.LongestStreak,
		VocabularyCount:     statsModel.VocabularyCount,
		ListeningCount:      statsModel.ListeningCount,
		SpeakingCount:       statsModel.SpeakingCount,
		GrammarCount:        statsModel.GrammarCount,
		TotalPoints:         statsModel.TotalPoints,
		ChallengesCompleted: statsModel.ChallengesCompleted,
		HelpedUsers:         statsModel.HelpedUsers,
		TotalPracticeTime:   int64(statsModel.TotalPracticeTime.Seconds()),
		AverageScore:        statsModel.AverageScore,
		LearningDays:        statsModel.LearningDays,
		CompletedLessons:    statsModel.CompletedLessons,
		LastActive:          statsModel.LastActive,
		UpdatedAt:           statsModel.UpdatedAt,
	}, nil
}

// Update updates existing user stats in the database
func (r *userStatsRepository) Update(ctx context.Context, statsEntity entity.UserStats) (entity.UserStats, error) {
	statsModel := &models.UserStats{}
	// Assuming there's a FromEntity method on models.UserStats or convert manually
	statsModel.ID = statsEntity.ID // Ensure ID is set for update
	statsModel.UserID = statsEntity.UserID
	statsModel.CurrentStreak = statsEntity.CurrentStreak
	statsModel.LongestStreak = statsEntity.LongestStreak
	statsModel.VocabularyCount = statsEntity.VocabularyCount
	statsModel.ListeningCount = statsEntity.ListeningCount
	statsModel.SpeakingCount = statsEntity.SpeakingCount
	statsModel.GrammarCount = statsEntity.GrammarCount
	statsModel.TotalPoints = statsEntity.TotalPoints
	statsModel.ChallengesCompleted = statsEntity.ChallengesCompleted
	statsModel.HelpedUsers = statsEntity.HelpedUsers
	statsModel.TotalPracticeTime = time.Duration(statsEntity.TotalPracticeTime) * time.Second
	statsModel.AverageScore = statsEntity.AverageScore
	statsModel.LearningDays = statsEntity.LearningDays
	statsModel.CompletedLessons = statsEntity.CompletedLessons
	statsModel.LastActive = statsEntity.LastActive
	statsModel.UpdatedAt = time.Now() // Update UpdatedAt on update

	if err := r.db.WithContext(ctx).Save(statsModel).Error; err != nil {
		return entity.UserStats{}, err
	}

	// Assuming there's a ToEntity method on models.UserStats or convert manually
	return entity.UserStats{
		ID:                  statsModel.ID,
		UserID:              statsModel.UserID,
		CurrentStreak:       statsModel.CurrentStreak,
		LongestStreak:       statsModel.LongestStreak,
		VocabularyCount:     statsModel.VocabularyCount,
		ListeningCount:      statsModel.ListeningCount,
		SpeakingCount:       statsModel.SpeakingCount,
		GrammarCount:        statsModel.GrammarCount,
		TotalPoints:         statsModel.TotalPoints,
		ChallengesCompleted: statsModel.ChallengesCompleted,
		HelpedUsers:         statsModel.HelpedUsers,
		TotalPracticeTime:   int64(statsModel.TotalPracticeTime.Seconds()),
		AverageScore:        statsModel.AverageScore,
		LearningDays:        statsModel.LearningDays,
		CompletedLessons:    statsModel.CompletedLessons,
		LastActive:          statsModel.LastActive,
		UpdatedAt:           statsModel.UpdatedAt,
	}, nil
}

// Delete deletes user stats by user ID
func (r *userStatsRepository) Delete(ctx context.Context, userID uuid.UUID) error {
	// Find the stats by user ID first to get the stats ID
	statsEntity, err := r.FindByUserID(ctx, userID)
	if err != nil {
		// If not found, consider it already deleted or log
		if err == gorm.ErrRecordNotFound {
			return nil // Already deleted or never existed
		}
		return err
	}

	if err := r.db.WithContext(ctx).Delete(&models.UserStats{}, "id = ?", statsEntity.ID).Error; err != nil {
		return err
	}
	return nil
}

// GetProgressReport retrieves a progress report for a user for a given period
// This implementation is a placeholder and needs actual logic based on period and other data
func (r *userStatsRepository) GetProgressReport(ctx context.Context, userID uuid.UUID, period string) (entity.UserStats, error) {
	// This is a complex query and would require joining with other tables (e.g., practice attempts, lesson progress)
	// Returning a basic implementation for now, finding stats by user ID
	return r.FindByUserID(ctx, userID)
} 