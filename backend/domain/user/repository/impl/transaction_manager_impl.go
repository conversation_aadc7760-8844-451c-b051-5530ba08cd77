package impl

import (
	"context"
	"languagelearning/domain/user/repository"

	"gorm.io/gorm"
)

// gormTransactionManager implements TransactionManager using GORM
type gormTransactionManager struct {
	db *gorm.DB
}

// NewTransactionManager creates a new GORM-based transaction manager
func NewTransactionManager(db *gorm.DB) repository.TransactionManager {
	return &gormTransactionManager{
		db: db,
	}
}

// WithTransaction executes the given function within a database transaction
func (tm *gormTransactionManager) WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return tm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Create a new context with the transaction
		txCtx := context.WithValue(ctx, "tx", tx)
		return fn(txCtx)
	})
}
