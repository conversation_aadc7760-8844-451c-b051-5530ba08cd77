package impl

import (
	"context"
	"errors"
	"languagelearning/domain/user/entity"
	"languagelearning/domain/user/repository"
	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type userRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) repository.UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) Create(ctx context.Context, user entity.User) (entity.User, error) {
	model := &models.User{}
	model.FromEntity(&user)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return entity.User{}, err
	}

	return *model.ToEntity(), nil
}

func (r *userRepository) FindByID(ctx context.Context, id uuid.UUID) (entity.User, error) {
	var model models.User
	if err := r.db.WithContext(ctx).Preload("Settings").Preload("Profile").Preload("Stats").First(&model, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return entity.User{}, repository.ErrNotFound
		}
		return entity.User{}, err
	}
	return *model.ToEntity(), nil
}

func (r *userRepository) FindByEmail(ctx context.Context, email string) (entity.User, error) {
	var model models.User
	if err := r.db.WithContext(ctx).Preload("Settings").Preload("Profile").Preload("Stats").First(&model, "email = ?", email).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return entity.User{}, repository.ErrNotFound
		}
		return entity.User{}, err
	}
	return *model.ToEntity(), nil
}

func (r *userRepository) FindByUsername(ctx context.Context, username string) (entity.User, error) {
	var model models.User
	if err := r.db.WithContext(ctx).Preload("Settings").Preload("Profile").Preload("Stats").First(&model, "username = ?", username).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return entity.User{}, repository.ErrNotFound
		}
		return entity.User{}, err
	}
	return *model.ToEntity(), nil
}

func (r *userRepository) Update(ctx context.Context, user entity.User) (entity.User, error) {
	model := &models.User{}
	model.FromEntity(&user)

	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		return entity.User{}, err
	}

	return *model.ToEntity(), nil
}

func (r *userRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.User{}, "id = ?", id).Error
}

func (r *userRepository) Search(ctx context.Context, query string) ([]entity.User, error) {
	var models []models.User
	if err := r.db.WithContext(ctx).Preload("Settings").Preload("Profile").Preload("Stats").
		Where("username ILIKE ? OR email ILIKE ?", "%"+query+"%", "%"+query+"%").
		Find(&models).Error; err != nil {
		return nil, err
	}

	users := make([]entity.User, len(models))
	for i, model := range models {
		users[i] = *model.ToEntity()
	}
	return users, nil
}

func (r *userRepository) ChangePassword(ctx context.Context, userID uuid.UUID, hashedPassword string) error {
	return r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Update("password", hashedPassword).Error
}

type userProfileRepository struct {
	db *gorm.DB
}

func NewUserProfileRepository(db *gorm.DB) repository.UserProfileRepository {
	return &userProfileRepository{db: db}
}

func (r *userProfileRepository) Create(ctx context.Context, profile entity.UserProfile) (entity.UserProfile, error) {
	model := &models.UserProfile{}
	model.FromEntity(&profile)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return entity.UserProfile{}, err
	}

	return *model.ToEntity(), nil
}

func (r *userProfileRepository) FindByUserID(ctx context.Context, userID uuid.UUID) (entity.UserProfile, error) {
	var model models.UserProfile
	if err := r.db.WithContext(ctx).First(&model, "user_id = ?", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return entity.UserProfile{}, repository.ErrNotFound
		}
		return entity.UserProfile{}, err
	}
	return *model.ToEntity(), nil
}

func (r *userProfileRepository) Update(ctx context.Context, profile entity.UserProfile) (entity.UserProfile, error) {
	model := &models.UserProfile{}
	model.FromEntity(&profile)

	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		return entity.UserProfile{}, err
	}

	return *model.ToEntity(), nil
}

func (r *userProfileRepository) Delete(ctx context.Context, userID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.UserProfile{}, "user_id = ?", userID).Error
}

// type userStatsRepository struct {
// 	db *gorm.DB
// }

// func NewUserStatsRepository(db *gorm.DB) repository.UserStatsRepository {
// 	return &userStatsRepository{db: db}
// }

// func (r *userStatsRepository) Create(ctx context.Context, stats entity.UserStats) (entity.UserStats, error) {
// 	model := &models.UserStats{}
// 	model.FromEntity(&stats)

// 	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
// 		return entity.UserStats{}, err
// 	}

// 	return *model.ToEntity(), nil
// }

// func (r *userStatsRepository) FindByUserID(ctx context.Context, userID uuid.UUID) (entity.UserStats, error) {
// 	var model models.UserStats
// 	if err := r.db.WithContext(ctx).First(&model, "user_id = ?", userID).Error; err != nil {
// 		if errors.Is(err, gorm.ErrRecordNotFound) {
// 			return entity.UserStats{}, repository.ErrNotFound
// 		}
// 		return entity.UserStats{}, err
// 	}
// 	return *model.ToEntity(), nil
// }

// func (r *userStatsRepository) Update(ctx context.Context, stats entity.UserStats) (entity.UserStats, error) {
// 	model := &models.UserStats{}
// 	model.FromEntity(&stats)

// 	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
// 		return entity.UserStats{}, err
// 	}

// 	return *model.ToEntity(), nil
// }

// func (r *userStatsRepository) Delete(ctx context.Context, userID uuid.UUID) error {
// 	return r.db.WithContext(ctx).Delete(&models.UserStats{}, "user_id = ?", userID).Error
// }

// func (r *userStatsRepository) GetProgressReport(ctx context.Context, userID uuid.UUID, period string) (entity.UserStats, error) {
// 	var model models.UserStats
// 	// TODO: Implement period-based filtering
// 	if err := r.db.WithContext(ctx).First(&model, "user_id = ?", userID).Error; err != nil {
// 		if errors.Is(err, gorm.ErrRecordNotFound) {
// 			return entity.UserStats{}, repository.ErrNotFound
// 		}
// 		return entity.UserStats{}, err
// 	}
// 	return *model.ToEntity(), nil
// }

type userWordRepository struct {
	db *gorm.DB
}

func NewUserWordRepository(db *gorm.DB) repository.UserWordRepository {
	return &userWordRepository{db: db}
}

func (r *userWordRepository) Create(ctx context.Context, userWord *entity.UserWord) (*entity.UserWord, error) {
	model := &models.UserWord{}
	model.FromEntity(userWord)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return nil, err
	}

	return model.ToEntity(), nil
}

func (r *userWordRepository) BatchCreate(ctx context.Context, userWords []*entity.UserWord) error {
	if len(userWords) == 0 {
		return nil
	}
	//	Convert entity.UserWord to a slice of maps for batch insert
	values := make([]map[string]interface{}, len(userWords))
	for i, uw := range userWords {
		values[i] = map[string]interface{}{
			"id":          uw.ID,
			"user_id":     uw.UserID,
			"word_id":     uw.WordID,
			"is_learned":  uw.IsLearned,
			"is_favorite": uw.IsFavorite,
		}
	}

	if err := r.db.WithContext(ctx).CreateInBatches(values, 100).Error; err != nil {
		return err
	}
	return nil
}

func (r *userWordRepository) FindByID(ctx context.Context, id uuid.UUID) (*entity.UserWord, error) {
	var model models.UserWord
	if err := r.db.WithContext(ctx).First(&model, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return model.ToEntity(), nil
}

func (r *userWordRepository) FindByUserID(ctx context.Context, userID uuid.UUID) ([]entity.UserWord, error) {
	var modelList []models.UserWord
	if err := r.db.WithContext(ctx).Find(&modelList, "user_id = ?", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	// Convert each models.UserWord to entity.UserWord
	result := make([]entity.UserWord, len(modelList))
	for i, model := range modelList {
		result[i] = *model.ToEntity()
	}
	return result, nil
}

func (r *userWordRepository) FindByUserAndWordID(ctx context.Context, userID, wordID uuid.UUID) (entity.UserWord, error) {
	var model models.UserWord
	if err := r.db.WithContext(ctx).First(&model, "user_id = ? AND word_id = ?", userID, wordID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return entity.UserWord{}, repository.ErrNotFound
		}
		return entity.UserWord{}, err
	}
	return *model.ToEntity(), nil
}

func (r *userWordRepository) GetFavorites(ctx context.Context, userID uuid.UUID, offset, limit int) ([]entity.UserWord, error) {
	var modelList []models.UserWord
	if err := r.db.WithContext(ctx).Find(&modelList, "user_id = ? AND is_favorite = true", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	// 	// Convert each models.UserWord to entity.UserWord
	result := make([]entity.UserWord, len(modelList))
	for i, model := range modelList {
		result[i] = *model.ToEntity()
	}
	return result, nil
}

func (r *userWordRepository) GetLearned(ctx context.Context, userID uuid.UUID, offset, limit int) ([]entity.UserWord, error) {
	var modelList []models.UserWord
	if err := r.db.WithContext(ctx).Find(&modelList, "user_id = ? AND is_learned = true", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	// 	// Convert each models.UserWord to entity.UserWord
	result := make([]entity.UserWord, len(modelList))
	for i, model := range modelList {
		result[i] = *model.ToEntity()
	}
	return result, nil
}

func (r *userWordRepository) GetUserWordStats(ctx context.Context, userID uuid.UUID) (int, int, error) {
	type Result struct {
		LearnedCount  int
		FavoriteCount int
	}

	var result Result
	err := r.db.WithContext(ctx).Model(&models.UserWord{}).
		Select(
			"SUM(CASE WHEN is_learned = true THEN 1 ELSE 0 END) as learned_count",
			"SUM(CASE WHEN is_favorite = true THEN 1 ELSE 0 END) as favorite_count",
		).
		Where("user_id = ?", userID).
		Scan(&result).Error

	if err != nil {
		return 0, 0, err
	}

	return result.LearnedCount, result.FavoriteCount, nil
}

func (r *userWordRepository) Search(ctx context.Context, userID uuid.UUID, query string, offset, limit int) ([]entity.UserWord, error) {
	var modelList []models.UserWord
	if err := r.db.WithContext(ctx).Find(&modelList, "user_id = ? AND (word_id = ? OR word_id = ?)", userID, query, query).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	// Convert each models.UserWord to entity.UserWord
	result := make([]entity.UserWord, len(modelList))
	for i, model := range modelList {
		result[i] = *model.ToEntity()
	}
	return result, nil
}

func (r *userWordRepository) Update(ctx context.Context, userWord *entity.UserWord) error {
	model := &models.UserWord{}
	model.FromEntity(userWord)

	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		return err
	}

	// 	 Update the original userWord with the updated values
	updated := model.ToEntity()
	*userWord = *updated
	return nil
}

func (r *userWordRepository) Delete(ctx context.Context, userID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.UserWord{}, "user_id = ?", userID).Error
}
