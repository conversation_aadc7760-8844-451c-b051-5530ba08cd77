package repository

import (
	"context"
	"languagelearning/domain/user/entity"

	"github.com/google/uuid"
)

// TransactionManager defines the interface for transaction management
type TransactionManager interface {
	WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error
}

// UserRepository defines the interface for user data operations
type UserRepository interface {
	Create(ctx context.Context, user entity.User) (entity.User, error)
	FindByID(ctx context.Context, id uuid.UUID) (entity.User, error)
	FindByEmail(ctx context.Context, email string) (entity.User, error)
	FindByUsername(ctx context.Context, username string) (entity.User, error)
	Update(ctx context.Context, user entity.User) (entity.User, error)
	Delete(ctx context.Context, id uuid.UUID) error
	Search(ctx context.Context, query string) ([]entity.User, error)
	ChangePassword(ctx context.Context, userID uuid.UUID, hashedPassword string) error
}

// UserProfileRepository defines the interface for user profile data operations
type UserProfileRepository interface {
	Create(ctx context.Context, profile entity.UserProfile) (entity.UserProfile, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) (entity.UserProfile, error)
	Update(ctx context.Context, profile entity.UserProfile) (entity.UserProfile, error)
	Delete(ctx context.Context, userID uuid.UUID) error
}

// UserStatsRepository defines the interface for user statistics data operations
type UserStatsRepository interface {
	Create(ctx context.Context, stats entity.UserStats) (entity.UserStats, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) (entity.UserStats, error)
	Update(ctx context.Context, stats entity.UserStats) (entity.UserStats, error)
	Delete(ctx context.Context, userID uuid.UUID) error
	GetProgressReport(ctx context.Context, userID uuid.UUID, period string) (entity.UserStats, error)
}

type UserWordRepository interface {
	Create(ctx context.Context, userWord *entity.UserWord) (*entity.UserWord, error)
	FindByID(ctx context.Context, id uuid.UUID) (*entity.UserWord, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]entity.UserWord, error)
	FindByUserAndWordID(ctx context.Context, userID, wordID uuid.UUID) (entity.UserWord, error)
	Update(ctx context.Context, userWord *entity.UserWord) error
	Delete(ctx context.Context, id uuid.UUID) error
	BatchCreate(ctx context.Context, userWords []*entity.UserWord) error
	GetUserWordStats(ctx context.Context, userID uuid.UUID) (int, int, error)
	Search(ctx context.Context, userID uuid.UUID, query string, offset, limit int) ([]entity.UserWord, error)
	GetFavorites(ctx context.Context, userID uuid.UUID, offset, limit int) ([]entity.UserWord, error)
	GetLearned(ctx context.Context, userID uuid.UUID, offset, limit int) ([]entity.UserWord, error)
}

// AchievementRepository defines the interface for achievement data operations
type AchievementRepository interface {
	Create(ctx context.Context, achievement entity.Achievement) (entity.Achievement, error)
	FindByID(ctx context.Context, id uuid.UUID) (entity.Achievement, error)
	FindAll(ctx context.Context) ([]entity.Achievement, error)
	Update(ctx context.Context, achievement entity.Achievement) (entity.Achievement, error)
	Delete(ctx context.Context, id uuid.UUID) error
}

// UserAchievementRepository defines the interface for user achievement data operations
type UserAchievementRepository interface {
	Create(ctx context.Context, userAchievement entity.UserAchievement) (entity.UserAchievement, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]entity.UserAchievement, error)
	FindByID(ctx context.Context, id uuid.UUID) (entity.UserAchievement, error)
	FindByUserAndAchievementID(ctx context.Context, userID, achievementID uuid.UUID) (entity.UserAchievement, error)
	Update(ctx context.Context, userAchievement entity.UserAchievement) (entity.UserAchievement, error)
	Delete(ctx context.Context, id uuid.UUID) error
	FindByUserIDAndType(ctx context.Context, userID uuid.UUID, achievementType entity.AchievementType) ([]entity.UserAchievement, error)
}

// NotificationRepository defines the interface for notification data operations
type NotificationRepository interface {
	Create(ctx context.Context, notification entity.Notification) (entity.Notification, error)
	FindByUserID(ctx context.Context, userID uuid.UUID, status entity.NotificationStatus, limit, offset int) ([]entity.Notification, error)
	FindByID(ctx context.Context, id uuid.UUID) (entity.Notification, error)
	Update(ctx context.Context, notification entity.Notification) (entity.Notification, error)
	Delete(ctx context.Context, id uuid.UUID) error
	DeleteByUserID(ctx context.Context, userID uuid.UUID) error
}

// NotificationPreferenceRepository defines the interface for notification preference data operations
type NotificationPreferenceRepository interface {
	Create(ctx context.Context, preference entity.NotificationPreference) (entity.NotificationPreference, error)
	FindByUserID(ctx context.Context, userID uuid.UUID) (entity.NotificationPreference, error)
	Update(ctx context.Context, preference entity.NotificationPreference) (entity.NotificationPreference, error)
	Delete(ctx context.Context, userID uuid.UUID) error
}
