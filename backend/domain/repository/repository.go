package repository

import "context"

// Repository 定义所有存储库必须实现的通用接口
type Repository[T any, ID any] interface {
	// FindByID 通过ID获取实体
	FindByID(ctx context.Context, id ID) (T, error)

	// FindAll 获取所有实体
	FindAll(ctx context.Context) ([]T, error)

	// Create 创建一个新实体
	Create(ctx context.Context, entity T) (T, error)

	// Update 更新实体
	Update(ctx context.Context, entity T) (T, error)

	// Delete 通过ID删除实体
	Delete(ctx context.Context, id ID) error

	// Count 获取实体数量
	Count(ctx context.Context) (int64, error)
}

// Pageable 分页请求接口
type Pageable interface {
	GetPage() int
	GetSize() int
	GetSort() []string
}

// PageRequest 分页请求实现
type PageRequest struct {
	Page int      `json:"page"`
	Size int      `json:"size"`
	Sort []string `json:"sort,omitempty"`
}

// GetPage 获取页码
func (p PageRequest) GetPage() int {
	return p.Page
}

// GetSize 获取每页大小
func (p PageRequest) GetSize() int {
	return p.Size
}

// GetSort 获取排序
func (p PageRequest) GetSort() []string {
	return p.Sort
}

// Page 分页结果
type Page[T any] struct {
	Content       []T   `json:"content"`
	TotalElements int64 `json:"totalElements"`
	TotalPages    int   `json:"totalPages"`
	Page          int   `json:"page"`
	Size          int   `json:"size"`
	HasNext       bool  `json:"hasNext"`
	HasPrevious   bool  `json:"hasPrevious"`
}

// PageableRepository 支持分页的存储库接口
type PageableRepository[T any, ID any] interface {
	Repository[T, ID]

	// FindPage 分页查询
	FindPage(ctx context.Context, pageable Pageable) (Page[T], error)
}

// SearchableRepository 支持搜索的存储库接口
type SearchableRepository[T any, ID any] interface {
	Repository[T, ID]

	// Search 搜索实体
	Search(ctx context.Context, query string) ([]T, error)

	// SearchPage 分页搜索
	SearchPage(ctx context.Context, query string, pageable Pageable) (Page[T], error)
}
