package event

import (
	"time"

	"github.com/google/uuid"
)

// ExerciseStatsUpdatedEvent 练习统计更新事件
type ExerciseStatsUpdatedEvent struct {
	ExerciseID    uuid.UUID `json:"exerciseId"`
	ExerciseType  string    `json:"exerciseType"`
	SuccessRate   float64   `json:"successRate"`
	TotalAttempts int       `json:"totalAttempts"`
	Timestamp     time.Time `json:"timestamp"`
}

// NewExerciseStatsUpdatedEvent 创建练习统计更新事件
func NewExerciseStatsUpdatedEvent(
	exerciseID uuid.UUID,
	exerciseType string,
	successRate float64,
	totalAttempts int,
) *ExerciseStatsUpdatedEvent {
	return &ExerciseStatsUpdatedEvent{
		ExerciseID:    exerciseID,
		ExerciseType:  exerciseType,
		SuccessRate:   successRate,
		TotalAttempts: totalAttempts,
		Timestamp:     time.Now(),
	}
}

// GetEventType 获取事件类型
func (e *ExerciseStatsUpdatedEvent) GetEventType() string {
	return "exercise.stats.updated"
}

// GetTimestamp 获取事件时间戳
func (e *ExerciseStatsUpdatedEvent) GetTimestamp() time.Time {
	return e.Timestamp
}
