package event

import (
	"fmt"
	"log"

	"languagelearning/domain/core/event"
)

// ExerciseAttemptedHandler 練習嘗試事件處理器
type ExerciseAttemptedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 學習路徑服務
	// - 成就服務
}

// NewExerciseAttemptedHandler 創建練習嘗試事件處理器
func NewExerciseAttemptedHandler() *ExerciseAttemptedHandler {
	return &ExerciseAttemptedHandler{}
}

// Handle 處理練習嘗試事件
func (h *ExerciseAttemptedHandler) Handle(e event.Event) error {
	attemptedEvent, ok := e.(*ExerciseAttemptedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶學習統計
	// 2. 更新學習路徑進度
	// 3. 檢查是否需要調整練習難度
	// 4. 檢查是否需要解鎖成就

	log.Printf("Exercise attempted: %s by user %s, score: %s, duration: %s",
		attemptedEvent.ExerciseID,
		attemptedEvent.UserID,
		attemptedEvent.Score,
		attemptedEvent.Duration,
	)
	return nil
}

// ExerciseDifficultyChangedHandler 練習難度變更事件處理器
type ExerciseDifficultyChangedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 學習路徑服務
}

// NewExerciseDifficultyChangedHandler 創建練習難度變更事件處理器
func NewExerciseDifficultyChangedHandler() *ExerciseDifficultyChangedHandler {
	return &ExerciseDifficultyChangedHandler{}
}

// Handle 處理練習難度變更事件
func (h *ExerciseDifficultyChangedHandler) Handle(e event.Event) error {
	changedEvent, ok := e.(*ExerciseDifficultyChangedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶學習統計
	// 2. 更新學習路徑進度
	// 3. 發送難度變更通知

	log.Printf("Exercise difficulty changed: %s for user %s, from %s to %s, reason: %s",
		changedEvent.ExerciseID,
		changedEvent.UserID,
		changedEvent.OldDifficulty,
		changedEvent.NewDifficulty,
		changedEvent.Reason,
	)
	return nil
}

// RegisterExerciseEventHandlers 註冊所有練習相關的事件處理器
func RegisterExerciseEventHandlers(bus event.EventBus) error {
	// 註冊練習嘗試事件處理器
	attemptedHandler := NewExerciseAttemptedHandler()
	if err := bus.Subscribe("exercise.attempted", attemptedHandler); err != nil {
		return fmt.Errorf("failed to register exercise attempted handler: %w", err)
	}

	// 註冊練習難度變更事件處理器
	difficultyChangedHandler := NewExerciseDifficultyChangedHandler()
	if err := bus.Subscribe("exercise.difficulty_changed", difficultyChangedHandler); err != nil {
		return fmt.Errorf("failed to register exercise difficulty changed handler: %w", err)
	}

	return nil
}
