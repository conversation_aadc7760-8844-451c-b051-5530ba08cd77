package event

import (
	"time"

	"languagelearning/domain/core/event"
	"languagelearning/domain/exercise/entity"

	"github.com/google/uuid"
)

// ExerciseAttemptedEvent 練習嘗試事件
type ExerciseAttemptedEvent struct {
	*event.BaseEvent
	ExerciseID  uuid.UUID        `json:"exercise_id"`
	UserID      uuid.UUID        `json:"user_id"`
	Score       *entity.Score    `json:"score"`
	Duration    *entity.Duration `json:"duration"`
	AttemptedAt time.Time        `json:"attempted_at"`
}

// NewExerciseAttemptedEvent 創建練習嘗試事件
func NewExerciseAttemptedEvent(exerciseID, userID uuid.UUID, score entity.Score, duration entity.Duration, difficulty entity.ExerciseDifficulty, isCorrect bool) *ExerciseAttemptedEvent {
	return &ExerciseAttemptedEvent{
		BaseEvent: event.NewBaseEvent(
			"exercise.attempted",
			exerciseID,
			"exercise",
			score,
		),
		ExerciseID:  exerciseID,
		UserID:      userID,
		Score:       &score,
		Duration:    &duration,
		AttemptedAt: time.Now(),
	}
}

// ExerciseDifficultyChangedEvent 練習難度變更事件
type ExerciseDifficultyChangedEvent struct {
	*event.BaseEvent
	ExerciseID    uuid.UUID                  `json:"exercise_id"`
	UserID        uuid.UUID                  `json:"user_id"`
	OldDifficulty *entity.ExerciseDifficulty `json:"old_difficulty"`
	NewDifficulty *entity.ExerciseDifficulty `json:"new_difficulty"`
	Reason        string                     `json:"reason"`
	ChangedAt     time.Time                  `json:"changed_at"`
}

// NewExerciseDifficultyChangedEvent 創建練習難度變更事件
func NewExerciseDifficultyChangedEvent(
	exerciseID, userID uuid.UUID,
	oldDifficulty, newDifficulty *entity.ExerciseDifficulty,
	reason string,
) *ExerciseDifficultyChangedEvent {
	return &ExerciseDifficultyChangedEvent{
		BaseEvent: event.NewBaseEvent(
			"exercise.difficulty_changed",
			exerciseID,
			"exercise",
			newDifficulty,
		),
		ExerciseID:    exerciseID,
		UserID:        userID,
		OldDifficulty: oldDifficulty,
		NewDifficulty: newDifficulty,
		Reason:        reason,
		ChangedAt:     time.Now(),
	}
}
