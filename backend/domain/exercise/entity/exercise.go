package entity

import (
	"time"

	"languagelearning/domain/core/entity"
	coreentity "languagelearning/domain/core/entity"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// ExerciseType 练习类型
type ExerciseType string

const (
	// 基础练习类型
	// MultipleChoice 多选题
	MultipleChoice ExerciseType = "multiple_choice"
	// FillInBlank 填空题
	FillInBlank ExerciseType = "fill_in_blank"
	// Matching 匹配题
	Matching ExerciseType = "matching"
	// TrueFalse 判断题
	TrueFalse ExerciseType = "true_false"
	// OpenEnded 开放性问题
	OpenEnded ExerciseType = "open_ended"

	// 技能练习类型
	// SpeakingExercise 口语练习
	SpeakingExercise ExerciseType = "speaking"
	// ListeningExercise 听力练习
	ListeningExercise ExerciseType = "listening"
	// WritingExercise 写作练习
	WritingExercise ExerciseType = "writing"
	// ReadingExercise 阅读练习
	ReadingExercise ExerciseType = "reading"

	// 学科练习类型 (兼容learning领域)
	// VocabularyExercise 词汇练习
	VocabularyExercise ExerciseType = "vocabulary"
	// GrammarExercise 语法练习
	GrammarExercise ExerciseType = "grammar"
)

// Difficulty 难度级别
type Difficulty string

const (
	// Easy 简单难度
	Easy Difficulty = "easy"
	// Medium 中等难度
	Medium Difficulty = "medium"
	// Hard 困难难度
	Hard Difficulty = "hard"
	// Expert 专家难度
	Expert Difficulty = "expert"
)

// ExerciseStats 练习统计信息
type ExerciseStats struct {
	TotalAttempts int     `json:"totalAttempts"`
	SuccessCount  int     `json:"successCount"`
	SuccessRate   float64 `json:"successRate"`
}

// Exercise 练习实体
type Exercise struct {
	ID            uuid.UUID                      `json:"id"`
	Type          ExerciseType                   `json:"type"`
	Title         string                         `json:"title"`
	Description   string                         `json:"description"`
	Content       string                         `json:"content"`
	Difficulty    *coreentity.ExerciseDifficulty `json:"difficulty"`
	Question      string                         `json:"question"`
	Instructions  string                         `json:"instructions,omitempty"`
	Options       pq.StringArray                 `json:"options,omitempty"`
	CorrectAnswer string                         `json:"correctAnswer,omitempty"`
	Explanation   string                         `json:"explanation,omitempty"`
	Points        int                            `json:"points"`
	TimeLimit     *coreentity.Duration           `json:"timeLimit,omitempty"`
	MediaURL      string                         `json:"mediaUrl,omitempty"`
	MediaType     string                         `json:"mediaType,omitempty"`
	Tags          pq.StringArray                 `json:"tags,omitempty"`
	Language      *coreentity.Language           `json:"language"`
	AuthorID      uuid.UUID                      `json:"authorId"`
	IsPublished   bool                           `json:"isPublished"`
	Stats         *ExerciseStats                 `json:"stats,omitempty"`
	CreatedAt     time.Time                      `json:"createdAt"`
	UpdatedAt     time.Time                      `json:"updatedAt"`
}

// NewExercise 创建新练习
func NewExercise(exerciseType ExerciseType, title string, difficulty *coreentity.ExerciseDifficulty, question string, correctAnswer string, language *entity.Language, authorID uuid.UUID) *Exercise {
	now := time.Now()
	return &Exercise{
		ID:            uuid.New(),
		Type:          exerciseType,
		Title:         title,
		Difficulty:    difficulty,
		Question:      question,
		CorrectAnswer: correctAnswer,
		Points:        10,
		Language:      language,
		AuthorID:      authorID,
		IsPublished:   false,
		CreatedAt:     now,
		UpdatedAt:     now,
	}
}

// NewExerciseWithContent 创建包含内容的新练习
func NewExerciseWithContent(exerciseType ExerciseType, title, description, content string, difficulty *coreentity.ExerciseDifficulty, language *entity.Language, authorID uuid.UUID) *Exercise {
	now := time.Now()
	return &Exercise{
		ID:          uuid.New(),
		Type:        exerciseType,
		Title:       title,
		Description: description,
		Content:     content,
		Difficulty:  difficulty,
		Points:      10,
		Language:    language,
		AuthorID:    authorID,
		IsPublished: false,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// SetDifficulty 設置練習難度
func (e *Exercise) SetDifficulty(difficulty *coreentity.ExerciseDifficulty) {
	e.Difficulty = difficulty
	e.UpdatedAt = time.Now()
}

// SetLanguage 設置練習語言
func (e *Exercise) SetLanguage(language *entity.Language) {
	e.Language = language
	e.UpdatedAt = time.Now()
}

// SetTimeLimit 設置時間限制
func (e *Exercise) SetTimeLimit(minutes int) error {
	duration, err := entity.NewDuration(minutes)
	if err != nil {
		return err
	}
	e.TimeLimit = duration
	e.UpdatedAt = time.Now()
	return nil
}

// Publish 發布練習
func (e *Exercise) Publish() {
	e.IsPublished = true
	e.UpdatedAt = time.Now()
}

// Unpublish 取消發布練習
func (e *Exercise) Unpublish() {
	e.IsPublished = false
	e.UpdatedAt = time.Now()
}

// SetTitle 設置練習標題
func (e *Exercise) SetTitle(title string) {
	e.Title = title
	e.UpdatedAt = time.Now()
}

// SetDescription 設置練習描述
func (e *Exercise) SetDescription(description string) {
	e.Description = description
	e.UpdatedAt = time.Now()
}

// SetContent 設置練習內容
func (e *Exercise) SetContent(content string) {
	e.Content = content
	e.UpdatedAt = time.Now()
}

// TypeMapping 类型映射，用于兼容不同领域的类型定义
var TypeMapping = map[string]ExerciseType{
	// 英文映射 (兼容learning领域)
	"vocabulary": VocabularyExercise,
	"grammar":    GrammarExercise,
	"listening":  ListeningExercise,
	"speaking":   SpeakingExercise,
	"reading":    ReadingExercise,
	"writing":    WritingExercise,

	// 基础类型映射
	"multiple_choice": MultipleChoice,
	"fill_in_blank":   FillInBlank,
	"true_false":      TrueFalse,
	"matching":        Matching,
	"open_ended":      OpenEnded,
}

// ConvertFromString 从字符串转换为ExerciseType
func ConvertFromString(typeStr string) ExerciseType {
	if mappedType, exists := TypeMapping[typeStr]; exists {
		return mappedType
	}
	return ExerciseType(typeStr)
}

// IsValidType 检查练习类型是否有效
func (t ExerciseType) IsValidType() bool {
	switch t {
	case MultipleChoice, FillInBlank, Matching, TrueFalse, OpenEnded,
		SpeakingExercise, ListeningExercise, WritingExercise, ReadingExercise,
		VocabularyExercise, GrammarExercise:
		return true
	default:
		return false
	}
}
