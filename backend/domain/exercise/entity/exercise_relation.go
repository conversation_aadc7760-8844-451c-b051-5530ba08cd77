package entity

import (
	"time"

	"github.com/google/uuid"
)

// ExerciseRelation 表示两个练习之间的关系
type ExerciseRelation struct {
	ID               uuid.UUID `json:"id"`
	SourceExerciseID uuid.UUID `json:"sourceExerciseId"`
	TargetExerciseID uuid.UUID `json:"targetExerciseId"`
	RelationType     string    `json:"relationType"` // prerequisite（前置条件）, similar（相似）, complementary（互补）, etc.
	Strength         float64   `json:"strength"`     // 关系强度，0-1
	Metadata         string    `json:"metadata"`     // 附加元数据，JSON格式
	CreatedAt        time.Time `json:"createdAt"`
	UpdatedAt        time.Time `json:"updatedAt"`
}

// NewExerciseRelation 创建一个新的练习关系
func NewExerciseRelation(sourceID, targetID uuid.UUID, relationType string, strength float64) *ExerciseRelation {
	now := time.Now()
	return &ExerciseRelation{
		ID:               uuid.New(),
		SourceExerciseID: sourceID,
		TargetExerciseID: targetID,
		RelationType:     relationType,
		Strength:         strength,
		CreatedAt:        now,
		UpdatedAt:        now,
	}
}

// IsPrerequisite 判断关系是否为前置条件
func (r *ExerciseRelation) IsPrerequisite() bool {
	return r.RelationType == "prerequisite"
}

// IsSimilar 判断关系是否为相似
func (r *ExerciseRelation) IsSimilar() bool {
	return r.RelationType == "similar"
}

// IsComplementary 判断关系是否为互补
func (r *ExerciseRelation) IsComplementary() bool {
	return r.RelationType == "complementary"
}

// IsStrong 判断关系是否为强关系
func (r *ExerciseRelation) IsStrong(threshold float64) bool {
	return r.Strength >= threshold
}
