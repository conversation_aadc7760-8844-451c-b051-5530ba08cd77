package entity

import (
	"time"

	"github.com/google/uuid"
)

// ExerciseAttempt 表示用户尝试练习的记录
type ExerciseAttempt struct {
	ID             uuid.UUID  `json:"id"`
	UserID         uuid.UUID  `json:"userId"`
	ExerciseID     uuid.UUID  `json:"exerciseId"`
	UserAnswer     string     `json:"userAnswer"`
	IsCorrect      bool       `json:"isCorrect"`
	Score          *Score     `json:"score"`
	Duration       *Duration  `json:"duration"`
	AttemptedAt    time.Time  `json:"attemptedAt"`
	Feedback       string     `json:"feedback,omitempty"`
	LessonID       *uuid.UUID `json:"lessonId,omitempty"`
	LearningPathID *uuid.UUID `json:"learningPathId,omitempty"`
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      time.Time  `json:"updatedAt"`
}

// ExerciseAttemptSummary 表示用户练习尝试的统计摘要
type ExerciseAttemptSummary struct {
	TotalAttempts    int64   `json:"totalAttempts"`
	CorrectAttempts  int64   `json:"correctAttempts"`
	SuccessRate      float64 `json:"successRate"`
	AverageScore     float64 `json:"averageScore"`
	AverageTimeSpent float64 `json:"averageTimeSpent"`
	TotalTimeSpent   int64   `json:"totalTimeSpent"`
}

// NewExerciseAttempt 创建一个新的练习尝试记录
func NewExerciseAttempt(userID, exerciseID uuid.UUID, userAnswer string, timeSpentSeconds int) (*ExerciseAttempt, error) {
	// 將秒轉換為分鐘
	minutes := timeSpentSeconds / 60
	if timeSpentSeconds%60 > 0 {
		minutes++
	}

	duration, err := NewDuration(minutes)
	if err != nil {
		return nil, err
	}

	score, err := NewScore(0, 10) // 默認滿分為10分
	if err != nil {
		return nil, err
	}

	now := time.Now()
	return &ExerciseAttempt{
		ID:          uuid.New(),
		UserID:      userID,
		ExerciseID:  exerciseID,
		UserAnswer:  userAnswer,
		IsCorrect:   false, // 初始未验证
		Score:       score,
		Duration:    duration,
		AttemptedAt: now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}, nil
}

// UpdateScore 更新分數
func (ea *ExerciseAttempt) UpdateScore(value int) error {
	score, err := NewScore(value, ea.Score.MaxValue())
	if err != nil {
		return err
	}
	ea.Score = score
	ea.IsCorrect = value == ea.Score.MaxValue()
	ea.UpdatedAt = time.Now()
	return nil
}

// UpdateDuration 更新持續時間
func (ea *ExerciseAttempt) UpdateDuration(minutes int) error {
	duration, err := NewDuration(minutes)
	if err != nil {
		return err
	}
	ea.Duration = duration
	ea.UpdatedAt = time.Now()
	return nil
}
