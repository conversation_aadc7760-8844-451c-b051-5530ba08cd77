package entity

import (
	"errors"
	"fmt"
	"time"
)

// Duration 表示時間長度的值對象
type Duration struct {
	minutes int
}

// NewDuration 創建一個新的時間長度值對象
func NewDuration(minutes int) (*Duration, error) {
	if minutes < 0 {
		return nil, errors.New("duration cannot be negative")
	}
	return &Duration{minutes: minutes}, nil
}

// Minutes 獲取分鐘數
func (d Duration) Minutes() int {
	return d.minutes
}

// Hours 獲取小時數
func (d Duration) Hours() float64 {
	return float64(d.minutes) / 60.0
}

// String 返回字符串表示
func (d Duration) String() string {
	if d.minutes < 60 {
		return fmt.Sprintf("%d分鐘", d.minutes)
	}
	hours := d.minutes / 60
	remainingMinutes := d.minutes % 60
	if remainingMinutes == 0 {
		return fmt.Sprintf("%d小時", hours)
	}
	return fmt.Sprintf("%d小時%d分鐘", hours, remainingMinutes)
}

// Score 表示分數的值對象
type Score struct {
	value     int
	maxValue  int
	timestamp time.Time
}

// NewScore 創建一個新的分數值對象
func NewScore(value, maxValue int) (*Score, error) {
	if value < 0 || maxValue <= 0 || value > maxValue {
		return nil, errors.New("invalid score value or max value")
	}
	return &Score{
		value:     value,
		maxValue:  maxValue,
		timestamp: time.Now(),
	}, nil
}

// Value 獲取分數值
func (s Score) Value() int {
	return s.value
}

// MaxValue 獲取最大分數值
func (s Score) MaxValue() int {
	return s.maxValue
}

// Percentage 獲取百分比
func (s Score) Percentage() float64 {
	return float64(s.value) / float64(s.maxValue) * 100
}

// Timestamp 獲取時間戳
func (s Score) Timestamp() time.Time {
	return s.timestamp
}

// String 返回字符串表示
func (s Score) String() string {
	return fmt.Sprintf("%d/%d (%.1f%%)", s.value, s.maxValue, s.Percentage())
}

// ExerciseDifficulty 表示練習難度的值對象
type ExerciseDifficulty struct {
	level     string
	threshold int // 用於計算的閾值
}

// NewExerciseDifficulty 創建一個新的練習難度值對象
func NewExerciseDifficulty(level string) (*ExerciseDifficulty, error) {
	var threshold int
	switch level {
	case "easy":
		threshold = 1
	case "medium":
		threshold = 2
	case "hard":
		threshold = 3
	case "expert":
		threshold = 4
	default:
		return nil, errors.New("invalid difficulty level")
	}
	return &ExerciseDifficulty{
		level:     level,
		threshold: threshold,
	}, nil
}

// Level 獲取難度級別
func (d ExerciseDifficulty) Level() string {
	return d.level
}

// Threshold 獲取難度閾值
func (d ExerciseDifficulty) Threshold() int {
	return d.threshold
}

// IsHarderThan 判斷是否比另一個難度更難
func (d ExerciseDifficulty) IsHarderThan(other ExerciseDifficulty) bool {
	return d.threshold > other.threshold
}

// String 返回字符串表示
func (d ExerciseDifficulty) String() string {
	return d.level
}
