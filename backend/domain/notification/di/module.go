package di

import (
	coredi "languagelearning/domain/core/di"
	notificationRepoImpl "languagelearning/domain/notification/repository/impl"
	notificationSvcImpl "languagelearning/domain/notification/service/impl"

	"go.uber.org/dig"
)

// NotificationModule 通知領域的DI模塊
type NotificationModule struct {
	coredi.BaseModule
}

// NewNotificationModule 創建通知領域模塊
func NewNotificationModule() coredi.Module {
	return &NotificationModule{
		BaseModule: coredi.NewBaseModule(
			"notification",
			[]string{"core", "user"}, // 依賴核心模塊和用戶模塊
			[]coredi.ModuleFeature{coredi.FeatureBasic},
		),
	}
}

// Register 註冊通知領域的所有依賴
func (m *NotificationModule) Register(container *dig.Container) error {
	// 註冊存儲庫
	if err := m.registerRepositories(container); err != nil {
		return err
	}

	// 註冊服務
	if err := m.registerServices(container); err != nil {
		return err
	}

	return nil
}

// registerRepositories 註冊通知領域的存儲庫
func (m *NotificationModule) registerRepositories(container *dig.Container) error {
	// 註冊通知存儲庫
	if err := container.Provide(notificationRepoImpl.NewNotificationRepository); err != nil {
		return err
	}

	// 註冊通知偏好存儲庫
	if err := container.Provide(notificationRepoImpl.NewNotificationPreferenceRepository); err != nil {
		return err
	}

	return nil
}

// registerServices 註冊通知領域的服務
func (m *NotificationModule) registerServices(container *dig.Container) error {
	// 註冊領域通知服務
	if err := container.Provide(notificationSvcImpl.NewNotificationService); err != nil {
		return err
	}

	// 舊的應用層通知服務已經移除，現在只使用domain層的服務

	return nil
}
