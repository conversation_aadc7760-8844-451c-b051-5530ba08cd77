package event

import (
	"time"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/notification/entity"

	"github.com/google/uuid"
)

// NotificationCreatedEvent 通知創建事件
type NotificationCreatedEvent struct {
	*coreevent.BaseEvent
	Notification *entity.Notification
	CreatedAt    time.Time
}

// NewNotificationCreatedEvent 創建通知創建事件
func NewNotificationCreatedEvent(notification *entity.Notification) *NotificationCreatedEvent {
	return &NotificationCreatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"notification.created",
			notification.ID,
			"notification",
			map[string]interface{}{
				"notification": notification,
			},
		),
		Notification: notification,
		CreatedAt:    time.Now(),
	}
}

// NotificationReadEvent 通知已讀事件
type NotificationReadEvent struct {
	*coreevent.BaseEvent
	NotificationID uuid.UUID
	UserID         uuid.UUID
	ReadAt         time.Time
}

// NewNotificationReadEvent 創建通知已讀事件
func NewNotificationReadEvent(notificationID, userID uuid.UUID) *NotificationReadEvent {
	return &NotificationReadEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"notification.read",
			notificationID,
			"notification",
			map[string]interface{}{
				"notificationID": notificationID,
				"userID":         userID,
			},
		),
		NotificationID: notificationID,
		UserID:         userID,
		ReadAt:         time.Now(),
	}
}

// NotificationDeletedEvent 通知刪除事件
type NotificationDeletedEvent struct {
	*coreevent.BaseEvent
	NotificationID uuid.UUID
	UserID         uuid.UUID
	DeletedAt      time.Time
	Reason         string
}

// NewNotificationDeletedEvent 創建通知刪除事件
func NewNotificationDeletedEvent(notificationID, userID uuid.UUID, reason string) *NotificationDeletedEvent {
	return &NotificationDeletedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"notification.deleted",
			notificationID,
			"notification",
			map[string]interface{}{
				"notificationID": notificationID,
				"userID":         userID,
				"reason":         reason,
			},
		),
		NotificationID: notificationID,
		UserID:         userID,
		DeletedAt:      time.Now(),
		Reason:         reason,
	}
}

// NotificationExpiredEvent 通知過期事件
type NotificationExpiredEvent struct {
	*coreevent.BaseEvent
	NotificationID uuid.UUID
	UserID         uuid.UUID
	ExpiredAt      time.Time
}

// NewNotificationExpiredEvent 創建通知過期事件
func NewNotificationExpiredEvent(notificationID, userID uuid.UUID) *NotificationExpiredEvent {
	return &NotificationExpiredEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"notification.expired",
			notificationID,
			"notification",
			map[string]interface{}{
				"notificationID": notificationID,
				"userID":         userID,
			},
		),
		NotificationID: notificationID,
		UserID:         userID,
		ExpiredAt:      time.Now(),
	}
}
