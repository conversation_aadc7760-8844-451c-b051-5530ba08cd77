package event

import (
	"encoding/json"
	"fmt"
	"time"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/notification/entity"

	"github.com/google/uuid"
)

// NotificationEventReconstructor 通知事件重構器
type NotificationEventReconstructor struct{}

// NewNotificationEventReconstructor 創建通知事件重構器
func NewNotificationEventReconstructor() *NotificationEventReconstructor {
	return &NotificationEventReconstructor{}
}

// Reconstruct 重構事件
func (r *NotificationEventReconstructor) Reconstruct(eventType string, data map[string]interface{}) (coreevent.Event, error) {
	switch eventType {
	case "notification.created":
		return r.reconstructNotificationCreated(data)
	case "notification.read":
		return r.reconstructNotificationRead(data)
	case "notification.deleted":
		return r.reconstructNotificationDeleted(data)
	case "notification.expired":
		return r.reconstructNotificationExpired(data)
	default:
		return nil, fmt.Errorf("unsupported event type: %s", eventType)
	}
}

// reconstructNotificationCreated 重構通知創建事件
func (r *NotificationEventReconstructor) reconstructNotificationCreated(data map[string]interface{}) (*NotificationCreatedEvent, error) {
	notificationID, err := uuid.Parse(data["notificationID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid notification ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	createdAt, err := time.Parse(time.RFC3339, data["createdAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid created at time: %v", err)
	}

	var rawData json.RawMessage
	if dataStr, ok := data["data"].(string); ok && dataStr != "" {
		rawData = json.RawMessage(dataStr)
	}

	notification := &entity.Notification{
		ID:        notificationID,
		UserID:    userID,
		Type:      entity.NotificationType(data["type"].(string)),
		Title:     data["title"].(string),
		Message:   data["message"].(string),
		Data:      rawData,
		CreatedAt: createdAt,
		UpdatedAt: createdAt,
	}

	return &NotificationCreatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"notification.created",
			notification.ID,
			"notification",
			data,
		),
		Notification: notification,
	}, nil
}

// reconstructNotificationRead 重構通知已讀事件
func (r *NotificationEventReconstructor) reconstructNotificationRead(data map[string]interface{}) (*NotificationReadEvent, error) {
	notificationID, err := uuid.Parse(data["notificationID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid notification ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	readAt, err := time.Parse(time.RFC3339, data["readAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid read at time: %v", err)
	}

	return &NotificationReadEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"notification.read",
			notificationID,
			"notification",
			data,
		),
		NotificationID: notificationID,
		UserID:         userID,
		ReadAt:         readAt,
	}, nil
}

// reconstructNotificationDeleted 重構通知刪除事件
func (r *NotificationEventReconstructor) reconstructNotificationDeleted(data map[string]interface{}) (*NotificationDeletedEvent, error) {
	notificationID, err := uuid.Parse(data["notificationID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid notification ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	deletedAt, err := time.Parse(time.RFC3339, data["deletedAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid deleted at time: %v", err)
	}

	return &NotificationDeletedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"notification.deleted",
			notificationID,
			"notification",
			data,
		),
		NotificationID: notificationID,
		UserID:         userID,
		DeletedAt:      deletedAt,
		Reason:         data["reason"].(string),
	}, nil
}

// reconstructNotificationExpired 重構通知過期事件
func (r *NotificationEventReconstructor) reconstructNotificationExpired(data map[string]interface{}) (*NotificationExpiredEvent, error) {
	notificationID, err := uuid.Parse(data["notificationID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid notification ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	expiredAt, err := time.Parse(time.RFC3339, data["expiredAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid expired at time: %v", err)
	}

	return &NotificationExpiredEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"notification.expired",
			notificationID,
			"notification",
			data,
		),
		NotificationID: notificationID,
		UserID:         userID,
		ExpiredAt:      expiredAt,
	}, nil
}
