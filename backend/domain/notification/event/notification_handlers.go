package event

import (
	"fmt"
	"log"

	"languagelearning/domain/core/event"
)

// NotificationCreatedHandler 通知創建事件處理器
type NotificationCreatedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 通知服務
	// - 推送服務
	// - 郵件服務
}

// NewNotificationCreatedHandler 創建通知創建事件處理器
func NewNotificationCreatedHandler() *NotificationCreatedHandler {
	return &NotificationCreatedHandler{}
}

// Handle 處理通知創建事件
func (h *NotificationCreatedHandler) Handle(e event.Event) error {
	createdEvent, ok := e.(*NotificationCreatedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 根據通知類型選擇發送渠道
	// 2. 發送推送通知
	// 3. 發送郵件通知
	// 4. 更新通知統計

	log.Printf("Notification created: %s for user %s, type: %s, title: %s",
		createdEvent.Notification.ID,
		createdEvent.Notification.UserID,
		createdEvent.Notification.Type,
		createdEvent.Notification.Title,
	)
	return nil
}

// NotificationReadHandler 通知已讀事件處理器
type NotificationReadHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 通知服務
	// - 統計服務
}

// NewNotificationReadHandler 創建通知已讀事件處理器
func NewNotificationReadHandler() *NotificationReadHandler {
	return &NotificationReadHandler{}
}

// Handle 處理通知已讀事件
func (h *NotificationReadHandler) Handle(e event.Event) error {
	readEvent, ok := e.(*NotificationReadEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新通知狀態
	// 2. 更新未讀通知計數
	// 3. 更新通知統計
	// 4. 清理過期通知

	log.Printf("Notification read: %s by user %s at %s",
		readEvent.NotificationID,
		readEvent.UserID,
		readEvent.ReadAt,
	)
	return nil
}

// NotificationDeletedHandler 通知刪除事件處理器
type NotificationDeletedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 通知服務
	// - 統計服務
}

// NewNotificationDeletedHandler 創建通知刪除事件處理器
func NewNotificationDeletedHandler() *NotificationDeletedHandler {
	return &NotificationDeletedHandler{}
}

// Handle 處理通知刪除事件
func (h *NotificationDeletedHandler) Handle(e event.Event) error {
	deletedEvent, ok := e.(*NotificationDeletedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新通知狀態
	// 2. 更新通知統計
	// 3. 清理相關資源
	// 4. 記錄刪除日誌

	log.Printf("Notification deleted: %s by user %s, reason: %s",
		deletedEvent.NotificationID,
		deletedEvent.UserID,
		deletedEvent.Reason,
	)
	return nil
}

// NotificationExpiredHandler 通知過期事件處理器
type NotificationExpiredHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 通知服務
	// - 統計服務
}

// NewNotificationExpiredHandler 創建通知過期事件處理器
func NewNotificationExpiredHandler() *NotificationExpiredHandler {
	return &NotificationExpiredHandler{}
}

// Handle 處理通知過期事件
func (h *NotificationExpiredHandler) Handle(e event.Event) error {
	expiredEvent, ok := e.(*NotificationExpiredEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新通知狀態
	// 2. 更新通知統計
	// 3. 清理過期通知
	// 4. 記錄過期日誌

	log.Printf("Notification expired: %s for user %s at %s",
		expiredEvent.NotificationID,
		expiredEvent.UserID,
		expiredEvent.ExpiredAt,
	)
	return nil
}

// RegisterNotificationEventHandlers 註冊所有通知相關的事件處理器
func RegisterNotificationEventHandlers(bus event.EventBus) error {
	// 註冊通知創建事件處理器
	createdHandler := NewNotificationCreatedHandler()
	if err := bus.Subscribe("notification.created", createdHandler); err != nil {
		return fmt.Errorf("failed to register notification created handler: %w", err)
	}

	// 註冊通知已讀事件處理器
	readHandler := NewNotificationReadHandler()
	if err := bus.Subscribe("notification.read", readHandler); err != nil {
		return fmt.Errorf("failed to register notification read handler: %w", err)
	}

	// 註冊通知刪除事件處理器
	deletedHandler := NewNotificationDeletedHandler()
	if err := bus.Subscribe("notification.deleted", deletedHandler); err != nil {
		return fmt.Errorf("failed to register notification deleted handler: %w", err)
	}

	// 註冊通知過期事件處理器
	expiredHandler := NewNotificationExpiredHandler()
	if err := bus.Subscribe("notification.expired", expiredHandler); err != nil {
		return fmt.Errorf("failed to register notification expired handler: %w", err)
	}

	return nil
}
