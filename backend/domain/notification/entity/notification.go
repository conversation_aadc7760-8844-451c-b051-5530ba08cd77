package entity

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// NotificationType 通知類型
type NotificationType string

const (
	// NotificationTypeSystem 系統通知
	NotificationTypeSystem NotificationType = "system"
	// NotificationTypeAchievement 成就通知
	NotificationTypeAchievement NotificationType = "achievement"
	// NotificationTypeLesson 課程通知
	NotificationTypeLesson NotificationType = "lesson"
	// NotificationTypeStreak 連續學習通知
	NotificationTypeStreak NotificationType = "streak"
	// NotificationTypeReminder 提醒通知
	NotificationTypeReminder NotificationType = "reminder"
	// NotificationTypeWelcome 歡迎通知
	NotificationTypeWelcome NotificationType = "welcome"
	// NotificationTypeLevelUp 升級通知
	NotificationTypeLevelUp NotificationType = "level_up"
	// NotificationTypeEvaluation 評估通知
	NotificationTypeEvaluation NotificationType = "evaluation"
	// NotificationTypeLearning 學習通知
	NotificationTypeLearning NotificationType = "learning"
)

// NotificationStatus represents the status of a notification
type NotificationStatus string

const (
	// NotificationStatusUnread is for unread notifications
	NotificationStatusUnread NotificationStatus = "unread"
	// NotificationStatusRead is for read notifications
	NotificationStatusRead NotificationStatus = "read"
	// NotificationStatusArchived is for archived notifications
	NotificationStatusArchived NotificationStatus = "archived"
)

// Notification 通知實體
type Notification struct {
	ID        uuid.UUID          `json:"id"`
	UserID    uuid.UUID          `json:"userId"`
	Type      NotificationType   `json:"type"`
	Title     string             `json:"title"`
	Message   string             `json:"message"`
	Status    NotificationStatus `json:"status"`
	Data      json.RawMessage    `json:"data,omitempty"`
	ReadAt    *time.Time         `json:"readAt,omitempty"`
	CreatedAt time.Time          `json:"createdAt"`
	UpdatedAt time.Time          `json:"updatedAt"`
}

// NewNotification 創建新通知
func NewNotification(userID uuid.UUID, notificationType NotificationType, title, message string, data interface{}) (*Notification, error) {
	var rawData json.RawMessage
	var err error

	if data != nil {
		rawData, err = json.Marshal(data)
		if err != nil {
			return nil, err
		}
	}

	now := time.Now()
	return &Notification{
		ID:        uuid.New(),
		UserID:    userID,
		Type:      notificationType,
		Title:     title,
		Message:   message,
		Status:    NotificationStatusUnread,
		Data:      rawData,
		CreatedAt: now,
		UpdatedAt: now,
	}, nil
}

// MarkAsRead 標記為已讀
func (n *Notification) MarkAsRead() {
	if n.Status != NotificationStatusRead {
		now := time.Now()
		n.Status = NotificationStatusRead
		n.ReadAt = &now
		n.UpdatedAt = now
	}
}

// MarkAsArchived 標記為已歸檔
func (n *Notification) MarkAsArchived() {
	if n.Status != NotificationStatusArchived {
		n.Status = NotificationStatusArchived
		n.UpdatedAt = time.Now()
	}
}

// GetData 獲取通知數據
func (n *Notification) GetData(target interface{}) error {
	if n.Data == nil {
		return nil
	}
	return json.Unmarshal(n.Data, target)
}

// NotificationPreference 通知偏好設置
type NotificationPreference struct {
	ID                    uuid.UUID `json:"id"`
	UserID                uuid.UUID `json:"userId"`
	EmailEnabled          bool      `json:"emailEnabled"`
	PushEnabled           bool      `json:"pushEnabled"`
	InAppEnabled          bool      `json:"inAppEnabled"`
	DailyDigestEnabled    bool      `json:"dailyDigestEnabled"`
	WeeklyReportEnabled   bool      `json:"weeklyReportEnabled"`
	StreakReminderEnabled bool      `json:"streakReminderEnabled"`
	CreatedAt             time.Time `json:"createdAt"`
	UpdatedAt             time.Time `json:"updatedAt"`
}

// NewNotificationPreference 創建新通知偏好設置
func NewNotificationPreference(userID uuid.UUID) *NotificationPreference {
	now := time.Now()
	return &NotificationPreference{
		ID:                    uuid.New(),
		UserID:                userID,
		EmailEnabled:          true,
		PushEnabled:           true,
		InAppEnabled:          true,
		DailyDigestEnabled:    true,
		WeeklyReportEnabled:   true,
		StreakReminderEnabled: true,
		CreatedAt:             now,
		UpdatedAt:             now,
	}
}

// UpdatePreferences 更新通知偏好設置
func (p *NotificationPreference) UpdatePreferences(prefs map[string]bool) {
	if email, ok := prefs["emailEnabled"]; ok {
		p.EmailEnabled = email
	}
	if push, ok := prefs["pushEnabled"]; ok {
		p.PushEnabled = push
	}
	if inApp, ok := prefs["inAppEnabled"]; ok {
		p.InAppEnabled = inApp
	}
	if dailyDigest, ok := prefs["dailyDigestEnabled"]; ok {
		p.DailyDigestEnabled = dailyDigest
	}
	if weeklyReport, ok := prefs["weeklyReportEnabled"]; ok {
		p.WeeklyReportEnabled = weeklyReport
	}
	if streakReminder, ok := prefs["streakReminderEnabled"]; ok {
		p.StreakReminderEnabled = streakReminder
	}
	p.UpdatedAt = time.Now()
}
