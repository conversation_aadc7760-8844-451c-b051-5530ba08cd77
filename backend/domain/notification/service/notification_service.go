package service

import (
	"context"

	"languagelearning/domain/notification/entity"

	"github.com/google/uuid"
)

// NotificationService defines the interface for notification operations
type NotificationService interface {
	// CreateNotification creates a new notification
	CreateNotification(ctx context.Context, userID uuid.UUID, notificationType entity.NotificationType, title, message string, data interface{}) (*entity.Notification, error)

	// GetUserNotifications gets all notifications for a user
	GetUserNotifications(ctx context.Context, userID uuid.UUID, status *entity.NotificationStatus, limit, offset int) ([]*entity.Notification, int64, error)

	// MarkNotificationAsRead marks a notification as read
	MarkNotificationAsRead(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error

	// MarkAllNotificationsAsRead marks all notifications for a user as read
	MarkAllNotificationsAsRead(ctx context.Context, userID uuid.UUID) error

	// ArchiveNotification archives a notification
	ArchiveNotification(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error

	// GetUserNotificationPreferences gets a user's notification preferences
	GetUserNotificationPreferences(ctx context.Context, userID uuid.UUID) (*entity.NotificationPreference, error)

	// UpdateUserNotificationPreferences updates a user's notification preferences
	UpdateUserNotificationPreferences(ctx context.Context, userID uuid.UUID, preferences *entity.NotificationPreference) error

	// DeleteNotification deletes a notification
	DeleteNotification(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error

	// GetUnreadCount gets the count of unread notifications for a user
	GetUnreadCount(ctx context.Context, userID uuid.UUID) (int, error)
}
