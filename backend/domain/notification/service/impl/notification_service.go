package impl

import (
	"context"
	"fmt"
	"time"

	"languagelearning/domain/core/event"
	"languagelearning/domain/notification/entity"
	notificationEvent "languagelearning/domain/notification/event"
	"languagelearning/domain/notification/repository"
	"languagelearning/domain/notification/service"

	"github.com/google/uuid"
)

// notificationService implements the NotificationService interface
type notificationService struct {
	notificationRepo repository.NotificationRepository
	preferenceRepo   repository.NotificationPreferenceRepository
	eventPublisher   event.EventPublisher
}

// NewNotificationService creates a new notification service
func NewNotificationService(
	notificationRepo repository.NotificationRepository,
	preferenceRepo repository.NotificationPreferenceRepository,
	eventPublisher event.EventPublisher,
) service.NotificationService {
	return &notificationService{
		notificationRepo: notificationRepo,
		preferenceRepo:   preferenceRepo,
		eventPublisher:   eventPublisher,
	}
}

// CreateNotification creates a new notification
func (s *notificationService) CreateNotification(ctx context.Context, userID uuid.UUID, notificationType entity.NotificationType, title, message string, data interface{}) (*entity.Notification, error) {
	notification, err := entity.NewNotification(userID, notificationType, title, message, data)
	if err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	// Save notification to database
	notification, err = s.notificationRepo.Create(ctx, *notification)
	if err != nil {
		return nil, fmt.Errorf("failed to save notification: %w", err)
	}

	// Publish notification created event
	if err := s.eventPublisher.Publish(ctx, notificationEvent.NewNotificationCreatedEvent(notification)); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Failed to publish notification created event: %v\n", err)
	}

	return notification, nil
}

// GetUserNotifications gets all notifications for a user
func (s *notificationService) GetUserNotifications(ctx context.Context, userID uuid.UUID, status *entity.NotificationStatus, limit, offset int) ([]*entity.Notification, int64, error) {
	var statusStr *string
	if status != nil {
		statusValue := string(*status)
		statusStr = &statusValue
	}
	notifications, err := s.notificationRepo.FindByUserID(ctx, userID, statusStr, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get notifications: %w", err)
	}

	// Get total count using repository
	count, err := s.notificationRepo.CountByUserID(ctx, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count notifications: %w", err)
	}

	return notifications, count, nil
}

// MarkNotificationAsRead marks a notification as read
func (s *notificationService) MarkNotificationAsRead(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error {
	notification, err := s.notificationRepo.FindByID(ctx, notificationID)
	if err != nil {
		return fmt.Errorf("notification not found: %w", err)
	}

	if notification.UserID != userID {
		return fmt.Errorf("unauthorized to mark this notification as read")
	}

	notification.MarkAsRead()
	if _, err := s.notificationRepo.Update(ctx, *notification); err != nil {
		return fmt.Errorf("failed to mark notification as read: %w", err)
	}

	// Publish notification read event
	if err := s.eventPublisher.Publish(ctx, notificationEvent.NewNotificationReadEvent(notificationID, userID)); err != nil {
		fmt.Printf("Failed to publish notification read event: %v\n", err)
	}

	return nil
}

// MarkAllNotificationsAsRead marks all notifications for a user as read
func (s *notificationService) MarkAllNotificationsAsRead(ctx context.Context, userID uuid.UUID) error {
	status := string(entity.NotificationStatusUnread)
	notifications, err := s.notificationRepo.FindByUserID(ctx, userID, &status, 0, 0)
	if err != nil {
		return fmt.Errorf("failed to get notifications: %w", err)
	}

	for _, notification := range notifications {
		if err := s.MarkNotificationAsRead(ctx, notification.ID, userID); err != nil {
			return fmt.Errorf("failed to mark notification as read: %w", err)
		}
	}

	return nil
}

// ArchiveNotification archives a notification
func (s *notificationService) ArchiveNotification(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error {
	notification, err := s.notificationRepo.FindByID(ctx, notificationID)
	if err != nil {
		return fmt.Errorf("notification not found: %w", err)
	}

	if notification.UserID != userID {
		return fmt.Errorf("unauthorized to archive this notification")
	}

	notification.MarkAsArchived()
	if _, err := s.notificationRepo.Update(ctx, *notification); err != nil {
		return fmt.Errorf("failed to archive notification: %w", err)
	}

	return nil
}

// GetUserNotificationPreferences gets a user's notification preferences
func (s *notificationService) GetUserNotificationPreferences(ctx context.Context, userID uuid.UUID) (*entity.NotificationPreference, error) {
	preferences, err := s.preferenceRepo.FindByUserID(ctx, userID)
	if err != nil {
		// Check if it's a "not found" error by checking the error message
		if err.Error() == "notification preferences not found" {
			// Create default preferences if not found
			preferences = entity.NewNotificationPreference(userID)
			preferences, err = s.preferenceRepo.Create(ctx, *preferences)
			if err != nil {
				return nil, fmt.Errorf("failed to create notification preferences: %w", err)
			}
			return preferences, nil
		}
		return nil, fmt.Errorf("failed to get notification preferences: %w", err)
	}

	return preferences, nil
}

// UpdateUserNotificationPreferences updates a user's notification preferences
func (s *notificationService) UpdateUserNotificationPreferences(ctx context.Context, userID uuid.UUID, preferences *entity.NotificationPreference) error {
	if preferences.UserID != userID {
		return fmt.Errorf("unauthorized to update these preferences")
	}

	preferences.UpdatedAt = time.Now()
	if _, err := s.preferenceRepo.Update(ctx, *preferences); err != nil {
		return fmt.Errorf("failed to update notification preferences: %w", err)
	}

	return nil
}

// DeleteNotification deletes a notification
func (s *notificationService) DeleteNotification(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error {
	notification, err := s.notificationRepo.FindByID(ctx, notificationID)
	if err != nil {
		return fmt.Errorf("notification not found: %w", err)
	}

	if notification.UserID != userID {
		return fmt.Errorf("unauthorized to delete this notification")
	}

	if err := s.notificationRepo.Delete(ctx, notificationID); err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	// Publish notification deleted event
	if err := s.eventPublisher.Publish(ctx, notificationEvent.NewNotificationDeletedEvent(notificationID, userID, "user_deleted")); err != nil {
		fmt.Printf("Failed to publish notification deleted event: %v\n", err)
	}

	return nil
}

// GetUnreadCount gets the count of unread notifications for a user
func (s *notificationService) GetUnreadCount(ctx context.Context, userID uuid.UUID) (int, error) {
	status := string(entity.NotificationStatusUnread)
	notifications, err := s.notificationRepo.FindByUserID(ctx, userID, &status, 0, 0)
	if err != nil {
		return 0, fmt.Errorf("failed to get unread notifications: %w", err)
	}

	return len(notifications), nil
}
