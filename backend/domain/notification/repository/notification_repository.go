package repository

import (
	"context"

	"languagelearning/domain/notification/entity"

	"github.com/google/uuid"
)

// NotificationRepository defines the interface for notification data access
type NotificationRepository interface {
	// Create creates a new notification
	Create(ctx context.Context, notification entity.Notification) (*entity.Notification, error)

	// FindByUserID finds notifications by user ID
	FindByUserID(ctx context.Context, userID uuid.UUID, status *string, limit, offset int) ([]*entity.Notification, error)

	// FindByID finds a notification by ID
	FindByID(ctx context.Context, id uuid.UUID) (*entity.Notification, error)

	// Update updates a notification
	Update(ctx context.Context, notification entity.Notification) (*entity.Notification, error)

	// Delete deletes a notification
	Delete(ctx context.Context, id uuid.UUID) error

	// DeleteByUserID deletes all notifications for a user
	DeleteByUserID(ctx context.Context, userID uuid.UUID) error

	// CountByUserID counts notifications for a user
	CountByUser<PERSON>(ctx context.Context, userID uuid.UUID) (int64, error)
}

// NotificationPreferenceRepository defines the interface for notification preference data access
type NotificationPreferenceRepository interface {
	// Create creates new notification preferences
	Create(ctx context.Context, preference entity.NotificationPreference) (*entity.NotificationPreference, error)

	// FindByUserID finds notification preferences by user ID
	FindByUserID(ctx context.Context, userID uuid.UUID) (*entity.NotificationPreference, error)

	// Update updates notification preferences
	Update(ctx context.Context, preference entity.NotificationPreference) (*entity.NotificationPreference, error)

	// Delete deletes notification preferences
	Delete(ctx context.Context, userID uuid.UUID) error
}
