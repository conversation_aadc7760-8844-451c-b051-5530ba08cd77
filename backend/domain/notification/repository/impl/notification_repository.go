package impl

import (
	"context"
	"fmt"

	"languagelearning/domain/notification/entity"
	"languagelearning/domain/notification/repository"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// notificationRepository implements the NotificationRepository interface
type notificationRepository struct {
	db *gorm.DB
}

// NewNotificationRepository creates a new notification repository
func NewNotificationRepository(db *gorm.DB) repository.NotificationRepository {
	return &notificationRepository{db: db}
}

// Create creates a new notification
func (r *notificationRepository) Create(ctx context.Context, notification entity.Notification) (*entity.Notification, error) {
	if err := r.db.WithContext(ctx).Create(&notification).Error; err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}
	return &notification, nil
}

// FindByUserID finds notifications by user ID
func (r *notificationRepository) FindByUserID(ctx context.Context, userID uuid.UUID, status *string, limit, offset int) ([]*entity.Notification, error) {
	var notifications []*entity.Notification
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Order("created_at DESC").Find(&notifications).Error; err != nil {
		return nil, fmt.Errorf("failed to find notifications: %w", err)
	}

	return notifications, nil
}

// FindByID finds a notification by ID
func (r *notificationRepository) FindByID(ctx context.Context, id uuid.UUID) (*entity.Notification, error) {
	var notification entity.Notification
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&notification).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("notification not found")
		}
		return nil, fmt.Errorf("failed to find notification: %w", err)
	}
	return &notification, nil
}

// Update updates a notification
func (r *notificationRepository) Update(ctx context.Context, notification entity.Notification) (*entity.Notification, error) {
	if err := r.db.WithContext(ctx).Save(&notification).Error; err != nil {
		return nil, fmt.Errorf("failed to update notification: %w", err)
	}
	return &notification, nil
}

// Delete deletes a notification
func (r *notificationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Where("id = ?", id).Delete(&entity.Notification{}).Error; err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}
	return nil
}

// DeleteByUserID deletes all notifications for a user
func (r *notificationRepository) DeleteByUserID(ctx context.Context, userID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).Delete(&entity.Notification{}).Error; err != nil {
		return fmt.Errorf("failed to delete notifications: %w", err)
	}
	return nil
}

// CountByUserID counts notifications for a user
func (r *notificationRepository) CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.Notification{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count notifications: %w", err)
	}
	return count, nil
}

// notificationPreferenceRepository implements the NotificationPreferenceRepository interface
type notificationPreferenceRepository struct {
	db *gorm.DB
}

// NewNotificationPreferenceRepository creates a new notification preference repository
func NewNotificationPreferenceRepository(db *gorm.DB) repository.NotificationPreferenceRepository {
	return &notificationPreferenceRepository{db: db}
}

// Create creates new notification preferences
func (r *notificationPreferenceRepository) Create(ctx context.Context, preference entity.NotificationPreference) (*entity.NotificationPreference, error) {
	if err := r.db.WithContext(ctx).Create(&preference).Error; err != nil {
		return nil, fmt.Errorf("failed to create notification preferences: %w", err)
	}
	return &preference, nil
}

// FindByUserID finds notification preferences by user ID
func (r *notificationPreferenceRepository) FindByUserID(ctx context.Context, userID uuid.UUID) (*entity.NotificationPreference, error) {
	var preference entity.NotificationPreference
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&preference).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("notification preferences not found")
		}
		return nil, fmt.Errorf("failed to find notification preferences: %w", err)
	}
	return &preference, nil
}

// Update updates notification preferences
func (r *notificationPreferenceRepository) Update(ctx context.Context, preference entity.NotificationPreference) (*entity.NotificationPreference, error) {
	if err := r.db.WithContext(ctx).Save(&preference).Error; err != nil {
		return nil, fmt.Errorf("failed to update notification preferences: %w", err)
	}
	return &preference, nil
}

// Delete deletes notification preferences
func (r *notificationPreferenceRepository) Delete(ctx context.Context, userID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).Delete(&entity.NotificationPreference{}).Error; err != nil {
		return fmt.Errorf("failed to delete notification preferences: %w", err)
	}
	return nil
}
