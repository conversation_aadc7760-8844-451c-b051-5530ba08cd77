package valueobject

import (
	"errors"
	"strings"

	"github.com/google/uuid"
)

// Language 表示语言的值对象
type Language struct {
	id         uuid.UUID
	code       string
	name       string
	nativeName string
}

// NewLanguage 创建新的语言值对象
func NewLanguage(code, name, nativeName string) (*Language, error) {
	if strings.TrimSpace(code) == "" {
		return nil, errors.New("language code cannot be empty")
	}
	if strings.TrimSpace(name) == "" {
		return nil, errors.New("language name cannot be empty")
	}
	if strings.TrimSpace(nativeName) == "" {
		return nil, errors.New("language native name cannot be empty")
	}

	// 验证语言代码格式 (ISO 639-1)
	code = strings.ToLower(strings.TrimSpace(code))
	if len(code) != 2 {
		return nil, errors.New("language code must be 2 characters (ISO 639-1)")
	}

	return &Language{
		id:         uuid.New(),
		code:       code,
		name:       strings.TrimSpace(name),
		nativeName: strings.TrimSpace(nativeName),
	}, nil
}

// NewLanguageWithID 使用指定ID创建语言值对象
func NewLanguageWithID(id uuid.UUID, code, name, nativeName string) (*Language, error) {
	lang, err := NewLanguage(code, name, nativeName)
	if err != nil {
		return nil, err
	}
	lang.id = id
	return lang, nil
}

// ID 返回语言ID
func (l *Language) ID() uuid.UUID {
	return l.id
}

// Code 返回语言代码
func (l *Language) Code() string {
	return l.code
}

// Name 返回语言名称
func (l *Language) Name() string {
	return l.name
}

// NativeName 返回语言本地名称
func (l *Language) NativeName() string {
	return l.nativeName
}

// String 返回字符串表示
func (l *Language) String() string {
	return l.name
}

// Equals 比较两个语言是否相等
func (l *Language) Equals(other *Language) bool {
	if other == nil {
		return false
	}
	return l.code == other.code
}

// IsValid 检查语言是否有效
func (l *Language) IsValid() bool {
	return l.code != "" && l.name != "" && l.nativeName != ""
}

// 预定义的常用语言
var (
	English = &Language{
		id:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		code:       "en",
		name:       "English",
		nativeName: "English",
	}
	Chinese = &Language{
		id:         uuid.MustParse("00000000-0000-0000-0000-000000000002"),
		code:       "zh",
		name:       "Chinese",
		nativeName: "中文",
	}
	Spanish = &Language{
		id:         uuid.MustParse("00000000-0000-0000-0000-000000000003"),
		code:       "es",
		name:       "Spanish",
		nativeName: "Español",
	}
	French = &Language{
		id:         uuid.MustParse("00000000-0000-0000-0000-000000000004"),
		code:       "fr",
		name:       "French",
		nativeName: "Français",
	}
	German = &Language{
		id:         uuid.MustParse("00000000-0000-0000-0000-000000000005"),
		code:       "de",
		name:       "German",
		nativeName: "Deutsch",
	}
	Japanese = &Language{
		id:         uuid.MustParse("00000000-0000-0000-0000-000000000006"),
		code:       "ja",
		name:       "Japanese",
		nativeName: "日本語",
	}
)

// GetLanguageByCode 根据代码获取预定义语言
func GetLanguageByCode(code string) (*Language, error) {
	code = strings.ToLower(strings.TrimSpace(code))
	
	languages := []*Language{English, Chinese, Spanish, French, German, Japanese}
	for _, lang := range languages {
		if lang.code == code {
			return lang, nil
		}
	}
	
	return nil, errors.New("language not found")
}

// AllSupportedLanguages 返回所有支持的语言
func AllSupportedLanguages() []*Language {
	return []*Language{English, Chinese, Spanish, French, German, Japanese}
}
