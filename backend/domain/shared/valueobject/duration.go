package valueobject

import (
	"errors"
	"fmt"
)

// Duration 表示时长的值对象
type Duration struct {
	minutes int
}

// NewDuration 创建新的时长值对象
func NewDuration(minutes int) (*Duration, error) {
	if minutes < 0 {
		return nil, errors.New("duration cannot be negative")
	}
	if minutes > 1440 { // 24小时
		return nil, errors.New("duration cannot exceed 24 hours")
	}
	return &Duration{minutes: minutes}, nil
}

// Minutes 返回分钟数
func (d *Duration) Minutes() int {
	return d.minutes
}

// Hours 返回小时数
func (d *Duration) Hours() float64 {
	return float64(d.minutes) / 60.0
}

// String 返回字符串表示
func (d *Duration) String() string {
	if d.minutes < 60 {
		return fmt.Sprintf("%d分钟", d.minutes)
	}
	hours := d.minutes / 60
	remainingMinutes := d.minutes % 60
	if remainingMinutes == 0 {
		return fmt.Sprintf("%d小时", hours)
	}
	return fmt.Sprintf("%d小时%d分钟", hours, remainingMinutes)
}

// IsValid 检查时长是否有效
func (d *Duration) IsValid() bool {
	return d.minutes >= 0 && d.minutes <= 1440
}

// Add 增加时长
func (d *Duration) Add(minutes int) (*Duration, error) {
	return NewDuration(d.minutes + minutes)
}

// Subtract 减少时长
func (d *Duration) Subtract(minutes int) (*Duration, error) {
	return NewDuration(d.minutes - minutes)
}

// Equals 比较两个时长是否相等
func (d *Duration) Equals(other *Duration) bool {
	if other == nil {
		return false
	}
	return d.minutes == other.minutes
}

// IsLongerThan 检查是否比另一个时长更长
func (d *Duration) IsLongerThan(other *Duration) bool {
	if other == nil {
		return true
	}
	return d.minutes > other.minutes
}

// IsShorterThan 检查是否比另一个时长更短
func (d *Duration) IsShorterThan(other *Duration) bool {
	if other == nil {
		return false
	}
	return d.minutes < other.minutes
}
