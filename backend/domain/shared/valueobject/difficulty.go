package valueobject

import (
	"errors"
	"strings"
)

// Difficulty 表示难度的值对象
type Difficulty string

const (
	// DifficultyEasy 简单
	DifficultyEasy Difficulty = "easy"
	// DifficultyMedium 中等
	DifficultyMedium Difficulty = "medium"
	// DifficultyHard 困难
	DifficultyHard Difficulty = "hard"
)

// NewDifficulty 创建新的难度值对象
func NewDifficulty(value string) (*Difficulty, error) {
	normalized := Difficulty(strings.ToLower(strings.TrimSpace(value)))
	
	switch normalized {
	case DifficultyEasy, DifficultyMedium, DifficultyHard:
		return &normalized, nil
	default:
		return nil, errors.New("invalid difficulty level")
	}
}

// String 返回字符串表示
func (d Difficulty) String() string {
	return string(d)
}

// IsValid 检查难度是否有效
func (d Difficulty) IsValid() bool {
	switch d {
	case DifficultyEasy, DifficultyMedium, DifficultyHard:
		return true
	default:
		return false
	}
}

// Level 返回难度等级 (1-3)
func (d Difficulty) Level() int {
	switch d {
	case DifficultyEasy:
		return 1
	case DifficultyMedium:
		return 2
	case DifficultyHard:
		return 3
	default:
		return 0
	}
}

// ChineseName 返回中文名称
func (d Difficulty) ChineseName() string {
	switch d {
	case DifficultyEasy:
		return "简单"
	case DifficultyMedium:
		return "中等"
	case DifficultyHard:
		return "困难"
	default:
		return "未知"
	}
}

// EnglishName 返回英文名称
func (d Difficulty) EnglishName() string {
	switch d {
	case DifficultyEasy:
		return "Easy"
	case DifficultyMedium:
		return "Medium"
	case DifficultyHard:
		return "Hard"
	default:
		return "Unknown"
	}
}

// IsEasierThan 检查是否比另一个难度更简单
func (d Difficulty) IsEasierThan(other Difficulty) bool {
	return d.Level() < other.Level()
}

// IsHarderThan 检查是否比另一个难度更困难
func (d Difficulty) IsHarderThan(other Difficulty) bool {
	return d.Level() > other.Level()
}

// Equals 比较两个难度是否相等
func (d Difficulty) Equals(other Difficulty) bool {
	return d == other
}

// AllDifficulties 返回所有有效的难度级别
func AllDifficulties() []Difficulty {
	return []Difficulty{DifficultyEasy, DifficultyMedium, DifficultyHard}
}

// FromLevel 根据等级创建难度
func FromLevel(level int) (*Difficulty, error) {
	switch level {
	case 1:
		return &DifficultyEasy, nil
	case 2:
		return &DifficultyMedium, nil
	case 3:
		return &DifficultyHard, nil
	default:
		return nil, errors.New("invalid difficulty level")
	}
}
