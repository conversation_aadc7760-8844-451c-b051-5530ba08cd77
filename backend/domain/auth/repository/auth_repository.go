package repository

import (
	"context"
	"languagelearning/domain/repository"
	"languagelearning/domain/user/entity"
	"github.com/google/uuid"
)

// AuthRepository defines the interface for authentication data operations
type AuthRepository interface {
	repository.Repository[*entity.User, string] // Embed generic repository for basic operations

	// FindByEmail finds a user by their email address
	FindByEmail(ctx context.Context, email string) (*entity.User, error)

	// FindByUsername finds a user by their username
	Find<PERSON><PERSON><PERSON>sername(ctx context.Context, username string) (*entity.User, error)

	// UpdateLastLogin updates the last login time for a user
	UpdateLastLogin(ctx context.Context, userID string) error

	// UpdatePassword updates the password for a user
	UpdatePassword(ctx context.Context, userID string, hashedPassword string) error

	// CreateSettings creates new user settings
	CreateSettings(ctx context.Context, settings *entity.UserSettings) error

	// FindSettingsByUserID finds user settings by user ID
	FindSettingsByUserID(ctx context.Context, userID uuid.UUID) (*entity.UserSettings, error)
} 