package impl

import (
	"context"
	"languagelearning/domain/auth/repository"
	"languagelearning/domain/user/entity"
	"languagelearning/models"
	"time"

	"gorm.io/gorm"
	"github.com/google/uuid"
)

type authRepository struct {
	db *gorm.DB
}

// NewAuthRepository creates a new AuthRepository implementation
func NewAuthRepository(db *gorm.DB) repository.AuthRepository {
	return &authRepository{db: db}
}

// <PERSON><PERSON> creates a new user in the database
func (r *authRepository) Create(ctx context.Context, userEntity *entity.User) (*entity.User, error) {
	userModel := &models.User{}
	userModel.FromEntity(userEntity)

	if err := r.db.WithContext(ctx).Create(userModel).Error; err != nil {
		return nil, err
	}

	return userModel.ToEntity(), nil
}

// FindByID finds a user by their ID
func (r *authRepository) FindByID(ctx context.Context, id string) (*entity.User, error) {
	var userModel models.User
	if result := r.db.WithContext(ctx).Where("id = ?", id).First(&userModel); result.Error != nil {
		return nil, result.Error
	}
	return userModel.ToEntity(), nil
}

// FindAll is not typically needed for auth, but implemented to satisfy the interface
func (r *authRepository) FindAll(ctx context.Context) ([]*entity.User, error) {
	return nil, nil // Or implement if necessary
}

// Update updates an existing user in the database
func (r *authRepository) Update(ctx context.Context, userEntity *entity.User) (*entity.User, error) {
	userModel := &models.User{}
	userModel.FromEntity(userEntity)

	if err := r.db.WithContext(ctx).Save(userModel).Error; err != nil {
		return nil, err
	}

	return userModel.ToEntity(), nil
}

// Delete deletes a user by their ID
func (r *authRepository) Delete(ctx context.Context, id string) error {
	if err := r.db.WithContext(ctx).Delete(&models.User{}, "id = ?", id).Error; err != nil {
		return err
	}
	return nil
}

// Count returns the total number of users
func (r *authRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.User{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// FindByEmail finds a user by their email address
func (r *authRepository) FindByEmail(ctx context.Context, email string) (*entity.User, error) {
	var userModel models.User
	if result := r.db.WithContext(ctx).Where("email = ?", email).First(&userModel); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil // Or a specific "not found" error
		}
		return nil, result.Error
	}
	return userModel.ToEntity(), nil
}

// FindByUsername finds a user by their username
func (r *authRepository) FindByUsername(ctx context.Context, username string) (*entity.User, error) {
	var userModel models.User
	if result := r.db.WithContext(ctx).Where("username = ?", username).First(&userModel); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil // Or a specific "not found" error
		}
		return nil, result.Error
	}
	return userModel.ToEntity(), nil
}

// UpdateLastLogin updates the last login time for a user
func (r *authRepository) UpdateLastLogin(ctx context.Context, userID string) error {
	if err := r.db.WithContext(ctx).Model(&models.User{}).Where("id = ?", userID).Update("last_login_at", time.Now()).Error; err != nil {
		return err
	}
	return nil
}

// UpdatePassword updates the password for a user
func (r *authRepository) UpdatePassword(ctx context.Context, userID string, hashedPassword string) error {
	if err := r.db.WithContext(ctx).Model(&models.User{}).Where("id = ?", userID).Update("password", hashedPassword).Error; err != nil {
		return err
	}
	return nil
}

// CreateSettings creates new user settings in the database
func (r *authRepository) CreateSettings(ctx context.Context, settingsEntity *entity.UserSettings) error {
	settingsModel := &models.UserSettings{}
	settingsModel.FromEntity(settingsEntity)

	if err := r.db.WithContext(ctx).Create(settingsModel).Error; err != nil {
		return err
	}
	return nil
}

// FindSettingsByUserID finds user settings by user ID
func (r *authRepository) FindSettingsByUserID(ctx context.Context, userID uuid.UUID) (*entity.UserSettings, error) {
	var settingsModel models.UserSettings
	if result := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&settingsModel); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, result.Error
	}
	return settingsModel.ToEntity(), nil
} 