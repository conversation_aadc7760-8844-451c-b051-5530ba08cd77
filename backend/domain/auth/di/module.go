package di

import (
	"go.uber.org/dig"
	authSvcImpl "languagelearning/domain/auth/service/impl"
	coredi "languagelearning/domain/core/di"
)

// AuthModule 認證領域的DI模塊
type AuthModule struct {
	coredi.BaseModule
}

// NewAuthModule 創建認證領域模塊
func NewAuthModule() coredi.Module {
	return &AuthModule{
		BaseModule: coredi.NewBaseModule(
			"auth",
			[]string{"core", "user"}, // 依賴核心模塊和用戶模塊
			[]coredi.ModuleFeature{coredi.FeatureBasic},
		),
	}
}

// Register 註冊認證領域的所有依賴
func (m *AuthModule) Register(container *dig.Container) error {
	// 註冊認證服務
	if err := container.Provide(authSvcImpl.NewAuthService); err != nil {
		return err
	}

	return nil
}
