package impl

import (
	"context"
	"errors"
	"time"

	authRepo "languagelearning/domain/auth/repository"
	authRepoImpl "languagelearning/domain/auth/repository/impl"
	"languagelearning/domain/auth/service"
	"languagelearning/domain/user/entity"
	"languagelearning/middleware"
	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// authService implements the AuthService interface
type authService struct {
	repo authRepo.AuthRepository
}

// NewAuthService creates a new auth service
func NewAuthService(db *gorm.DB) service.AuthService {
	return &authService{repo: authRepoImpl.NewAuthRepository(db)}
}

// Register registers a new user
func (s *authService) Register(email, password, username string) (*models.User, error) {
	ctx := context.Background() // Use a context

	// Check if username exists using the repository
	existingUserByUsername, err := s.repo.FindByUsername(ctx, username)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	if existingUserByUsername != nil {
		return nil, errors.New("username already exists")
	}

	// Check if email exists using the repository
	existingUserByEmail, err := s.repo.FindByEmail(ctx, email)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	if existingUserByEmail != nil {
		return nil, errors.New("email already registered")
	}

	userEntity := &entity.User{
		Username:    username,
		Email:       email,
		Password:    password, // Password will be hashed in the entity/model conversion
		CreatedAt:   time.Now(),
		LastLoginAt: time.Now(),
	}

	// Hash password before creating
	userModel := &models.User{}
	userModel.FromEntity(userEntity)

	if err := userModel.HashPassword(); err != nil {
		return nil, err
	}
	userEntity.Password = userModel.Password // Update entity with hashed password

	// Create user using the repository
	createdUserEntity, err := s.repo.Create(ctx, userEntity)
	if err != nil {
		return nil, err
	}

	settings := entity.UserSettings{
		UserID: createdUserEntity.ID,
	}

	// Assuming settings creation still uses direct DB for now, needs refactoring
	if err := s.repo.CreateSettings(ctx, &settings); err != nil {
		return nil, err
	}

	// Retrieve settings and attach to the model for the response
	fetchedSettings, err := s.repo.FindSettingsByUserID(ctx, createdUserEntity.ID)
	if err != nil {
		// Handle potential error, maybe log it or return a partial user model without settings
		println("Error fetching user settings after creation:", err.Error())
	}

	// Attach settings to the user model if successfully fetched
	settingsModel := &models.UserSettings{}
	if fetchedSettings != nil {
		settingsModel.FromEntity(fetchedSettings)
	}

	createdUserModel := &models.User{}
	createdUserModel.FromEntity(createdUserEntity)
	createdUserModel.Settings = settingsModel // Attach the fetched or empty settings model

	return createdUserModel, nil
}

// Login authenticates a user and returns a JWT token
func (s *authService) Login(email, password string) (string, error) {
	ctx := context.Background() // Use a context

	// Find user by username or email using the repository
	userEntity, err := s.repo.FindByUsername(ctx, email) // Assuming login can use username or email
	if err != nil && err != gorm.ErrRecordNotFound {
		return "", err
	}
	if userEntity == nil { // If not found by username, try email
		userEntity, err = s.repo.FindByEmail(ctx, email)
		if err != nil && err != gorm.ErrRecordNotFound {
			return "", err
		}
		if userEntity == nil {
			return "", errors.New("invalid credentials")
		}
	}

	// Check password using the user model method
	userModel := &models.User{}
	userModel.FromEntity(userEntity)

	if err := userModel.CheckPassword(password); err != nil {
		return "", errors.New("invalid credentials")
	}

	// Generate token
	token, err := middleware.GenerateToken(userEntity.ID) // Convert entity ID to uuid.UUID
	if err != nil {
		return "", err
	}

	// Update last login using the repository
	if err := s.repo.UpdateLastLogin(ctx, userEntity.ID.String()); err != nil {
		// Log the error but don't return it, as login was successful
		println("Error updating last login:", err.Error())
	}

	return token, nil
}

// RefreshToken refreshes a user's JWT token
func (s *authService) RefreshToken(userID uuid.UUID) (string, error) {
	ctx := context.Background() // Use a context

	// Check if user exists and is active using the repository
	userEntity, err := s.repo.FindByID(ctx, userID.String()) // Convert uuid.UUID to string
	if err != nil {
		return "", errors.New("user not found")
	}

	if !userEntity.IsActive {
		return "", errors.New("user account is not active")
	}

	// Generate new access token
	accessToken, err := middleware.GenerateToken(userID)
	if err != nil {
		return "", err
	}

	return accessToken, nil
}

// ValidateToken validates a JWT token and returns the user ID
func (s *authService) ValidateToken(token string) (uuid.UUID, error) {
	ctx := context.Background() // Use a context

	claims, err := middleware.ValidateToken(token)
	if err != nil {
		return uuid.Nil, errors.New("invalid token")
	}

	// Check if user exists and is active using the repository
	userEntity, err := s.repo.FindByID(ctx, claims.UserID.String()) // Convert uuid.UUID to string
	if err != nil {
		return uuid.Nil, errors.New("user not found")
	}

	if !userEntity.IsActive {
		return uuid.Nil, errors.New("user account is not active")
	}

	return uuid.MustParse(userEntity.ID.String()), nil // Convert entity ID back to uuid.UUID
}

// ChangePassword changes a user's password
func (s *authService) ChangePassword(userID uuid.UUID, oldPassword, newPassword string) error {
	ctx := context.Background() // Use a context

	// Find user by ID using the repository
	userEntity, err := s.repo.FindByID(ctx, userID.String()) // Convert uuid.UUID to string
	if err != nil {
		return errors.New("user not found")
	}

	// Check old password using the user model method
	userModel := &models.User{}
	userModel.FromEntity(userEntity)

	if err := userModel.CheckPassword(oldPassword); err != nil {
		return errors.New("invalid password")
	}

	// Hash the new password using the user model method
	userModel.Password = newPassword
	if err := userModel.HashPassword(); err != nil {
		return err
	}
	newHashedPassword := userModel.Password

	// Update password using the repository
	if err := s.repo.UpdatePassword(ctx, userEntity.ID.String(), newHashedPassword); err != nil {
		return err
	}

	return nil
}

// RequestPasswordReset initiates a password reset request
func (s *authService) RequestPasswordReset(email string) error {
	ctx := context.Background() // Use a context

	// Find user by email using the repository
	userEntity, err := s.repo.FindByEmail(ctx, email)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	// Don't return error to avoid revealing if email exists
	if userEntity == nil {
		return nil
	}

	// In production, send password reset email here (using userEntity.Email)

	return nil
}

// ResetPassword resets a user's password using a reset token
func (s *authService) ResetPassword(token, newPassword string) error {
	// In production, validate reset token and update password (using the repository)
	return nil
}

// VerifyEmail verifies a user's email using a verification token
func (s *authService) VerifyEmail(token string) error {
	// In production, validate verification token and update user's email status (using the repository)
	return nil
}

// RequestEmailVerification sends a new email verification request
func (s *authService) RequestEmailVerification(userID uuid.UUID) error {
	// In production, generate and send new verification email (using the repository to get user email)
	return nil
}
