package service

import (
	"languagelearning/models"

	"github.com/google/uuid"
)

// AuthService defines the interface for authentication-related operations
type AuthService interface {
	// Register registers a new user
	Register(email, password, username string) (*models.User, error)

	// Login authenticates a user and returns a JWT token
	Login(email, password string) (string, error)

	// RefreshToken refreshes a user's JWT token
	RefreshToken(userID uuid.UUID) (string, error)

	// ValidateToken validates a JWT token and returns the user ID
	ValidateToken(token string) (uuid.UUID, error)

	// ChangePassword changes a user's password
	ChangePassword(userID uuid.UUID, oldPassword, newPassword string) error

	// RequestPasswordReset initiates a password reset request
	RequestPasswordReset(email string) error

	// ResetPassword resets a user's password using a reset token
	ResetPassword(token, newPassword string) error

	// VerifyEmail verifies a user's email using a verification token
	VerifyEmail(token string) error

	// RequestEmailVerification sends a new email verification request
	RequestEmailVerification(userID uuid.UUID) error
}
