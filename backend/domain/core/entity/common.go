package entity

import (
	"errors"
	"fmt"
)

// Duration 表示時間長度的值對象
type Duration struct {
	minutes int
}

// NewDuration 創建新的時間長度
func NewDuration(minutes int) (*Duration, error) {
	if minutes < 0 {
		return nil, errors.New("duration cannot be negative")
	}
	return &Duration{minutes: minutes}, nil
}

// Minutes 返回分鐘數
func (d *Duration) Minutes() int {
	return d.minutes
}

// Hours 返回小時數
func (d *Duration) Hours() float64 {
	return float64(d.minutes) / 60.0
}

// String 返回字符串表示
func (d *Duration) String() string {
	if d.minutes < 60 {
		return fmt.Sprintf("%d minutes", d.minutes)
	}
	hours := d.minutes / 60
	remainingMinutes := d.minutes % 60
	if remainingMinutes == 0 {
		return fmt.Sprintf("%d hours", hours)
	}
	return fmt.Sprintf("%d hours %d minutes", hours, remainingMinutes)
}

// Language 表示語言的值對象
type Language struct {
	Code       string `json:"code"`
	Name       string `json:"name"`
	NativeName string `json:"nativeName"`
}

// NewLanguage 創建新的語言
func NewLanguage(code, name, nativeName string) (*Language, error) {
	if code == "" {
		return nil, errors.New("language code is required")
	}
	if name == "" {
		return nil, errors.New("language name is required")
	}
	return &Language{
		Code:       code,
		Name:       name,
		NativeName: nativeName,
	}, nil
}

// String 返回語言的字符串表示
func (l *Language) String() string {
	return l.Name
}

// ExerciseDifficulty 表示練習難度的值對象
type ExerciseDifficulty struct {
	level        string
	numericValue int
}

// NewExerciseDifficulty 創建新的練習難度
func NewExerciseDifficulty(level string) (*ExerciseDifficulty, error) {
	var numericValue int
	switch level {
	case "beginner", "easy":
		numericValue = 1
	case "intermediate", "medium":
		numericValue = 2
	case "advanced", "hard":
		numericValue = 3
	case "expert":
		numericValue = 4
	default:
		return nil, fmt.Errorf("invalid difficulty level: %s", level)
	}

	return &ExerciseDifficulty{
		level:        level,
		numericValue: numericValue,
	}, nil
}

// Level 返回難度級別
func (d *ExerciseDifficulty) Level() string {
	return d.level
}

// NumericValue 返回數值表示
func (d *ExerciseDifficulty) NumericValue() int {
	return d.numericValue
}

// String 返回字符串表示
func (d *ExerciseDifficulty) String() string {
	return d.level
}

// IsEasierThan 檢查是否比另一個難度更容易
func (d *ExerciseDifficulty) IsEasierThan(other *ExerciseDifficulty) bool {
	return d.numericValue < other.numericValue
}

// IsHarderThan 檢查是否比另一個難度更困難
func (d *ExerciseDifficulty) IsHarderThan(other *ExerciseDifficulty) bool {
	return d.numericValue > other.numericValue
}

// Score 表示分數的值對象
type Score struct {
	value    int
	maxValue int
}

// NewScore 創建新的分數
func NewScore(value, maxValue int) (*Score, error) {
	if value < 0 {
		return nil, errors.New("score value cannot be negative")
	}
	if maxValue <= 0 {
		return nil, errors.New("max value must be positive")
	}
	if value > maxValue {
		return nil, errors.New("score value cannot exceed max value")
	}
	return &Score{
		value:    value,
		maxValue: maxValue,
	}, nil
}

// Value 返回分數值
func (s *Score) Value() int {
	return s.value
}

// MaxValue 返回最大分數值
func (s *Score) MaxValue() int {
	return s.maxValue
}

// Percentage 返回百分比
func (s *Score) Percentage() float64 {
	return float64(s.value) / float64(s.maxValue) * 100
}

// String 返回字符串表示
func (s *Score) String() string {
	return fmt.Sprintf("%d/%d (%.1f%%)", s.value, s.maxValue, s.Percentage())
}

// Progress 表示進度的值對象
type Progress struct {
	current int
	total   int
}

// NewProgress 創建新的進度
func NewProgress(current, total int) (*Progress, error) {
	if current < 0 {
		return nil, errors.New("current progress cannot be negative")
	}
	if total <= 0 {
		return nil, errors.New("total must be positive")
	}
	if current > total {
		return nil, errors.New("current progress cannot exceed total")
	}
	return &Progress{
		current: current,
		total:   total,
	}, nil
}

// Current 返回當前進度
func (p *Progress) Current() int {
	return p.current
}

// Total 返回總進度
func (p *Progress) Total() int {
	return p.total
}

// Percentage 返回百分比
func (p *Progress) Percentage() float64 {
	return float64(p.current) / float64(p.total) * 100
}

// IsComplete 檢查是否完成
func (p *Progress) IsComplete() bool {
	return p.current >= p.total
}

// String 返回字符串表示
func (p *Progress) String() string {
	return fmt.Sprintf("%d/%d (%.1f%%)", p.current, p.total, p.Percentage())
}

// Advance 推進進度
func (p *Progress) Advance(amount int) error {
	if amount < 0 {
		return errors.New("advance amount cannot be negative")
	}
	newCurrent := p.current + amount
	if newCurrent > p.total {
		newCurrent = p.total
	}
	p.current = newCurrent
	return nil
}
