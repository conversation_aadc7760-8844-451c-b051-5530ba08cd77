package di

import (
	"fmt"
	"sort"

	"go.uber.org/dig"
)

// ModuleRegistry 模塊註冊表
type ModuleRegistry struct {
	modules         map[string]Module
	enabledFeatures map[ModuleFeature]bool
	loadOrder       []string
}

// NewModuleRegistry 創建新的模塊註冊表
func NewModuleRegistry() *ModuleRegistry {
	return &ModuleRegistry{
		modules: make(map[string]Module),
		enabledFeatures: map[ModuleFeature]bool{
			FeatureBasic: true, // 基本功能默認啟用
		},
		loadOrder: make([]string, 0),
	}
}

// RegisterModule 註冊模塊
func (r *ModuleRegistry) RegisterModule(module Module) error {
	name := module.Name()
	if _, exists := r.modules[name]; exists {
		return fmt.Errorf("module %s already registered", name)
	}

	r.modules[name] = module
	return nil
}

// EnableFeature 啟用功能特性
func (r *ModuleRegistry) EnableFeature(feature ModuleFeature) {
	r.enabledFeatures[feature] = true
}

// DisableFeature 禁用功能特性
func (r *ModuleRegistry) DisableFeature(feature ModuleFeature) {
	r.enabledFeatures[feature] = false
}

// IsFeatureEnabled 檢查功能是否啟用
func (r *ModuleRegistry) IsFeatureEnabled(feature ModuleFeature) bool {
	enabled, exists := r.enabledFeatures[feature]
	return exists && enabled
}

// ResolveDependencies 解析模塊依賴關係並確定加載順序
func (r *ModuleRegistry) ResolveDependencies() error {
	// 檢測循環依賴
	if err := r.detectCircularDependencies(); err != nil {
		return err
	}

	// 拓撲排序確定加載順序
	order, err := r.topologicalSort()
	if err != nil {
		return err
	}

	r.loadOrder = order
	return nil
}

// GetLoadOrder 獲取模塊加載順序
func (r *ModuleRegistry) GetLoadOrder() []string {
	return r.loadOrder
}

// GetModule 獲取指定模塊
func (r *ModuleRegistry) GetModule(name string) (Module, bool) {
	module, exists := r.modules[name]
	return module, exists
}

// GetEnabledModules 獲取所有啟用的模塊
func (r *ModuleRegistry) GetEnabledModules() []Module {
	var enabled []Module

	for _, module := range r.modules {
		// 檢查模塊的功能特性是否啟用
		hasEnabledFeature := false
		for _, feature := range module.Features() {
			if r.IsFeatureEnabled(feature) {
				hasEnabledFeature = true
				break
			}
		}

		if hasEnabledFeature {
			enabled = append(enabled, module)
		}
	}

	return enabled
}

// detectCircularDependencies 檢測循環依賴
func (r *ModuleRegistry) detectCircularDependencies() error {
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	for name := range r.modules {
		if !visited[name] {
			if r.hasCycle(name, visited, recStack) {
				return fmt.Errorf("circular dependency detected involving module: %s", name)
			}
		}
	}

	return nil
}

// hasCycle 使用DFS檢測循環依賴
func (r *ModuleRegistry) hasCycle(name string, visited, recStack map[string]bool) bool {
	visited[name] = true
	recStack[name] = true

	module, exists := r.modules[name]
	if !exists {
		return false
	}

	for _, dep := range module.Dependencies() {
		if !visited[dep] {
			if r.hasCycle(dep, visited, recStack) {
				return true
			}
		} else if recStack[dep] {
			return true
		}
	}

	recStack[name] = false
	return false
}

// topologicalSort 拓撲排序確定加載順序
func (r *ModuleRegistry) topologicalSort() ([]string, error) {
	inDegree := make(map[string]int)

	// 初始化入度
	for name := range r.modules {
		inDegree[name] = 0
	}

	// 計算入度
	for _, module := range r.modules {
		for _, dep := range module.Dependencies() {
			if _, exists := r.modules[dep]; !exists {
				return nil, fmt.Errorf("module %s depends on non-existent module %s", module.Name(), dep)
			}
			inDegree[module.Name()]++
		}
	}

	// 拓撲排序
	var queue []string
	var result []string

	// 找到所有入度為0的節點
	for name, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, name)
		}
	}

	// 排序以確保結果的確定性
	sort.Strings(queue)

	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]
		result = append(result, current)

		// 找到所有依賴當前模塊的模塊
		for _, module := range r.modules {
			for _, dep := range module.Dependencies() {
				if dep == current {
					inDegree[module.Name()]--
					if inDegree[module.Name()] == 0 {
						queue = append(queue, module.Name())
						sort.Strings(queue) // 保持排序
					}
				}
			}
		}
	}

	if len(result) != len(r.modules) {
		return nil, fmt.Errorf("circular dependency detected during topological sort")
	}

	return result, nil
}

// RegisterToContainer 將所有啟用的模塊註冊到容器
func (r *ModuleRegistry) RegisterToContainer(container *dig.Container) error {
	if err := r.ResolveDependencies(); err != nil {
		return fmt.Errorf("failed to resolve dependencies: %w", err)
	}

	enabledModules := r.GetEnabledModules()
	enabledModuleMap := make(map[string]Module)
	for _, module := range enabledModules {
		enabledModuleMap[module.Name()] = module
	}

	// 按依賴順序註冊模塊
	for _, name := range r.loadOrder {
		if module, exists := enabledModuleMap[name]; exists {
			if err := module.Register(container); err != nil {
				return fmt.Errorf("failed to register module %s: %w", name, err)
			}
		}
	}

	return nil
}
