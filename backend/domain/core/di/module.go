package di

import (
	"fmt"
	"go.uber.org/dig"
)

// ModuleFeature 模塊功能特性
type ModuleFeature string

const (
	// FeatureBasic 基本功能，默認啟用
	FeatureBasic ModuleFeature = "basic"
	// FeatureAdvanced 進階功能，可選啟用
	FeatureAdvanced ModuleFeature = "advanced"
	// FeatureExperimental 實驗性功能，通常在開發環境啟用
	FeatureExperimental ModuleFeature = "experimental"
)

// Module 模塊接口
type Module interface {
	// Name 返回模塊名稱
	Name() string
	// Dependencies 返回此模塊依賴的其他模塊名稱
	Dependencies() []string
	// Features 返回此模塊支持的功能特性
	Features() []ModuleFeature
	// Register 註冊模塊到容器
	Register(container *dig.Container) error
}

// BaseModule 基礎模塊實現
type BaseModule struct {
	name         string
	dependencies []string
	features     []ModuleFeature
}

// NewBaseModule 創建基礎模塊
func NewBaseModule(name string, dependencies []string, features []ModuleFeature) BaseModule {
	return BaseModule{
		name:         name,
		dependencies: dependencies,
		features:     features,
	}
}

// Name 實現 Module 接口
func (m *BaseModule) Name() string {
	return m.name
}

// Dependencies 實現 Module 接口
func (m *BaseModule) Dependencies() []string {
	return m.dependencies
}

// Features 實現 Module 接口
func (m *BaseModule) Features() []ModuleFeature {
	return m.features
}

// Register 基礎實現，子類需要重寫
func (m *BaseModule) Register(container *dig.Container) error {
	return fmt.Errorf("module %s must implement Register method", m.name)
}

// HasFeature 檢查模塊是否支持指定功能
func (m *BaseModule) HasFeature(feature ModuleFeature) bool {
	for _, f := range m.features {
		if f == feature {
			return true
		}
	}
	return false
}
