package di

import (
	"context"
	"languagelearning/config"
	"languagelearning/domain/core/event"
	"languagelearning/models"

	"go.uber.org/dig"
)

// CoreModule 核心模塊，提供基礎設施服務
type CoreModule struct {
	BaseModule
}

// NewCoreModule 創建核心模塊
func NewCoreModule() Module {
	return &CoreModule{
		BaseModule: NewBaseModule(
			"core",
			[]string{}, // 核心模塊不依賴其他模塊
			[]ModuleFeature{FeatureBasic},
		),
	}
}

// Register 註冊核心模塊的所有依賴
func (m *CoreModule) Register(container *dig.Container) error {
	// 註冊配置
	if err := container.Provide(config.LoadConfig); err != nil {
		return err
	}

	// 註冊數據庫連接
	if err := container.Provide(models.ConnectDatabase); err != nil {
		return err
	}

	// 註冊事件系統
	if err := m.registerEventBus(container); err != nil {
		return err
	}

	return nil
}

// registerEventBus 註冊事件總線相關組件
func (m *CoreModule) registerEventBus(container *dig.Container) error {
	// 提供事件總線配置
	if err := container.Provide(m.createEventBusConfig); err != nil {
		return err
	}

	// 提供事件總線工廠
	if err := container.Provide(m.createEventBusFactory); err != nil {
		return err
	}

	// 提供 EventBus 接口
	if err := container.Provide(m.createEventBus); err != nil {
		return err
	}

	// 提供 EventPublisher 接口
	if err := container.Provide(func(eventBus event.EventBus) event.EventPublisher {
		return eventBus
	}); err != nil {
		return err
	}

	return nil
}

// createEventBusConfig 創建事件總線配置
func (m *CoreModule) createEventBusConfig() *event.EventBusConfig {
	// 直接從環境變量讀取配置，就像原來的容器一樣
	return event.DefaultEventBusConfig()
}

// createEventBusFactory 創建事件總線工廠
func (m *CoreModule) createEventBusFactory(config *event.EventBusConfig) *event.EventBusFactory {
	return event.NewEventBusFactory(config)
}

// createEventBus 創建事件總線實例
func (m *CoreModule) createEventBus(factory *event.EventBusFactory) (event.EventBus, error) {
	ctx := context.Background()
	return factory.CreateEventBus(ctx)
}
