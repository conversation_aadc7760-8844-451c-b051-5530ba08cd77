package core

// Pageable represents pagination parameters
type Pageable struct {
	Page int
	Size int
	Sort []string
}

// Page represents a paginated result
type Page[T any] struct {
	Content       []T
	TotalElements int64
	TotalPages    int
	CurrentPage   int
	PageSize      int
}

// NewPage creates a new page
func NewPage[T any](content []T, totalElements int64, currentPage, pageSize int) *Page[T] {
	totalPages := (int(totalElements) + pageSize - 1) / pageSize
	return &Page[T]{
		Content:       content,
		TotalElements: totalElements,
		TotalPages:    totalPages,
		CurrentPage:   currentPage,
		PageSize:      pageSize,
	}
}

// HasNext returns whether there is a next page
func (p *Page[T]) HasNext() bool {
	return p.CurrentPage < p.TotalPages
}

// HasPrevious returns whether there is a previous page
func (p *Page[T]) HasPrevious() bool {
	return p.CurrentPage > 1
}

// IsFirst returns whether this is the first page
func (p *Page[T]) IsFirst() bool {
	return p.CurrentPage == 1
}

// IsLast returns whether this is the last page
func (p *Page[T]) IsLast() bool {
	return p.CurrentPage == p.TotalPages
}

// GetNextPage returns the next page number
func (p *Page[T]) GetNextPage() int {
	if p.HasNext() {
		return p.CurrentPage + 1
	}
	return p.CurrentPage
}

// GetPreviousPage returns the previous page number
func (p *Page[T]) GetPreviousPage() int {
	if p.HasPrevious() {
		return p.CurrentPage - 1
	}
	return p.CurrentPage
}
