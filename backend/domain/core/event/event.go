package event

import (
	"context"
	"time"

	"github.com/google/uuid"
)

// NewBaseEvent 創建基礎事件
func NewBaseEvent(eventType string, aggregateID uuid.UUID, aggregateType string, data interface{}) *BaseEvent {
	return &BaseEvent{
		EventType:     eventType,
		EventTime:     time.Now(),
		AggregateID:   aggregateID,
		AggregateType: aggregateType,
		EventData:     data,
	}
}

// NewInMemoryEventBus 創建內存事件總線
func NewInMemoryEventBus() *InMemoryEventBus {
	return &InMemoryEventBus{
		handlers: make(map[string][]EventHandler),
	}
}

// Publish 實現 EventPublisher 接口
func (b *InMemoryEventBus) Publish(ctx context.Context, event interface{}) error {
	e, ok := event.(Event)
	if !ok {
		return nil // 或者返回錯誤
	}

	handlers := b.handlers[e.GetEventType()]
	for _, handler := range handlers {
		if err := handler.Handle(e); err != nil {
			return err
		}
	}
	return nil
}

// Subscribe 實現 EventSubscriber 接口
func (b *InMemoryEventBus) Subscribe(eventType string, handler EventHandler) error {
	handlers := b.handlers[eventType]
	handlers = append(handlers, handler)
	b.handlers[eventType] = handlers
	return nil
}

// Unsubscribe 實現 EventSubscriber 接口
func (b *InMemoryEventBus) Unsubscribe(eventType string, handler EventHandler) error {
	handlers := b.handlers[eventType]
	for i, h := range handlers {
		if h == handler {
			handlers = append(handlers[:i], handlers[i+1:]...)
			b.handlers[eventType] = handlers
			return nil
		}
	}
	return nil
}
