package event

import (
	"encoding/json"
	"fmt"
)

// EventFactory 事件工廠函數類型
type EventFactory func(data json.RawMessage) (Event, error)

// DefaultEventReconstructor 默認事件重構器
type DefaultEventReconstructor struct {
	factories map[string]EventFactory
}

// NewDefaultEventReconstructor 創建默認事件重構器
func NewDefaultEventReconstructor() *DefaultEventReconstructor {
	return &DefaultEventReconstructor{
		factories: make(map[string]EventFactory),
	}
}

// RegisterEventFactory 註冊事件工廠
func (r *DefaultEventReconstructor) RegisterEventFactory(eventType string, factory EventFactory) {
	r.factories[eventType] = factory
}

// Reconstruct 重構事件
func (r *DefaultEventReconstructor) Reconstruct(eventType string, data json.RawMessage) (Event, error) {
	factory, exists := r.factories[eventType]
	if !exists {
		return nil, fmt.Errorf("no factory registered for event type: %s", eventType)
	}

	return factory(data)
}
