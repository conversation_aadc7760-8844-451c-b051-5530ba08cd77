package event

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/segmentio/kafka-go"
	amqp "github.com/rabbitmq/amqp091-go"
)

// EventBusType 事件总线类型
type EventBusType string

const (
	// InMemoryBusType 内存事件总线（无持久化）
	InMemoryBusType EventBusType = "memory"
	// RedisBusType Redis 事件总线
	RedisBusType EventBusType = "redis"
	// RabbitMQBusType RabbitMQ 事件总线
	RabbitMQBusType EventBusType = "rabbitmq"
	// KafkaBusType Kafka 事件总线
	KafkaBusType EventBusType = "kafka"
)

// EventBusConfig 事件总线配置
type EventBusConfig struct {
	Type EventBusType `json:"type" yaml:"type"`
	
	// Redis 配置
	Redis *RedisConfig `json:"redis,omitempty" yaml:"redis,omitempty"`
	
	// RabbitMQ 配置
	RabbitMQ *RabbitMQConfig `json:"rabbitmq,omitempty" yaml:"rabbitmq,omitempty"`
	
	// Kafka 配置
	Kafka *KafkaConfig `json:"kafka,omitempty" yaml:"kafka,omitempty"`
	
	// 通用配置
	RetryAttempts int           `json:"retryAttempts" yaml:"retryAttempts"`
	RetryDelay    time.Duration `json:"retryDelay" yaml:"retryDelay"`
	BatchSize     int           `json:"batchSize" yaml:"batchSize"`
	FlushInterval time.Duration `json:"flushInterval" yaml:"flushInterval"`
}

// RedisConfig Redis 配置
type RedisConfig struct {
	Addr     string `json:"addr" yaml:"addr"`
	Password string `json:"password" yaml:"password"`
	DB       int    `json:"db" yaml:"db"`
	
	// Redis Stream 配置
	StreamKey    string `json:"streamKey" yaml:"streamKey"`
	ConsumerGroup string `json:"consumerGroup" yaml:"consumerGroup"`
	ConsumerName  string `json:"consumerName" yaml:"consumerName"`
	
	// 连接池配置
	PoolSize     int           `json:"poolSize" yaml:"poolSize"`
	MinIdleConns int           `json:"minIdleConns" yaml:"minIdleConns"`
	MaxRetries   int           `json:"maxRetries" yaml:"maxRetries"`
	DialTimeout  time.Duration `json:"dialTimeout" yaml:"dialTimeout"`
	ReadTimeout  time.Duration `json:"readTimeout" yaml:"readTimeout"`
	WriteTimeout time.Duration `json:"writeTimeout" yaml:"writeTimeout"`
}

// RabbitMQConfig RabbitMQ 配置
type RabbitMQConfig struct {
	URL      string `json:"url" yaml:"url"`
	Exchange string `json:"exchange" yaml:"exchange"`
	
	// 连接配置
	ConnectionName string        `json:"connectionName" yaml:"connectionName"`
	Heartbeat      time.Duration `json:"heartbeat" yaml:"heartbeat"`
	
	// 队列配置
	QueueDurable    bool `json:"queueDurable" yaml:"queueDurable"`
	QueueAutoDelete bool `json:"queueAutoDelete" yaml:"queueAutoDelete"`
	QueueExclusive  bool `json:"queueExclusive" yaml:"queueExclusive"`
	
	// 消息配置
	MessagePersistent bool `json:"messagePersistent" yaml:"messagePersistent"`
	PrefetchCount     int  `json:"prefetchCount" yaml:"prefetchCount"`
}

// KafkaConfig Kafka 配置
type KafkaConfig struct {
	Brokers []string `json:"brokers" yaml:"brokers"`
	Topic   string   `json:"topic" yaml:"topic"`
	
	// 生产者配置
	ProducerConfig *KafkaProducerConfig `json:"producer,omitempty" yaml:"producer,omitempty"`
	
	// 消费者配置
	ConsumerConfig *KafkaConsumerConfig `json:"consumer,omitempty" yaml:"consumer,omitempty"`
}

// KafkaProducerConfig Kafka 生产者配置
type KafkaProducerConfig struct {
	BatchSize    int           `json:"batchSize" yaml:"batchSize"`
	BatchTimeout time.Duration `json:"batchTimeout" yaml:"batchTimeout"`
	Compression  string        `json:"compression" yaml:"compression"` // none, gzip, snappy, lz4, zstd
	MaxRetries   int           `json:"maxRetries" yaml:"maxRetries"`
	RequiredAcks int           `json:"requiredAcks" yaml:"requiredAcks"` // 0, 1, -1
}

// KafkaConsumerConfig Kafka 消费者配置
type KafkaConsumerConfig struct {
	GroupID          string        `json:"groupId" yaml:"groupId"`
	StartOffset      int64         `json:"startOffset" yaml:"startOffset"` // kafka.FirstOffset, kafka.LastOffset
	CommitInterval   time.Duration `json:"commitInterval" yaml:"commitInterval"`
	SessionTimeout   time.Duration `json:"sessionTimeout" yaml:"sessionTimeout"`
	HeartbeatTimeout time.Duration `json:"heartbeatTimeout" yaml:"heartbeatTimeout"`
	MaxBytes         int           `json:"maxBytes" yaml:"maxBytes"`
}

// EventBusFactory 事件总线工厂
type EventBusFactory struct {
	config *EventBusConfig
}

// NewEventBusFactory 创建事件总线工厂
func NewEventBusFactory(config *EventBusConfig) *EventBusFactory {
	// 设置默认值
	if config.RetryAttempts == 0 {
		config.RetryAttempts = 3
	}
	if config.RetryDelay == 0 {
		config.RetryDelay = time.Second
	}
	if config.BatchSize == 0 {
		config.BatchSize = 100
	}
	if config.FlushInterval == 0 {
		config.FlushInterval = time.Second * 5
	}
	
	return &EventBusFactory{
		config: config,
	}
}

// CreateEventBus 创建事件总线
func (f *EventBusFactory) CreateEventBus(ctx context.Context) (EventBus, error) {
	switch f.config.Type {
	case InMemoryBusType:
		return f.createInMemoryBus()
	case RedisBusType:
		return f.createRedisBus(ctx)
	case RabbitMQBusType:
		return f.createRabbitMQBus(ctx)
	case KafkaBusType:
		return f.createKafkaBus(ctx)
	default:
		return nil, fmt.Errorf("unsupported event bus type: %s", f.config.Type)
	}
}

// createInMemoryBus 创建内存事件总线
func (f *EventBusFactory) createInMemoryBus() (EventBus, error) {
	return NewDefaultEventBus(), nil
}

// createRedisBus 创建 Redis 事件总线
func (f *EventBusFactory) createRedisBus(ctx context.Context) (EventBus, error) {
	if f.config.Redis == nil {
		return nil, fmt.Errorf("redis config is required for redis event bus")
	}
	
	// 设置 Redis 默认值
	redisConfig := f.config.Redis
	if redisConfig.StreamKey == "" {
		redisConfig.StreamKey = "events"
	}
	if redisConfig.ConsumerGroup == "" {
		redisConfig.ConsumerGroup = "event-handlers"
	}
	if redisConfig.ConsumerName == "" {
		redisConfig.ConsumerName = "handler-1"
	}
	if redisConfig.PoolSize == 0 {
		redisConfig.PoolSize = 10
	}
	if redisConfig.DialTimeout == 0 {
		redisConfig.DialTimeout = time.Second * 5
	}
	if redisConfig.ReadTimeout == 0 {
		redisConfig.ReadTimeout = time.Second * 3
	}
	if redisConfig.WriteTimeout == 0 {
		redisConfig.WriteTimeout = time.Second * 3
	}
	
	client := redis.NewClient(&redis.Options{
		Addr:         redisConfig.Addr,
		Password:     redisConfig.Password,
		DB:           redisConfig.DB,
		PoolSize:     redisConfig.PoolSize,
		MinIdleConns: redisConfig.MinIdleConns,
		MaxRetries:   redisConfig.MaxRetries,
		DialTimeout:  redisConfig.DialTimeout,
		ReadTimeout:  redisConfig.ReadTimeout,
		WriteTimeout: redisConfig.WriteTimeout,
	})
	
	// 测试连接
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to redis: %w", err)
	}
	
	return NewRedisEventBus(client, redisConfig, f.config), nil
}

// createRabbitMQBus 创建 RabbitMQ 事件总线
func (f *EventBusFactory) createRabbitMQBus(ctx context.Context) (EventBus, error) {
	if f.config.RabbitMQ == nil {
		return nil, fmt.Errorf("rabbitmq config is required for rabbitmq event bus")
	}
	
	// 设置 RabbitMQ 默认值
	rabbitConfig := f.config.RabbitMQ
	if rabbitConfig.Exchange == "" {
		rabbitConfig.Exchange = "events"
	}
	if rabbitConfig.ConnectionName == "" {
		rabbitConfig.ConnectionName = "event-bus"
	}
	if rabbitConfig.Heartbeat == 0 {
		rabbitConfig.Heartbeat = time.Second * 10
	}
	if rabbitConfig.PrefetchCount == 0 {
		rabbitConfig.PrefetchCount = 10
	}
	
	conn, err := amqp.Dial(rabbitConfig.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to rabbitmq: %w", err)
	}
	
	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("failed to open rabbitmq channel: %w", err)
	}
	
	return NewRabbitMQEventBus(conn, ch, rabbitConfig, f.config), nil
}

// createKafkaBus 创建 Kafka 事件总线
func (f *EventBusFactory) createKafkaBus(ctx context.Context) (EventBus, error) {
	if f.config.Kafka == nil {
		return nil, fmt.Errorf("kafka config is required for kafka event bus")
	}
	
	// 设置 Kafka 默认值
	kafkaConfig := f.config.Kafka
	if kafkaConfig.Topic == "" {
		kafkaConfig.Topic = "events"
	}
	
	// 设置生产者默认值
	if kafkaConfig.ProducerConfig == nil {
		kafkaConfig.ProducerConfig = &KafkaProducerConfig{}
	}
	if kafkaConfig.ProducerConfig.BatchSize == 0 {
		kafkaConfig.ProducerConfig.BatchSize = 100
	}
	if kafkaConfig.ProducerConfig.BatchTimeout == 0 {
		kafkaConfig.ProducerConfig.BatchTimeout = time.Millisecond * 10
	}
	if kafkaConfig.ProducerConfig.Compression == "" {
		kafkaConfig.ProducerConfig.Compression = "gzip"
	}
	if kafkaConfig.ProducerConfig.MaxRetries == 0 {
		kafkaConfig.ProducerConfig.MaxRetries = 3
	}
	if kafkaConfig.ProducerConfig.RequiredAcks == 0 {
		kafkaConfig.ProducerConfig.RequiredAcks = 1
	}
	
	// 设置消费者默认值
	if kafkaConfig.ConsumerConfig == nil {
		kafkaConfig.ConsumerConfig = &KafkaConsumerConfig{}
	}
	if kafkaConfig.ConsumerConfig.GroupID == "" {
		kafkaConfig.ConsumerConfig.GroupID = "event-handlers"
	}
	if kafkaConfig.ConsumerConfig.StartOffset == 0 {
		kafkaConfig.ConsumerConfig.StartOffset = kafka.LastOffset
	}
	if kafkaConfig.ConsumerConfig.CommitInterval == 0 {
		kafkaConfig.ConsumerConfig.CommitInterval = time.Second
	}
	if kafkaConfig.ConsumerConfig.SessionTimeout == 0 {
		kafkaConfig.ConsumerConfig.SessionTimeout = time.Second * 10
	}
	if kafkaConfig.ConsumerConfig.HeartbeatTimeout == 0 {
		kafkaConfig.ConsumerConfig.HeartbeatTimeout = time.Second * 3
	}
	if kafkaConfig.ConsumerConfig.MaxBytes == 0 {
		kafkaConfig.ConsumerConfig.MaxBytes = 10e6 // 10MB
	}
	
	return NewKafkaEventBus(kafkaConfig, f.config), nil
}

// DefaultEventBusConfig 返回默认的事件总线配置
func DefaultEventBusConfig() *EventBusConfig {
	return &EventBusConfig{
		Type:          InMemoryBusType,
		RetryAttempts: 3,
		RetryDelay:    time.Second,
		BatchSize:     100,
		FlushInterval: time.Second * 5,
	}
}

// RedisEventBusConfig 返回 Redis 事件总线配置
func RedisEventBusConfig(addr, password string, db int) *EventBusConfig {
	return &EventBusConfig{
		Type: RedisBusType,
		Redis: &RedisConfig{
			Addr:          addr,
			Password:      password,
			DB:            db,
			StreamKey:     "events",
			ConsumerGroup: "event-handlers",
			ConsumerName:  "handler-1",
			PoolSize:      10,
			DialTimeout:   time.Second * 5,
			ReadTimeout:   time.Second * 3,
			WriteTimeout:  time.Second * 3,
		},
		RetryAttempts: 3,
		RetryDelay:    time.Second,
		BatchSize:     100,
		FlushInterval: time.Second * 5,
	}
}

// RabbitMQEventBusConfig 返回 RabbitMQ 事件总线配置
func RabbitMQEventBusConfig(url string) *EventBusConfig {
	return &EventBusConfig{
		Type: RabbitMQBusType,
		RabbitMQ: &RabbitMQConfig{
			URL:               url,
			Exchange:          "events",
			ConnectionName:    "event-bus",
			Heartbeat:         time.Second * 10,
			QueueDurable:      true,
			QueueAutoDelete:   false,
			QueueExclusive:    false,
			MessagePersistent: true,
			PrefetchCount:     10,
		},
		RetryAttempts: 3,
		RetryDelay:    time.Second,
		BatchSize:     100,
		FlushInterval: time.Second * 5,
	}
}

// KafkaEventBusConfig 返回 Kafka 事件总线配置
func KafkaEventBusConfig(brokers []string) *EventBusConfig {
	return &EventBusConfig{
		Type: KafkaBusType,
		Kafka: &KafkaConfig{
			Brokers: brokers,
			Topic:   "events",
			ProducerConfig: &KafkaProducerConfig{
				BatchSize:    100,
				BatchTimeout: time.Millisecond * 10,
				Compression:  "gzip",
				MaxRetries:   3,
				RequiredAcks: 1,
			},
			ConsumerConfig: &KafkaConsumerConfig{
				GroupID:          "event-handlers",
				StartOffset:      kafka.LastOffset,
				CommitInterval:   time.Second,
				SessionTimeout:   time.Second * 10,
				HeartbeatTimeout: time.Second * 3,
				MaxBytes:         10e6,
			},
		},
		RetryAttempts: 3,
		RetryDelay:    time.Second,
		BatchSize:     100,
		FlushInterval: time.Second * 5,
	}
}
