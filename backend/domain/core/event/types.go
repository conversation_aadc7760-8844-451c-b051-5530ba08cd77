package event

import (
	"context"
	"time"

	"languagelearning/domain/learning/entity"

	"github.com/google/uuid"
)

// Event 定義了事件接口
type Event interface {
	// GetEventType 返回事件類型
	GetEventType() string
	// GetEventTime 返回事件發生時間
	GetEventTime() time.Time
	// GetAggregateID 返回聚合根ID
	GetAggregateID() uuid.UUID
	// GetAggregateType 返回聚合根類型
	GetAggregateType() string
	// GetData 返回事件數據
	GetData() interface{}
	// EventName returns the specific name of the event (often same as EventType or more specific)
	EventName() string
	// Payload returns the event data, typically as a map for broader compatibility
	Payload() map[string]interface{}
}

// BaseEvent 基礎事件實現
type BaseEvent struct {
	EventType     string      `json:"eventType"`
	EventTime     time.Time   `json:"eventTime"`
	AggregateID   uuid.UUID   `json:"aggregateId"`
	AggregateType string      `json:"aggregateType"`
	EventData     interface{} `json:"data"`
}

// GetEventType implementation for core Event interface
func (be *BaseEvent) GetEventType() string {
	return be.EventType
}

// GetEventTime implementation for core Event interface
func (be *BaseEvent) GetEventTime() time.Time {
	return be.EventTime
}

// GetAggregateID implementation for core Event interface
func (be *BaseEvent) GetAggregateID() uuid.UUID {
	return be.AggregateID
}

// GetAggregateType implementation for core Event interface
func (be *BaseEvent) GetAggregateType() string {
	return be.AggregateType
}

// GetData implementation for core Event interface
func (be *BaseEvent) GetData() interface{} {
	return be.EventData
}

// EventName provides compatibility with other event interfaces (e.g., learning domain's Event).
// Often, this might be the same as EventType or a more specific version.
func (be *BaseEvent) EventName() string {
	return be.EventType // Or a more specific name if needed
}

// Payload provides compatibility with other event interfaces (e.g., learning domain's Event).
// It attempts to return eventData as map[string]interface{}, wrapping if necessary.
func (be *BaseEvent) Payload() map[string]interface{} {
	if m, ok := be.EventData.(map[string]interface{}); ok {
		return m
	}
	return map[string]interface{}{"data": be.EventData}
}

// EventHandler 事件處理器接口
type EventHandler interface {
	// Handle 處理事件
	Handle(event Event) error
}

// EventSubscriber 事件訂閱者接口
type EventSubscriber interface {
	// Subscribe 訂閱事件
	Subscribe(eventType string, handler EventHandler) error
	// Unsubscribe 取消訂閱
	Unsubscribe(eventType string, handler EventHandler) error
}

// EventPublisher 事件發布者接口
type EventPublisher interface {
	// Publish 發布事件
	Publish(ctx context.Context, event interface{}) error
}

// EventBus 事件總線接口
type EventBus interface {
	EventPublisher
	EventSubscriber
}

// InMemoryEventBus 內存事件總線實現
type InMemoryEventBus struct {
	handlers map[string][]EventHandler
}

// LessonCompletedEvent 課程完成事件
type LessonCompletedEvent struct {
	*BaseEvent
	LessonID     uuid.UUID
	UserID       uuid.UUID
	Score        *entity.Score
	Duration     *entity.Duration
	CompletedAt  time.Time
	MasteryLevel entity.MasteryLevel
}

// LessonStartedEvent 課程開始事件
type LessonStartedEvent struct {
	*BaseEvent
	LessonID   uuid.UUID
	UserID     uuid.UUID
	StartedAt  time.Time
	Difficulty *entity.ExerciseDifficulty
}

// LessonEvaluatedEvent 課程評估事件
type LessonEvaluatedEvent struct {
	*BaseEvent
	LessonID    uuid.UUID
	UserID      uuid.UUID
	EvaluatedAt time.Time
	Score       *entity.Score
	Feedback    string
}

// ExerciseAttemptedEvent 練習嘗試事件
type ExerciseAttemptedEvent struct {
	*BaseEvent
	ExerciseID  uuid.UUID
	UserID      uuid.UUID
	Score       *entity.Score
	Duration    *entity.Duration
	Difficulty  *entity.ExerciseDifficulty
	AttemptedAt time.Time
	IsCorrect   bool
}

// ExerciseEvaluatedEvent 練習評估事件
type ExerciseEvaluatedEvent struct {
	*BaseEvent
	ExerciseID   uuid.UUID
	UserID       uuid.UUID
	EvaluatedAt  time.Time
	Score        *entity.Score
	Feedback     string
	NextExercise *uuid.UUID
}

// ExerciseDifficultyChangedEvent 練習難度變更事件
type ExerciseDifficultyChangedEvent struct {
	*BaseEvent
	ExerciseID    uuid.UUID
	UserID        uuid.UUID
	ChangedAt     time.Time
	OldDifficulty *entity.ExerciseDifficulty
	NewDifficulty *entity.ExerciseDifficulty
	Reason        string
}

// LearningProgressUpdatedEvent 學習進度更新事件
type LearningProgressUpdatedEvent struct {
	*BaseEvent
	UserID          uuid.UUID
	UpdatedAt       time.Time
	CurrentProgress int
	TargetProgress  int
}

// LearningPathUpdatedEvent 學習路徑更新事件
type LearningPathUpdatedEvent struct {
	*BaseEvent
	UserID    uuid.UUID
	UpdatedAt time.Time
	OldPath   string
	NewPath   string
	Reason    string
}
