package event

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	amqp "github.com/rabbitmq/amqp091-go"
)

// RabbitMQEventBus RabbitMQ 事件总线实现
type RabbitMQEventBus struct {
	conn         *amqp.Connection
	channel      *amqp.Channel
	rabbitConfig *RabbitMQConfig
	busConfig    *EventBusConfig
	handlers     map[string][]EventHandler
	mu           sync.RWMutex
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
	isRunning    bool
	runningMu    sync.Mutex
	queues       map[string]string // eventType -> queueName
}

// NewRabbitMQEventBus 创建 RabbitMQ 事件总线
func NewRabbitMQEventBus(conn *amqp.Connection, channel *amqp.Channel, rabbitConfig *RabbitMQConfig, busConfig *EventBusConfig) *RabbitMQEventBus {
	ctx, cancel := context.WithCancel(context.Background())
	
	bus := &RabbitMQEventBus{
		conn:         conn,
		channel:      channel,
		rabbitConfig: rabbitConfig,
		busConfig:    busConfig,
		handlers:     make(map[string][]EventHandler),
		ctx:          ctx,
		cancel:       cancel,
		queues:       make(map[string]string),
	}
	
	// 初始化交换机
	if err := bus.setupExchange(); err != nil {
		log.Printf("Failed to setup exchange: %v", err)
	}
	
	// 设置 QoS
	if err := bus.setupQoS(); err != nil {
		log.Printf("Failed to setup QoS: %v", err)
	}
	
	// 启动消费者
	bus.startConsumer()
	
	return bus
}

// setupExchange 设置交换机
func (b *RabbitMQEventBus) setupExchange() error {
	return b.channel.ExchangeDeclare(
		b.rabbitConfig.Exchange, // name
		"topic",                 // type
		true,                    // durable
		false,                   // auto-deleted
		false,                   // internal
		false,                   // no-wait
		nil,                     // arguments
	)
}

// setupQoS 设置 QoS
func (b *RabbitMQEventBus) setupQoS() error {
	return b.channel.Qos(
		b.rabbitConfig.PrefetchCount, // prefetch count
		0,     // prefetch size
		false, // global
	)
}

// Publish 发布事件到 RabbitMQ
func (b *RabbitMQEventBus) Publish(ctx context.Context, event interface{}) error {
	e, ok := event.(Event)
	if !ok {
		return fmt.Errorf("event must implement Event interface")
	}
	
	// 序列化事件数据
	eventData, err := json.Marshal(map[string]interface{}{
		"event_type":     e.GetEventType(),
		"event_time":     e.GetEventTime(),
		"aggregate_id":   e.GetAggregateID(),
		"aggregate_type": e.GetAggregateType(),
		"data":           e.GetData(),
		"payload":        e.Payload(),
	})
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}
	
	// 准备消息
	message := amqp.Publishing{
		ContentType:  "application/json",
		Body:         eventData,
		DeliveryMode: amqp.Transient, // 默认非持久化
		Timestamp:    time.Now(),
		MessageId:    uuid.New().String(),
		Headers: amqp.Table{
			"event_type":     e.GetEventType(),
			"aggregate_id":   e.GetAggregateID().String(),
			"aggregate_type": e.GetAggregateType(),
		},
	}
	
	// 如果配置了持久化
	if b.rabbitConfig.MessagePersistent {
		message.DeliveryMode = amqp.Persistent
	}
	
	// 重试机制
	var lastErr error
	for i := 0; i < b.busConfig.RetryAttempts; i++ {
		err := b.channel.PublishWithContext(
			ctx,
			b.rabbitConfig.Exchange, // exchange
			e.GetEventType(),        // routing key
			false,                   // mandatory
			false,                   // immediate
			message,
		)
		
		if err != nil {
			lastErr = err
			if i < b.busConfig.RetryAttempts-1 {
				time.Sleep(b.busConfig.RetryDelay)
			}
			continue
		}
		return nil
	}
	
	return fmt.Errorf("failed to publish event after %d attempts: %w", b.busConfig.RetryAttempts, lastErr)
}

// Subscribe 订阅事件
func (b *RabbitMQEventBus) Subscribe(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()
	
	// 添加处理器
	if _, ok := b.handlers[eventType]; !ok {
		b.handlers[eventType] = make([]EventHandler, 0)
	}
	b.handlers[eventType] = append(b.handlers[eventType], handler)
	
	// 如果是第一个处理器，创建队列并绑定
	if len(b.handlers[eventType]) == 1 {
		if err := b.createQueueForEventType(eventType); err != nil {
			return fmt.Errorf("failed to create queue for event type %s: %w", eventType, err)
		}
	}
	
	log.Printf("Subscribed to event type: %s", eventType)
	return nil
}

// createQueueForEventType 为事件类型创建队列
func (b *RabbitMQEventBus) createQueueForEventType(eventType string) error {
	// 生成队列名称
	queueName := fmt.Sprintf("events.%s", eventType)
	
	// 声明队列
	queue, err := b.channel.QueueDeclare(
		queueName,                        // name
		b.rabbitConfig.QueueDurable,      // durable
		b.rabbitConfig.QueueAutoDelete,   // delete when unused
		b.rabbitConfig.QueueExclusive,    // exclusive
		false,                            // no-wait
		nil,                              // arguments
	)
	if err != nil {
		return err
	}
	
	// 绑定队列到交换机
	err = b.channel.QueueBind(
		queue.Name,              // queue name
		eventType,               // routing key
		b.rabbitConfig.Exchange, // exchange
		false,                   // no-wait
		nil,                     // arguments
	)
	if err != nil {
		return err
	}
	
	// 记录队列名称
	b.queues[eventType] = queue.Name
	
	return nil
}

// Unsubscribe 取消订阅事件
func (b *RabbitMQEventBus) Unsubscribe(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()
	
	if handlers, ok := b.handlers[eventType]; ok {
		for i, h := range handlers {
			if h == handler {
				b.handlers[eventType] = append(handlers[:i], handlers[i+1:]...)
				log.Printf("Unsubscribed from event type: %s", eventType)
				
				// 如果没有更多处理器，可以考虑删除队列
				if len(b.handlers[eventType]) == 0 {
					delete(b.handlers, eventType)
					// 注意：这里不删除队列，因为可能还有未处理的消息
				}
				
				return nil
			}
		}
	}
	return fmt.Errorf("handler not found for event type: %s", eventType)
}

// startConsumer 启动消费者
func (b *RabbitMQEventBus) startConsumer() {
	b.runningMu.Lock()
	if b.isRunning {
		b.runningMu.Unlock()
		return
	}
	b.isRunning = true
	b.runningMu.Unlock()
	
	b.wg.Add(1)
	go b.consumeEvents()
}

// consumeEvents 消费事件
func (b *RabbitMQEventBus) consumeEvents() {
	defer b.wg.Done()
	
	for {
		select {
		case <-b.ctx.Done():
			return
		default:
			b.processQueues()
			time.Sleep(time.Millisecond * 100) // 短暂休眠避免过度轮询
		}
	}
}

// processQueues 处理所有队列
func (b *RabbitMQEventBus) processQueues() {
	b.mu.RLock()
	queues := make(map[string]string)
	for eventType, queueName := range b.queues {
		queues[eventType] = queueName
	}
	b.mu.RUnlock()
	
	for eventType, queueName := range queues {
		b.processQueue(eventType, queueName)
	}
}

// processQueue 处理单个队列
func (b *RabbitMQEventBus) processQueue(eventType, queueName string) {
	// 消费消息
	msgs, err := b.channel.Consume(
		queueName, // queue
		"",        // consumer
		false,     // auto-ack (手动确认)
		false,     // exclusive
		false,     // no-local
		false,     // no-wait
		nil,       // args
	)
	if err != nil {
		log.Printf("Failed to register consumer for queue %s: %v", queueName, err)
		return
	}
	
	// 处理消息（非阻塞）
	select {
	case msg := <-msgs:
		b.handleMessage(eventType, msg)
	default:
		// 没有消息，继续
	}
}

// handleMessage 处理单个消息
func (b *RabbitMQEventBus) handleMessage(eventType string, delivery amqp.Delivery) {
	// 反序列化事件数据
	var eventData map[string]interface{}
	if err := json.Unmarshal(delivery.Body, &eventData); err != nil {
		log.Printf("Failed to unmarshal event data: %v", err)
		delivery.Nack(false, false) // 拒绝消息，不重新入队
		return
	}
	
	// 重构事件对象
	event, err := b.reconstructEvent(eventType, eventData)
	if err != nil {
		log.Printf("Failed to reconstruct event: %v", err)
		delivery.Nack(false, false) // 拒绝消息，不重新入队
		return
	}
	
	// 获取事件处理器
	b.mu.RLock()
	handlers := b.handlers[eventType]
	b.mu.RUnlock()
	
	// 执行事件处理器
	var wg sync.WaitGroup
	for _, handler := range handlers {
		wg.Add(1)
		go func(h EventHandler, e Event) {
			defer wg.Done()
			if err := h.Handle(e); err != nil {
				log.Printf("Error handling event: %v", err)
			}
		}(handler, event)
	}
	
	// 等待所有处理器完成
	wg.Wait()
	
	// 确认消息
	delivery.Ack(false)
}

// reconstructEvent 重构事件对象
func (b *RabbitMQEventBus) reconstructEvent(eventType string, eventData map[string]interface{}) (Event, error) {
	// 提取基础事件字段
	aggregateIDStr, ok := eventData["aggregate_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid aggregate_id")
	}
	
	aggregateID, err := uuid.Parse(aggregateIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid aggregate_id format: %w", err)
	}
	
	aggregateType, ok := eventData["aggregate_type"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid aggregate_type")
	}
	
	data := eventData["data"]
	
	// 创建基础事件
	baseEvent := NewBaseEvent(eventType, aggregateID, aggregateType, data)
	
	// 设置事件时间
	if eventTimeStr, ok := eventData["event_time"].(string); ok {
		if eventTime, err := time.Parse(time.RFC3339, eventTimeStr); err == nil {
			baseEvent.EventTime = eventTime
		}
	}
	
	return baseEvent, nil
}

// Close 关闭事件总线
func (b *RabbitMQEventBus) Close() error {
	b.runningMu.Lock()
	if !b.isRunning {
		b.runningMu.Unlock()
		return nil
	}
	b.isRunning = false
	b.runningMu.Unlock()
	
	// 取消上下文
	b.cancel()
	
	// 等待所有 goroutine 完成
	b.wg.Wait()
	
	// 关闭通道和连接
	if b.channel != nil {
		b.channel.Close()
	}
	if b.conn != nil {
		return b.conn.Close()
	}
	
	return nil
}

// Health 健康检查
func (b *RabbitMQEventBus) Health(ctx context.Context) error {
	if b.conn.IsClosed() {
		return fmt.Errorf("rabbitmq connection is closed")
	}
	return nil
}

// GetStats 获取统计信息
func (b *RabbitMQEventBus) GetStats(ctx context.Context) (map[string]interface{}, error) {
	stats := map[string]interface{}{
		"connection_closed": b.conn.IsClosed(),
		"subscribed_types":  len(b.handlers),
		"queue_count":       len(b.queues),
	}
	
	// 添加每个事件类型的处理器数量
	b.mu.RLock()
	handlerCounts := make(map[string]int)
	for eventType, handlers := range b.handlers {
		handlerCounts[eventType] = len(handlers)
	}
	b.mu.RUnlock()
	
	stats["handler_counts"] = handlerCounts
	stats["queues"] = b.queues
	
	return stats, nil
}
