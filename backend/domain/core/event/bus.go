package event

import (
	"context"
	"sync"
)

// DefaultEventBus 默認事件總線實現
type DefaultEventBus struct {
	handlers map[string][]EventHandler
	mu       sync.RWMutex
}

// NewDefaultEventBus 創建新的事件總線
func NewDefaultEventBus() *DefaultEventBus {
	return &DefaultEventBus{
		handlers: make(map[string][]EventHandler),
	}
}

// Publish 實現 EventPublisher 接口
func (b *DefaultEventBus) Publish(ctx context.Context, event interface{}) error {
	e, ok := event.(Event)
	if !ok {
		return nil // 或者返回錯誤
	}

	b.mu.RLock()
	defer b.mu.RUnlock()

	handlers := b.handlers[e.GetEventType()]
	for _, handler := range handlers {
		// 在 goroutine 中異步執行處理器
		go func(h EventHandler, ev Event) {
			_ = h.Handle(ev) // 忽略錯誤，或者可以添加錯誤處理邏輯
		}(handler, e)
	}
	return nil
}

// Subscribe 實現 EventSubscriber 接口
func (b *DefaultEventBus) Subscribe(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if _, ok := b.handlers[eventType]; !ok {
		b.handlers[eventType] = make([]EventHandler, 0)
	}
	b.handlers[eventType] = append(b.handlers[eventType], handler)
	return nil
}

// Unsubscribe 實現 EventSubscriber 接口
func (b *DefaultEventBus) Unsubscribe(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if handlers, ok := b.handlers[eventType]; ok {
		for i, h := range handlers {
			if h == handler {
				b.handlers[eventType] = append(handlers[:i], handlers[i+1:]...)
				return nil
			}
		}
	}
	return nil
}
