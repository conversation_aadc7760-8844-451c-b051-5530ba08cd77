package event

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/compress"
)

// KafkaEventBus Kafka 事件总线实现
type KafkaEventBus struct {
	writer       *kafka.Writer
	reader       *kafka.Reader
	kafkaConfig  *KafkaConfig
	busConfig    *EventBusConfig
	handlers     map[string][]EventHandler
	mu           sync.RWMutex
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
	isRunning    bool
	runningMu    sync.Mutex
}

// NewKafkaEventBus 创建 Kafka 事件总线
func NewKafkaEventBus(kafkaConfig *KafkaConfig, busConfig *EventBusConfig) *KafkaEventBus {
	ctx, cancel := context.WithCancel(context.Background())
	
	bus := &KafkaEventBus{
		kafkaConfig: kafkaConfig,
		busConfig:   busConfig,
		handlers:    make(map[string][]EventHandler),
		ctx:         ctx,
		cancel:      cancel,
	}
	
	// 创建生产者
	bus.createWriter()
	
	// 创建消费者
	bus.createReader()
	
	// 启动消费者
	bus.startConsumer()
	
	return bus
}

// createWriter 创建 Kafka 生产者
func (b *KafkaEventBus) createWriter() {
	// 设置压缩算法
	var compression kafka.Compression
	switch b.kafkaConfig.ProducerConfig.Compression {
	case "gzip":
		compression = compress.Gzip
	case "snappy":
		compression = compress.Snappy
	case "lz4":
		compression = compress.Lz4
	case "zstd":
		compression = compress.Zstd
	default:
		compression = compress.None
	}
	
	// 设置 RequiredAcks
	var requiredAcks kafka.RequiredAcks
	switch b.kafkaConfig.ProducerConfig.RequiredAcks {
	case 0:
		requiredAcks = kafka.RequireNone
	case 1:
		requiredAcks = kafka.RequireOne
	case -1:
		requiredAcks = kafka.RequireAll
	default:
		requiredAcks = kafka.RequireOne
	}
	
	b.writer = &kafka.Writer{
		Addr:         kafka.TCP(b.kafkaConfig.Brokers...),
		Topic:        b.kafkaConfig.Topic,
		Balancer:     &kafka.LeastBytes{},
		BatchSize:    b.kafkaConfig.ProducerConfig.BatchSize,
		BatchTimeout: b.kafkaConfig.ProducerConfig.BatchTimeout,
		Compression:  compression,
		RequiredAcks: requiredAcks,
		MaxAttempts:  b.kafkaConfig.ProducerConfig.MaxRetries,
	}
}

// createReader 创建 Kafka 消费者
func (b *KafkaEventBus) createReader() {
	b.reader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:          b.kafkaConfig.Brokers,
		Topic:            b.kafkaConfig.Topic,
		GroupID:          b.kafkaConfig.ConsumerConfig.GroupID,
		StartOffset:      b.kafkaConfig.ConsumerConfig.StartOffset,
		CommitInterval:   b.kafkaConfig.ConsumerConfig.CommitInterval,
		GroupBalancers:   []kafka.GroupBalancer{&kafka.RoundRobinGroupBalancer{}},
		MaxBytes:         b.kafkaConfig.ConsumerConfig.MaxBytes,
		MinBytes:         1,
		MaxWait:          time.Second,
	})
}

// Publish 发布事件到 Kafka
func (b *KafkaEventBus) Publish(ctx context.Context, event interface{}) error {
	e, ok := event.(Event)
	if !ok {
		return fmt.Errorf("event must implement Event interface")
	}
	
	// 序列化事件数据
	eventData, err := json.Marshal(map[string]interface{}{
		"event_type":     e.GetEventType(),
		"event_time":     e.GetEventTime(),
		"aggregate_id":   e.GetAggregateID(),
		"aggregate_type": e.GetAggregateType(),
		"data":           e.GetData(),
		"payload":        e.Payload(),
	})
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}
	
	// 创建 Kafka 消息
	message := kafka.Message{
		Key:   []byte(e.GetAggregateID().String()),
		Value: eventData,
		Headers: []kafka.Header{
			{Key: "event_type", Value: []byte(e.GetEventType())},
			{Key: "aggregate_id", Value: []byte(e.GetAggregateID().String())},
			{Key: "aggregate_type", Value: []byte(e.GetAggregateType())},
			{Key: "event_id", Value: []byte(uuid.New().String())},
		},
		Time: time.Now(),
	}
	
	// 发布消息
	return b.writer.WriteMessages(ctx, message)
}

// Subscribe 订阅事件
func (b *KafkaEventBus) Subscribe(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()
	
	if _, ok := b.handlers[eventType]; !ok {
		b.handlers[eventType] = make([]EventHandler, 0)
	}
	b.handlers[eventType] = append(b.handlers[eventType], handler)
	
	log.Printf("Subscribed to event type: %s", eventType)
	return nil
}

// Unsubscribe 取消订阅事件
func (b *KafkaEventBus) Unsubscribe(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()
	
	if handlers, ok := b.handlers[eventType]; ok {
		for i, h := range handlers {
			if h == handler {
				b.handlers[eventType] = append(handlers[:i], handlers[i+1:]...)
				log.Printf("Unsubscribed from event type: %s", eventType)
				return nil
			}
		}
	}
	return fmt.Errorf("handler not found for event type: %s", eventType)
}

// startConsumer 启动消费者
func (b *KafkaEventBus) startConsumer() {
	b.runningMu.Lock()
	if b.isRunning {
		b.runningMu.Unlock()
		return
	}
	b.isRunning = true
	b.runningMu.Unlock()
	
	b.wg.Add(1)
	go b.consumeEvents()
}

// consumeEvents 消费事件
func (b *KafkaEventBus) consumeEvents() {
	defer b.wg.Done()
	
	for {
		select {
		case <-b.ctx.Done():
			return
		default:
			b.processMessage()
		}
	}
}

// processMessage 处理消息
func (b *KafkaEventBus) processMessage() {
	// 读取消息
	message, err := b.reader.ReadMessage(b.ctx)
	if err != nil {
		if err != context.Canceled {
			log.Printf("Error reading from Kafka: %v", err)
		}
		return
	}
	
	// 处理消息
	b.handleMessage(message)
}

// handleMessage 处理单个消息
func (b *KafkaEventBus) handleMessage(message kafka.Message) {
	// 提取事件类型
	var eventType string
	for _, header := range message.Headers {
		if header.Key == "event_type" {
			eventType = string(header.Value)
			break
		}
	}
	
	if eventType == "" {
		log.Printf("Message missing event_type header")
		return
	}
	
	// 反序列化事件数据
	var eventData map[string]interface{}
	if err := json.Unmarshal(message.Value, &eventData); err != nil {
		log.Printf("Failed to unmarshal event data: %v", err)
		return
	}
	
	// 重构事件对象
	event, err := b.reconstructEvent(eventType, eventData)
	if err != nil {
		log.Printf("Failed to reconstruct event: %v", err)
		return
	}
	
	// 获取事件处理器
	b.mu.RLock()
	handlers := b.handlers[eventType]
	b.mu.RUnlock()
	
	// 执行事件处理器
	for _, handler := range handlers {
		go func(h EventHandler, e Event) {
			if err := h.Handle(e); err != nil {
				log.Printf("Error handling event: %v", err)
			}
		}(handler, event)
	}
}

// reconstructEvent 重构事件对象
func (b *KafkaEventBus) reconstructEvent(eventType string, eventData map[string]interface{}) (Event, error) {
	// 提取基础事件字段
	aggregateIDStr, ok := eventData["aggregate_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid aggregate_id")
	}
	
	aggregateID, err := uuid.Parse(aggregateIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid aggregate_id format: %w", err)
	}
	
	aggregateType, ok := eventData["aggregate_type"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid aggregate_type")
	}
	
	data := eventData["data"]
	
	// 创建基础事件
	baseEvent := NewBaseEvent(eventType, aggregateID, aggregateType, data)
	
	// 设置事件时间
	if eventTimeStr, ok := eventData["event_time"].(string); ok {
		if eventTime, err := time.Parse(time.RFC3339, eventTimeStr); err == nil {
			baseEvent.EventTime = eventTime
		}
	}
	
	return baseEvent, nil
}

// Close 关闭事件总线
func (b *KafkaEventBus) Close() error {
	b.runningMu.Lock()
	if !b.isRunning {
		b.runningMu.Unlock()
		return nil
	}
	b.isRunning = false
	b.runningMu.Unlock()
	
	// 取消上下文
	b.cancel()
	
	// 等待所有 goroutine 完成
	b.wg.Wait()
	
	// 关闭 writer 和 reader
	var errs []error
	if b.writer != nil {
		if err := b.writer.Close(); err != nil {
			errs = append(errs, err)
		}
	}
	if b.reader != nil {
		if err := b.reader.Close(); err != nil {
			errs = append(errs, err)
		}
	}
	
	if len(errs) > 0 {
		return fmt.Errorf("errors closing kafka bus: %v", errs)
	}
	
	return nil
}

// Health 健康检查
func (b *KafkaEventBus) Health(ctx context.Context) error {
	// 尝试获取 topic 元数据来检查连接
	conn, err := kafka.Dial("tcp", b.kafkaConfig.Brokers[0])
	if err != nil {
		return fmt.Errorf("failed to connect to kafka: %w", err)
	}
	defer conn.Close()
	
	_, err = conn.ReadPartitions(b.kafkaConfig.Topic)
	if err != nil {
		return fmt.Errorf("failed to read topic partitions: %w", err)
	}
	
	return nil
}

// GetStats 获取统计信息
func (b *KafkaEventBus) GetStats(ctx context.Context) (map[string]interface{}, error) {
	stats := map[string]interface{}{
		"topic":            b.kafkaConfig.Topic,
		"brokers":          b.kafkaConfig.Brokers,
		"consumer_group":   b.kafkaConfig.ConsumerConfig.GroupID,
		"subscribed_types": len(b.handlers),
	}
	
	// 添加每个事件类型的处理器数量
	b.mu.RLock()
	handlerCounts := make(map[string]int)
	for eventType, handlers := range b.handlers {
		handlerCounts[eventType] = len(handlers)
	}
	b.mu.RUnlock()
	
	stats["handler_counts"] = handlerCounts
	
	// 获取 writer 统计信息
	if b.writer != nil {
		writerStats := b.writer.Stats()
		stats["writer_stats"] = map[string]interface{}{
			"writes":  writerStats.Writes,
			"messages": writerStats.Messages,
			"bytes":   writerStats.Bytes,
			"errors":  writerStats.Errors,
		}
	}
	
	// 获取 reader 统计信息
	if b.reader != nil {
		readerStats := b.reader.Stats()
		stats["reader_stats"] = map[string]interface{}{
			"messages": readerStats.Messages,
			"bytes":    readerStats.Bytes,
			"rebalances": readerStats.Rebalances,
			"timeouts": readerStats.Timeouts,
			"errors":   readerStats.Errors,
		}
	}
	
	return stats, nil
}
