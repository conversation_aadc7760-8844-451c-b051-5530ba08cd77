package event

import (
	"context"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// EventStore 事件存儲接口
type EventStore interface {
	// Save 保存事件
	Save(ctx context.Context, event Event) error
	// GetEvents 獲取聚合根的所有事件
	GetEvents(ctx context.Context, aggregateID uuid.UUID) ([]Event, error)
	// GetEventsByType 獲取指定類型的所有事件
	GetEventsByType(ctx context.Context, eventType string) ([]Event, error)
	// GetEventsByTimeRange 獲取指定時間範圍內的事件
	GetEventsByTimeRange(ctx context.Context, start, end time.Time) ([]Event, error)
}

// StoredEvent 存儲的事件結構
type StoredEvent struct {
	ID            uuid.UUID       `json:"id"`
	Type          string          `json:"type"`
	AggregateID   uuid.UUID       `json:"aggregateId"`
	AggregateType string          `json:"aggregateType"`
	Data          json.RawMessage `json:"data"`
	Version       int             `json:"version"`
	CreatedAt     time.Time       `json:"createdAt"`
}

// EventReconstructor 事件重建器接口
type EventReconstructor interface {
	// Reconstruct 重建事件
	Reconstruct(stored StoredEvent) (Event, error)
}
