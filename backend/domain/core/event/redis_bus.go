package event

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
)

// RedisEventBus Redis 事件总线实现
type RedisEventBus struct {
	client      *redis.Client
	redisConfig *RedisConfig
	busConfig   *EventBusConfig
	handlers    map[string][]EventHandler
	mu          sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	isRunning   bool
	runningMu   sync.Mutex
}

// NewRedisEventBus 创建 Redis 事件总线
func NewRedisEventBus(client *redis.Client, redisConfig *RedisConfig, busConfig *EventBusConfig) *RedisEventBus {
	ctx, cancel := context.WithCancel(context.Background())

	bus := &RedisEventBus{
		client:      client,
		redisConfig: redisConfig,
		busConfig:   busConfig,
		handlers:    make(map[string][]EventHandler),
		ctx:         ctx,
		cancel:      cancel,
	}

	// 启动消费者
	bus.startConsumer()

	return bus
}

// Publish 发布事件到 Redis Stream
func (b *RedisEventBus) Publish(ctx context.Context, event interface{}) error {
	e, ok := event.(Event)
	if !ok {
		return fmt.Errorf("event must implement Event interface")
	}

	// 序列化事件数据
	eventData, err := json.Marshal(map[string]interface{}{
		"event_type":     e.GetEventType(),
		"event_time":     e.GetEventTime(),
		"aggregate_id":   e.GetAggregateID(),
		"aggregate_type": e.GetAggregateType(),
		"data":           e.GetData(),
		"payload":        e.Payload(),
	})
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	// 发布到 Redis Stream
	args := &redis.XAddArgs{
		Stream: b.redisConfig.StreamKey,
		Values: map[string]interface{}{
			"event_id":   uuid.New().String(),
			"event_type": e.GetEventType(),
			"event_data": string(eventData),
			"timestamp":  time.Now().Unix(),
		},
	}

	// 重试机制
	var lastErr error
	for i := 0; i < b.busConfig.RetryAttempts; i++ {
		if _, err := b.client.XAdd(ctx, args).Result(); err != nil {
			lastErr = err
			if i < b.busConfig.RetryAttempts-1 {
				time.Sleep(b.busConfig.RetryDelay)
			}
			continue
		}
		return nil
	}

	return fmt.Errorf("failed to publish event after %d attempts: %w", b.busConfig.RetryAttempts, lastErr)
}

// Subscribe 订阅事件
func (b *RedisEventBus) Subscribe(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if _, ok := b.handlers[eventType]; !ok {
		b.handlers[eventType] = make([]EventHandler, 0)
	}
	b.handlers[eventType] = append(b.handlers[eventType], handler)

	log.Printf("Subscribed to event type: %s", eventType)
	return nil
}

// Unsubscribe 取消订阅事件
func (b *RedisEventBus) Unsubscribe(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if handlers, ok := b.handlers[eventType]; ok {
		for i, h := range handlers {
			if h == handler {
				b.handlers[eventType] = append(handlers[:i], handlers[i+1:]...)
				log.Printf("Unsubscribed from event type: %s", eventType)
				return nil
			}
		}
	}
	return fmt.Errorf("handler not found for event type: %s", eventType)
}

// startConsumer 启动消费者
func (b *RedisEventBus) startConsumer() {
	b.runningMu.Lock()
	if b.isRunning {
		b.runningMu.Unlock()
		return
	}
	b.isRunning = true
	b.runningMu.Unlock()

	// 创建消费者组
	b.createConsumerGroup()

	b.wg.Add(1)
	go b.consumeEvents()
}

// createConsumerGroup 创建消费者组
func (b *RedisEventBus) createConsumerGroup() {
	// 尝试创建消费者组，如果已存在则忽略错误
	err := b.client.XGroupCreate(b.ctx, b.redisConfig.StreamKey, b.redisConfig.ConsumerGroup, "0").Err()
	if err != nil && err.Error() != "BUSYGROUP Consumer Group name already exists" {
		log.Printf("Warning: failed to create consumer group: %v", err)
	}
}

// consumeEvents 消费事件
func (b *RedisEventBus) consumeEvents() {
	defer b.wg.Done()

	for {
		select {
		case <-b.ctx.Done():
			return
		default:
			b.processMessages()
		}
	}
}

// processMessages 处理消息
func (b *RedisEventBus) processMessages() {
	// 读取消息
	streams, err := b.client.XReadGroup(b.ctx, &redis.XReadGroupArgs{
		Group:    b.redisConfig.ConsumerGroup,
		Consumer: b.redisConfig.ConsumerName,
		Streams:  []string{b.redisConfig.StreamKey, ">"},
		Count:    int64(b.busConfig.BatchSize),
		Block:    b.busConfig.FlushInterval,
	}).Result()

	if err != nil {
		if err != redis.Nil {
			log.Printf("Error reading from Redis stream: %v", err)
		}
		return
	}

	// 处理每个流的消息
	for _, stream := range streams {
		for _, message := range stream.Messages {
			b.handleMessage(message)
		}
	}
}

// handleMessage 处理单个消息
func (b *RedisEventBus) handleMessage(message redis.XMessage) {
	// 提取事件数据
	eventDataStr, ok := message.Values["event_data"].(string)
	if !ok {
		log.Printf("Invalid event data format in message: %s", message.ID)
		b.ackMessage(message.ID)
		return
	}

	eventTypeStr, ok := message.Values["event_type"].(string)
	if !ok {
		log.Printf("Invalid event type format in message: %s", message.ID)
		b.ackMessage(message.ID)
		return
	}

	// 反序列化事件数据
	var eventData map[string]interface{}
	if err := json.Unmarshal([]byte(eventDataStr), &eventData); err != nil {
		log.Printf("Failed to unmarshal event data: %v", err)
		b.ackMessage(message.ID)
		return
	}

	// 重构事件对象
	event, err := b.reconstructEvent(eventTypeStr, eventData)
	if err != nil {
		log.Printf("Failed to reconstruct event: %v", err)
		b.ackMessage(message.ID)
		return
	}

	// 获取事件处理器
	b.mu.RLock()
	handlers := b.handlers[eventTypeStr]
	b.mu.RUnlock()

	// 执行事件处理器
	for _, handler := range handlers {
		go func(h EventHandler, e Event, msgID string) {
			if err := h.Handle(e); err != nil {
				log.Printf("Error handling event %s: %v", msgID, err)
			}
		}(handler, event, message.ID)
	}

	// 确认消息
	b.ackMessage(message.ID)
}

// reconstructEvent 重构事件对象
func (b *RedisEventBus) reconstructEvent(eventType string, eventData map[string]interface{}) (Event, error) {
	// 提取基础事件字段
	aggregateIDStr, ok := eventData["aggregate_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid aggregate_id")
	}

	aggregateID, err := uuid.Parse(aggregateIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid aggregate_id format: %w", err)
	}

	aggregateType, ok := eventData["aggregate_type"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid aggregate_type")
	}

	data := eventData["data"]

	// 创建基础事件
	baseEvent := NewBaseEvent(eventType, aggregateID, aggregateType, data)

	// 设置事件时间
	if eventTimeStr, ok := eventData["event_time"].(string); ok {
		if eventTime, err := time.Parse(time.RFC3339, eventTimeStr); err == nil {
			baseEvent.EventTime = eventTime
		}
	}

	return baseEvent, nil
}

// ackMessage 确认消息
func (b *RedisEventBus) ackMessage(messageID string) {
	if err := b.client.XAck(b.ctx, b.redisConfig.StreamKey, b.redisConfig.ConsumerGroup, messageID).Err(); err != nil {
		log.Printf("Failed to ack message %s: %v", messageID, err)
	}
}

// Close 关闭事件总线
func (b *RedisEventBus) Close() error {
	b.runningMu.Lock()
	if !b.isRunning {
		b.runningMu.Unlock()
		return nil
	}
	b.isRunning = false
	b.runningMu.Unlock()

	// 取消上下文
	b.cancel()

	// 等待所有 goroutine 完成
	b.wg.Wait()

	// 关闭 Redis 客户端
	return b.client.Close()
}

// Health 健康检查
func (b *RedisEventBus) Health(ctx context.Context) error {
	return b.client.Ping(ctx).Err()
}

// GetStats 获取统计信息
func (b *RedisEventBus) GetStats(ctx context.Context) (map[string]interface{}, error) {
	// 获取流信息
	streamInfo, err := b.client.XInfoStream(ctx, b.redisConfig.StreamKey).Result()
	if err != nil {
		return nil, err
	}

	// 获取消费者组信息
	groupInfo, err := b.client.XInfoGroups(ctx, b.redisConfig.StreamKey).Result()
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"stream_length":    streamInfo.Length,
		"consumer_groups":  len(groupInfo),
		"subscribed_types": len(b.handlers),
	}

	// 添加每个事件类型的处理器数量
	b.mu.RLock()
	handlerCounts := make(map[string]int)
	for eventType, handlers := range b.handlers {
		handlerCounts[eventType] = len(handlers)
	}
	b.mu.RUnlock()

	stats["handler_counts"] = handlerCounts

	return stats, nil
}
