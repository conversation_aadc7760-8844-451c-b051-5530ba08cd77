package di

import (
	coredi "languagelearning/domain/core/di"
	learningRepoImpl "languagelearning/domain/learning/repository/impl"
	learningSvcImpl "languagelearning/domain/learning/service/impl"

	"go.uber.org/dig"
)

// LearningModule 學習領域的DI模塊
type LearningModule struct {
	coredi.BaseModule
}

// NewLearningModule 創建學習領域模塊
func NewLearningModule() coredi.Module {
	return &LearningModule{
		BaseModule: coredi.NewBaseModule(
			"learning",
			[]string{"core"}, // 依賴核心模塊（事件系統）
			[]coredi.ModuleFeature{coredi.FeatureBasic},
		),
	}
}

// Register 註冊學習領域的所有依賴
func (m *LearningModule) Register(container *dig.Container) error {
	// 註冊存儲庫
	if err := m.registerRepositories(container); err != nil {
		return err
	}

	// 註冊服務
	if err := m.registerServices(container); err != nil {
		return err
	}

	return nil
}

// registerRepositories 註冊學習領域的存儲庫
func (m *LearningModule) registerRepositories(container *dig.Container) error {
	// 註冊課程存儲庫
	if err := container.Provide(learningRepoImpl.NewGormLessonRepository); err != nil {
		return err
	}

	// 註冊課程進度存儲庫
	if err := container.Provide(learningRepoImpl.NewGormLessonProgressRepository); err != nil {
		return err
	}

	// 註冊練習存儲庫
	if err := container.Provide(learningRepoImpl.NewGormExerciseRepository); err != nil {
		return err
	}

	// 註冊練習嘗試存儲庫
	if err := container.Provide(learningRepoImpl.NewGormExerciseAttemptRepository); err != nil {
		return err
	}

	// 註冊練習關係存儲庫
	if err := container.Provide(learningRepoImpl.NewGormExerciseRelationRepository); err != nil {
		return err
	}

	// 註冊難度元數據存儲庫
	if err := container.Provide(learningRepoImpl.NewGormDifficultyMetadataRepository); err != nil {
		return err
	}

	// 註冊練習會話存儲庫
	if err := container.Provide(learningRepoImpl.NewPracticeRepository); err != nil {
		return err
	}

	// 註冊單詞存儲庫
	if err := container.Provide(learningRepoImpl.NewWordRepository); err != nil {
		return err
	}

	// 註冊評估存儲庫
	if err := container.Provide(learningRepoImpl.NewEvaluationRepository); err != nil {
		return err
	}

	// 註冊評估結果存儲庫
	if err := container.Provide(learningRepoImpl.NewEvaluationResultRepository); err != nil {
		return err
	}

	// 註冊評估進度存儲庫
	if err := container.Provide(learningRepoImpl.NewAssessmentProgressRepository); err != nil {
		return err
	}

	// 註冊評估問題存儲庫
	if err := container.Provide(learningRepoImpl.NewEvalQuestionRepository); err != nil {
		return err
	}

	// 註冊評估部分存儲庫
	if err := container.Provide(learningRepoImpl.NewEvalSectionRepository); err != nil {
		return err
	}

	// 註冊學習路徑存儲庫
	if err := container.Provide(learningRepoImpl.NewModelsLessonRepository); err != nil {
		return err
	}

	// 註冊學習路徑存儲庫
	if err := container.Provide(learningRepoImpl.NewModelsLearningPathRepository); err != nil {
		return err
	}

	// 註冊學習路徑課程存儲庫
	if err := container.Provide(learningRepoImpl.NewModelsLearningPathLessonRepository); err != nil {
		return err
	}

	return nil
}

// registerServices 註冊學習領域的服務
func (m *LearningModule) registerServices(container *dig.Container) error {
	// 註冊自適應學習服務
	if err := container.Provide(learningSvcImpl.NewAdaptiveLearningService); err != nil {
		return err
	}

	// 註冊學習路徑服務
	if err := container.Provide(learningSvcImpl.NewLearningPathService); err != nil {
		return err
	}

	// 註冊練習服務
	if err := container.Provide(learningSvcImpl.NewPracticeService); err != nil {
		return err
	}

	// 註冊個性化學習服務
	if err := container.Provide(learningSvcImpl.NewPersonalizedLearningService); err != nil {
		return err
	}

	// 註冊練習服務
	if err := container.Provide(learningSvcImpl.NewExerciseService); err != nil {
		return err
	}

	// 註冊練習關係服務
	if err := container.Provide(learningSvcImpl.NewExerciseRelationService); err != nil {
		return err
	}

	// 註冊課程服務
	if err := container.Provide(learningSvcImpl.NewLessonService); err != nil {
		return err
	}

	// 註冊單詞服務
	if err := container.Provide(learningSvcImpl.NewWordService); err != nil {
		return err
	}

	return nil
}
