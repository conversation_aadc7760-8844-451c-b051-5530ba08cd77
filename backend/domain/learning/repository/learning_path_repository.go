package repository

import (
	"context"
	"languagelearning/domain/learning/path/entity"
	baseRepo "languagelearning/domain/repository"
	"time"

	"github.com/google/uuid"
)

// LearningPathRepository 学习路径仓库接口
type LearningPathRepository interface {
	baseRepo.Repository[entity.LearningPath, uuid.UUID]
	baseRepo.PageableRepository[entity.LearningPath, uuid.UUID]

	// FindByUser 查找用户的学习路径
	FindByUser(ctx context.Context, userID uuid.UUID) ([]*entity.LearningPath, error)

	// FindByUserAndStatus 查找用户指定状态的学习路径
	FindByUserAndStatus(ctx context.Context, userID uuid.UUID, status entity.PathStatus) ([]*entity.LearningPath, error)

	// GetActiveLearningPaths 获取用户的活跃学习路径
	GetActiveLearningPaths(ctx context.Context, userID uuid.UUID) ([]*entity.LearningPath, error)

	// GetCompletedLearningPaths 获取用户的已完成学习路径
	GetCompletedLearningPaths(ctx context.Context, userID uuid.UUID) ([]*entity.LearningPath, error)

	// FindByLevel 查找指定级别的学习路径
	FindByLevel(ctx context.Context, level string) ([]*entity.LearningPath, error)

	// FindByFocusArea 查找包含指定焦点领域的学习路径
	FindByFocusArea(ctx context.Context, focusArea string) ([]*entity.LearningPath, error)

	// GetDetail 获取学习路径详情，包括关联的课程
	GetDetail(ctx context.Context, pathID uuid.UUID) (*entity.LearningPath, error)

	// UpdateProgress 更新学习路径进度
	UpdateProgress(ctx context.Context, pathID uuid.UUID, progress int) error

	// UpdateStatus 更新学习路径状态
	UpdateStatus(ctx context.Context, pathID uuid.UUID, status entity.PathStatus) error
}

// LearningPathLessonRepository 学习路径课程仓库接口
type LearningPathLessonRepository interface {
	baseRepo.Repository[entity.LearningPathLesson, uuid.UUID]

	// FindByLearningPath 查找学习路径的课程
	FindByLearningPath(ctx context.Context, pathID uuid.UUID) ([]*entity.LearningPathLesson, error)

	// FindByLearningPathAndOrder 通过顺序查找学习路径的课程
	FindByLearningPathAndOrder(ctx context.Context, pathID uuid.UUID, order int) (*entity.LearningPathLesson, error)

	// GetCompletedLessons 获取学习路径的已完成课程
	GetCompletedLessons(ctx context.Context, pathID uuid.UUID) ([]*entity.LearningPathLesson, error)

	// UpdateOrder 更新课程顺序
	UpdateOrder(ctx context.Context, lessonID uuid.UUID, newOrder int) error

	// MarkAsCompleted 将课程标记为已完成
	MarkAsCompleted(ctx context.Context, lessonID uuid.UUID) error

	// DeleteFromPath 从学习路径中删除课程
	DeleteFromPath(ctx context.Context, pathID uuid.UUID, lessonID string) error
}

// LearningPathRecommendationRepository 学习路径推荐仓库接口
type LearningPathRecommendationRepository interface {
	baseRepo.Repository[entity.LearningPathRecommendation, uuid.UUID]

	// FindByUser 查找用户的学习路径推荐
	FindByUser(ctx context.Context, userID uuid.UUID) ([]*entity.LearningPathRecommendation, error)

	// FindTopRecommendations 查找用户的顶级推荐
	FindTopRecommendations(ctx context.Context, userID uuid.UUID, limit int) ([]*entity.LearningPathRecommendation, error)

	// DeleteOldRecommendations 删除旧的推荐
	DeleteOldRecommendations(ctx context.Context, userID uuid.UUID, olderThan time.Time) error
}
