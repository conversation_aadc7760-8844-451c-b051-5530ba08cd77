package repository

import (
	"context"
	"languagelearning/models"

	"github.com/google/uuid"
)

// EvaluationRepository defines the interface for evaluation data access
type EvaluationRepository interface {
	// FindByID retrieves an evaluation by ID
	FindByID(ctx context.Context, id uuid.UUID) (*models.Evaluation, error)

	// FindByUserID retrieves evaluations for a user
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.Evaluation, error)

	// FindByUserIDAndType retrieves evaluations by user and type
	FindByUserIDAndType(ctx context.Context, userID uuid.UUID, evalType models.EvaluationType) ([]models.Evaluation, error)

	// Create creates a new evaluation
	Create(ctx context.Context, evaluation models.Evaluation) (*models.Evaluation, error)

	// Update updates an evaluation
	Update(ctx context.Context, evaluation models.Evaluation) (*models.Evaluation, error)

	// Delete deletes an evaluation
	Delete(ctx context.Context, id uuid.UUID) error

	// FindActiveByUserID retrieves active evaluations for a user
	FindActiveByUserID(ctx context.Context, userID uuid.UUID) ([]models.Evaluation, error)

	// FindCompletedByUserID retrieves completed evaluations for a user
	FindCompletedByUserID(ctx context.Context, userID uuid.UUID) ([]models.Evaluation, error)

	// FindByIDAndUserIDWithDetails retrieves an evaluation by ID and user ID with sections and questions
	FindByIDAndUserIDWithDetails(ctx context.Context, id, userID uuid.UUID) (*models.Evaluation, error)

	// FindByIDWithDetails retrieves an evaluation by ID with sections and questions
	FindByIDWithDetails(ctx context.Context, id uuid.UUID) (*models.Evaluation, error)
}

// EvaluationResultRepository defines the interface for evaluation result data access
type EvaluationResultRepository interface {
	// FindByEvaluationID retrieves evaluation results by evaluation ID
	FindByEvaluationID(ctx context.Context, evaluationID uuid.UUID) (*models.EvaluationResult, error)

	// FindByUserID retrieves evaluation results for a user
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.EvaluationResult, error)

	// Create creates a new evaluation result
	Create(ctx context.Context, result models.EvaluationResult) (*models.EvaluationResult, error)

	// Update updates an evaluation result
	Update(ctx context.Context, result models.EvaluationResult) (*models.EvaluationResult, error)

	// Delete deletes an evaluation result
	Delete(ctx context.Context, id uuid.UUID) error

	// FindByEvaluationIDAndUserID retrieves evaluation result by evaluation and user
	FindByEvaluationIDAndUserID(ctx context.Context, evaluationID, userID uuid.UUID) (*models.EvaluationResult, error)

	// FindByEvaluationIDAndUserIDWithDetails retrieves evaluation result by evaluation and user with section scores
	FindByEvaluationIDAndUserIDWithDetails(ctx context.Context, evaluationID, userID uuid.UUID) (*models.EvaluationResult, error)

	// FindByUserIDOrderedByDate retrieves evaluation results for a user ordered by completion date
	FindByUserIDOrderedByDate(ctx context.Context, userID uuid.UUID) ([]models.EvaluationResult, error)
}

// AssessmentProgressRepository defines the interface for assessment progress data access
type AssessmentProgressRepository interface {
	// FindByUserEvaluationAndSession retrieves assessment progress by user, evaluation and session
	FindByUserEvaluationAndSession(ctx context.Context, userID, evaluationID uuid.UUID, sessionToken string) (*models.AssessmentProgress, error)

	// Create creates a new assessment progress
	Create(ctx context.Context, progress models.AssessmentProgress) (*models.AssessmentProgress, error)

	// Update updates an assessment progress
	Update(ctx context.Context, progress models.AssessmentProgress) (*models.AssessmentProgress, error)

	// DeleteByUserAndEvaluation deletes assessment progress by user and evaluation
	DeleteByUserAndEvaluation(ctx context.Context, userID, evaluationID uuid.UUID) error
}

// EvalQuestionRepository defines the interface for evaluation question data access
type EvalQuestionRepository interface {
	// FindByID retrieves an evaluation question by ID
	FindByID(ctx context.Context, id uint) (*models.EvalQuestion, error)

	// Update updates an evaluation question
	Update(ctx context.Context, question models.EvalQuestion) (*models.EvalQuestion, error)
}

// EvalSectionRepository defines the interface for evaluation section data access
type EvalSectionRepository interface {
	// Update updates an evaluation section
	Update(ctx context.Context, section models.EvalSection) (*models.EvalSection, error)
}
