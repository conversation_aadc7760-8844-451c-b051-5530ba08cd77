package repository

import (
	"context"
	entity "languagelearning/domain/learning/entity"
	baseRepo "languagelearning/domain/repository"

	"github.com/google/uuid"
)

// LessonRepository 课程仓库接口
type LessonRepository interface {
	baseRepo.Repository[entity.Lesson, uuid.UUID]
	baseRepo.PageableRepository[entity.Lesson, uuid.UUID]

	// FindByLevel 查找指定级别的课程
	FindByLevel(ctx context.Context, level entity.LessonLevel) ([]*entity.Lesson, error)

	// FindByCategory 查找指定类别的课程
	FindByCategory(ctx context.Context, category entity.LessonCategory) ([]*entity.Lesson, error)

	// FindByTags 查找包含指定标签的课程
	FindByTags(ctx context.Context, tags []string) ([]*entity.Lesson, error)

	// FindByLanguage 查找指定语言的课程
	FindByLanguage(ctx context.Context, languageCode string) ([]*entity.Lesson, error)

	// GetPublishedLessons 获取已发布的课程
	GetPublishedLessons(ctx context.Context) ([]*entity.Lesson, error)

	// GetFeaturedLessons 获取精选课程
	GetFeaturedLessons(ctx context.Context, limit int) ([]*entity.Lesson, error)

	// GetRelatedLessons 获取相关课程
	GetRelatedLessons(ctx context.Context, lessonID uuid.UUID, limit int) ([]*entity.Lesson, error)

	// FindByAuthorID 查找作者的所有课程
	FindByAuthorID(ctx context.Context, authorID uuid.UUID) ([]*entity.Lesson, error)

	// FindByStatus 根据状态查找课程
	FindByStatus(ctx context.Context, status entity.LessonStatus) ([]*entity.Lesson, error)

	// FindByType 根据类型查找课程
	FindByType(ctx context.Context, lessonType entity.LessonType) ([]*entity.Lesson, error)

	// FindByDifficulty 根据难度查找课程
	FindByDifficulty(ctx context.Context, difficulty *entity.ExerciseDifficulty) ([]*entity.Lesson, error)

	// FindPrerequisites 查找课程的所有前置课程
	FindPrerequisites(ctx context.Context, lessonID uuid.UUID) ([]*entity.Lesson, error)

	// FindByPrerequisite 查找将指定课程作为前置课程的所有课程
	FindByPrerequisite(ctx context.Context, prerequisiteID uuid.UUID) ([]*entity.Lesson, error)

	// Search 搜索课程
	Search(ctx context.Context, query string) ([]*entity.Lesson, error)

	// List 列出课程，支持分页和排序
	List(ctx context.Context, offset, limit int, orderBy string) ([]*entity.Lesson, error)

	// Count 获取课程总数
	Count(ctx context.Context) (int64, error)

	// CountByAuthor 获取作者的课程总数
	CountByAuthor(ctx context.Context, authorID uuid.UUID) (int64, error)

	// CountByStatus 获取指定状态的课程总数
	CountByStatus(ctx context.Context, status entity.LessonStatus) (int64, error)

	// CountByType 获取指定类型的课程总数
	CountByType(ctx context.Context, lessonType entity.LessonType) (int64, error)

	// CountByLevel 获取指定级别的课程总数
	CountByLevel(ctx context.Context, level entity.LessonLevel) (int64, error)

	// CountByDifficulty 获取指定难度的课程总数
	CountByDifficulty(ctx context.Context, difficulty *entity.ExerciseDifficulty) (int64, error)

	// AddExercise 添加练习到课程
	AddExercise(ctx context.Context, lessonID, exerciseID uuid.UUID) error

	// RemoveExercise 从课程中移除练习
	RemoveExercise(ctx context.Context, lessonID, exerciseID uuid.UUID) error

	// AddResource 添加资源到课程
	AddResource(ctx context.Context, lessonID uuid.UUID, resource *entity.Resource) error

	// RemoveResource 从课程中移除资源
	RemoveResource(ctx context.Context, lessonID, resourceID uuid.UUID) error

	// AddPrerequisite 添加前置课程
	AddPrerequisite(ctx context.Context, lessonID, prerequisiteID uuid.UUID) error

	// RemovePrerequisite 移除前置课程
	RemovePrerequisite(ctx context.Context, lessonID, prerequisiteID uuid.UUID) error

	// AddTag 添加标签
	AddTag(ctx context.Context, lessonID uuid.UUID, tag string) error

	// RemoveTag 移除标签
	RemoveTag(ctx context.Context, lessonID uuid.UUID, tag string) error

	// AddObjective 添加学习目标
	AddObjective(ctx context.Context, lessonID uuid.UUID, objective string) error

	// RemoveObjective 移除学习目标
	RemoveObjective(ctx context.Context, lessonID uuid.UUID, objective string) error

	// UpdateStatus 更新课程状态
	UpdateStatus(ctx context.Context, lessonID uuid.UUID, status entity.LessonStatus) error

	// UpdateDuration 更新课程时长
	UpdateDuration(ctx context.Context, lessonID uuid.UUID, minutes int) error
}

// LessonProgressRepository 课程进度仓库接口
type LessonProgressRepository interface {
	baseRepo.Repository[entity.LessonProgress, uuid.UUID]

	// FindByUser 查找用户的课程进度
	FindByUser(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error)

	// FindByLesson 查找课程的进度记录
	FindByLesson(ctx context.Context, lessonID uuid.UUID) ([]*entity.LessonProgress, error)

	// GetUserProgress 获取用户在课程中的进度
	GetUserProgress(ctx context.Context, userID, lessonID uuid.UUID) (*entity.LessonProgress, error)

	// GetCompletedLessons 获取用户已完成的课程
	GetCompletedLessons(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error)

	// GetInProgressLessons 获取用户进行中的课程
	GetInProgressLessons(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error)

	// UpdateProgress 更新课程进度
	UpdateProgress(ctx context.Context, progressID uuid.UUID, value int) error

	// MarkAsCompleted 将课程标记为已完成
	MarkAsCompleted(ctx context.Context, progressID uuid.UUID) error

	// GetUserStatistics 获取用户的课程统计信息
	GetUserStatistics(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error)
}
