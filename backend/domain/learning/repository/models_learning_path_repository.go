package repository

import (
	"context"
	"languagelearning/models"

	"github.com/google/uuid"
)

// ModelsLearningPathRepository defines the interface for learning path data access using models
type ModelsLearningPathRepository interface {
	// FindByID retrieves a learning path by ID
	FindByID(ctx context.Context, id uuid.UUID) (*models.LearningPath, error)
	
	// FindByUserID retrieves learning paths for a user
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.LearningPath, error)
	
	// FindByUserIDAndStatus retrieves learning paths by user and status
	FindByUserIDAndStatus(ctx context.Context, userID uuid.UUID, status models.LearningPathStatus) ([]models.LearningPath, error)
	
	// FindByUserIDWithLessons retrieves learning path with lessons preloaded
	FindByUserIDWithLessons(ctx context.Context, userID, pathID uuid.UUID) (*models.LearningPath, error)
	
	// FindActiveByUserID retrieves active learning paths for a user
	FindActiveByUserID(ctx context.Context, userID uuid.UUID) ([]models.LearningPath, error)
	
	// Create creates a new learning path
	Create(ctx context.Context, path models.LearningPath) (*models.LearningPath, error)
	
	// Update updates a learning path
	Update(ctx context.Context, path models.LearningPath) (*models.LearningPath, error)
	
	// Delete deletes a learning path
	Delete(ctx context.Context, id uuid.UUID) error
	
	// UpdateProgress updates learning path progress
	UpdateProgress(ctx context.Context, pathID uuid.UUID, progress int) error
	
	// MarkAsCompleted marks a learning path as completed
	MarkAsCompleted(ctx context.Context, pathID uuid.UUID) error
}

// ModelsLearningPathLessonRepository defines the interface for learning path lesson data access using models
type ModelsLearningPathLessonRepository interface {
	// FindByPathID retrieves lessons for a learning path
	FindByPathID(ctx context.Context, pathID uuid.UUID) ([]models.LearningPathLesson, error)
	
	// Create creates a new learning path lesson
	Create(ctx context.Context, pathLesson models.LearningPathLesson) (*models.LearningPathLesson, error)
	
	// Update updates a learning path lesson
	Update(ctx context.Context, pathLesson models.LearningPathLesson) (*models.LearningPathLesson, error)
	
	// Delete deletes a learning path lesson
	Delete(ctx context.Context, pathID, lessonID uuid.UUID) error
	
	// MarkLessonCompleted marks a lesson as completed in a learning path
	MarkLessonCompleted(ctx context.Context, pathID, lessonID uuid.UUID) error
	
	// GetCompletionStats gets completion statistics for a learning path
	GetCompletionStats(ctx context.Context, pathID uuid.UUID) (*models.LearningPathStats, error)
}
