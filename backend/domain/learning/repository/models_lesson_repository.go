package repository

import (
	"context"
	"languagelearning/models"

	"github.com/google/uuid"
)

// ModelsLessonRepository defines the interface for lesson data access using models
type ModelsLessonRepository interface {
	// FindByID retrieves a lesson by ID
	FindByID(ctx context.Context, id uuid.UUID) (*models.Lesson, error)
	
	// FindAll retrieves all lessons
	FindAll(ctx context.Context) ([]models.Lesson, error)
	
	// FindByLevel retrieves lessons by level
	FindByLevel(ctx context.Context, level models.LessonLevel) ([]models.Lesson, error)
	
	// FindByLevelWithLimit retrieves lessons by level with limit
	FindByLevelWithLimit(ctx context.Context, level models.LessonLevel, limit int) ([]models.Lesson, error)
	
	// FindByCategory retrieves lessons by category
	FindByCategory(ctx context.Context, category string) ([]models.Lesson, error)
	
	// FindByLevelAndCategory retrieves lessons by level and category
	FindByLevelAndCategory(ctx context.Context, level models.LessonLevel, category string) ([]models.Lesson, error)
	
	// FindWithPagination retrieves lessons with pagination
	FindWithPagination(ctx context.Context, limit, offset int) ([]models.Lesson, error)
	
	// Count counts total lessons
	Count(ctx context.Context) (int64, error)
	
	// Create creates a new lesson
	Create(ctx context.Context, lesson models.Lesson) (*models.Lesson, error)
	
	// Update updates a lesson
	Update(ctx context.Context, lesson models.Lesson) (*models.Lesson, error)
	
	// Delete deletes a lesson
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Search searches lessons by title or description
	Search(ctx context.Context, query string, limit int) ([]models.Lesson, error)
	
	// FindRecommendedForUser finds recommended lessons for a user based on their level
	FindRecommendedForUser(ctx context.Context, userID uuid.UUID, level models.LessonLevel, limit int) ([]models.Lesson, error)
}
