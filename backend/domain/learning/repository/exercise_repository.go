package repository

import (
	"context"
	"errors"
	exerciseentity "languagelearning/domain/exercise/entity"
	baseRepo "languagelearning/domain/repository"

	"github.com/google/uuid"
)

// ErrExerciseRelationNotFound is returned when an exercise relation is not found.
var ErrExerciseRelationNotFound = errors.New("exercise relation not found")

// ExerciseRepository 练习仓库接口
type ExerciseRepository interface {
	baseRepo.Repository[exerciseentity.Exercise, uuid.UUID]
	baseRepo.PageableRepository[exerciseentity.Exercise, uuid.UUID]
	baseRepo.SearchableRepository[exerciseentity.Exercise, uuid.UUID]

	// FindByType 查找指定类型的练习
	FindByType(ctx context.Context, exerciseType exerciseentity.ExerciseType) ([]*exerciseentity.Exercise, error)

	// FindByDifficulty 查找指定难度的练习
	FindByDifficulty(ctx context.Context, difficulty exerciseentity.ExerciseDifficulty) ([]*exerciseentity.Exercise, error)

	// FindByLanguage 查找指定语言的练习
	FindByLanguage(ctx context.Context, language string) ([]*exerciseentity.Exercise, error)

	// FindByTags 查找包含指定标签的练习
	FindByTags(ctx context.Context, tags []string) ([]*exerciseentity.Exercise, error)

	// FindByAuthor 查找指定作者的练习
	FindByAuthor(ctx context.Context, authorID uuid.UUID) ([]*exerciseentity.Exercise, error)

	// GetPublishedExercises 获取所有已发布的练习
	GetPublishedExercises(ctx context.Context) ([]*exerciseentity.Exercise, error)

	// GetRandomExercises 获取随机练习
	GetRandomExercises(ctx context.Context, count int, difficulty exerciseentity.ExerciseDifficulty, exerciseType exerciseentity.ExerciseType) ([]*exerciseentity.Exercise, error)
}

// ExerciseAttemptRepository 练习尝试仓库接口
type ExerciseAttemptRepository interface {
	baseRepo.Repository[exerciseentity.ExerciseAttempt, uuid.UUID]

	// FindByUserAndExercise 查找用户的特定练习尝试
	FindByUserAndExercise(ctx context.Context, userID, exerciseID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error)

	// FindByUser 查找用户所有练习尝试
	FindByUser(ctx context.Context, userID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error)

	// FindByUserAndLesson 查找用户在特定课程中的练习尝试
	FindByUserAndLesson(ctx context.Context, userID, lessonID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error)

	// FindByUserAndLearningPath 查找用户在特定学习路径中的练习尝试
	FindByUserAndLearningPath(ctx context.Context, userID, learningPathID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error)

	// GetCorrectAttempts 获取用户的正确尝试
	GetCorrectAttempts(ctx context.Context, userID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error)

	// GetIncorrectAttempts 获取用户的错误尝试
	GetIncorrectAttempts(ctx context.Context, userID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error)

	// GetUserStats 获取用户的练习统计
	GetUserStats(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error)
}

// ExerciseRelationRepository 练习关系仓库接口
type ExerciseRelationRepository interface {
	baseRepo.Repository[exerciseentity.ExerciseRelation, uuid.UUID]

	// FindBySourceExercise 查找源练习的关系
	FindBySourceExercise(ctx context.Context, exerciseID uuid.UUID) ([]*exerciseentity.ExerciseRelation, error)

	// FindByTargetExercise 查找目标练习的关系
	FindByTargetExercise(ctx context.Context, exerciseID uuid.UUID) ([]*exerciseentity.ExerciseRelation, error)

	// FindByRelationType 查找指定类型的关系
	FindByRelationType(ctx context.Context, relationType string) ([]*exerciseentity.ExerciseRelation, error)

	// FindBySourceAndTarget 查找两个练习之间的关系
	FindBySourceAndTarget(ctx context.Context, sourceID, targetID uuid.UUID) (*exerciseentity.ExerciseRelation, error)
}
