package impl

import (
	"context"
	"errors"
	coreentity "languagelearning/domain/core/entity"
	exerciseentity "languagelearning/domain/exercise/entity"
	"languagelearning/domain/learning/repository"
	baseRepo "languagelearning/domain/repository"
	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GormExerciseRepository 基于GORM的练习仓库实现
type GormExerciseRepository struct {
	db *gorm.DB
}

// NewGormExerciseRepository 创建一个新的GORM练习仓库
func NewGormExerciseRepository(db *gorm.DB) repository.ExerciseRepository {
	return &GormExerciseRepository{
		db: db,
	}
}

// FindByID 通过ID获取练习
func (r *GormExerciseRepository) FindByID(ctx context.Context, id uuid.UUID) (exerciseentity.Exercise, error) {
	var exerciseModel models.Exercise
	if err := r.db.First(&exerciseModel, "id = ?", id.String()).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exerciseentity.Exercise{}, errors.New("exercise not found")
		}
		return exerciseentity.Exercise{}, err
	}
	entity := r.mapToEntity(exerciseModel)
	if entity == nil {
		return exerciseentity.Exercise{}, errors.New("failed to map exercise model to entity")
	}
	return *entity, nil
}

// FindAll 获取所有练习
func (r *GormExerciseRepository) FindAll(ctx context.Context) ([]exerciseentity.Exercise, error) {
	var exerciseModels []models.Exercise
	if err := r.db.Find(&exerciseModels).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityValues(exerciseModels), nil
}

// Create 创建一个新练习
func (r *GormExerciseRepository) Create(ctx context.Context, exercise exerciseentity.Exercise) (exerciseentity.Exercise, error) {
	model := r.mapToModel(&exercise) // Pass address of exercise
	if err := r.db.Create(&model).Error; err != nil {
		return exerciseentity.Exercise{}, err
	}
	createdEntity := r.mapToEntity(model)
	if createdEntity == nil {
		return exerciseentity.Exercise{}, errors.New("failed to map created exercise model to entity")
	}
	return *createdEntity, nil
}

// Update 更新练习
func (r *GormExerciseRepository) Update(ctx context.Context, exercise exerciseentity.Exercise) (exerciseentity.Exercise, error) {
	model := r.mapToModel(&exercise) // Pass address of exercise
	if err := r.db.Save(&model).Error; err != nil {
		return exerciseentity.Exercise{}, err
	}
	updatedEntity := r.mapToEntity(model)
	if updatedEntity == nil {
		return exerciseentity.Exercise{}, errors.New("failed to map updated exercise model to entity")
	}
	return *updatedEntity, nil
}

// Delete 删除练习
func (r *GormExerciseRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.Delete(&models.Exercise{}, "id = ?", id.String()).Error
}

// Count 获取练习数量
func (r *GormExerciseRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.Model(&models.Exercise{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// FindPage 分页查询
func (r *GormExerciseRepository) FindPage(ctx context.Context, pageable baseRepo.Pageable) (baseRepo.Page[exerciseentity.Exercise], error) {
	var count int64
	var exerciseModels []models.Exercise

	// 获取总数
	if err := r.db.Model(&models.Exercise{}).Count(&count).Error; err != nil {
		return baseRepo.Page[exerciseentity.Exercise]{}, err
	}

	// 分页查询
	offset := pageable.GetPage() * pageable.GetSize()
	query := r.db.Offset(offset).Limit(pageable.GetSize())

	// 添加排序
	if len(pageable.GetSort()) > 0 {
		for _, sort := range pageable.GetSort() {
			query = query.Order(sort)
		}
	}

	if err := query.Find(&exerciseModels).Error; err != nil {
		return baseRepo.Page[exerciseentity.Exercise]{}, err
	}

	// 计算总页数
	totalPages := int(count) / pageable.GetSize()
	if int(count)%pageable.GetSize() > 0 {
		totalPages++
	}

	// 构建分页结果
	page := baseRepo.Page[exerciseentity.Exercise]{
		Content:       r.mapToEntityValues(exerciseModels),
		TotalElements: count,
		TotalPages:    totalPages,
		Page:          pageable.GetPage(),
		Size:          pageable.GetSize(),
		HasNext:       pageable.GetPage() < totalPages-1,
		HasPrevious:   pageable.GetPage() > 0,
	}

	return page, nil
}

// Search 搜索练习
func (r *GormExerciseRepository) Search(ctx context.Context, query string) ([]exerciseentity.Exercise, error) {
	var exerciseModels []models.Exercise
	if err := r.db.Where("question ILIKE ? OR instructions ILIKE ?", "%"+query+"%", "%"+query+"%").Find(&exerciseModels).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityValues(exerciseModels), nil
}

// SearchPage 分页搜索
func (r *GormExerciseRepository) SearchPage(ctx context.Context, query string, pageable baseRepo.Pageable) (baseRepo.Page[exerciseentity.Exercise], error) {
	var count int64
	var exerciseModels []models.Exercise

	// 获取搜索结果总数
	countQuery := r.db.Model(&models.Exercise{}).Where("question ILIKE ? OR instructions ILIKE ?", "%"+query+"%", "%"+query+"%")
	if err := countQuery.Count(&count).Error; err != nil {
		return baseRepo.Page[exerciseentity.Exercise]{}, err
	}

	// 分页查询
	offset := pageable.GetPage() * pageable.GetSize()
	searchQuery := r.db.Where("question ILIKE ? OR instructions ILIKE ?", "%"+query+"%", "%"+query+"%").Offset(offset).Limit(pageable.GetSize())

	// 添加排序
	if len(pageable.GetSort()) > 0 {
		for _, sort := range pageable.GetSort() {
			searchQuery = searchQuery.Order(sort)
		}
	}

	if err := searchQuery.Find(&exerciseModels).Error; err != nil {
		return baseRepo.Page[exerciseentity.Exercise]{}, err
	}

	// 计算总页数
	totalPages := int(count) / pageable.GetSize()
	if int(count)%pageable.GetSize() > 0 {
		totalPages++
	}

	// 构建分页结果
	page := baseRepo.Page[exerciseentity.Exercise]{
		Content:       r.mapToEntityValues(exerciseModels),
		TotalElements: count,
		TotalPages:    totalPages,
		Page:          pageable.GetPage(),
		Size:          pageable.GetSize(),
		HasNext:       pageable.GetPage() < totalPages-1,
		HasPrevious:   pageable.GetPage() > 0,
	}

	return page, nil
}

// FindByType 查找指定类型的练习
func (r *GormExerciseRepository) FindByType(ctx context.Context, exerciseType exerciseentity.ExerciseType) ([]*exerciseentity.Exercise, error) {
	var exerciseModels []models.Exercise
	if err := r.db.Where("type = ?", string(exerciseType)).Find(&exerciseModels).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(exerciseModels), nil
}

// FindByDifficulty 查找指定难度的练习
func (r *GormExerciseRepository) FindByDifficulty(ctx context.Context, difficulty exerciseentity.ExerciseDifficulty) ([]*exerciseentity.Exercise, error) {
	var exerciseModels []models.Exercise
	if err := r.db.Where("difficulty = ?", difficulty.Level()).Find(&exerciseModels).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(exerciseModels), nil
}

// FindByLanguage 查找指定语言的练习
func (r *GormExerciseRepository) FindByLanguage(ctx context.Context, language string) ([]*exerciseentity.Exercise, error) {
	var exerciseModels []models.Exercise
	// Assuming 'language_code' is the column name in the database for the model
	if err := r.db.Where("language_code = ?", language).Find(&exerciseModels).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(exerciseModels), nil
}

// FindByTags 查找包含指定标签的练习
func (r *GormExerciseRepository) FindByTags(ctx context.Context, tags []string) ([]*exerciseentity.Exercise, error) {
	var exercises []models.Exercise
	if err := r.db.Where("tags @> ?", tags).Find(&exercises).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(exercises), nil
}

// FindByAuthor 查找指定作者的练习
func (r *GormExerciseRepository) FindByAuthor(ctx context.Context, authorID uuid.UUID) ([]*exerciseentity.Exercise, error) {
	var exercises []models.Exercise
	if err := r.db.Where("author_id = ?", authorID.String()).Find(&exercises).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(exercises), nil
}

// GetPublishedExercises 获取所有已发布的练习
func (r *GormExerciseRepository) GetPublishedExercises(ctx context.Context) ([]*exerciseentity.Exercise, error) {
	var exercises []models.Exercise
	if err := r.db.Where("is_published = ?", true).Find(&exercises).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(exercises), nil
}

// GetRandomExercises 获取随机练习
func (r *GormExerciseRepository) GetRandomExercises(ctx context.Context, count int, difficulty exerciseentity.ExerciseDifficulty, exerciseType exerciseentity.ExerciseType) ([]*exerciseentity.Exercise, error) {
	var exerciseModels []models.Exercise
	query := r.db.Limit(count).Order("RANDOM()")

	// Check if difficulty is a non-zero struct before applying filter
	if difficulty.Level() != "" {
		query = query.Where("difficulty = ?", difficulty.Level())
	}

	if exerciseType != "" {
		query = query.Where("type = ?", string(exerciseType))
	}

	if err := query.Find(&exerciseModels).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(exerciseModels), nil
}

// 辅助方法: 将数据库模型映射到领域实体
func (r *GormExerciseRepository) mapToEntity(model models.Exercise) *exerciseentity.Exercise {
	// 创建默认的难度对象 - 使用core entity
	difficultyObj, _ := coreentity.NewExerciseDifficulty("medium")

	// 创建默认的时间限制 - 使用core entity
	durationObj, _ := coreentity.NewDuration(30)

	// 创建默认的语言对象 - 使用core entity
	languageObj, _ := coreentity.NewLanguage("en", "English", "English")

	// 转换练习类型
	exerciseType := exerciseentity.ConvertFromString(string(model.Type))

	return &exerciseentity.Exercise{
		ID:            model.ID,
		Type:          exerciseType,
		Title:         model.Title,
		Description:   model.Description,
		Content:       model.Content,
		Difficulty:    difficultyObj,
		Question:      model.Question,
		Instructions:  model.Instruction,
		Options:       model.Options,
		CorrectAnswer: model.CorrectAnswer,
		Explanation:   model.Explanation,
		Points:        model.Points,
		TimeLimit:     durationObj,
		MediaURL:      model.AudioURL,
		MediaType:     "audio",
		Tags:          model.Tags,
		Language:      languageObj,
		AuthorID:      model.AuthorID,
		IsPublished:   model.IsPublished,
		CreatedAt:     model.CreatedAt,
		UpdatedAt:     model.UpdatedAt,
	}
}

// 辅助方法: 将多个数据库模型映射到领域实体指针
func (r *GormExerciseRepository) mapToEntityPointers(exerciseModels []models.Exercise) []*exerciseentity.Exercise {
	entities := make([]*exerciseentity.Exercise, len(exerciseModels))
	for i, model := range exerciseModels {
		entities[i] = r.mapToEntity(model)
	}
	return entities
}

// 辅助方法: 将多个数据库模型映射到领域实体值
func (r *GormExerciseRepository) mapToEntityValues(exerciseModels []models.Exercise) []exerciseentity.Exercise {
	values := make([]exerciseentity.Exercise, 0, len(exerciseModels))
	for _, model := range exerciseModels {
		entityPtr := r.mapToEntity(model)
		if entityPtr != nil { // Ensure not nil before dereferencing
			values = append(values, *entityPtr)
		}
	}
	return values
}

// 辅助方法: 将领域实体映射到数据库模型
func (r *GormExerciseRepository) mapToModel(exercise *exerciseentity.Exercise) models.Exercise {
	// 转换练习类型到数据库类型
	var modelType models.ExerciseType
	switch exercise.Type {
	case exerciseentity.VocabularyExercise:
		modelType = models.ExVocabulary
	case exerciseentity.GrammarExercise:
		modelType = models.ExGrammar
	case exerciseentity.ListeningExercise:
		modelType = models.ExListening
	case exerciseentity.SpeakingExercise:
		modelType = models.ExSpeaking
	case exerciseentity.ReadingExercise:
		modelType = models.ExReading
	case exerciseentity.WritingExercise:
		modelType = models.ExWriting
	case exerciseentity.MultipleChoice:
		modelType = models.ExMultipleChoice
	case exerciseentity.FillInBlank:
		modelType = models.ExFillInBlank
	case exerciseentity.Matching:
		modelType = models.ExMatching
	case exerciseentity.TrueFalse:
		modelType = models.ExTrueFalse
	case exerciseentity.OpenEnded:
		modelType = models.ExOpenEnded
	default:
		modelType = models.ExMultipleChoice // 默认类型
	}

	return models.Exercise{
		ID:            exercise.ID,
		Type:          modelType,
		Title:         exercise.Title,
		Description:   exercise.Description,
		Content:       exercise.Content,
		Question:      exercise.Question,
		Instruction:   exercise.Instructions,
		Options:       exercise.Options,
		CorrectAnswer: exercise.CorrectAnswer,
		Explanation:   exercise.Explanation,
		Points:        exercise.Points,
		AudioURL:      exercise.MediaURL,
		ImageURL:      "", // 可以从MediaURL推断
		Tags:          exercise.Tags,
		AuthorID:      exercise.AuthorID,
		IsPublished:   exercise.IsPublished,
		CreatedAt:     exercise.CreatedAt,
		UpdatedAt:     exercise.UpdatedAt,
	}
}
