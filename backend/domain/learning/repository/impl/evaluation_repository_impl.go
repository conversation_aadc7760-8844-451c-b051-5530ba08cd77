package impl

import (
	"context"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// evaluationRepository implements the EvaluationRepository interface
type evaluationRepository struct {
	db *gorm.DB
}

// NewEvaluationRepository creates a new evaluation repository
func NewEvaluationRepository(db *gorm.DB) repository.EvaluationRepository {
	return &evaluationRepository{db: db}
}

// FindByID retrieves an evaluation by ID
func (r *evaluationRepository) FindByID(ctx context.Context, id uuid.UUID) (*models.Evaluation, error) {
	var evaluation models.Evaluation
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&evaluation).Error
	if err != nil {
		return nil, err
	}
	return &evaluation, nil
}

// FindByUserID retrieves evaluations for a user
func (r *evaluationRepository) FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.Evaluation, error) {
	var evaluations []models.Evaluation
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&evaluations).Error
	return evaluations, err
}

// FindByUserIDAndType retrieves evaluations by user and type
func (r *evaluationRepository) FindByUserIDAndType(ctx context.Context, userID uuid.UUID, evalType models.EvaluationType) ([]models.Evaluation, error) {
	var evaluations []models.Evaluation
	err := r.db.WithContext(ctx).Where("user_id = ? AND type = ?", userID, evalType).Order("created_at DESC").Find(&evaluations).Error
	return evaluations, err
}

// Create creates a new evaluation
func (r *evaluationRepository) Create(ctx context.Context, evaluation models.Evaluation) (*models.Evaluation, error) {
	err := r.db.WithContext(ctx).Create(&evaluation).Error
	if err != nil {
		return nil, err
	}
	return &evaluation, nil
}

// Update updates an evaluation
func (r *evaluationRepository) Update(ctx context.Context, evaluation models.Evaluation) (*models.Evaluation, error) {
	err := r.db.WithContext(ctx).Save(&evaluation).Error
	if err != nil {
		return nil, err
	}
	return &evaluation, nil
}

// Delete deletes an evaluation
func (r *evaluationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&models.Evaluation{}).Error
}

// FindActiveByUserID retrieves active evaluations for a user
func (r *evaluationRepository) FindActiveByUserID(ctx context.Context, userID uuid.UUID) ([]models.Evaluation, error) {
	var evaluations []models.Evaluation
	err := r.db.WithContext(ctx).Where("user_id = ? AND is_completed = ?", userID, false).Order("created_at DESC").Find(&evaluations).Error
	return evaluations, err
}

// FindCompletedByUserID retrieves completed evaluations for a user
func (r *evaluationRepository) FindCompletedByUserID(ctx context.Context, userID uuid.UUID) ([]models.Evaluation, error) {
	var evaluations []models.Evaluation
	err := r.db.WithContext(ctx).Where("user_id = ? AND is_completed = ?", userID, true).Order("created_at DESC").Find(&evaluations).Error
	return evaluations, err
}

// FindByIDAndUserIDWithDetails retrieves an evaluation by ID and user ID with sections and questions
func (r *evaluationRepository) FindByIDAndUserIDWithDetails(ctx context.Context, id, userID uuid.UUID) (*models.Evaluation, error) {
	var evaluation models.Evaluation
	err := r.db.WithContext(ctx).Where("id = ? AND user_id = ?", id, userID).
		Preload("Sections.Questions").
		First(&evaluation).Error
	if err != nil {
		return nil, err
	}
	return &evaluation, nil
}

// FindByIDWithDetails retrieves an evaluation by ID with sections and questions
func (r *evaluationRepository) FindByIDWithDetails(ctx context.Context, id uuid.UUID) (*models.Evaluation, error) {
	var evaluation models.Evaluation
	err := r.db.WithContext(ctx).Where("id = ?", id).
		Preload("Sections.Questions").
		First(&evaluation).Error
	if err != nil {
		return nil, err
	}
	return &evaluation, nil
}

// evaluationResultRepository implements the EvaluationResultRepository interface
type evaluationResultRepository struct {
	db *gorm.DB
}

// NewEvaluationResultRepository creates a new evaluation result repository
func NewEvaluationResultRepository(db *gorm.DB) repository.EvaluationResultRepository {
	return &evaluationResultRepository{db: db}
}

// FindByEvaluationID retrieves evaluation results by evaluation ID
func (r *evaluationResultRepository) FindByEvaluationID(ctx context.Context, evaluationID uuid.UUID) (*models.EvaluationResult, error) {
	var result models.EvaluationResult
	err := r.db.WithContext(ctx).Where("evaluation_id = ?", evaluationID).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// FindByUserID retrieves evaluation results for a user
func (r *evaluationResultRepository) FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.EvaluationResult, error) {
	var results []models.EvaluationResult
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&results).Error
	return results, err
}

// Create creates a new evaluation result
func (r *evaluationResultRepository) Create(ctx context.Context, result models.EvaluationResult) (*models.EvaluationResult, error) {
	err := r.db.WithContext(ctx).Create(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// Update updates an evaluation result
func (r *evaluationResultRepository) Update(ctx context.Context, result models.EvaluationResult) (*models.EvaluationResult, error) {
	err := r.db.WithContext(ctx).Save(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// Delete deletes an evaluation result
func (r *evaluationResultRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&models.EvaluationResult{}).Error
}

// FindByEvaluationIDAndUserID retrieves evaluation result by evaluation and user
func (r *evaluationResultRepository) FindByEvaluationIDAndUserID(ctx context.Context, evaluationID, userID uuid.UUID) (*models.EvaluationResult, error) {
	var result models.EvaluationResult
	err := r.db.WithContext(ctx).Where("evaluation_id = ? AND user_id = ?", evaluationID, userID).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// FindByEvaluationIDAndUserIDWithDetails retrieves evaluation result by evaluation and user with section scores
func (r *evaluationResultRepository) FindByEvaluationIDAndUserIDWithDetails(ctx context.Context, evaluationID, userID uuid.UUID) (*models.EvaluationResult, error) {
	var result models.EvaluationResult
	err := r.db.WithContext(ctx).Where("evaluation_id = ? AND user_id = ?", evaluationID, userID).
		Preload("SectionScores").
		First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// FindByUserIDOrderedByDate retrieves evaluation results for a user ordered by completion date
func (r *evaluationResultRepository) FindByUserIDOrderedByDate(ctx context.Context, userID uuid.UUID) ([]models.EvaluationResult, error) {
	var results []models.EvaluationResult
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("completed_at DESC").Find(&results).Error
	return results, err
}

// assessmentProgressRepository implements the AssessmentProgressRepository interface
type assessmentProgressRepository struct {
	db *gorm.DB
}

// NewAssessmentProgressRepository creates a new assessment progress repository
func NewAssessmentProgressRepository(db *gorm.DB) repository.AssessmentProgressRepository {
	return &assessmentProgressRepository{db: db}
}

// FindByUserEvaluationAndSession retrieves assessment progress by user, evaluation and session
func (r *assessmentProgressRepository) FindByUserEvaluationAndSession(ctx context.Context, userID, evaluationID uuid.UUID, sessionToken string) (*models.AssessmentProgress, error) {
	var progress models.AssessmentProgress
	err := r.db.WithContext(ctx).Where("user_id = ? AND evaluation_id = ? AND session_token = ?", userID, evaluationID, sessionToken).First(&progress).Error
	if err != nil {
		return nil, err
	}
	return &progress, nil
}

// Create creates a new assessment progress
func (r *assessmentProgressRepository) Create(ctx context.Context, progress models.AssessmentProgress) (*models.AssessmentProgress, error) {
	err := r.db.WithContext(ctx).Create(&progress).Error
	if err != nil {
		return nil, err
	}
	return &progress, nil
}

// Update updates an assessment progress
func (r *assessmentProgressRepository) Update(ctx context.Context, progress models.AssessmentProgress) (*models.AssessmentProgress, error) {
	err := r.db.WithContext(ctx).Save(&progress).Error
	if err != nil {
		return nil, err
	}
	return &progress, nil
}

// DeleteByUserAndEvaluation deletes assessment progress by user and evaluation
func (r *assessmentProgressRepository) DeleteByUserAndEvaluation(ctx context.Context, userID, evaluationID uuid.UUID) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND evaluation_id = ?", userID, evaluationID).Delete(&models.AssessmentProgress{}).Error
}

// evalQuestionRepository implements the EvalQuestionRepository interface
type evalQuestionRepository struct {
	db *gorm.DB
}

// NewEvalQuestionRepository creates a new evaluation question repository
func NewEvalQuestionRepository(db *gorm.DB) repository.EvalQuestionRepository {
	return &evalQuestionRepository{db: db}
}

// FindByID retrieves an evaluation question by ID
func (r *evalQuestionRepository) FindByID(ctx context.Context, id uint) (*models.EvalQuestion, error) {
	var question models.EvalQuestion
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&question).Error
	if err != nil {
		return nil, err
	}
	return &question, nil
}

// Update updates an evaluation question
func (r *evalQuestionRepository) Update(ctx context.Context, question models.EvalQuestion) (*models.EvalQuestion, error) {
	err := r.db.WithContext(ctx).Save(&question).Error
	if err != nil {
		return nil, err
	}
	return &question, nil
}

// evalSectionRepository implements the EvalSectionRepository interface
type evalSectionRepository struct {
	db *gorm.DB
}

// NewEvalSectionRepository creates a new evaluation section repository
func NewEvalSectionRepository(db *gorm.DB) repository.EvalSectionRepository {
	return &evalSectionRepository{db: db}
}

// Update updates an evaluation section
func (r *evalSectionRepository) Update(ctx context.Context, section models.EvalSection) (*models.EvalSection, error) {
	err := r.db.WithContext(ctx).Save(&section).Error
	if err != nil {
		return nil, err
	}
	return &section, nil
}
