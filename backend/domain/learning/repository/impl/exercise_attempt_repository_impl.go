package impl

import (
	"context"
	"errors"
	exerciseentity "languagelearning/domain/exercise/entity"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GormExerciseAttemptRepository 基于GORM的练习尝试仓库实现
type GormExerciseAttemptRepository struct {
	db *gorm.DB
}

// NewGormExerciseAttemptRepository 创建一个新的GORM练习尝试仓库
func NewGormExerciseAttemptRepository(db *gorm.DB) repository.ExerciseAttemptRepository {
	return &GormExerciseAttemptRepository{
		db: db,
	}
}

// FindByID 通过ID获取练习尝试
func (r *GormExerciseAttemptRepository) FindByID(ctx context.Context, id uuid.UUID) (exerciseentity.ExerciseAttempt, error) {
	var attempt models.ExerciseAttempt
	if err := r.db.First(&attempt, "id = ?", id.String()).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exerciseentity.ExerciseAttempt{}, errors.New("exercise attempt not found")
		}
		return exerciseentity.ExerciseAttempt{}, err
	}
	return r.mapToEntity(attempt), nil
}

// FindAll 获取所有练习尝试
func (r *GormExerciseAttemptRepository) FindAll(ctx context.Context) ([]exerciseentity.ExerciseAttempt, error) {
	var attempts []models.ExerciseAttempt
	if err := r.db.Find(&attempts).Error; err != nil {
		return nil, err
	}
	return r.mapToEntities(attempts), nil
}

// Create 创建一个新练习尝试
func (r *GormExerciseAttemptRepository) Create(ctx context.Context, attempt exerciseentity.ExerciseAttempt) (exerciseentity.ExerciseAttempt, error) {
	model := r.mapToModel(attempt)
	if err := r.db.Create(&model).Error; err != nil {
		return exerciseentity.ExerciseAttempt{}, err
	}
	return r.mapToEntity(model), nil
}

// Update 更新练习尝试
func (r *GormExerciseAttemptRepository) Update(ctx context.Context, attempt exerciseentity.ExerciseAttempt) (exerciseentity.ExerciseAttempt, error) {
	model := r.mapToModel(attempt)
	if err := r.db.Save(&model).Error; err != nil {
		return exerciseentity.ExerciseAttempt{}, err
	}
	return r.mapToEntity(model), nil
}

// Delete 删除练习尝试
func (r *GormExerciseAttemptRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.Delete(&models.ExerciseAttempt{}, "id = ?", id.String()).Error
}

// Count 获取练习尝试数量
func (r *GormExerciseAttemptRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.Model(&models.ExerciseAttempt{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// FindByUserAndExercise 查找用户的特定练习尝试
func (r *GormExerciseAttemptRepository) FindByUserAndExercise(ctx context.Context, userID, exerciseID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error) {
	var attempts []models.ExerciseAttempt
	if err := r.db.Where("user_id = ? AND exercise_id = ?", userID.String(), exerciseID.String()).Find(&attempts).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(attempts), nil
}

// FindByUser 查找用户的所有练习尝试
func (r *GormExerciseAttemptRepository) FindByUser(ctx context.Context, userID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error) {
	var attempts []models.ExerciseAttempt
	if err := r.db.Where("user_id = ?", userID.String()).Find(&attempts).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(attempts), nil
}

// FindByUserAndLesson 查找用户在特定课程中的练习尝试
func (r *GormExerciseAttemptRepository) FindByUserAndLesson(ctx context.Context, userID, lessonID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error) {
	var attempts []models.ExerciseAttempt
	if err := r.db.Where("user_id = ? AND lesson_id = ?", userID.String(), lessonID.String()).Find(&attempts).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(attempts), nil
}

// FindByUserAndLearningPath 查找用户在特定学习路径中的练习尝试
func (r *GormExerciseAttemptRepository) FindByUserAndLearningPath(ctx context.Context, userID, learningPathID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error) {
	var attempts []models.ExerciseAttempt
	if err := r.db.Where("user_id = ? AND learning_path_id = ?", userID.String(), learningPathID.String()).Find(&attempts).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(attempts), nil
}

// GetCorrectAttempts 获取用户的正确尝试
func (r *GormExerciseAttemptRepository) GetCorrectAttempts(ctx context.Context, userID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error) {
	var attempts []models.ExerciseAttempt
	if err := r.db.Where("user_id = ? AND is_correct = ?", userID.String(), true).Find(&attempts).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(attempts), nil
}

// GetIncorrectAttempts 获取用户的错误尝试
func (r *GormExerciseAttemptRepository) GetIncorrectAttempts(ctx context.Context, userID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error) {
	var attempts []models.ExerciseAttempt
	if err := r.db.Where("user_id = ? AND is_correct = ?", userID.String(), false).Find(&attempts).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(attempts), nil
}

// GetUserStats 获取用户的练习统计
func (r *GormExerciseAttemptRepository) GetUserStats(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error) {
	var correctCount, incorrectCount int64
	stats := make(map[string]interface{})

	// 获取正确尝试次数
	if err := r.db.Model(&models.ExerciseAttempt{}).Where("user_id = ? AND is_correct = ?", userID.String(), true).Count(&correctCount).Error; err != nil {
		return nil, err
	}

	// 获取错误尝试次数
	if err := r.db.Model(&models.ExerciseAttempt{}).Where("user_id = ? AND is_correct = ?", userID.String(), false).Count(&incorrectCount).Error; err != nil {
		return nil, err
	}

	// 计算总分
	var totalScore int64
	if err := r.db.Model(&models.ExerciseAttempt{}).Where("user_id = ?", userID.String()).Select("SUM(score)").Row().Scan(&totalScore); err != nil {
		return nil, err
	}

	// 计算平均分
	var avgScore float64
	if correctCount+incorrectCount > 0 {
		avgScore = float64(totalScore) / float64(correctCount+incorrectCount)
	}

	stats["correctCount"] = correctCount
	stats["incorrectCount"] = incorrectCount
	stats["totalAttempts"] = correctCount + incorrectCount
	stats["totalScore"] = totalScore
	stats["averageScore"] = avgScore
	stats["successRate"] = 0.0
	if correctCount+incorrectCount > 0 {
		stats["successRate"] = float64(correctCount) / float64(correctCount+incorrectCount) * 100
	}

	return stats, nil
}

// 映射方法 - 模型到实体
func (r *GormExerciseAttemptRepository) mapToEntity(model models.ExerciseAttempt) exerciseentity.ExerciseAttempt {
	var lessonID *uuid.UUID
	if model.LessonID.Valid {
		parsed, _ := uuid.Parse(model.LessonID.UUID.String())
		lessonID = &parsed
	}

	var learningPathID *uuid.UUID
	if model.LearningPathID.Valid {
		parsed, _ := uuid.Parse(model.LearningPathID.UUID.String())
		learningPathID = &parsed
	}

	// Create value objects
	score, _ := exerciseentity.NewScore(model.Score, 10) // Assuming max score is 10
	duration, _ := exerciseentity.NewDuration(model.TimeSpent)

	return exerciseentity.ExerciseAttempt{
		ID:             model.ID,
		UserID:         model.UserID,
		ExerciseID:     model.ExerciseID,
		UserAnswer:     model.UserAnswer,
		IsCorrect:      model.IsCorrect,
		Score:          score,
		Duration:       duration,
		AttemptedAt:    model.AttemptedAt,
		Feedback:       model.Feedback,
		LessonID:       lessonID,
		LearningPathID: learningPathID,
		CreatedAt:      model.CreatedAt,
		UpdatedAt:      model.UpdatedAt,
	}
}

// 映射方法 - 实体到模型
func (r *GormExerciseAttemptRepository) mapToModel(entity exerciseentity.ExerciseAttempt) models.ExerciseAttempt {
	model := models.ExerciseAttempt{
		ID:          entity.ID,
		UserID:      entity.UserID,
		ExerciseID:  entity.ExerciseID,
		UserAnswer:  entity.UserAnswer,
		IsCorrect:   entity.IsCorrect,
		AttemptedAt: entity.AttemptedAt,
		Feedback:    entity.Feedback,
		CreatedAt:   entity.CreatedAt,
		UpdatedAt:   entity.UpdatedAt,
	}

	// Convert value objects to primitive types
	if entity.Score != nil {
		model.Score = entity.Score.Value()
	}
	if entity.Duration != nil {
		model.TimeSpent = entity.Duration.Minutes()
	}

	if entity.LessonID != nil {
		model.LessonID = uuid.NullUUID{UUID: *entity.LessonID, Valid: true}
	}

	if entity.LearningPathID != nil {
		model.LearningPathID = uuid.NullUUID{UUID: *entity.LearningPathID, Valid: true}
	}

	return model
}

// 映射方法 - 模型数组到实体数组
func (r *GormExerciseAttemptRepository) mapToEntities(models []models.ExerciseAttempt) []exerciseentity.ExerciseAttempt {
	entities := make([]exerciseentity.ExerciseAttempt, len(models))
	for i, model := range models {
		entities[i] = r.mapToEntity(model)
	}
	return entities
}

// 映射方法 - 模型数组到实体指针数组
func (r *GormExerciseAttemptRepository) mapToPtrEntities(models []models.ExerciseAttempt) []*exerciseentity.ExerciseAttempt {
	entities := make([]*exerciseentity.ExerciseAttempt, len(models))
	for i, model := range models {
		entity := r.mapToEntity(model)
		entities[i] = &entity
	}
	return entities
}
