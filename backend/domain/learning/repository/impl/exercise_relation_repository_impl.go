package impl

import (
	"context"
	"errors"
	"time"

	exerciseentity "languagelearning/domain/exercise/entity"
	"languagelearning/domain/learning/repository"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GormExerciseRelationRepository GORM 练习关系存储库
type GormExerciseRelationRepository struct {
	db *gorm.DB
}

// NewGormExerciseRelationRepository 创建一个新的GORM练习关系仓库
func NewGormExerciseRelationRepository(db *gorm.DB) repository.ExerciseRelationRepository {
	return &GormExerciseRelationRepository{db: db}
}

// model 定义了 GORM 模型
type exerciseRelationModel struct {
	ID               uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	SourceExerciseID uuid.UUID `gorm:"type:uuid;not null"`
	TargetExerciseID uuid.UUID `gorm:"type:uuid;not null"`
	RelationType     string    `gorm:"size:50;not null"`
	Strength         float64   `gorm:"type:decimal(3,2);default:0.0"`
	Metadata         string    `gorm:"type:jsonb"` // Changed from text to jsonb for better querying if needed
	CreatedAt        time.Time `gorm:"not null;default:current_timestamp"`
	UpdatedAt        time.Time `gorm:"not null;default:current_timestamp"`
}

func (exerciseRelationModel) TableName() string {
	return "exercise_relations"
}

// Create 创建一个新练习关系
func (r *GormExerciseRelationRepository) Create(ctx context.Context, relation exerciseentity.ExerciseRelation) (exerciseentity.ExerciseRelation, error) {
	model := r.mapToModel(relation)
	if err := r.db.WithContext(ctx).Create(&model).Error; err != nil {
		return exerciseentity.ExerciseRelation{}, err
	}
	return r.mapToEntity(model), nil
}

// FindByID 通过 ID 查找练习关系
func (r *GormExerciseRelationRepository) FindByID(ctx context.Context, id uuid.UUID) (exerciseentity.ExerciseRelation, error) {
	var model exerciseRelationModel
	if err := r.db.WithContext(ctx).First(&model, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exerciseentity.ExerciseRelation{}, repository.ErrExerciseRelationNotFound // Correctly using the imported error
		}
		return exerciseentity.ExerciseRelation{}, err
	}
	return r.mapToEntity(model), nil
}

// Update 更新练习关系
func (r *GormExerciseRelationRepository) Update(ctx context.Context, relation exerciseentity.ExerciseRelation) (exerciseentity.ExerciseRelation, error) {
	model := r.mapToModel(relation)
	if model.ID == uuid.Nil {
		return exerciseentity.ExerciseRelation{}, errors.New("cannot update exercise relation without ID")
	}
	if err := r.db.WithContext(ctx).Save(&model).Error; err != nil {
		return exerciseentity.ExerciseRelation{}, err
	}
	return r.mapToEntity(model), nil
}

// Delete 删除练习关系
func (r *GormExerciseRelationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&exerciseRelationModel{}, "id = ?", id).Error
}

// FindBySourceExercise 查找源练习的关系 (implements interface)
func (r *GormExerciseRelationRepository) FindBySourceExercise(ctx context.Context, exerciseID uuid.UUID) ([]*exerciseentity.ExerciseRelation, error) {
	var models []exerciseRelationModel
	if err := r.db.WithContext(ctx).Where("source_exercise_id = ?", exerciseID).Find(&models).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(models), nil
}

// FindByTargetExercise 查找目标练习的关系 (implements interface)
func (r *GormExerciseRelationRepository) FindByTargetExercise(ctx context.Context, exerciseID uuid.UUID) ([]*exerciseentity.ExerciseRelation, error) {
	var models []exerciseRelationModel
	if err := r.db.WithContext(ctx).Where("target_exercise_id = ?", exerciseID).Find(&models).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(models), nil
}

// FindByRelationType 查找指定类型的关系 (implements interface)
func (r *GormExerciseRelationRepository) FindByRelationType(ctx context.Context, relationType string) ([]*exerciseentity.ExerciseRelation, error) {
	var models []exerciseRelationModel
	if err := r.db.WithContext(ctx).Where("relation_type = ?", relationType).Find(&models).Error; err != nil {
		return nil, err
	}
	return r.mapToPtrEntities(models), nil
}

// FindBySourceAndTarget 查找两个练习之间的关系
func (r *GormExerciseRelationRepository) FindBySourceAndTarget(ctx context.Context, sourceID, targetID uuid.UUID) (*exerciseentity.ExerciseRelation, error) {
	var model exerciseRelationModel
	if err := r.db.WithContext(ctx).Where("source_exercise_id = ? AND target_exercise_id = ?", sourceID, targetID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrExerciseRelationNotFound // Correctly using the imported error
		}
		return nil, err
	}
	entity := r.mapToEntity(model)
	return &entity, nil
}

// FindAll 获取所有练习关系 (implements baseRepo.Repository)
func (r *GormExerciseRelationRepository) FindAll(ctx context.Context) ([]exerciseentity.ExerciseRelation, error) {
	var models []exerciseRelationModel
	if err := r.db.WithContext(ctx).Find(&models).Error; err != nil {
		return nil, err
	}
	return r.mapToEntities(models), nil
}

// Count returns the total number of exercise relations.
func (r *GormExerciseRelationRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&exerciseRelationModel{}).Count(&count).Error
	return count, err
}

// mapToModel 将领域实体映射到 GORM 模型
func (r *GormExerciseRelationRepository) mapToModel(entity exerciseentity.ExerciseRelation) exerciseRelationModel {
	return exerciseRelationModel{
		ID:               entity.ID,
		SourceExerciseID: entity.SourceExerciseID,
		TargetExerciseID: entity.TargetExerciseID,
		RelationType:     entity.RelationType,
		Strength:         entity.Strength,
		Metadata:         entity.Metadata,
		CreatedAt:        entity.CreatedAt,
		UpdatedAt:        entity.UpdatedAt,
	}
}

// mapToEntity 将 GORM 模型映射到领域实体
func (r *GormExerciseRelationRepository) mapToEntity(model exerciseRelationModel) exerciseentity.ExerciseRelation {
	return exerciseentity.ExerciseRelation{
		ID:               model.ID,
		SourceExerciseID: model.SourceExerciseID,
		TargetExerciseID: model.TargetExerciseID,
		RelationType:     model.RelationType,
		Strength:         model.Strength,
		Metadata:         model.Metadata,
		CreatedAt:        model.CreatedAt,
		UpdatedAt:        model.UpdatedAt,
	}
}

// mapToEntities 将 GORM 模型数组映射到领域实体数组
func (r *GormExerciseRelationRepository) mapToEntities(models []exerciseRelationModel) []exerciseentity.ExerciseRelation {
	entities := make([]exerciseentity.ExerciseRelation, len(models))
	for i, model := range models {
		entities[i] = r.mapToEntity(model)
	}
	return entities
}

// mapToPtrEntities 将 GORM 模型数组映射到领域实体指针数组
func (r *GormExerciseRelationRepository) mapToPtrEntities(models []exerciseRelationModel) []*exerciseentity.ExerciseRelation {
	entities := make([]*exerciseentity.ExerciseRelation, len(models))
	for i, model := range models {
		entity := r.mapToEntity(model)
		entities[i] = &entity
	}
	return entities
}
