package impl

import (
	"context"
	"errors"
	"languagelearning/domain/learning/entity"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"
	"log/slog"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GormLessonProgressRepository 基于GORM的课程进度仓库实现
type GormLessonProgressRepository struct {
	db *gorm.DB
}

// NewGormLessonProgressRepository 创建一个新的GORM课程进度仓库
func NewGormLessonProgressRepository(db *gorm.DB) repository.LessonProgressRepository {
	return &GormLessonProgressRepository{
		db: db,
	}
}

// FindByID 通过ID获取课程进度
func (r *GormLessonProgressRepository) FindByID(ctx context.Context, id uuid.UUID) (entity.LessonProgress, error) {
	var progress models.LessonProgress
	if err := r.db.First(&progress, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return entity.LessonProgress{}, errors.New("lesson progress not found")
		}
		return entity.LessonProgress{}, err
	}
	return r.mapToEntity(progress), nil
}

// FindAll 获取所有课程进度
func (r *GormLessonProgressRepository) FindAll(ctx context.Context) ([]entity.LessonProgress, error) {
	var progresses []models.LessonProgress
	if err := r.db.Find(&progresses).Error; err != nil {
		return nil, err
	}
	return r.mapToEntities(progresses), nil
}

// Create 创建一个新课程进度
func (r *GormLessonProgressRepository) Create(ctx context.Context, progress entity.LessonProgress) (entity.LessonProgress, error) {
	model := r.mapToModel(progress)
	if err := r.db.Create(&model).Error; err != nil {
		return entity.LessonProgress{}, err
	}
	return r.mapToEntity(model), nil
}

// Update 更新课程进度
func (r *GormLessonProgressRepository) Update(ctx context.Context, progress entity.LessonProgress) (entity.LessonProgress, error) {
	model := r.mapToModel(progress)
	if err := r.db.Save(&model).Error; err != nil {
		return entity.LessonProgress{}, err
	}
	return r.mapToEntity(model), nil
}

// Delete 删除课程进度
func (r *GormLessonProgressRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.Delete(&models.LessonProgress{}, "id = ?", id).Error
}

// Count 获取课程进度数量
func (r *GormLessonProgressRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.Model(&models.LessonProgress{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetUserProgress 获取用户在课程中的进度 (Renamed from FindByUserAndLesson)
func (r *GormLessonProgressRepository) GetUserProgress(ctx context.Context, userID, lessonID uuid.UUID) (*entity.LessonProgress, error) {
	var progress models.LessonProgress
	if err := r.db.Where("user_id = ? AND lesson_id = ?", userID, lessonID).First(&progress).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	result := r.mapToEntity(progress)
	return &result, nil
}

// FindByUser 查找用户的所有课程进度
func (r *GormLessonProgressRepository) FindByUser(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error) {
	var progresses []models.LessonProgress
	if err := r.db.Where("user_id = ?", userID).Find(&progresses).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(progresses), nil
}

// FindByLesson 查找课程的所有课程进度
func (r *GormLessonProgressRepository) FindByLesson(ctx context.Context, lessonID uuid.UUID) ([]*entity.LessonProgress, error) {
	var progresses []models.LessonProgress
	if err := r.db.Where("lesson_id = ?", lessonID).Find(&progresses).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(progresses), nil
}

// GetCompletedLessons 获取用户已完成的课程
func (r *GormLessonProgressRepository) GetCompletedLessons(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error) {
	var progresses []models.LessonProgress
	if err := r.db.Where("user_id = ? AND completed = ?", userID, true).Find(&progresses).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(progresses), nil
}

// GetInProgressLessons 获取用户正在进行的课程
func (r *GormLessonProgressRepository) GetInProgressLessons(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error) {
	var progresses []models.LessonProgress
	if err := r.db.Where("user_id = ? AND completed = ?", userID, false).Find(&progresses).Error; err != nil {
		return nil, err
	}
	return r.mapToEntityPointers(progresses), nil
}

// UpdateProgress 更新课程进度
func (r *GormLessonProgressRepository) UpdateProgress(ctx context.Context, progressID uuid.UUID, newProgress int) error {
	// Ensure progress is within bounds (0-100)
	if newProgress < 0 {
		newProgress = 0
	} else if newProgress > 100 {
		newProgress = 100
	}

	updates := map[string]interface{}{
		"progress":         newProgress,
		"last_accessed_at": time.Now(),
	}

	// If progress is 100, also mark as completed
	if newProgress == 100 {
		now := time.Now()
		updates["completed"] = true
		updates["completed_date"] = &now
	}

	return r.db.Model(&models.LessonProgress{}).Where("id = ?", progressID).Updates(updates).Error
}

// MarkAsCompleted 将课程标记为已完成
func (r *GormLessonProgressRepository) MarkAsCompleted(ctx context.Context, progressID uuid.UUID) error {
	now := time.Now()
	return r.db.Model(&models.LessonProgress{}).Where("id = ?", progressID).
		Updates(map[string]interface{}{
			"completed":        true,
			"completed_date":   &now,
			"last_accessed_at": now,
			"progress":         100,
		}).Error
}

// GetUserStatistics 获取用户的课程统计信息
func (r *GormLessonProgressRepository) GetUserStatistics(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	var completedCount int64
	if err := r.db.Model(&models.LessonProgress{}).Where("user_id = ? AND completed = ?", userID, true).Count(&completedCount).Error; err != nil {
		return nil, err
	}
	stats["completed_lessons"] = completedCount

	var inProgressCount int64
	if err := r.db.Model(&models.LessonProgress{}).Where("user_id = ? AND completed = ? AND progress > 0 AND progress < 100", userID, false).Count(&inProgressCount).Error; err != nil {
		return nil, err
	}
	stats["in_progress_lessons"] = inProgressCount

	var totalAttempted int64
	if err := r.db.Model(&models.LessonProgress{}).Where("user_id = ?", userID).Count(&totalAttempted).Error; err != nil {
		return nil, err
	}
	stats["total_attempted_lessons"] = totalAttempted

	var avgProgress float64
	// Ensure inProgressCount is not zero to avoid division by zero
	if inProgressCount > 0 {
		if err := r.db.Model(&models.LessonProgress{}).
			Where("user_id = ? AND completed = ? AND progress > 0 AND progress < 100", userID, false).
			Select("COALESCE(AVG(progress), 0)").
			Row().Scan(&avgProgress); err != nil {
			// Log error but don't fail the whole stats retrieval if one stat fails
			slog.ErrorContext(ctx, "Failed to calculate average progress for user", "userID", userID, "error", err)
		}
	}
	stats["average_progress_in_progress"] = avgProgress

	return stats, nil
}

// 辅助方法: 将数据库模型映射到领域实体
func (r *GormLessonProgressRepository) mapToEntity(model models.LessonProgress) entity.LessonProgress {
	return entity.LessonProgress{
		ID:             model.ID,
		UserID:         model.UserID,
		LessonID:       model.LessonID,
		Progress:       model.Progress,
		Completed:      model.Completed,
		Score:          model.Progress,
		StartedAt:      model.CreatedAt,
		CompletedAt:    &model.CompletedDate,
		LastAccessedAt: model.UpdatedAt,
		Notes:          "",
		CreatedAt:      model.CreatedAt,
		UpdatedAt:      model.UpdatedAt,
	}
}

// 辅助方法: 将多个数据库模型映射到领域实体
func (r *GormLessonProgressRepository) mapToEntities(models []models.LessonProgress) []entity.LessonProgress {
	entities := make([]entity.LessonProgress, len(models))
	for i, model := range models {
		entities[i] = r.mapToEntity(model)
	}
	return entities
}

// 辅助方法: 将多个数据库模型映射到领域实体指针
func (r *GormLessonProgressRepository) mapToEntityPointers(models []models.LessonProgress) []*entity.LessonProgress {
	entities := make([]*entity.LessonProgress, len(models))
	for i, model := range models {
		entity := r.mapToEntity(model)
		entities[i] = &entity
	}
	return entities
}

// 辅助方法: 将领域实体映射到数据库模型
func (r *GormLessonProgressRepository) mapToModel(entity entity.LessonProgress) models.LessonProgress {
	return models.LessonProgress{
		UserID:        entity.UserID,
		LessonID:      entity.LessonID,
		Progress:      entity.Progress,
		Completed:     entity.Completed,
		CompletedDate: *entity.CompletedAt,
	}
}
