package impl

import (
	"context"
	"errors"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GormDifficultyMetadataRepository GORM 难度元数据存储库实现
type GormDifficultyMetadataRepository struct {
	db *gorm.DB
}

// NewGormDifficultyMetadataRepository 创建一个新的GORM难度元数据仓库
func NewGormDifficultyMetadataRepository(db *gorm.DB) repository.DifficultyMetadataRepository {
	return &GormDifficultyMetadataRepository{db: db}
}

// Create 创建难度元数据
func (r *GormDifficultyMetadataRepository) Create(ctx context.Context, metadata models.ExerciseDifficultyMetadata) (*models.ExerciseDifficultyMetadata, error) {
	if err := r.db.WithContext(ctx).Create(&metadata).Error; err != nil {
		return nil, err
	}
	return &metadata, nil
}

// Update 更新难度元数据
func (r *GormDifficultyMetadataRepository) Update(ctx context.Context, metadata models.ExerciseDifficultyMetadata) (*models.ExerciseDifficultyMetadata, error) {
	if metadata.ID == 0 {
		return nil, errors.New("cannot update difficulty metadata without ID")
	}
	if err := r.db.WithContext(ctx).Save(&metadata).Error; err != nil {
		return nil, err
	}
	return &metadata, nil
}

// FindByExerciseIDAndType 根据练习ID和类型查找难度元数据
func (r *GormDifficultyMetadataRepository) FindByExerciseIDAndType(ctx context.Context, exerciseID uuid.UUID, exerciseType string) (*models.ExerciseDifficultyMetadata, error) {
	var metadata models.ExerciseDifficultyMetadata
	err := r.db.WithContext(ctx).Where("exercise_id = ? AND exercise_type = ?", exerciseID, exerciseType).First(&metadata).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Return nil instead of error for not found
		}
		return nil, err
	}
	return &metadata, nil
}

// FindByExerciseID 根据练习ID查找难度元数据
func (r *GormDifficultyMetadataRepository) FindByExerciseID(ctx context.Context, exerciseID uuid.UUID) (*models.ExerciseDifficultyMetadata, error) {
	var metadata models.ExerciseDifficultyMetadata
	err := r.db.WithContext(ctx).Where("exercise_id = ?", exerciseID).First(&metadata).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Return nil instead of error for not found
		}
		return nil, err
	}
	return &metadata, nil
}

// Delete 删除难度元数据
func (r *GormDifficultyMetadataRepository) Delete(ctx context.Context, exerciseID uuid.UUID, exerciseType string) error {
	return r.db.WithContext(ctx).Where("exercise_id = ? AND exercise_type = ?", exerciseID, exerciseType).Delete(&models.ExerciseDifficultyMetadata{}).Error
}

// FindByComplexityRange 根据复杂度范围查找元数据
func (r *GormDifficultyMetadataRepository) FindByComplexityRange(ctx context.Context, minComplexity, maxComplexity float64) ([]*models.ExerciseDifficultyMetadata, error) {
	var metadataList []models.ExerciseDifficultyMetadata
	err := r.db.WithContext(ctx).Where("complexity_score BETWEEN ? AND ?", minComplexity, maxComplexity).Find(&metadataList).Error
	if err != nil {
		return nil, err
	}

	// Convert to pointer slice
	result := make([]*models.ExerciseDifficultyMetadata, len(metadataList))
	for i := range metadataList {
		result[i] = &metadataList[i]
	}
	return result, nil
}

// FindByTags 根据标签查找元数据
func (r *GormDifficultyMetadataRepository) FindByTags(ctx context.Context, tags []string) ([]*models.ExerciseDifficultyMetadata, error) {
	var metadataList []models.ExerciseDifficultyMetadata
	
	// Use PostgreSQL array overlap operator
	err := r.db.WithContext(ctx).Where("tags && ?", tags).Find(&metadataList).Error
	if err != nil {
		return nil, err
	}

	// Convert to pointer slice
	result := make([]*models.ExerciseDifficultyMetadata, len(metadataList))
	for i := range metadataList {
		result[i] = &metadataList[i]
	}
	return result, nil
}

// UpdateSuccessRate 更新成功率统计
func (r *GormDifficultyMetadataRepository) UpdateSuccessRate(ctx context.Context, exerciseID uuid.UUID, exerciseType string, isSuccess bool) error {
	// Find existing metadata
	metadata, err := r.FindByExerciseIDAndType(ctx, exerciseID, exerciseType)
	if err != nil {
		return err
	}

	if metadata == nil {
		// Create new metadata with default values
		metadata = &models.ExerciseDifficultyMetadata{
			ExerciseID:      exerciseID,
			ExerciseType:    exerciseType,
			ComplexityScore: 5.0, // Default complexity
			TimeToComplete:  60,  // Default time in seconds
			SuccessRate:     0,
			AttemptCount:    0,
			SuccessCount:    0,
			Tags:            []string{},
		}
	}

	// Update statistics
	metadata.AttemptCount++
	if isSuccess {
		metadata.SuccessCount++
	}
	
	// Calculate new success rate
	if metadata.AttemptCount > 0 {
		metadata.SuccessRate = float64(metadata.SuccessCount) / float64(metadata.AttemptCount) * 100
	}

	// Save or update
	if metadata.ID == 0 {
		_, err = r.Create(ctx, *metadata)
	} else {
		_, err = r.Update(ctx, *metadata)
	}

	return err
}
