package impl

import (
	"context"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// wordRepository implements the WordRepository interface
type wordRepository struct {
	db *gorm.DB
}

// NewWordRepository creates a new word repository
func NewWordRepository(db *gorm.DB) repository.WordRepository {
	return &wordRepository{db: db}
}

// FindAll retrieves all words
func (r *wordRepository) FindAll(ctx context.Context) ([]models.Word, error) {
	var words []models.Word
	err := r.db.WithContext(ctx).Find(&words).Error
	return words, err
}

// FindByID retrieves a word by ID
func (r *wordRepository) FindByID(ctx context.Context, id uuid.UUID) (*models.Word, error) {
	var word models.Word
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&word).Error
	if err != nil {
		return nil, err
	}
	return &word, nil
}

// FindByDifficulty retrieves words by difficulty level
func (r *wordRepository) FindByDifficulty(ctx context.Context, difficulty models.Difficulty) ([]models.Word, error) {
	var words []models.Word
	err := r.db.WithContext(ctx).Where("difficulty = ?", difficulty).Find(&words).Error
	return words, err
}

// FindByCategory retrieves words by category
func (r *wordRepository) FindByCategory(ctx context.Context, category string) ([]models.Word, error) {
	var words []models.Word
	err := r.db.WithContext(ctx).Where("category = ?", category).Find(&words).Error
	return words, err
}

// FindByDifficultyAndCategory retrieves words by difficulty and category
func (r *wordRepository) FindByDifficultyAndCategory(ctx context.Context, difficulty models.Difficulty, category string) ([]models.Word, error) {
	var words []models.Word
	err := r.db.WithContext(ctx).Where("difficulty = ? AND category = ?", difficulty, category).Find(&words).Error
	return words, err
}

// FindWithPagination retrieves words with pagination
func (r *wordRepository) FindWithPagination(ctx context.Context, limit, offset int) ([]models.Word, error) {
	var words []models.Word
	err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Find(&words).Error
	return words, err
}

// Count counts total words
func (r *wordRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.Word{}).Count(&count).Error
	return count, err
}

// Create creates a new word
func (r *wordRepository) Create(ctx context.Context, word models.Word) (*models.Word, error) {
	err := r.db.WithContext(ctx).Create(&word).Error
	if err != nil {
		return nil, err
	}
	return &word, nil
}

// Update updates a word
func (r *wordRepository) Update(ctx context.Context, word models.Word) (*models.Word, error) {
	err := r.db.WithContext(ctx).Save(&word).Error
	if err != nil {
		return nil, err
	}
	return &word, nil
}

// Delete deletes a word
func (r *wordRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&models.Word{}).Error
}

// Search searches words by text
func (r *wordRepository) Search(ctx context.Context, query string, limit int) ([]models.Word, error) {
	var words []models.Word
	err := r.db.WithContext(ctx).Where("word ILIKE ? OR translation ILIKE ?", "%"+query+"%", "%"+query+"%").
		Limit(limit).
		Find(&words).Error
	return words, err
}

// FindRandomWords retrieves random words
func (r *wordRepository) FindRandomWords(ctx context.Context, count int) ([]models.Word, error) {
	var words []models.Word
	err := r.db.WithContext(ctx).Order("RANDOM()").Limit(count).Find(&words).Error
	return words, err
}
