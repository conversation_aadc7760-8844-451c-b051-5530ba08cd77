package impl

import (
	"context"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// practiceRepository implements the PracticeRepository interface
type practiceRepository struct {
	db *gorm.DB
}

// NewPracticeRepository creates a new practice repository
func NewPracticeRepository(db *gorm.DB) repository.PracticeRepository {
	return &practiceRepository{db: db}
}

// FindByUserID retrieves practice sessions for a user
func (r *practiceRepository) FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.PracticeSession, error) {
	var sessions []models.PracticeSession
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&sessions).Error
	return sessions, err
}

// Create creates a new practice session
func (r *practiceRepository) Create(ctx context.Context, session models.PracticeSession) (*models.PracticeSession, error) {
	err := r.db.WithContext(ctx).Create(&session).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

// FindByUserIDWithPagination retrieves practice sessions with pagination
func (r *practiceRepository) FindByUserIDWithPagination(ctx context.Context, userID uuid.UUID, limit, offset int) ([]models.PracticeSession, error) {
	var sessions []models.PracticeSession
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&sessions).Error
	return sessions, err
}

// CountByUserID counts total practice sessions for a user
func (r *practiceRepository) CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.PracticeSession{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

// FindByUserIDAndType retrieves practice sessions by user and type
func (r *practiceRepository) FindByUserIDAndType(ctx context.Context, userID uuid.UUID, practiceType string) ([]models.PracticeSession, error) {
	var sessions []models.PracticeSession
	err := r.db.WithContext(ctx).Where("user_id = ? AND type = ?", userID, practiceType).
		Order("created_at DESC").
		Find(&sessions).Error
	return sessions, err
}

// GetPracticeStats retrieves practice statistics for a user
func (r *practiceRepository) GetPracticeStats(ctx context.Context, userID uuid.UUID) (*models.PracticeStats, error) {
	var stats models.PracticeStats

	// Get total duration and average score
	var result struct {
		TotalDuration int64
		AverageScore  float64
	}
	err := r.db.WithContext(ctx).Model(&models.PracticeSession{}).
		Where("user_id = ?", userID).
		Select("SUM(duration) as total_duration, AVG(score) as average_score").
		Scan(&result).Error
	if err != nil {
		return nil, err
	}

	stats.TotalPracticeTime = time.Duration(result.TotalDuration) * time.Second
	stats.AverageScore = result.AverageScore

	// Get sessions by type and update counts
	var typeCounts []struct {
		Type  string
		Count int64
	}
	err = r.db.WithContext(ctx).Model(&models.PracticeSession{}).
		Where("user_id = ?", userID).
		Select("type, COUNT(*) as count").
		Group("type").
		Scan(&typeCounts).Error
	if err != nil {
		return nil, err
	}

	// Map session types to stats fields
	for _, tc := range typeCounts {
		switch tc.Type {
		case "vocabulary":
			stats.VocabularyCount = int(tc.Count)
		case "listening":
			stats.ListeningCount = int(tc.Count)
		case "speaking":
			stats.SpeakingCount = int(tc.Count)
		case "grammar":
			stats.GrammarCount = int(tc.Count)
		}
	}

	return &stats, nil
}
