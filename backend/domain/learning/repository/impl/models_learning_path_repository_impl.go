package impl

import (
	"context"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// modelsLearningPathRepository implements the ModelsLearningPathRepository interface
type modelsLearningPathRepository struct {
	db *gorm.DB
}

// NewModelsLearningPathRepository creates a new models learning path repository
func NewModelsLearningPathRepository(db *gorm.DB) repository.ModelsLearningPathRepository {
	return &modelsLearningPathRepository{db: db}
}

// FindByID retrieves a learning path by ID
func (r *modelsLearningPathRepository) FindByID(ctx context.Context, id uuid.UUID) (*models.LearningPath, error) {
	var path models.LearningPath
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&path).Error
	if err != nil {
		return nil, err
	}
	return &path, nil
}

// FindByUserID retrieves learning paths for a user
func (r *modelsLearningPathRepository) FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.LearningPath, error) {
	var paths []models.LearningPath
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&paths).Error
	return paths, err
}

// FindByUserIDAndStatus retrieves learning paths by user and status
func (r *modelsLearningPathRepository) FindByUserIDAndStatus(ctx context.Context, userID uuid.UUID, status models.LearningPathStatus) ([]models.LearningPath, error) {
	var paths []models.LearningPath
	err := r.db.WithContext(ctx).Where("user_id = ? AND status = ?", userID, status).Order("created_at DESC").Find(&paths).Error
	return paths, err
}

// FindByUserIDWithLessons retrieves learning path with lessons preloaded
func (r *modelsLearningPathRepository) FindByUserIDWithLessons(ctx context.Context, userID, pathID uuid.UUID) (*models.LearningPath, error) {
	var path models.LearningPath
	err := r.db.WithContext(ctx).Where("id = ? AND user_id = ?", pathID, userID).
		Preload("Lessons").
		Preload("Lessons.Lesson").
		First(&path).Error
	if err != nil {
		return nil, err
	}
	return &path, nil
}

// FindActiveByUserID retrieves active learning paths for a user
func (r *modelsLearningPathRepository) FindActiveByUserID(ctx context.Context, userID uuid.UUID) ([]models.LearningPath, error) {
	var paths []models.LearningPath
	err := r.db.WithContext(ctx).Where("user_id = ? AND status = ?", userID, models.PathActive).Order("created_at DESC").Find(&paths).Error
	return paths, err
}

// Create creates a new learning path
func (r *modelsLearningPathRepository) Create(ctx context.Context, path models.LearningPath) (*models.LearningPath, error) {
	err := r.db.WithContext(ctx).Create(&path).Error
	if err != nil {
		return nil, err
	}
	return &path, nil
}

// Update updates a learning path
func (r *modelsLearningPathRepository) Update(ctx context.Context, path models.LearningPath) (*models.LearningPath, error) {
	err := r.db.WithContext(ctx).Save(&path).Error
	if err != nil {
		return nil, err
	}
	return &path, nil
}

// Delete deletes a learning path
func (r *modelsLearningPathRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&models.LearningPath{}).Error
}

// UpdateProgress updates learning path progress
func (r *modelsLearningPathRepository) UpdateProgress(ctx context.Context, pathID uuid.UUID, progress int) error {
	return r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).
		Update("progress", progress).Error
}

// MarkAsCompleted marks a learning path as completed
func (r *modelsLearningPathRepository) MarkAsCompleted(ctx context.Context, pathID uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).
		Updates(map[string]interface{}{
			"status":         models.PathCompleted,
			"completed_date": time.Now(),
			"progress":       100,
		}).Error
}

// modelsLearningPathLessonRepository implements the ModelsLearningPathLessonRepository interface
type modelsLearningPathLessonRepository struct {
	db *gorm.DB
}

// NewModelsLearningPathLessonRepository creates a new models learning path lesson repository
func NewModelsLearningPathLessonRepository(db *gorm.DB) repository.ModelsLearningPathLessonRepository {
	return &modelsLearningPathLessonRepository{db: db}
}

// FindByPathID retrieves lessons for a learning path
func (r *modelsLearningPathLessonRepository) FindByPathID(ctx context.Context, pathID uuid.UUID) ([]models.LearningPathLesson, error) {
	var lessons []models.LearningPathLesson
	err := r.db.WithContext(ctx).Where("learning_path_id = ?", pathID).
		Preload("Lesson").
		Order("order ASC").
		Find(&lessons).Error
	return lessons, err
}

// Create creates a new learning path lesson
func (r *modelsLearningPathLessonRepository) Create(ctx context.Context, pathLesson models.LearningPathLesson) (*models.LearningPathLesson, error) {
	err := r.db.WithContext(ctx).Create(&pathLesson).Error
	if err != nil {
		return nil, err
	}
	return &pathLesson, nil
}

// Update updates a learning path lesson
func (r *modelsLearningPathLessonRepository) Update(ctx context.Context, pathLesson models.LearningPathLesson) (*models.LearningPathLesson, error) {
	err := r.db.WithContext(ctx).Save(&pathLesson).Error
	if err != nil {
		return nil, err
	}
	return &pathLesson, nil
}

// Delete deletes a learning path lesson
func (r *modelsLearningPathLessonRepository) Delete(ctx context.Context, pathID, lessonID uuid.UUID) error {
	return r.db.WithContext(ctx).Where("learning_path_id = ? AND lesson_id = ?", pathID, lessonID).
		Delete(&models.LearningPathLesson{}).Error
}

// MarkLessonCompleted marks a lesson as completed in a learning path
func (r *modelsLearningPathLessonRepository) MarkLessonCompleted(ctx context.Context, pathID, lessonID uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&models.LearningPathLesson{}).
		Where("learning_path_id = ? AND lesson_id = ?", pathID, lessonID).
		Update("is_completed", true).Error
}

// GetCompletionStats gets completion statistics for a learning path
func (r *modelsLearningPathLessonRepository) GetCompletionStats(ctx context.Context, pathID uuid.UUID) (*models.LearningPathStats, error) {
	var stats models.LearningPathStats
	
	// Get total lessons count
	err := r.db.WithContext(ctx).Model(&models.LearningPathLesson{}).
		Where("learning_path_id = ?", pathID).
		Count(&stats.TotalLessons).Error
	if err != nil {
		return nil, err
	}
	
	// Get completed lessons count
	err = r.db.WithContext(ctx).Model(&models.LearningPathLesson{}).
		Where("learning_path_id = ? AND is_completed = ?", pathID, true).
		Count(&stats.CompletedLessons).Error
	if err != nil {
		return nil, err
	}
	
	// Calculate progress percentage
	if stats.TotalLessons > 0 {
		stats.ProgressPercentage = int((stats.CompletedLessons * 100) / stats.TotalLessons)
	}
	
	return &stats, nil
}
