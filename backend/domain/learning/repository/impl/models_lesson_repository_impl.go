package impl

import (
	"context"
	"languagelearning/domain/learning/repository"
	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// modelsLessonRepository implements the ModelsLessonRepository interface
type modelsLessonRepository struct {
	db *gorm.DB
}

// NewModelsLessonRepository creates a new models lesson repository
func NewModelsLessonRepository(db *gorm.DB) repository.ModelsLessonRepository {
	return &modelsLessonRepository{db: db}
}

// FindByID retrieves a lesson by ID
func (r *modelsLessonRepository) FindByID(ctx context.Context, id uuid.UUID) (*models.Lesson, error) {
	var lesson models.Lesson
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&lesson).Error
	if err != nil {
		return nil, err
	}
	return &lesson, nil
}

// FindAll retrieves all lessons
func (r *modelsLessonRepository) FindAll(ctx context.Context) ([]models.Lesson, error) {
	var lessons []models.Lesson
	err := r.db.WithContext(ctx).Find(&lessons).Error
	return lessons, err
}

// FindByLevel retrieves lessons by level
func (r *modelsLessonRepository) FindByLevel(ctx context.Context, level models.LessonLevel) ([]models.Lesson, error) {
	var lessons []models.Lesson
	err := r.db.WithContext(ctx).Where("level = ?", level).Find(&lessons).Error
	return lessons, err
}

// FindByLevelWithLimit retrieves lessons by level with limit
func (r *modelsLessonRepository) FindByLevelWithLimit(ctx context.Context, level models.LessonLevel, limit int) ([]models.Lesson, error) {
	var lessons []models.Lesson
	err := r.db.WithContext(ctx).Where("level = ?", level).Limit(limit).Find(&lessons).Error
	return lessons, err
}

// FindByCategory retrieves lessons by category
func (r *modelsLessonRepository) FindByCategory(ctx context.Context, category string) ([]models.Lesson, error) {
	var lessons []models.Lesson
	err := r.db.WithContext(ctx).Where("category = ?", category).Find(&lessons).Error
	return lessons, err
}

// FindByLevelAndCategory retrieves lessons by level and category
func (r *modelsLessonRepository) FindByLevelAndCategory(ctx context.Context, level models.LessonLevel, category string) ([]models.Lesson, error) {
	var lessons []models.Lesson
	err := r.db.WithContext(ctx).Where("level = ? AND category = ?", level, category).Find(&lessons).Error
	return lessons, err
}

// FindWithPagination retrieves lessons with pagination
func (r *modelsLessonRepository) FindWithPagination(ctx context.Context, limit, offset int) ([]models.Lesson, error) {
	var lessons []models.Lesson
	err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Find(&lessons).Error
	return lessons, err
}

// Count counts total lessons
func (r *modelsLessonRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.Lesson{}).Count(&count).Error
	return count, err
}

// Create creates a new lesson
func (r *modelsLessonRepository) Create(ctx context.Context, lesson models.Lesson) (*models.Lesson, error) {
	err := r.db.WithContext(ctx).Create(&lesson).Error
	if err != nil {
		return nil, err
	}
	return &lesson, nil
}

// Update updates a lesson
func (r *modelsLessonRepository) Update(ctx context.Context, lesson models.Lesson) (*models.Lesson, error) {
	err := r.db.WithContext(ctx).Save(&lesson).Error
	if err != nil {
		return nil, err
	}
	return &lesson, nil
}

// Delete deletes a lesson
func (r *modelsLessonRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&models.Lesson{}).Error
}

// Search searches lessons by title or description
func (r *modelsLessonRepository) Search(ctx context.Context, query string, limit int) ([]models.Lesson, error) {
	var lessons []models.Lesson
	err := r.db.WithContext(ctx).Where("title ILIKE ? OR description ILIKE ?", "%"+query+"%", "%"+query+"%").
		Limit(limit).
		Find(&lessons).Error
	return lessons, err
}

// FindRecommendedForUser finds recommended lessons for a user based on their level
func (r *modelsLessonRepository) FindRecommendedForUser(ctx context.Context, userID uuid.UUID, level models.LessonLevel, limit int) ([]models.Lesson, error) {
	var lessons []models.Lesson
	
	// For now, just return lessons by level
	// In the future, this could be enhanced with user preferences, completed lessons, etc.
	err := r.db.WithContext(ctx).Where("level = ?", level).
		Order("created_at DESC").
		Limit(limit).
		Find(&lessons).Error
	
	return lessons, err
}
