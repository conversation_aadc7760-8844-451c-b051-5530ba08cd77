package repository

import (
	"context"
	"languagelearning/models"

	"github.com/google/uuid"
)

// WordRepository defines the interface for word data access
type WordRepository interface {
	// FindAll retrieves all words
	FindAll(ctx context.Context) ([]models.Word, error)

	// FindByID retrieves a word by ID
	FindByID(ctx context.Context, id uuid.UUID) (*models.Word, error)

	// FindByDifficulty retrieves words by difficulty level
	FindByDifficulty(ctx context.Context, difficulty models.Difficulty) ([]models.Word, error)

	// FindByCategory retrieves words by category
	FindByCategory(ctx context.Context, category string) ([]models.Word, error)

	// FindByDifficultyAndCategory retrieves words by difficulty and category
	FindByDifficultyAndCategory(ctx context.Context, difficulty models.Difficulty, category string) ([]models.Word, error)

	// FindWithPagination retrieves words with pagination
	FindWithPagination(ctx context.Context, limit, offset int) ([]models.Word, error)

	// Count counts total words
	Count(ctx context.Context) (int64, error)

	// Create creates a new word
	Create(ctx context.Context, word models.Word) (*models.Word, error)

	// Update updates a word
	Update(ctx context.Context, word models.Word) (*models.Word, error)

	// Delete deletes a word
	Delete(ctx context.Context, id uuid.UUID) error

	// Search searches words by text
	Search(ctx context.Context, query string, limit int) ([]models.Word, error)

	// FindRandomWords retrieves random words
	FindRandomWords(ctx context.Context, count int) ([]models.Word, error)
}
