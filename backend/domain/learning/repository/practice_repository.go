package repository

import (
	"context"
	"languagelearning/models"

	"github.com/google/uuid"
)

// PracticeRepository defines the interface for practice session data access
type PracticeRepository interface {
	// FindByUserID retrieves practice sessions for a user
	FindByUserID(ctx context.Context, userID uuid.UUID) ([]models.PracticeSession, error)
	
	// Create creates a new practice session
	Create(ctx context.Context, session models.PracticeSession) (*models.PracticeSession, error)
	
	// FindByUserIDWithPagination retrieves practice sessions with pagination
	FindByUserIDWithPagination(ctx context.Context, userID uuid.UUID, limit, offset int) ([]models.PracticeSession, error)
	
	// CountByUserID counts total practice sessions for a user
	CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error)
	
	// FindByUserIDAndType retrieves practice sessions by user and type
	FindByUserIDAndType(ctx context.Context, userID uuid.UUID, practiceType string) ([]models.PracticeSession, error)
	
	// GetPracticeStats retrieves practice statistics for a user
	GetPracticeStats(ctx context.Context, userID uuid.UUID) (*models.PracticeStats, error)
}
