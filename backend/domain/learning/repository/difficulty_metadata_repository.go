package repository

import (
	"context"
	"languagelearning/models"

	"github.com/google/uuid"
)

// DifficultyMetadataRepository 练习难度元数据仓库接口
type DifficultyMetadataRepository interface {
	// Create 创建难度元数据
	Create(ctx context.Context, metadata models.ExerciseDifficultyMetadata) (*models.ExerciseDifficultyMetadata, error)

	// Update 更新难度元数据
	Update(ctx context.Context, metadata models.ExerciseDifficultyMetadata) (*models.ExerciseDifficultyMetadata, error)

	// FindByExerciseIDAndType 根据练习ID和类型查找难度元数据
	FindByExerciseIDAndType(ctx context.Context, exerciseID uuid.UUID, exerciseType string) (*models.ExerciseDifficultyMetadata, error)

	// FindByExerciseID 根据练习ID查找难度元数据
	FindByExerciseID(ctx context.Context, exerciseID uuid.UUID) (*models.ExerciseDifficultyMetadata, error)

	// Delete 删除难度元数据
	Delete(ctx context.Context, exerciseID uuid.UUID, exerciseType string) error

	// FindByComplexityRange 根据复杂度范围查找元数据
	FindByComplexityRange(ctx context.Context, minComplexity, maxComplexity float64) ([]*models.ExerciseDifficultyMetadata, error)

	// FindByTags 根据标签查找元数据
	FindByTags(ctx context.Context, tags []string) ([]*models.ExerciseDifficultyMetadata, error)

	// UpdateSuccessRate 更新成功率统计
	UpdateSuccessRate(ctx context.Context, exerciseID uuid.UUID, exerciseType string, isSuccess bool) error
}
