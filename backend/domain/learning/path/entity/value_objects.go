package entity

import (
	"errors"
	"fmt"
)

// Progress 表示進度的值對象
type Progress struct {
	value int // 0-100
}

// NewProgress 創建一個新的進度值對象
func NewProgress(value int) (*Progress, error) {
	if value < 0 || value > 100 {
		return nil, errors.New("progress must be between 0 and 100")
	}
	return &Progress{value: value}, nil
}

// Value 獲取進度值
func (p Progress) Value() int {
	return p.value
}

// IsComplete 判斷是否完成
func (p Progress) IsComplete() bool {
	return p.value == 100
}

// String 返回字符串表示
func (p Progress) String() string {
	return fmt.Sprintf("%d%%", p.value)
}
