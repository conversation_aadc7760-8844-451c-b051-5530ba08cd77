package entity

import (
	"time"

	lessonentity "languagelearning/domain/learning/entity"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// PathStatus 学习路径状态
type PathStatus string

const (
	// PathActive 活跃状态
	PathActive PathStatus = "active"
	// PathPaused 暂停状态
	PathPaused PathStatus = "paused"
	// PathCompleted 已完成状态
	PathCompleted PathStatus = "completed"
	// PathArchived 已归档状态
	PathArchived PathStatus = "archived"
)

// LearningPath 学习路径实体
type LearningPath struct {
	ID            uuid.UUID              `json:"id"`
	Title         string                 `json:"title"`
	Description   string                 `json:"description"`
	UserID        uuid.UUID              `json:"userId"`
	Level         string                 `json:"level"`
	FocusAreas    pq.StringArray         `json:"focusAreas"`
	Status        PathStatus             `json:"status"`
	Progress      *Progress              `json:"progress"`
	Duration      *lessonentity.Duration `json:"duration"`
	EvaluationID  *uuid.UUID             `json:"evaluationId,omitempty"`
	StartedAt     time.Time              `json:"startedAt"`
	CompletedDate time.Time              `json:"completedDate,omitempty"`
	CreatedAt     time.Time              `json:"createdAt"`
	UpdatedAt     time.Time              `json:"updatedAt"`
	Lessons       []LearningPathLesson   `json:"lessons,omitempty"`
}

// NewLearningPath 创建一个新的学习路径
func NewLearningPath(userID uuid.UUID, title, description, level string, focusAreas []string, estimatedDuration int) (*LearningPath, error) {
	progress, err := NewProgress(0)
	if err != nil {
		return nil, err
	}

	duration, err := lessonentity.NewDuration(estimatedDuration)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	return &LearningPath{
		ID:          uuid.New(),
		Title:       title,
		Description: description,
		UserID:      userID,
		Level:       level,
		FocusAreas:  focusAreas,
		Status:      PathActive,
		Progress:    progress,
		Duration:    duration,
		StartedAt:   now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}, nil
}

// UpdateProgress 更新進度
func (lp *LearningPath) UpdateProgress(value int) error {
	progress, err := NewProgress(value)
	if err != nil {
		return err
	}
	lp.Progress = progress
	if progress.IsComplete() {
		lp.Status = PathCompleted
		lp.CompletedDate = time.Now()
	}
	lp.UpdatedAt = time.Now()
	return nil
}

// SetDuration 設置學習路徑時長
func (lp *LearningPath) SetDuration(minutes int) error {
	duration, err := lessonentity.NewDuration(minutes)
	if err != nil {
		return err
	}
	lp.Duration = duration
	lp.UpdatedAt = time.Now()
	return nil
}

// Complete 完成學習路徑
func (lp *LearningPath) Complete() error {
	return lp.UpdateProgress(100)
}

// LearningPathLesson 学习路径中的课程
type LearningPathLesson struct {
	ID             uuid.UUID            `json:"id"`
	LearningPathID uuid.UUID            `json:"learningPathId"`
	LessonID       string               `json:"lessonId"`
	Order          int                  `json:"order"`
	IsRequired     bool                 `json:"isRequired"`
	IsCompleted    bool                 `json:"isCompleted"`
	CompletedDate  time.Time            `json:"completedDate,omitempty"`
	CreatedAt      time.Time            `json:"createdAt"`
	UpdatedAt      time.Time            `json:"updatedAt"`
	Lesson         *lessonentity.Lesson `json:"lesson,omitempty"`
}

// NewLearningPathLesson 创建一个新的学习路径课程
func NewLearningPathLesson(pathID uuid.UUID, lessonID string, order int, isRequired bool) *LearningPathLesson {
	now := time.Now()
	return &LearningPathLesson{
		ID:             uuid.New(),
		LearningPathID: pathID,
		LessonID:       lessonID,
		Order:          order,
		IsRequired:     isRequired,
		IsCompleted:    false,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
}

// LearningPathRecommendation 学习路径推荐
type LearningPathRecommendation struct {
	ID                uuid.UUID      `json:"id"`
	UserID            uuid.UUID      `json:"userId"`
	Title             string         `json:"title"`
	Description       string         `json:"description"`
	Level             string         `json:"level"`
	FocusAreas        pq.StringArray `json:"focusAreas"`
	EstimatedDuration int            `json:"estimatedDuration"`
	MatchScore        float64        `json:"matchScore"`
	RecommendedAt     time.Time      `json:"recommendedAt"`
	CreatedAt         time.Time      `json:"createdAt"`
	UpdatedAt         time.Time      `json:"updatedAt"`
}

// LearningPathProgress 學習路徑進度
type LearningPathProgress struct {
	ID             uuid.UUID  `json:"id"`
	LearningPathID uuid.UUID  `json:"learningPathId"`
	UserID         uuid.UUID  `json:"userId"`
	Progress       *Progress  `json:"progress"`
	Level          int        `json:"level"`
	Experience     int        `json:"experience"`
	StreakDays     int        `json:"streakDays"`
	TotalLessons   int        `json:"totalLessons"`
	StartedAt      time.Time  `json:"startedAt"`
	LastAccessedAt time.Time  `json:"lastAccessedAt"`
	CompletedAt    *time.Time `json:"completedAt,omitempty"`
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      time.Time  `json:"updatedAt"`
}

// NewLearningPathProgress 創建新的學習路徑進度
func NewLearningPathProgress(learningPathID, userID uuid.UUID) (*LearningPathProgress, error) {
	progress, err := NewProgress(0)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	return &LearningPathProgress{
		ID:             uuid.New(),
		LearningPathID: learningPathID,
		UserID:         userID,
		Progress:       progress,
		Level:          1,
		Experience:     0,
		StreakDays:     0,
		TotalLessons:   0,
		StartedAt:      now,
		LastAccessedAt: now,
		CreatedAt:      now,
		UpdatedAt:      now,
	}, nil
}

// UpdateProgress 更新進度
func (p *LearningPathProgress) UpdateProgress(value int) error {
	progress, err := NewProgress(value)
	if err != nil {
		return err
	}
	p.Progress = progress
	if progress.IsComplete() && p.CompletedAt == nil {
		now := time.Now()
		p.CompletedAt = &now
	}
	p.UpdatedAt = time.Now()
	return nil
}

// Complete 完成學習路徑
func (p *LearningPathProgress) Complete() error {
	return p.UpdateProgress(100)
}

// LearningPathStatistics 學習路徑統計信息
type LearningPathStatistics struct {
	ID                    uuid.UUID `json:"id"`
	LearningPathID        uuid.UUID `json:"learningPathId"`
	TotalUsers            int       `json:"totalUsers"`
	ActiveUsers           int       `json:"activeUsers"`
	CompletedUsers        int       `json:"completedUsers"`
	AverageProgress       float64   `json:"averageProgress"`
	AverageCompletionTime int       `json:"averageCompletionTime"` // 以天為單位
	AverageScore          float64   `json:"averageScore"`
	SuccessRate           float64   `json:"successRate"`
	CreatedAt             time.Time `json:"createdAt"`
	UpdatedAt             time.Time `json:"updatedAt"`
}

// NewLearningPathStatistics 創建新的學習路徑統計信息
func NewLearningPathStatistics(learningPathID uuid.UUID) *LearningPathStatistics {
	now := time.Now()
	return &LearningPathStatistics{
		ID:             uuid.New(),
		LearningPathID: learningPathID,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
}

// UpdateStatistics 更新統計信息
func (s *LearningPathStatistics) UpdateStatistics(totalUsers, activeUsers, completedUsers int, averageProgress, averageScore, successRate float64, averageCompletionTime int) {
	s.TotalUsers = totalUsers
	s.ActiveUsers = activeUsers
	s.CompletedUsers = completedUsers
	s.AverageProgress = averageProgress
	s.AverageScore = averageScore
	s.SuccessRate = successRate
	s.AverageCompletionTime = averageCompletionTime
	s.UpdatedAt = time.Now()
}
