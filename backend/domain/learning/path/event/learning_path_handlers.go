package event

import (
	"fmt"
	"log"

	"languagelearning/domain/core/event"
)

// LearningPathUpdatedHandler 學習路徑更新事件處理器
type LearningPathUpdatedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 成就服務
	// - 通知服務
}

// NewLearningPathUpdatedHandler 創建學習路徑更新事件處理器
func NewLearningPathUpdatedHandler() *LearningPathUpdatedHandler {
	return &LearningPathUpdatedHandler{}
}

// Handle 處理學習路徑更新事件
func (h *LearningPathUpdatedHandler) Handle(e event.Event) error {
	updatedEvent, ok := e.(*LearningPathUpdatedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶學習統計
	// 2. 檢查是否需要解鎖成就
	// 3. 發送進度更新通知
	// 4. 更新用戶學習時間統計

	log.Printf("Learning path updated: %s for user %s, progress: %s",
		updatedEvent.PathID,
		updatedEvent.UserID,
		updatedEvent.Progress,
	)
	return nil
}

// LearningPathCompletedHandler 學習路徑完成事件處理器
type LearningPathCompletedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 用戶服務
	// - 成就服務
	// - 通知服務
}

// NewLearningPathCompletedHandler 創建學習路徑完成事件處理器
func NewLearningPathCompletedHandler() *LearningPathCompletedHandler {
	return &LearningPathCompletedHandler{}
}

// Handle 處理學習路徑完成事件
func (h *LearningPathCompletedHandler) Handle(e event.Event) error {
	completedEvent, ok := e.(*LearningPathCompletedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶學習統計
	// 2. 解鎖相關成就
	// 3. 發送完成通知
	// 4. 推薦下一個學習路徑

	log.Printf("Learning path completed: %s by user %s",
		completedEvent.PathID,
		completedEvent.UserID,
	)
	return nil
}

// RegisterLearningPathEventHandlers 註冊所有學習路徑相關的事件處理器
func RegisterLearningPathEventHandlers(bus event.EventBus) error {
	// 註冊學習路徑更新事件處理器
	updatedHandler := NewLearningPathUpdatedHandler()
	if err := bus.Subscribe("learning_path.updated", updatedHandler); err != nil {
		return fmt.Errorf("failed to register learning path updated handler: %w", err)
	}

	// 註冊學習路徑完成事件處理器
	completedHandler := NewLearningPathCompletedHandler()
	if err := bus.Subscribe("learning_path.completed", completedHandler); err != nil {
		return fmt.Errorf("failed to register learning path completed handler: %w", err)
	}

	return nil
}
