package event

import (
	"time"

	"languagelearning/domain/core/event"
	learningEvent "languagelearning/domain/learning/event"
	lesson "languagelearning/domain/learning/entity"
	"languagelearning/domain/learning/path/entity"

	"github.com/google/uuid"
)

// LearningPathCreatedEvent 學習路徑創建事件
type LearningPathCreatedEvent struct {
	*learningEvent.BaseLearningEvent
	Path *entity.LearningPath
}

// NewLearningPathCreatedEvent 創建學習路徑創建事件
func NewLearningPathCreatedEvent(path *entity.LearningPath) *LearningPathCreatedEvent {
	return &LearningPathCreatedEvent{
		BaseLearningEvent: learningEvent.NewBaseLearningEvent(
			"learning_path.created",
			path.ID,
			"learning_path",
			"learning_path",
			path,
		),
		Path: path,
	}
}

// LearningPathProgressUpdatedEvent 學習路徑進度更新事件
type LearningPathProgressUpdatedEvent struct {
	*learningEvent.BaseLearningEvent
	Path *entity.LearningPath
}

// NewLearningPathProgressUpdatedEvent 創建學習路徑進度更新事件
func NewLearningPathProgressUpdatedEvent(path *entity.LearningPath) *LearningPathProgressUpdatedEvent {
	return &LearningPathProgressUpdatedEvent{
		BaseLearningEvent: learningEvent.NewBaseLearningEvent(
			"learning_path.progress.updated",
			path.ID,
			"learning_path",
			"learning_path",
			path,
		),
		Path: path,
	}
}

// LearningPathUpdatedEvent 學習路徑更新事件
type LearningPathUpdatedEvent struct {
	*event.BaseEvent
	PathID     uuid.UUID        `json:"path_id"`
	UserID     uuid.UUID        `json:"user_id"`
	Progress   *entity.Progress `json:"progress"`
	NextLesson *lesson.Lesson   `json:"next_lesson,omitempty"`
	UpdatedAt  time.Time        `json:"updated_at"`
}

// NewLearningPathUpdatedEvent 創建學習路徑更新事件
func NewLearningPathUpdatedEvent(pathID, userID uuid.UUID, progress *entity.Progress, nextLesson *lesson.Lesson) *LearningPathUpdatedEvent {
	return &LearningPathUpdatedEvent{
		BaseEvent: event.NewBaseEvent(
			"learning_path.updated",
			pathID,
			"learning_path",
			progress,
		),
		PathID:     pathID,
		UserID:     userID,
		Progress:   progress,
		NextLesson: nextLesson,
		UpdatedAt:  time.Now(),
	}
}

// LearningPathCompletedEvent 學習路徑完成事件
type LearningPathCompletedEvent struct {
	*event.BaseEvent
	PathID      uuid.UUID        `json:"path_id"`
	UserID      uuid.UUID        `json:"user_id"`
	Progress    *entity.Progress `json:"progress"`
	CompletedAt time.Time        `json:"completed_at"`
}

// NewLearningPathCompletedEvent 創建學習路徑完成事件
func NewLearningPathCompletedEvent(pathID, userID uuid.UUID, progress *entity.Progress) *LearningPathCompletedEvent {
	return &LearningPathCompletedEvent{
		BaseEvent: event.NewBaseEvent(
			"learning_path.completed",
			pathID,
			"learning_path",
			progress,
		),
		PathID:      pathID,
		UserID:      userID,
		Progress:    progress,
		CompletedAt: time.Now(),
	}
}
