package service

import (
	"languagelearning/models"

	"github.com/google/uuid"
)

// AdaptiveLearningService handles the adaptive learning functionality
type AdaptiveLearningService interface {
	AutoUpdateLearningPathProgress(userID uuid.UUID, pathID uuid.UUID) error
	AutoUpdateLessonCompletion(userID uuid.UUID, pathID uuid.UUID) error
	SchedulePeriodicAssessment(userID uuid.UUID, pathID uuid.UUID) (*models.Evaluation, error)
	AdjustLearningPathBasedOnAssessment(userID uuid.UUID, pathID uuid.UUID, evalID uuid.UUID) error
	RecommendNextLearningPath(userID uuid.UUID, completedPathID uuid.UUID) (*models.LearningPathRecommendation, error)
}
