package impl

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"

	coreevent "languagelearning/domain/core/event" // For EventBus
	"languagelearning/domain/learning/entity"
	lessonDomainEntity "languagelearning/domain/learning/entity"
	learningevent "languagelearning/domain/learning/event"      // For NewLearningProgressUpdatedEvent
	lessonEvent "languagelearning/domain/learning/lesson/event" // For lesson events
	"languagelearning/domain/learning/repository"
	"languagelearning/domain/learning/service"
	baseRepo "languagelearning/domain/repository"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	DefaultRelatedLessonsLimit = 5
)

// LessonServiceImpl 课程服务实现
type LessonServiceImpl struct {
	lessonRepo         repository.LessonRepository
	lessonProgressRepo repository.LessonProgressRepository
	eventBus           coreevent.EventBus // Updated alias
}

// NewLessonService 创建课程服务
func NewLessonService(
	lessonRepo repository.LessonRepository,
	lessonProgressRepo repository.LessonProgressRepository,
	eventBus coreevent.EventBus, // Updated alias
) service.LessonService {
	return &LessonServiceImpl{
		lessonRepo:         lessonRepo,
		lessonProgressRepo: lessonProgressRepo,
		eventBus:           eventBus,
	}
}

// GetLessonByID 获取课程详情
func (s *LessonServiceImpl) GetLessonByID(ctx context.Context, id uuid.UUID) (*entity.Lesson, error) {
	lesson, err := s.lessonRepo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if lesson.Status != entity.PublishedStatus {
		// Optionally, return a specific error or nil if only published lessons are accessible by general ID lookup
		// For now, returning the lesson regardless of status, but this logic might need review
		// return nil, errors.New("lesson not published")
	}
	return &lesson, nil
}

// GetLessons 获取课程列表，支持分页和过滤
func (s *LessonServiceImpl) GetLessons(ctx context.Context, pageable baseRepo.Pageable, filters map[string]interface{}) (baseRepo.Page[entity.Lesson], error) {
	// 目前暂不支持过滤，直接返回分页结果
	return s.lessonRepo.FindPage(ctx, pageable)
}

// CreateLesson 创建新课程
func (s *LessonServiceImpl) CreateLesson(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error) {
	if lesson == nil {
		return nil, errors.New("lesson is nil")
	}
	result, err := s.lessonRepo.Create(ctx, *lesson)
	if err != nil {
		return nil, err
	}

	// 發布課程創建事件
	convertedLesson := toLessonDomainEntity(&result)
	createdEvent := lessonEvent.NewLessonCreatedEvent(convertedLesson)
	if errPub := s.eventBus.Publish(ctx, createdEvent); errPub != nil {
		slog.ErrorContext(ctx, "Failed to publish lesson created event", "error", errPub)
	}
	return &result, nil
}

// UpdateLesson 更新课程
func (s *LessonServiceImpl) UpdateLesson(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error) {
	if lesson == nil {
		return nil, errors.New("lesson is nil")
	}
	// 检查课程是否存在
	_, err := s.lessonRepo.FindByID(ctx, lesson.ID)
	if err != nil {
		return nil, err
	}

	result, err := s.lessonRepo.Update(ctx, *lesson)
	if err != nil {
		return nil, err
	}

	// 發布課程更新事件
	convertedLessonForUpdate := toLessonDomainEntity(&result)
	updatedEvent := lessonEvent.NewLessonUpdatedEvent(convertedLessonForUpdate)
	if errPub := s.eventBus.Publish(ctx, updatedEvent); errPub != nil {
		slog.ErrorContext(ctx, "Failed to publish lesson updated event", "error", errPub)
	}
	return &result, nil
}

// DeleteLesson 删除课程
func (s *LessonServiceImpl) DeleteLesson(ctx context.Context, id uuid.UUID) error {
	// 检查课程是否存在
	_, err := s.lessonRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	return s.lessonRepo.Delete(ctx, id)
}

// PublishLesson 发布课程
func (s *LessonServiceImpl) PublishLesson(ctx context.Context, id uuid.UUID) error {
	lesson, err := s.lessonRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	lesson.Status = entity.PublishedStatus
	_, err = s.lessonRepo.Update(ctx, lesson)
	return err
}

// UnpublishLesson 取消发布课程
func (s *LessonServiceImpl) UnpublishLesson(ctx context.Context, id uuid.UUID) error {
	lesson, err := s.lessonRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	lesson.Status = entity.DraftStatus
	_, err = s.lessonRepo.Update(ctx, lesson)
	return err
}

// GetLessonsByLanguage 获取指定语言的课程
func (s *LessonServiceImpl) GetLessonsByLanguage(ctx context.Context, language string) ([]*entity.Lesson, error) {
	return s.lessonRepo.FindByLanguage(ctx, language)
}

// GetLessonsByLevel 获取指定级别的课程
func (s *LessonServiceImpl) GetLessonsByLevel(ctx context.Context, level entity.LessonLevel) ([]*entity.Lesson, error) {
	return s.lessonRepo.FindByLevel(ctx, level)
}

// GetLessonsByCategory 获取指定类别的课程
func (s *LessonServiceImpl) GetLessonsByCategory(ctx context.Context, category entity.LessonCategory) ([]*entity.Lesson, error) {
	return s.lessonRepo.FindByCategory(ctx, category)
}

// SearchLessons 搜索课程
func (s *LessonServiceImpl) SearchLessons(ctx context.Context, query string, pageable baseRepo.Pageable) (baseRepo.Page[entity.Lesson], error) {
	// TODO: Add more sophisticated search logic if needed, e.g., using a search engine
	return s.lessonRepo.FindPage(ctx, pageable)
}

// GetRelatedLessons 获取相关课程
func (s *LessonServiceImpl) GetRelatedLessons(ctx context.Context, lessonID uuid.UUID) ([]*entity.Lesson, error) {
	return s.lessonRepo.GetRelatedLessons(ctx, lessonID, DefaultRelatedLessonsLimit)
}

// GetUserLessonProgress 获取用户的课程进度
func (s *LessonServiceImpl) GetUserLessonProgress(ctx context.Context, userID, lessonID uuid.UUID) (*entity.LessonProgress, error) {
	progress, err := s.lessonProgressRepo.GetUserProgress(ctx, userID, lessonID)
	if err != nil {
		return nil, err
	}

	// 如果没有找到进度记录，创建一个新的
	if progress == nil {
		learningDomainProgress := entity.LessonProgress{
			ID:        uuid.New(), // Repository might handle ID generation, or it's set here
			UserID:    userID,
			LessonID:  lessonID,
			Progress:  0,
			Completed: false,
			// Score, StartedAt, CompletedAt, LastAccessedAt, Notes can be zero/default initially
			CreatedAt: time.Now(), // Set creation time
			UpdatedAt: time.Now(), // Set update time
		}
		createdProgress, err := s.lessonProgressRepo.Create(ctx, learningDomainProgress) // Pass value type
		if err != nil {
			return nil, err
		}
		return &createdProgress, nil
	}

	return progress, nil
}

// UpdateLessonProgress 更新课程进度
func (s *LessonServiceImpl) UpdateLessonProgress(ctx context.Context, progress *entity.LessonProgress) (*entity.LessonProgress, error) {
	// Validate input
	if progress.UserID == uuid.Nil || progress.LessonID == uuid.Nil {
		return nil, errors.New("userID and lessonID are required")
	}

	// Ensure progress value is within bounds (0-100)
	if progress.Progress < 0 {
		progress.Progress = 0
	} else if progress.Progress > 100 {
		progress.Progress = 100
	}

	// If completed, ensure progress is 100 and CompletedAt is set
	if progress.Completed {
		progress.Progress = 100
		if progress.CompletedAt == nil {
			now := time.Now()
			progress.CompletedAt = &now
		}
	} else {
		// If not completed, ensure CompletedAt is nil
		// Also, if progress is 100 but not marked completed, should it auto-complete?
		// For now, we respect the 'Completed' flag explicitly.
		progress.CompletedAt = nil
	}

	// Check if progress already exists
	existingProgressPtr, err := s.lessonProgressRepo.GetUserProgress(ctx, progress.UserID, progress.LessonID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to check existing lesson progress: %w", err)
	}

	if existingProgressPtr != nil && existingProgressPtr.ID != uuid.Nil {
		// Progress exists, update it
		existingProgressPtr.Progress = progress.Progress
		existingProgressPtr.Completed = progress.Completed
		if progress.Completed && existingProgressPtr.CompletedAt == nil {
			now := time.Now()
			existingProgressPtr.CompletedAt = &now
		} else if !progress.Completed {
			existingProgressPtr.CompletedAt = nil
		}
		updated, updateErr := s.lessonProgressRepo.Update(ctx, *existingProgressPtr)
		if updateErr != nil {
			return nil, fmt.Errorf("failed to update existing lesson progress: %w", updateErr)
		}
		// Publish event for update
		if errPub := s.eventBus.Publish(ctx, learningevent.NewLearningProgressUpdatedEvent(existingProgressPtr.UserID, nil, 0, 0, 0, 0)); errPub != nil {
			slog.ErrorContext(ctx, "Failed to publish lesson progress updated event", "error", errPub)
		}
		return &updated, nil
	}

	// Progress does not exist, create it
	newProgressEntity, err := s.lessonProgressRepo.Create(ctx, *progress)
	if err != nil {
		return nil, fmt.Errorf("failed to create new lesson progress: %w", err)
	}

	// Publish event for creation
	if errPub := s.eventBus.Publish(ctx, learningevent.NewLearningProgressUpdatedEvent(newProgressEntity.UserID, nil, 0, 0, 0, 0)); errPub != nil {
		slog.ErrorContext(ctx, "Failed to publish lesson progress updated event", "error", errPub)
	}

	return &newProgressEntity, nil
}

// CompleteLessonProgress marks a lesson as completed for a user.
func (s *LessonServiceImpl) CompleteLessonProgress(ctx context.Context, userID, lessonID uuid.UUID) error {
	progressPtr, err := s.lessonProgressRepo.GetUserProgress(ctx, userID, lessonID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// If no progress record exists, create one and mark it as complete
			now := time.Now()
			newProgress := entity.LessonProgress{
				UserID:      userID,
				LessonID:    lessonID,
				Progress:    100,
				Completed:   true,
				CompletedAt: &now,
			}
			_, createErr := s.lessonProgressRepo.Create(ctx, newProgress)
			if createErr != nil {
				return fmt.Errorf("failed to create lesson progress for completion: %w", createErr)
			}
			// Publish event
			if errPub := s.eventBus.Publish(ctx, learningevent.NewLearningProgressUpdatedEvent(userID, nil, 0, 0, 0, 0)); errPub != nil {
				slog.ErrorContext(ctx, "Failed to publish lesson completed event after creation", "error", errPub)
			}
			return nil
		}
		return fmt.Errorf("failed to get lesson progress: %w", err)
	}

	// If already completed, nothing to do
	if progressPtr.Completed {
		return nil
	}

	progressPtr.Progress = 100
	progressPtr.Completed = true
	now := time.Now()
	progressPtr.CompletedAt = &now

	updatedProgress, err := s.lessonProgressRepo.Update(ctx, *progressPtr)
	if err != nil {
		return fmt.Errorf("failed to update lesson progress for completion: %w", err)
	}

	// Publish event
	if err := s.eventBus.Publish(ctx, learningevent.NewLearningProgressUpdatedEvent(userID, nil, 0, 0, 0, 0)); err != nil {
		slog.ErrorContext(ctx, "Failed to publish lesson completed event after update", "error", err, "progressID", updatedProgress.ID)
	}

	return nil
}

// GetUserLessonProgressList 获取用户的课程进度列表
func (s *LessonServiceImpl) GetUserLessonProgressList(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error) {
	return s.lessonProgressRepo.FindByUser(ctx, userID)
}

// GetUserCompletedLessons 获取用户已完成的课程
func (s *LessonServiceImpl) GetUserCompletedLessons(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error) {
	return s.lessonProgressRepo.GetCompletedLessons(ctx, userID)
}

// GetUserInProgressLessons 获取用户正在进行的课程
func (s *LessonServiceImpl) GetUserInProgressLessons(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error) {
	return s.lessonProgressRepo.GetInProgressLessons(ctx, userID)
}

// GetFavoriteLessons 获取用户收藏的课程 - Placeholder implementation
func (s *LessonServiceImpl) GetFavoriteLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error) {
	// TODO: Implement actual logic using lessonRepo
	return []*entity.Lesson{}, nil
}

// ToggleFavoriteLesson 切换课程收藏状态 - Placeholder implementation
func (s *LessonServiceImpl) ToggleFavoriteLesson(ctx context.Context, userID uuid.UUID, lessonID string, isFavorite bool) (map[string]bool, error) {
	// TODO: Implement actual logic using lessonRepo
	return map[string]bool{"isFavorite": isFavorite}, nil
}

// Helper function to convert entity.Lesson to lessonDomainEntity.Lesson
// TODO: This is a temporary solution to resolve build errors due to two different Lesson structs.
// A proper reconciliation of these types is needed.
func toLessonDomainEntity(sourceLesson *entity.Lesson) *lessonDomainEntity.Lesson {
	if sourceLesson == nil {
		return nil
	}

	// TODO: Fix type conversion issues
	// For now, return a minimal conversion to avoid compilation errors
	targetLesson := &lessonDomainEntity.Lesson{
		ID:          sourceLesson.ID,
		Title:       sourceLesson.Title,
		Description: sourceLesson.Description,
		Content:     sourceLesson.Content,
		AuthorID:    sourceLesson.AuthorID,
		CreatedAt:   sourceLesson.CreatedAt,
		UpdatedAt:   sourceLesson.UpdatedAt,
		// TODO: Fix these type conversions
		// Level: lessonDomainEntity.Level(sourceLesson.Level),
		// Tags: pq.StringArray(sourceLesson.Tags),
	}

	// TODO: Fix Duration conversion
	// if sourceLesson.Duration != nil {
	//     targetLesson.Duration = &lessonDomainEntity.Duration{Minutes: sourceLesson.Duration.Minutes()}
	// }

	// Mapping Status to IsPublished and PublishedAt
	if sourceLesson.Status == entity.PublishedStatus {
		targetLesson.IsPublished = true
		publishedAt := sourceLesson.UpdatedAt
		targetLesson.PublishedAt = &publishedAt
	} else {
		targetLesson.IsPublished = false
		targetLesson.PublishedAt = nil
	}

	return targetLesson
}
