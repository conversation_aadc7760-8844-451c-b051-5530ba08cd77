package impl

// Helper functions shared across service implementations

// getSimpleQuestionContent generates simple question content for assessments
func getSimpleQuestionContent(skill string, number int) string {
	switch skill {
	case "vocabulary":
		return "詞彙問題 " + string(rune('0'+number))
	case "grammar":
		return "語法問題 " + string(rune('0'+number))
	case "listening":
		return "聽力問題 " + string(rune('0'+number))
	case "speaking":
		return "口語問題 " + string(rune('0'+number))
	default:
		return "綜合問題 " + string(rune('0'+number))
	}
}

// getSimpleQuestionOptions generates simple question options
func getSimpleQuestionOptions(skill string) []string {
	return []string{"選項A", "選項B", "選項C", "選項D"}
}

// getSimpleQuestionAnswer generates simple question answer
func getSimpleQuestionAnswer(skill string) string {
	return "選項A" // Simplified: always A
}

// getSkillTitle returns a human-readable title for a skill
func getSkillTitle(skill string) string {
	switch skill {
	case "vocabulary":
		return "詞彙"
	case "grammar":
		return "語法"
	case "listening":
		return "聽力"
	case "speaking":
		return "口語"
	default:
		return "綜合"
	}
}
