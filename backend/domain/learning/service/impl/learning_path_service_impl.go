package impl

import (
	"context"
	"errors"
	"languagelearning/domain/core/event"
	learningevent "languagelearning/domain/learning/event"
	"languagelearning/domain/learning/repository"
	"languagelearning/domain/learning/service"
	"languagelearning/models"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LearningPathServiceImpl handles learning path functionality using repositories
type LearningPathServiceImpl struct {
	learningPathRepo       repository.ModelsLearningPathRepository
	learningPathLessonRepo repository.ModelsLearningPathLessonRepository
	lessonRepo             repository.ModelsLessonRepository
	evaluationRepo         repository.EvaluationRepository
	eventBus               event.EventBus
}

// NewLearningPathService creates a new learning path service
func NewLearningPathService(
	learningPathRepo repository.ModelsLearningPathRepository,
	learningPathLessonRepo repository.ModelsLearningPathLessonRepository,
	lessonRepo repository.ModelsLessonRepository,
	evaluationRepo repository.EvaluationRepository,
	eventBus event.EventBus,
) service.LearningPathService {
	return &LearningPathServiceImpl{
		learningPathRepo:       learningPathRepo,
		learningPathLessonRepo: learningPathLessonRepo,
		lessonRepo:             lessonRepo,
		evaluationRepo:         evaluationRepo,
		eventBus:               eventBus,
	}
}

// GetLearningPathsForUser retrieves learning paths for a user
func (s *LearningPathServiceImpl) GetLearningPathsForUser(userID uuid.UUID) ([]models.LearningPathSummary, error) {
	ctx := context.Background()

	// Get learning paths for the user
	paths, err := s.learningPathRepo.FindByUserID(ctx, userID)
	if err != nil {
		log.Printf("Failed to find learning paths for user %s: %v", userID, err)
		return nil, errors.New("failed to retrieve learning paths")
	}

	// Convert to summaries
	var summaries []models.LearningPathSummary
	for _, path := range paths {
		// Get lesson statistics for this path
		stats, err := s.learningPathLessonRepo.GetCompletionStats(ctx, path.ID)
		if err != nil {
			log.Printf("Failed to get completion stats for path %s: %v", path.ID, err)
			// Continue with default values
			stats = &models.LearningPathStats{
				TotalLessons:     0,
				CompletedLessons: 0,
			}
		}

		summary := models.LearningPathSummary{
			ID:                path.ID,
			Title:             path.Title,
			Description:       path.Description,
			Status:            path.Status,
			Level:             path.Level,
			FocusAreas:        path.FocusAreas,
			EstimatedDuration: path.EstimatedDuration,
			Progress:          path.Progress,
			StartDate:         path.StartDate,
			CompletedDate:     path.CompletedDate,
			TotalLessons:      int(stats.TotalLessons),
			CompletedLessons:  int(stats.CompletedLessons),
			CreatedAt:         path.CreatedAt,
		}
		summaries = append(summaries, summary)
	}

	return summaries, nil
}

// GetLearningPath retrieves a specific learning path with lessons
func (s *LearningPathServiceImpl) GetLearningPath(userID, pathID uuid.UUID) (*models.LearningPath, error) {
	ctx := context.Background()

	// Get the learning path with lessons
	path, err := s.learningPathRepo.FindByUserIDWithLessons(ctx, userID, pathID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("learning path not found")
		}
		log.Printf("Failed to find learning path: userID=%s, pathID=%s, error=%v", userID, pathID, err)
		return nil, errors.New("failed to retrieve learning path")
	}

	return path, nil
}

// CreateLearningPathFromRequest creates a new learning path for a user
func (s *LearningPathServiceImpl) CreateLearningPathFromRequest(userID uuid.UUID, request models.CreateLearningPathRequest) (*models.LearningPath, error) {
	ctx := context.Background()

	// Create the learning path
	path := models.LearningPath{
		ID:                uuid.New(),
		UserID:            userID,
		Title:             request.Title,
		Description:       request.Description,
		Level:             request.Level,
		FocusAreas:        request.FocusAreas,
		EstimatedDuration: request.EstimatedDuration,
		Status:            models.PathActive,
		Progress:          0,
		StartDate:         time.Now(),
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Save the learning path
	createdPath, err := s.learningPathRepo.Create(ctx, path)
	if err != nil {
		log.Printf("Failed to create learning path: userID=%s, error=%v", userID, err)
		return nil, errors.New("failed to create learning path")
	}

	// Add lessons to the learning path based on level and focus areas
	err = s.addLessonsToPath(ctx, createdPath.ID, request.Level, request.FocusAreas)
	if err != nil {
		log.Printf("Failed to add lessons to learning path: pathID=%s, error=%v", createdPath.ID, err)
		// Don't return error here, the path was created successfully
	}

	// Publish learning path updated event (creation is a type of update)
	if s.eventBus != nil {
		pathUpdatedEvent := learningevent.NewLearningPathUpdatedEvent(
			createdPath.ID, userID, nil, nil,
		)
		if err := s.eventBus.Publish(ctx, pathUpdatedEvent); err != nil {
			log.Printf("Failed to publish learning path updated event: %v", err)
			// Don't return error, as the main operation succeeded
		}
	}

	log.Printf("Created learning path: pathID=%s, userID=%s, title=%s", createdPath.ID, userID, request.Title)
	return createdPath, nil
}

// addLessonsToPath adds appropriate lessons to a learning path
func (s *LearningPathServiceImpl) addLessonsToPath(ctx context.Context, pathID uuid.UUID, level models.LessonLevel, focusAreas []string) error {
	var allLessons []models.Lesson

	// Get lessons for each focus area
	for _, area := range focusAreas {
		lessons, err := s.lessonRepo.FindByLevelAndCategory(ctx, level, area)
		if err != nil {
			log.Printf("Failed to find lessons for level %s and category %s: %v", level, area, err)
			continue
		}
		allLessons = append(allLessons, lessons...)
	}

	// If no specific lessons found, get general lessons for the level
	if len(allLessons) == 0 {
		lessons, err := s.lessonRepo.FindByLevelWithLimit(ctx, level, 10)
		if err != nil {
			return err
		}
		allLessons = lessons
	}

	// Add lessons to the learning path
	for i, lesson := range allLessons {
		pathLesson := models.LearningPathLesson{
			LearningPathID: pathID,
			LessonID:       lesson.ID,
			Order:          i + 1,
			IsRequired:     true,
			IsCompleted:    false,
		}

		_, err := s.learningPathLessonRepo.Create(ctx, pathLesson)
		if err != nil {
			log.Printf("Failed to add lesson to path: pathID=%s, lessonID=%s, error=%v", pathID, lesson.ID, err)
			// Continue with other lessons
		}
	}

	log.Printf("Added %d lessons to learning path %s", len(allLessons), pathID)
	return nil
}

// UpdateLearningPathProgress updates the progress of a learning path
func (s *LearningPathServiceImpl) UpdateLearningPathProgress(userID, pathID uuid.UUID) error {
	ctx := context.Background()

	// Get completion statistics
	stats, err := s.learningPathLessonRepo.GetCompletionStats(ctx, pathID)
	if err != nil {
		log.Printf("Failed to get completion stats: pathID=%s, error=%v", pathID, err)
		return errors.New("failed to get completion statistics")
	}

	// Calculate progress percentage
	progress := 0
	if stats.TotalLessons > 0 {
		progress = int((stats.CompletedLessons * 100) / stats.TotalLessons)
	}

	// Update the learning path progress
	err = s.learningPathRepo.UpdateProgress(ctx, pathID, progress)
	if err != nil {
		log.Printf("Failed to update learning path progress: pathID=%s, error=%v", pathID, err)
		return errors.New("failed to update progress")
	}

	// Mark as completed if all lessons are done
	if progress == 100 {
		err = s.learningPathRepo.MarkAsCompleted(ctx, pathID)
		if err != nil {
			log.Printf("Failed to mark learning path as completed: pathID=%s, error=%v", pathID, err)
			// Don't return error here, progress update was successful
		}
	}

	log.Printf("Updated learning path progress: pathID=%s, progress=%d%%", pathID, progress)
	return nil
}

// CompleteLessonInPath marks a lesson as completed in a learning path
func (s *LearningPathServiceImpl) CompleteLessonInPath(userID, pathID, lessonID uuid.UUID) error {
	ctx := context.Background()

	// Mark the lesson as completed
	err := s.learningPathLessonRepo.MarkLessonCompleted(ctx, pathID, lessonID)
	if err != nil {
		log.Printf("Failed to mark lesson as completed: pathID=%s, lessonID=%s, error=%v", pathID, lessonID, err)
		return errors.New("failed to mark lesson as completed")
	}

	// Publish lesson completed event
	if s.eventBus != nil {
		lessonCompletedEvent := learningevent.NewLessonCompletedEvent(
			lessonID, userID, nil, nil, "completed",
		)
		if err := s.eventBus.Publish(ctx, lessonCompletedEvent); err != nil {
			log.Printf("Failed to publish lesson completed event: %v", err)
			// Don't return error, as the main operation succeeded
		}
	}

	// Update the overall learning path progress
	err = s.UpdateLearningPathProgress(userID, pathID)
	if err != nil {
		log.Printf("Failed to update learning path progress after lesson completion: pathID=%s, error=%v", pathID, err)
		// Don't return error here, lesson was marked as completed successfully
	}

	log.Printf("Completed lesson in learning path: pathID=%s, lessonID=%s", pathID, lessonID)
	return nil
}

// DeleteLearningPath deletes a learning path
func (s *LearningPathServiceImpl) DeleteLearningPath(userID, pathID uuid.UUID) error {
	ctx := context.Background()

	// Verify the learning path belongs to the user
	path, err := s.learningPathRepo.FindByUserIDWithLessons(ctx, userID, pathID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("learning path not found")
		}
		log.Printf("Failed to find learning path for deletion: userID=%s, pathID=%s, error=%v", userID, pathID, err)
		return errors.New("failed to verify learning path")
	}

	// Delete the learning path (this should cascade delete lessons)
	err = s.learningPathRepo.Delete(ctx, pathID)
	if err != nil {
		log.Printf("Failed to delete learning path: pathID=%s, error=%v", pathID, err)
		return errors.New("failed to delete learning path")
	}

	log.Printf("Deleted learning path: pathID=%s, userID=%s, title=%s", pathID, userID, path.Title)
	return nil
}

// AddLessonToPath adds a lesson to a learning path
func (s *LearningPathServiceImpl) AddLessonToPath(userID, pathID uuid.UUID, lessonID uuid.UUID, order int, isRequired bool) (*models.LearningPathLesson, error) {
	ctx := context.Background()

	// Verify the learning path belongs to the user
	_, err := s.learningPathRepo.FindByUserIDWithLessons(ctx, userID, pathID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("learning path not found")
		}
		return nil, errors.New("failed to verify learning path")
	}

	// Verify the lesson exists
	_, err = s.lessonRepo.FindByID(ctx, lessonID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("lesson not found")
		}
		return nil, errors.New("failed to verify lesson")
	}

	// Create the learning path lesson
	pathLesson := models.LearningPathLesson{
		LearningPathID: pathID,
		LessonID:       lessonID,
		Order:          order,
		IsRequired:     isRequired,
		IsCompleted:    false,
	}

	// Save the learning path lesson
	createdPathLesson, err := s.learningPathLessonRepo.Create(ctx, pathLesson)
	if err != nil {
		log.Printf("Failed to add lesson to path: pathID=%s, lessonID=%s, error=%v", pathID, lessonID, err)
		return nil, errors.New("failed to add lesson to path")
	}

	log.Printf("Added lesson to learning path: pathID=%s, lessonID=%s, order=%d", pathID, lessonID, order)
	return createdPathLesson, nil
}

// Simplified implementations for interface compatibility

// CreateLearningPath creates a new learning path (interface method)
func (s *LearningPathServiceImpl) CreateLearningPath(userID uuid.UUID, title, description, level string, focusAreas []string, estimatedDuration int, evaluationID uuid.UUID) (*models.LearningPath, error) {
	request := models.CreateLearningPathRequest{
		Title:             title,
		Description:       description,
		Level:             models.LessonLevel(level),
		FocusAreas:        focusAreas,
		EstimatedDuration: estimatedDuration,
	}
	return s.CreateLearningPathFromRequest(userID, request)
}

// UpdateLearningPath updates a learning path (interface method)
func (s *LearningPathServiceImpl) UpdateLearningPath(userID, pathID uuid.UUID, title, description, status, level string, focusAreas []string, estimatedDuration int) (*models.LearningPath, error) {
	// TODO: Implement update logic using repository
	return nil, errors.New("not implemented")
}

// GetLearningPaths with status filter (interface method)
func (s *LearningPathServiceImpl) GetLearningPaths(userID uuid.UUID, status string) ([]models.LearningPathSummary, error) {
	// For now, ignore status filter and return all paths
	return s.GetLearningPathsForUser(userID)
}

// GetLearningPathDetail retrieves a specific learning path (interface method)
func (s *LearningPathServiceImpl) GetLearningPathDetail(userID, pathID uuid.UUID) (*models.LearningPath, error) {
	return s.GetLearningPath(userID, pathID)
}

// Placeholder implementations for remaining interface methods
func (s *LearningPathServiceImpl) AdjustLearningPathBasedOnAssessment(userID, pathID uuid.UUID, evalID uuid.UUID) error {
	return errors.New("not implemented")
}

func (s *LearningPathServiceImpl) CreateLearningPathFromRecommendation(userID, evalID uuid.UUID) (*models.LearningPath, error) {
	return nil, errors.New("not implemented")
}

func (s *LearningPathServiceImpl) SchedulePeriodicAssessment(userID, pathID uuid.UUID) (*models.Evaluation, error) {
	return nil, errors.New("not implemented")
}

func (s *LearningPathServiceImpl) GetRecommendedLearningPaths(userID, evalID uuid.UUID) ([]models.LearningPathRecommendation, error) {
	return nil, errors.New("not implemented")
}

func (s *LearningPathServiceImpl) RecommendNextLearningPath(userID, pathID uuid.UUID) (*models.LearningPathRecommendation, error) {
	return nil, errors.New("not implemented")
}

func (s *LearningPathServiceImpl) GetNextExercise(userID, pathID uuid.UUID) (gin.H, error) {
	return nil, errors.New("not implemented")
}

func (s *LearningPathServiceImpl) CompleteExercise(userID, pathID uuid.UUID, lessonIDStr string) (*models.LearningPath, error) {
	return nil, errors.New("not implemented")
}

func (s *LearningPathServiceImpl) AutoUpdateLearningPathProgress(userID, pathID uuid.UUID) error {
	return s.UpdateLearningPathProgress(userID, pathID)
}

func (s *LearningPathServiceImpl) AutoUpdateLessonCompletion(userID, pathID uuid.UUID) error {
	return errors.New("not implemented")
}

func (s *LearningPathServiceImpl) UpdateExerciseSetBasedOnResults(userID, pathID uuid.UUID) error {
	return errors.New("not implemented")
}
