package impl

import (
	"context"
	"errors"
	"languagelearning/domain/core/event"
	learningevent "languagelearning/domain/learning/event"
	"languagelearning/domain/learning/repository"
	"languagelearning/domain/learning/service"
	"languagelearning/models"
	"log"
	"time"

	"github.com/google/uuid"
)

// AdaptiveLearningServiceImpl handles the adaptive learning functionality using repositories
type AdaptiveLearningServiceImpl struct {
	learningPathRepo     repository.ModelsLearningPathRepository
	evaluationRepo       repository.EvaluationRepository
	evaluationResultRepo repository.EvaluationResultRepository
	lessonRepo           repository.ModelsLessonRepository
	wordRepo             repository.WordRepository
	eventBus             event.EventBus
}

// NewAdaptiveLearningService creates a new adaptive learning service
func NewAdaptiveLearningService(
	learningPathRepo repository.ModelsLearningPathRepository,
	evaluationRepo repository.EvaluationRepository,
	evaluationResultRepo repository.EvaluationResultRepository,
	lessonRepo repository.ModelsLessonRepository,
	wordRepo repository.WordRepository,
	eventBus event.EventBus,
) service.AdaptiveLearningService {
	return &AdaptiveLearningServiceImpl{
		learningPathRepo:     learningPathRepo,
		evaluationRepo:       evaluationRepo,
		evaluationResultRepo: evaluationResultRepo,
		lessonRepo:           lessonRepo,
		wordRepo:             wordRepo,
		eventBus:             eventBus,
	}
}

// AutoUpdateLearningPathProgress automatically updates the progress of a learning path
func (s *AdaptiveLearningServiceImpl) AutoUpdateLearningPathProgress(userID uuid.UUID, pathID uuid.UUID) error {
	ctx := context.Background()

	// Get the learning path with lessons
	path, err := s.learningPathRepo.FindByUserIDWithLessons(ctx, userID, pathID)
	if err != nil {
		log.Printf("Failed to find learning path: userID=%s, pathID=%s, error=%v", userID, pathID, err)
		return errors.New("learning path not found")
	}

	// Count total and completed lessons
	totalLessons := len(path.Lessons)
	completedLessons := 0
	for _, lesson := range path.Lessons {
		if lesson.IsCompleted {
			completedLessons++
		}
	}

	// Calculate progress percentage
	progress := 0
	if totalLessons > 0 {
		progress = (completedLessons * 100) / totalLessons
	}

	// Update the learning path progress
	path.Progress = progress
	path.UpdatedAt = time.Now()

	// Mark as completed if all lessons are done
	if progress == 100 && path.Status != models.PathCompleted {
		path.Status = models.PathCompleted
		path.CompletedDate = time.Now()
	}

	// Save the learning path
	_, err = s.learningPathRepo.Update(ctx, *path)
	if err != nil {
		log.Printf("Failed to update learning path progress: pathID=%s, error=%v", pathID, err)
		return errors.New("failed to update learning path progress")
	}

	// Publish learning path progress updated event
	if s.eventBus != nil {
		progressEvent := learningevent.NewLearningPathProgressUpdatedEvent(
			pathID, userID, nil, 0, 0, 0, totalLessons,
		)
		if err := s.eventBus.Publish(ctx, progressEvent); err != nil {
			log.Printf("Failed to publish learning path progress updated event: %v", err)
			// Don't return error, as the main operation succeeded
		}
	}

	log.Printf("Updated learning path progress: pathID=%s, progress=%d%%", pathID, progress)
	return nil
}

// AutoUpdateLessonCompletion automatically updates the completion status of lessons in a learning path
func (s *AdaptiveLearningServiceImpl) AutoUpdateLessonCompletion(userID uuid.UUID, pathID uuid.UUID) error {
	// TODO: This requires a LessonProgressRepository to track user lesson progress
	// For now, we'll implement a simplified version that just updates the learning path progress
	log.Printf("AutoUpdateLessonCompletion called for userID=%s, pathID=%s", userID, pathID)

	// Update the learning path progress based on current lesson completion status
	return s.AutoUpdateLearningPathProgress(userID, pathID)
}

// SchedulePeriodicAssessment schedules a periodic assessment for a user
func (s *AdaptiveLearningServiceImpl) SchedulePeriodicAssessment(userID uuid.UUID, pathID uuid.UUID) (*models.Evaluation, error) {
	ctx := context.Background()

	// Get the learning path
	path, err := s.learningPathRepo.FindByID(ctx, pathID)
	if err != nil {
		log.Printf("Failed to find learning path: pathID=%s, error=%v", pathID, err)
		return nil, errors.New("learning path not found")
	}

	// Check if there's already a scheduled assessment
	existingEvals, err := s.evaluationRepo.FindByUserIDAndType(ctx, userID, models.EvalProgress)
	if err == nil && len(existingEvals) > 0 {
		// Return the first existing assessment that's not completed
		for _, eval := range existingEvals {
			if !eval.IsCompleted {
				log.Printf("Found existing assessment: evalID=%s", eval.ID)
				return &eval, nil
			}
		}
	}

	// Create a new periodic assessment
	evaluation := models.Evaluation{
		ID:             uuid.New(),
		UserID:         userID,
		Type:           models.EvalProgress,
		Title:          "進度評估",
		Description:    "評估您在當前學習路徑中的進度，幫助調整學習計劃。",
		TotalQuestions: 10, // Simplified: 10 questions
		PassingScore:   70,
		Duration:       15, // 15 minutes
		IsCompleted:    false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Create a simple assessment based on the learning path focus areas
	sections := s.createSimpleAssessmentSections(path.FocusAreas)
	evaluation.Sections = sections

	// Save the evaluation
	createdEval, err := s.evaluationRepo.Create(ctx, evaluation)
	if err != nil {
		log.Printf("Failed to create evaluation: error=%v", err)
		return nil, errors.New("failed to create assessment")
	}

	log.Printf("Created periodic assessment: evalID=%s, userID=%s", createdEval.ID, userID)
	return createdEval, nil
}

// createSimpleAssessmentSections creates simplified assessment sections
func (s *AdaptiveLearningServiceImpl) createSimpleAssessmentSections(focusAreas []string) []models.EvalSection {
	var sections []models.EvalSection

	for _, area := range focusAreas {
		section := models.EvalSection{
			Title:  getSkillTitle(area),
			Skill:  area,
			Weight: 100 / len(focusAreas), // Equal weight for all areas
		}

		// Create simple questions for this section
		questions := s.createSimpleQuestions(area, 2) // 2 questions per area
		section.Questions = questions
		sections = append(sections, section)
	}

	return sections
}

// createSimpleQuestions creates simple questions for a skill area
func (s *AdaptiveLearningServiceImpl) createSimpleQuestions(skill string, count int) []models.EvalQuestion {
	var questions []models.EvalQuestion

	for i := 0; i < count; i++ {
		question := models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       getSimpleQuestionContent(skill, i+1),
			Options:       getSimpleQuestionOptions(skill),
			CorrectAnswer: getSimpleQuestionAnswer(skill),
			Points:        5,
		}
		questions = append(questions, question)
	}

	return questions
}

// Helper functions for simple question generation

// Helper functions are defined in helpers.go

// AdjustLearningPathBasedOnAssessment adjusts a learning path based on assessment results
func (s *AdaptiveLearningServiceImpl) AdjustLearningPathBasedOnAssessment(userID uuid.UUID, pathID uuid.UUID, evalID uuid.UUID) error {
	ctx := context.Background()

	// Get the learning path
	path, err := s.learningPathRepo.FindByUserIDWithLessons(ctx, userID, pathID)
	if err != nil {
		log.Printf("Failed to find learning path: pathID=%s, error=%v", pathID, err)
		return errors.New("learning path not found")
	}

	// Get the evaluation result
	evalResult, err := s.evaluationResultRepo.FindByEvaluationIDAndUserID(ctx, evalID, userID)
	if err != nil {
		log.Printf("Failed to find evaluation result: evalID=%s, userID=%s, error=%v", evalID, userID, err)
		return errors.New("evaluation result not found")
	}

	// Analyze the results and identify weak areas
	weakAreas := s.identifyWeakAreas(evalResult)

	if len(weakAreas) == 0 {
		log.Printf("No weak areas identified for user %s", userID)
		return nil
	}

	// Add additional lessons for weak areas
	for _, area := range weakAreas {
		err := s.addLessonsForWeakArea(ctx, path, area)
		if err != nil {
			log.Printf("Failed to add lessons for weak area %s: %v", area, err)
			// Continue with other areas even if one fails
		}
	}

	// Update the learning path
	path.UpdatedAt = time.Now()
	_, err = s.learningPathRepo.Update(ctx, *path)
	if err != nil {
		log.Printf("Failed to update learning path: pathID=%s, error=%v", pathID, err)
		return errors.New("failed to update learning path")
	}

	log.Printf("Adjusted learning path based on assessment: pathID=%s, weakAreas=%v", pathID, weakAreas)
	return nil
}

// identifyWeakAreas analyzes evaluation results to identify weak skill areas
func (s *AdaptiveLearningServiceImpl) identifyWeakAreas(evalResult *models.EvaluationResult) []string {
	var weakAreas []string

	// Simple logic: if overall score is below 70%, consider all focus areas as weak
	// In a real implementation, this would analyze section scores
	if evalResult.OverallScore < 70 {
		// For simplification, return common weak areas
		weakAreas = []string{"vocabulary", "grammar"}
	}

	return weakAreas
}

// addLessonsForWeakArea adds lessons to help with a specific weak area
func (s *AdaptiveLearningServiceImpl) addLessonsForWeakArea(ctx context.Context, path *models.LearningPath, area string) error {
	// Find lessons that match the user's level and focus on the weak area
	lessons, err := s.lessonRepo.FindByLevelAndCategory(ctx, path.Level, area)
	if err != nil {
		return err
	}

	// Limit to 2 additional lessons per weak area
	maxLessons := 2
	if len(lessons) > maxLessons {
		lessons = lessons[:maxLessons]
	}

	// TODO: Add lessons to the learning path
	// This would require a LearningPathLessonRepository
	log.Printf("Would add %d lessons for weak area %s to path %s", len(lessons), area, path.ID)

	return nil
}

// RecommendNextLearningPath recommends the next learning path after completing the current one
func (s *AdaptiveLearningServiceImpl) RecommendNextLearningPath(userID uuid.UUID, completedPathID uuid.UUID) (*models.LearningPathRecommendation, error) {
	ctx := context.Background()

	// Get the completed learning path
	completedPath, err := s.learningPathRepo.FindByID(ctx, completedPathID)
	if err != nil {
		log.Printf("Failed to find completed learning path: pathID=%s, error=%v", completedPathID, err)
		return nil, errors.New("completed learning path not found")
	}

	// Determine the next level
	nextLevel := s.getNextLevel(completedPath.Level)

	// Find lessons for the next level
	lessons, err := s.lessonRepo.FindByLevelWithLimit(ctx, nextLevel, 5)
	if err != nil {
		log.Printf("Failed to find lessons for next level: level=%s, error=%v", nextLevel, err)
		return nil, errors.New("failed to find lessons for next level")
	}

	// Create recommendation
	recommendation := &models.LearningPathRecommendation{
		Title:                s.getRecommendationTitle(nextLevel),
		Description:          s.getRecommendationDescription(nextLevel),
		Level:                nextLevel,
		FocusAreas:           completedPath.FocusAreas, // Keep the same focus areas
		EstimatedDuration:    21,                       // 3 weeks
		SampleLessons:        lessons,
		RecommendationReason: "基於您完成的 " + string(completedPath.Level) + " 級別路徑，我們建議您進階到 " + string(nextLevel) + " 級別。",
	}

	log.Printf("Created learning path recommendation: userID=%s, nextLevel=%s", userID, nextLevel)
	return recommendation, nil
}

// getNextLevel determines the next learning level
func (s *AdaptiveLearningServiceImpl) getNextLevel(currentLevel models.LessonLevel) models.LessonLevel {
	switch currentLevel {
	case models.LessonBeginner:
		return models.LessonIntermediate
	case models.LessonIntermediate:
		return models.LessonAdvanced
	case models.LessonAdvanced:
		// If already at advanced level, stay there
		return models.LessonAdvanced
	default:
		return models.LessonBeginner
	}
}

// getRecommendationTitle generates a title for the recommendation
func (s *AdaptiveLearningServiceImpl) getRecommendationTitle(level models.LessonLevel) string {
	switch level {
	case models.LessonBeginner:
		return "初級英語學習路徑"
	case models.LessonIntermediate:
		return "中級英語學習路徑"
	case models.LessonAdvanced:
		return "高級英語學習路徑"
	default:
		return "英語學習路徑"
	}
}

// getRecommendationDescription generates a description for the recommendation
func (s *AdaptiveLearningServiceImpl) getRecommendationDescription(level models.LessonLevel) string {
	switch level {
	case models.LessonBeginner:
		return "適合初學者的英語學習路徑，涵蓋基礎詞彙和語法。"
	case models.LessonIntermediate:
		return "適合中級學習者的英語學習路徑，提升聽說讀寫能力。"
	case models.LessonAdvanced:
		return "適合高級學習者的英語學習路徑，精進語言運用技巧。"
	default:
		return "個性化的英語學習路徑，根據您的水平量身定制。"
	}
}
