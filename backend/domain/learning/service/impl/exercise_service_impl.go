package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"languagelearning/domain/core/event"
	exerciseentity "languagelearning/domain/exercise/entity"
	exerciseevent "languagelearning/domain/exercise/event"
	"languagelearning/domain/learning/repository"
	"languagelearning/domain/learning/service"
	baseRepo "languagelearning/domain/repository"

	"github.com/google/uuid"
)

// ExerciseServiceImpl 练习服务实现
type ExerciseServiceImpl struct {
	exerciseRepo repository.ExerciseRepository
	attemptRepo  repository.ExerciseAttemptRepository
	relationRepo repository.ExerciseRelationRepository
	eventBus     event.EventBus
}

// NewExerciseService 创建练习服务实例
func NewExerciseService(
	exerciseRepo repository.ExerciseRepository,
	attemptRepo repository.ExerciseAttemptRepository,
	relationRepo repository.ExerciseRelationRepository,
	eventBus event.EventBus,
) service.ExerciseService {
	return &ExerciseServiceImpl{
		exerciseRepo: exerciseRepo,
		attemptRepo:  attemptRepo,
		relationRepo: relationRepo,
		eventBus:     eventBus,
	}
}

// GetExerciseByID 根据ID获取练习
func (s *ExerciseServiceImpl) GetExerciseByID(ctx context.Context, id uuid.UUID) (*exerciseentity.Exercise, error) {
	exercise, err := s.exerciseRepo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return &exercise, nil
}

// GetExercises 获取练习列表
func (s *ExerciseServiceImpl) GetExercises(ctx context.Context, pageable baseRepo.Pageable, filters map[string]interface{}) (baseRepo.Page[exerciseentity.Exercise], error) {
	return s.exerciseRepo.FindPage(ctx, pageable)
}

// CreateExercise 创建练习
func (s *ExerciseServiceImpl) CreateExercise(ctx context.Context, exercise *exerciseentity.Exercise) (*exerciseentity.Exercise, error) {
	if exercise == nil {
		return nil, errors.New("exercise cannot be nil")
	}

	exercise.ID = uuid.New()
	exercise.CreatedAt = time.Now()
	exercise.UpdatedAt = time.Now()

	createdExercise, err := s.exerciseRepo.Create(ctx, *exercise)
	if err != nil {
		return nil, err
	}
	return &createdExercise, nil
}

// UpdateExercise 更新练习
func (s *ExerciseServiceImpl) UpdateExercise(ctx context.Context, exercise *exerciseentity.Exercise) (*exerciseentity.Exercise, error) {
	if exercise == nil {
		return nil, errors.New("exercise cannot be nil")
	}

	_, err := s.exerciseRepo.FindByID(ctx, exercise.ID)
	if err != nil {
		return nil, err
	}

	exercise.UpdatedAt = time.Now()
	updatedExercise, err := s.exerciseRepo.Update(ctx, *exercise)
	if err != nil {
		return nil, err
	}
	return &updatedExercise, nil
}

// DeleteExercise 删除练习
func (s *ExerciseServiceImpl) DeleteExercise(ctx context.Context, id uuid.UUID) error {
	return s.exerciseRepo.Delete(ctx, id)
}

// PublishExercise 发布练习
func (s *ExerciseServiceImpl) PublishExercise(ctx context.Context, id uuid.UUID) error {
	exercise, err := s.exerciseRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	exercise.IsPublished = true
	exercise.UpdatedAt = time.Now()
	_, err = s.exerciseRepo.Update(ctx, exercise)
	return err
}

// UnpublishExercise 取消发布练习
func (s *ExerciseServiceImpl) UnpublishExercise(ctx context.Context, id uuid.UUID) error {
	exercise, err := s.exerciseRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	exercise.IsPublished = false
	exercise.UpdatedAt = time.Now()
	_, err = s.exerciseRepo.Update(ctx, exercise)
	return err
}

// GetExercisesByType 获取指定类型的练习
func (s *ExerciseServiceImpl) GetExercisesByType(ctx context.Context, exerciseType exerciseentity.ExerciseType) ([]*exerciseentity.Exercise, error) {
	return s.exerciseRepo.FindByType(ctx, exerciseType)
}

// GetExercisesByDifficulty 获取指定难度的练习
func (s *ExerciseServiceImpl) GetExercisesByDifficulty(ctx context.Context, difficulty exerciseentity.ExerciseDifficulty) ([]*exerciseentity.Exercise, error) {
	return s.exerciseRepo.FindByDifficulty(ctx, difficulty)
}

// GetExercisesByLanguage 获取指定语言的练习
func (s *ExerciseServiceImpl) GetExercisesByLanguage(ctx context.Context, language string) ([]*exerciseentity.Exercise, error) {
	return s.exerciseRepo.FindByLanguage(ctx, language)
}

// SearchExercises 搜索练习
func (s *ExerciseServiceImpl) SearchExercises(ctx context.Context, query string, pageable baseRepo.Pageable) (baseRepo.Page[exerciseentity.Exercise], error) {
	return s.exerciseRepo.SearchPage(ctx, query, pageable)
}

// GetRandomExercises 获取随机练习
func (s *ExerciseServiceImpl) GetRandomExercises(ctx context.Context, count int, filters map[string]interface{}) ([]*exerciseentity.Exercise, error) {
	var difficulty exerciseentity.ExerciseDifficulty
	var exerciseType exerciseentity.ExerciseType

	if diffStr, ok := filters["difficulty"].(string); ok {
		newDifficulty, err := exerciseentity.NewExerciseDifficulty(diffStr)
		if err != nil {
			return nil, err
		}
		difficulty = *newDifficulty
	}

	if typeValue, ok := filters["type"]; ok {
		if typeStr, ok := typeValue.(string); ok {
			exerciseType = exerciseentity.ExerciseType(typeStr)
		}
	}

	return s.exerciseRepo.GetRandomExercises(ctx, count, difficulty, exerciseType)
}

// SubmitExerciseAttempt 提交练习尝试
func (s *ExerciseServiceImpl) SubmitExerciseAttempt(ctx context.Context, attempt *exerciseentity.ExerciseAttempt) (*exerciseentity.ExerciseAttempt, error) {
	if attempt == nil {
		return nil, errors.New("attempt cannot be nil")
	}

	attempt.ID = uuid.New()
	attempt.CreatedAt = time.Now()
	attempt.UpdatedAt = time.Now()

	savedAttempt, err := s.attemptRepo.Create(ctx, *attempt)
	if err != nil {
		return nil, err
	}

	// 发布练习尝试事件
	if s.eventBus != nil {
		// 获取练习以获取难度
		exercise, err := s.exerciseRepo.FindByID(ctx, attempt.ExerciseID)
		if err != nil {
			return nil, fmt.Errorf("failed to find exercise: %v", err)
		}

		// 确保Score和Duration不为nil
		if attempt.Score == nil || attempt.Duration == nil {
			return nil, fmt.Errorf("attempt score or duration is nil")
		}

		// 确保练习难度不为nil
		if exercise.Difficulty == nil {
			return nil, fmt.Errorf("exercise difficulty is nil")
		}

		// 转换core entity类型到exercise entity类型
		exerciseScore, err := exerciseentity.NewScore(attempt.Score.Value(), attempt.Score.MaxValue())
		if err != nil {
			return nil, fmt.Errorf("failed to create exercise score: %v", err)
		}

		exerciseDuration, err := exerciseentity.NewDuration(attempt.Duration.Minutes())
		if err != nil {
			return nil, fmt.Errorf("failed to create exercise duration: %v", err)
		}

		exerciseDifficulty, err := exerciseentity.NewExerciseDifficulty(exercise.Difficulty.Level())
		if err != nil {
			return nil, fmt.Errorf("failed to create exercise difficulty: %v", err)
		}

		exerciseAttemptedEvent := exerciseevent.NewExerciseAttemptedEvent(
			attempt.ExerciseID,
			attempt.UserID,
			*exerciseScore,
			*exerciseDuration,
			*exerciseDifficulty,
			attempt.IsCorrect,
		)
		if err := s.eventBus.Publish(ctx, exerciseAttemptedEvent); err != nil {
			// 记录错误但不影响主流程
			// TODO: 添加日志记录
		}
	}

	return &savedAttempt, nil
}

// GetUserExerciseAttempts 获取用户的练习尝试
func (s *ExerciseServiceImpl) GetUserExerciseAttempts(ctx context.Context, userID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error) {
	return s.attemptRepo.FindByUser(ctx, userID)
}

// GetUserExerciseStats 获取用户的练习统计
func (s *ExerciseServiceImpl) GetUserExerciseStats(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error) {
	return s.attemptRepo.GetUserStats(ctx, userID)
}

// CheckExerciseAnswer 检查练习答案
func (s *ExerciseServiceImpl) CheckExerciseAnswer(ctx context.Context, exerciseID uuid.UUID, userAnswer string) (bool, string, error) {
	exercise, err := s.exerciseRepo.FindByID(ctx, exerciseID)
	if err != nil {
		return false, "", err
	}

	isCorrect := exercise.CorrectAnswer == userAnswer
	explanation := exercise.Explanation

	// 创建尝试记录
	attempt := &exerciseentity.ExerciseAttempt{
		ID:         uuid.New(),
		ExerciseID: exerciseID,
		UserID:     uuid.Nil, // TODO: 从上下文中获取用户ID
		UserAnswer: userAnswer,
		IsCorrect:  isCorrect,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 保存尝试记录
	_, err = s.attemptRepo.Create(ctx, *attempt)
	if err != nil {
		return false, "", err
	}

	// 发布答案检查事件
	if s.eventBus != nil {
		// 创建分数和持续时间
		score, err := exerciseentity.NewScore(0, 10) // 假设满分10分
		if err != nil {
			return false, "", fmt.Errorf("failed to create score: %v", err)
		}

		duration, err := exerciseentity.NewDuration(0) // 0分钟
		if err != nil {
			return false, "", fmt.Errorf("failed to create duration: %v", err)
		}

		// 确保练习难度不为nil
		if exercise.Difficulty == nil {
			return false, "", fmt.Errorf("exercise difficulty is nil")
		}

		// 转换core entity难度到exercise entity难度
		exerciseDifficulty, err := exerciseentity.NewExerciseDifficulty(exercise.Difficulty.Level())
		if err != nil {
			return false, "", fmt.Errorf("failed to create exercise difficulty: %v", err)
		}

		answerCheckedEvent := exerciseevent.NewExerciseAttemptedEvent(
			exerciseID,
			attempt.UserID,
			*score,
			*duration,
			*exerciseDifficulty,
			attempt.IsCorrect,
		)
		if err := s.eventBus.Publish(ctx, answerCheckedEvent); err != nil {
			// 记录错误但不影响主流程
			// TODO: 添加日志记录
		}
	}

	return isCorrect, explanation, nil
}

// GetRelatedExercises 获取相关练习
func (s *ExerciseServiceImpl) GetRelatedExercises(ctx context.Context, exerciseID uuid.UUID, relationType string) ([]*exerciseentity.Exercise, error) {
	relations, err := s.relationRepo.FindBySourceExercise(ctx, exerciseID)
	if err != nil {
		return nil, err
	}

	var exercises []*exerciseentity.Exercise
	for _, relation := range relations {
		if relation.RelationType == relationType {
			exercise, err := s.exerciseRepo.FindByID(ctx, relation.TargetExerciseID)
			if err != nil {
				continue
			}
			exercises = append(exercises, &exercise)
		}
	}

	return exercises, nil
}

// CreateExerciseRelation 创建练习关系
func (s *ExerciseServiceImpl) CreateExerciseRelation(ctx context.Context, relation *exerciseentity.ExerciseRelation) (*exerciseentity.ExerciseRelation, error) {
	if relation == nil {
		return nil, errors.New("relation cannot be nil")
	}

	relation.ID = uuid.New()
	relation.CreatedAt = time.Now()
	relation.UpdatedAt = time.Now()

	createdRelation, err := s.relationRepo.Create(ctx, *relation)
	if err != nil {
		return nil, err
	}
	return &createdRelation, nil
}

// UpdateExerciseRelation 更新练习关系
func (s *ExerciseServiceImpl) UpdateExerciseRelation(ctx context.Context, relation *exerciseentity.ExerciseRelation) (*exerciseentity.ExerciseRelation, error) {
	if relation == nil {
		return nil, errors.New("relation cannot be nil")
	}

	_, err := s.relationRepo.FindByID(ctx, relation.ID)
	if err != nil {
		return nil, err
	}

	relation.UpdatedAt = time.Now()
	updatedRelation, err := s.relationRepo.Update(ctx, *relation)
	if err != nil {
		return nil, err
	}
	return &updatedRelation, nil
}

// DeleteExerciseRelation 删除练习关系
func (s *ExerciseServiceImpl) DeleteExerciseRelation(ctx context.Context, id uuid.UUID) error {
	return s.relationRepo.Delete(ctx, id)
}

// GetGrammarExercises retrieves grammar exercises with optional filtering
// This method has been removed as it uses direct GORM calls.
// Use GetExercisesByType with appropriate filters instead.

// SubmitGrammarAnswer processes a grammar exercise answer submission
// This method has been removed as it uses direct GORM calls.
// Use SubmitExerciseAttempt with appropriate exercise entity instead.

// GetListeningExercises retrieves listening exercises with optional filtering
// This method has been removed as it uses direct GORM calls.
// Use GetExercisesByType with appropriate filters instead.

// SubmitListeningAnswer processes a listening exercise answer submission
// This method has been removed as it uses direct GORM calls.
// Use SubmitExerciseAttempt with appropriate exercise entity instead.

// GetSpeakingExercises retrieves speaking exercises with optional filtering
// This method has been removed as it uses direct GORM calls.
// Use GetExercisesByType with appropriate filters instead.

// SubmitSpeakingAnswer processes a speaking exercise answer submission
// This method has been removed as it uses direct GORM calls.
// Use SubmitExerciseAttempt with appropriate exercise entity instead.

// GetWordExercises retrieves word exercises with optional filtering
// This method has been removed as it uses direct GORM calls.
// Use GetExercisesByType with appropriate filters instead.

// SubmitWordAnswer processes a word exercise answer submission
// This method has been removed as it uses direct GORM calls.
// Use SubmitExerciseAttempt with appropriate exercise entity instead.
