package impl

import (
	"context"
	"languagelearning/domain/learning/repository"
	"languagelearning/domain/learning/service"
	"languagelearning/models"
	"log"
	"time"

	"github.com/google/uuid"
)

// practiceService implements the PracticeService interface
type practiceService struct {
	practiceRepo repository.PracticeRepository
}

// NewPracticeService creates a new practice service
func NewPracticeService(practiceRepo repository.PracticeRepository) service.PracticeService {
	return &practiceService{
		practiceRepo: practiceRepo,
	}
}

// GetPracticeHistory retrieves the current user's practice session history
func (s *practiceService) GetPracticeHistory(userID uuid.UUID) ([]models.PracticeSession, error) {
	ctx := context.Background()
	sessions, err := s.practiceRepo.FindByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	return sessions, nil
}

// GetRecommendedPractice retrieves personalized practice recommendations for the current user
func (s *practiceService) GetRecommendedPractice(userID uuid.UUID) ([]models.RecommendedPractice, error) {
	ctx := context.Background()

	// Get the user's practice sessions
	sessions, err := s.practiceRepo.FindByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Analyze the user's practice history to determine recommendations
	typeCounts := make(map[string]int)
	for _, session := range sessions {
		typeCounts[session.Type]++
	}

	// Determine the least practiced type
	leastPracticedType := "vocabulary"
	leastPracticedCount := typeCounts["vocabulary"]

	if typeCounts["grammar"] < leastPracticedCount {
		leastPracticedType = "grammar"
		leastPracticedCount = typeCounts["grammar"]
	}

	if typeCounts["listening"] < leastPracticedCount {
		leastPracticedType = "listening"
		leastPracticedCount = typeCounts["listening"]
	}

	if typeCounts["speaking"] < leastPracticedCount {
		leastPracticedType = "speaking"
	}

	// Create recommendations
	var recommendations []models.RecommendedPractice

	// Recommendation based on least practiced type
	recommendations = append(recommendations, models.RecommendedPractice{
		Type:        leastPracticedType,
		Title:       "Improve your " + leastPracticedType + " skills",
		Description: "You haven't practiced " + leastPracticedType + " much lately. Let's focus on it!",
		Duration:    15,
		Difficulty:  models.Easy,
		Points:      50,
	})

	// Recommendation based on recent activity
	if len(sessions) > 0 {
		recommendations = append(recommendations, models.RecommendedPractice{
			Type:        "mixed",
			Title:       "Keep your streak going!",
			Description: "Continue your learning journey with mixed practice!",
			Duration:    10,
			Difficulty:  models.Easy,
			Points:      30,
		})
	} else {
		recommendations = append(recommendations, models.RecommendedPractice{
			Type:        "mixed",
			Title:       "Start a new streak",
			Description: "Start a new learning streak today!",
			Duration:    5,
			Difficulty:  models.Easy,
			Points:      20,
		})
	}

	// Recommendation for vocabulary
	recommendations = append(recommendations, models.RecommendedPractice{
		Type:        "vocabulary",
		Title:       "Expand your vocabulary",
		Description: "Learn new words to enhance your language skills",
		Duration:    10,
		Difficulty:  models.Medium,
		Points:      40,
	})

	return recommendations, nil
}

// SavePracticeSession saves a completed practice session for the current user
func (s *practiceService) SavePracticeSession(userID uuid.UUID, practiceType string, duration, score int) (*models.PracticeSession, error) {
	// Create a new practice session
	session := models.PracticeSession{
		UserID:    userID,
		Type:      practiceType,
		Duration:  duration,
		Score:     score,
		CreatedAt: time.Now(),
	}

	// Save the session
	ctx := context.Background()
	createdSession, err := s.practiceRepo.Create(ctx, session)
	if err != nil {
		return nil, err
	}

	// TODO: Update user statistics and learning path progress
	// This should be handled by separate services or through events
	log.Printf("Practice session created for user %s: type=%s, score=%d, duration=%d",
		userID, practiceType, score, duration)

	return createdSession, nil
}
