package impl

import (
	"context"
	"errors"
	"fmt"
	"languagelearning/domain/learning/repository"
	learningSvc "languagelearning/domain/learning/service"
	"languagelearning/domain/user/entity"
	userRepo "languagelearning/domain/user/repository"
	"languagelearning/models"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// WordService handles the word functionality
type WordService struct {
	wordRepo      repository.WordRepository
	userWordRepo  userRepo.UserWordRepository
	userStatsRepo userRepo.UserStatsRepository
}

// Ensure that WordService implements the WordService interface.
var _ learningSvc.WordService = (*WordService)(nil)

// NewWordService creates a new word service
func NewWordService(
	wordRepo repository.WordRepository,
	userWordRepo userRepo.UserWordRepository,
	userStatsRepo userRepo.UserStatsRepository,
) learningSvc.WordService {
	return &WordService{
		wordRepo:      wordRepo,
		userWordRepo:  userWordRepo,
		userStatsRepo: userStatsRepo,
	}
}

// GetWords retrieves words with optional filtering
func (s *WordService) GetWords(userID uuid.UUID, category, difficulty string) ([]models.UserWordResponse, error) {
	ctx := context.Background()

	var words []models.Word
	var err error

	// Get words based on filters
	if category != "" && difficulty != "" {
		words, err = s.wordRepo.FindByDifficultyAndCategory(ctx, models.Difficulty(difficulty), category)
	} else if category != "" {
		words, err = s.wordRepo.FindByCategory(ctx, category)
	} else if difficulty != "" {
		words, err = s.wordRepo.FindByDifficulty(ctx, models.Difficulty(difficulty))
	} else {
		words, err = s.wordRepo.FindAll(ctx)
	}

	if err != nil {
		return nil, err
	}

	// Get user's word progress from UserWord repository
	userWords, err := s.userWordRepo.FindByUserID(ctx, userID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Printf("Error fetching user words: userID=%s, error=%v", userID, err)
		// Continue with default values if there's an error
	}

	// Create a map for quick lookup of user word progress
	userWordMap := make(map[uuid.UUID]entity.UserWord)
	for _, uw := range userWords {
		userWordMap[uw.WordID] = uw
	}

	// Build response with user progress
	var response []models.UserWordResponse
	for _, word := range words {
		userWord, exists := userWordMap[word.ID]
		response = append(response, models.UserWordResponse{
			Word:       word,
			IsLearned:  exists && userWord.IsLearned,
			IsFavorite: exists && userWord.IsFavorite,
		})
	}

	return response, nil
}

// GetWordDetail retrieves detailed information about a specific word
func (s *WordService) GetWordDetail(userID, wordID uuid.UUID) (*models.UserWordResponse, error) {
	ctx := context.Background()

	// Find the word
	word, err := s.wordRepo.FindByID(ctx, wordID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("word not found")
		}
		return nil, fmt.Errorf("failed to find word: %w", err)
	}

	// Get user's word progress from UserWord repository
	userWord, err := s.userWordRepo.FindByUserAndWordID(ctx, userID, wordID)
	var isLearned, isFavorite bool

	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Printf("Error fetching user word: userID=%s, wordID=%s, error=%v", userID, wordID, err)
		}
		// Use default values if not found or error
		isLearned = false
		isFavorite = false
	} else {
		isLearned = userWord.IsLearned
		isFavorite = userWord.IsFavorite
	}

	response := &models.UserWordResponse{
		Word:       *word,
		IsLearned:  isLearned,
		IsFavorite: isFavorite,
	}

	return response, nil
}

// MarkWordAsLearned marks a word as learned for a user
func (s *WordService) MarkWordAsLearned(userID, wordID uuid.UUID) (gin.H, error) {
	ctx := context.Background()

	// Verify the word exists
	word, err := s.wordRepo.FindByID(ctx, wordID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Printf("Word not found: wordID=%s", wordID)
			return nil, errors.New("word not found")
		}
		log.Printf("Error finding word: %v", err)
		return nil, fmt.Errorf("failed to find word: %w", err)
	}

	// Check if user word record already exists
	userWord, err := s.userWordRepo.FindByUserAndWordID(ctx, userID, wordID)
	var wasAlreadyLearned bool

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new user word record
			newUserWord := &entity.UserWord{
				ID:         uuid.New(),
				UserID:     userID,
				WordID:     wordID,
				IsLearned:  true,
				IsFavorite: false,
			}

			createdUserWord, err := s.userWordRepo.Create(ctx, newUserWord)
			if err != nil {
				log.Printf("Failed to create user word: userID=%s, wordID=%s, error=%v", userID, wordID, err)
				return nil, errors.New("failed to mark word as learned")
			}

			log.Printf("Created new user word record: UserID=%s, WordID=%s", userID, wordID)
			userWord = *createdUserWord
			wasAlreadyLearned = false
		} else {
			log.Printf("Error finding user word: userID=%s, wordID=%s, error=%v", userID, wordID, err)
			return nil, errors.New("database error occurred")
		}
	} else {
		// Update existing user word record
		wasAlreadyLearned = userWord.IsLearned
		if !userWord.IsLearned {
			userWord.IsLearned = true
			err = s.userWordRepo.Update(ctx, &userWord)
			if err != nil {
				log.Printf("Failed to update user word: userID=%s, wordID=%s, error=%v", userID, wordID, err)
				return nil, errors.New("failed to update word status")
			}
			log.Printf("Updated user word record: UserID=%s, WordID=%s", userID, wordID)
		} else {
			log.Printf("Word already marked as learned: UserID=%s, WordID=%s", userID, wordID)
		}
	}

	// Update user statistics - increment vocabulary count only if this is a new learning
	if !wasAlreadyLearned {
		userStats, err := s.userStatsRepo.FindByUserID(ctx, userID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Create new user stats if not exists
				newUserStats := entity.UserStats{
					ID:              uuid.New(),
					UserID:          userID,
					VocabularyCount: 1,
				}
				_, err = s.userStatsRepo.Create(ctx, newUserStats)
				if err != nil {
					log.Printf("Failed to create user stats: userID=%s, error=%v", userID, err)
					// Don't return error here, word marking was successful
				} else {
					log.Printf("Created new user stats with vocabulary count: UserID=%s", userID)
				}
			} else {
				log.Printf("Error finding user stats: userID=%s, error=%v", userID, err)
				// Don't return error here, word marking was successful
			}
		} else {
			// Update existing user stats
			userStats.VocabularyCount++
			_, err = s.userStatsRepo.Update(ctx, userStats)
			if err != nil {
				log.Printf("Failed to update user stats: userID=%s, error=%v", userID, err)
				// Don't return error here, word marking was successful
			} else {
				log.Printf("Updated user stats vocabulary count: UserID=%s, Count=%d", userID, userStats.VocabularyCount)
			}
		}
	} else {
		log.Printf("Word was already learned, skipping stats update: UserID=%s, WordID=%s", userID, wordID)
	}

	return gin.H{
		"isLearned": true,
		"word":      word.Word,
		"message":   "Word marked as learned successfully",
	}, nil
}

// MarkWordAsFavorite marks a word as favorite for a user
func (s *WordService) MarkWordAsFavorite(userID, wordID uuid.UUID, isFavorite bool) (gin.H, error) {
	ctx := context.Background()

	// Verify the word exists
	word, err := s.wordRepo.FindByID(ctx, wordID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Printf("Word not found: wordID=%s", wordID)
			return nil, errors.New("word not found")
		}
		log.Printf("Error finding word: %v", err)
		return nil, fmt.Errorf("failed to find word: %w", err)
	}

	// Check if user word record already exists
	userWord, err := s.userWordRepo.FindByUserAndWordID(ctx, userID, wordID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new user word record
			newUserWord := &entity.UserWord{
				ID:         uuid.New(),
				UserID:     userID,
				WordID:     wordID,
				IsLearned:  false,
				IsFavorite: isFavorite,
			}

			_, err := s.userWordRepo.Create(ctx, newUserWord)
			if err != nil {
				log.Printf("Failed to create user word: userID=%s, wordID=%s, error=%v", userID, wordID, err)
				return nil, errors.New("failed to update favorite status")
			}

			log.Printf("Created new user word record with favorite status: UserID=%s, WordID=%s, IsFavorite=%t", userID, wordID, isFavorite)
		} else {
			log.Printf("Error finding user word: userID=%s, wordID=%s, error=%v", userID, wordID, err)
			return nil, errors.New("database error occurred")
		}
	} else {
		// Update existing user word record
		userWord.IsFavorite = isFavorite
		err = s.userWordRepo.Update(ctx, &userWord)
		if err != nil {
			log.Printf("Failed to update user word favorite status: userID=%s, wordID=%s, error=%v", userID, wordID, err)
			return nil, errors.New("failed to update favorite status")
		}
		log.Printf("Updated user word favorite status: UserID=%s, WordID=%s, IsFavorite=%t", userID, wordID, isFavorite)
	}

	action := "removed from"
	if isFavorite {
		action = "added to"
	}

	return gin.H{
		"isFavorite": isFavorite,
		"word":       word.Word,
		"message":    fmt.Sprintf("Word %s favorites successfully", action),
	}, nil
}
