package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"languagelearning/domain/core/event"
	exerciseentity "languagelearning/domain/exercise/entity"
	exerciseevent "languagelearning/domain/exercise/event"
	"languagelearning/domain/learning/repository"
	"languagelearning/domain/learning/service"
	"languagelearning/models"
	"languagelearning/utils/logger"

	"github.com/google/uuid"
)

// ExerciseRelationServiceImpl 练习关系服务实现 - 使用repository模式
type ExerciseRelationServiceImpl struct {
	exerciseRepo           repository.ExerciseRepository
	relationRepo           repository.ExerciseRelationRepository
	wordRepo               repository.WordRepository
	difficultyMetadataRepo repository.DifficultyMetadataRepository
	eventBus               event.EventBus
}

// NewExerciseRelationServiceImpl 创建练习关系服务实例
func NewExerciseRelationService(
	exerciseRepo repository.ExerciseRepository,
	relationRepo repository.ExerciseRelationRepository,
	wordRepo repository.WordRepository,
	difficultyMetadataRepo repository.DifficultyMetadataRepository,
	eventBus event.EventBus,
) service.ExerciseRelationService {
	return &ExerciseRelationServiceImpl{
		exerciseRepo:           exerciseRepo,
		relationRepo:           relationRepo,
		wordRepo:               wordRepo,
		difficultyMetadataRepo: difficultyMetadataRepo,
		eventBus:               eventBus,
	}
}

// GetRelatedExercises 获取相关练习
func (s *ExerciseRelationServiceImpl) GetRelatedExercises(ctx context.Context, exerciseID uuid.UUID, relationType string) ([]*exerciseentity.Exercise, error) {
	relations, err := s.relationRepo.FindBySourceExercise(ctx, exerciseID)
	if err != nil {
		return nil, err
	}

	var exercises []*exerciseentity.Exercise
	for _, relation := range relations {
		if relation.RelationType == relationType {
			exercise, err := s.exerciseRepo.FindByID(ctx, relation.TargetExerciseID)
			if err != nil {
				continue
			}
			exercises = append(exercises, &exercise)
		}
	}

	return exercises, nil
}

// CreateExerciseRelation 创建练习关系
func (s *ExerciseRelationServiceImpl) CreateExerciseRelation(ctx context.Context, relation *exerciseentity.ExerciseRelation) (*exerciseentity.ExerciseRelation, error) {
	if relation == nil {
		return nil, errors.New("relation cannot be nil")
	}

	relation.ID = uuid.New()
	relation.CreatedAt = time.Now()
	relation.UpdatedAt = time.Now()

	createdRelation, err := s.relationRepo.Create(ctx, *relation)
	if err != nil {
		return nil, err
	}
	return &createdRelation, nil
}

// UpdateExerciseRelation 更新练习关系
func (s *ExerciseRelationServiceImpl) UpdateExerciseRelation(ctx context.Context, relation *exerciseentity.ExerciseRelation) (*exerciseentity.ExerciseRelation, error) {
	if relation == nil {
		return nil, errors.New("relation cannot be nil")
	}

	_, err := s.relationRepo.FindByID(ctx, relation.ID)
	if err != nil {
		return nil, err
	}

	relation.UpdatedAt = time.Now()
	updatedRelation, err := s.relationRepo.Update(ctx, *relation)
	if err != nil {
		return nil, err
	}
	return &updatedRelation, nil
}

// DeleteExerciseRelation 删除练习关系
func (s *ExerciseRelationServiceImpl) DeleteExerciseRelation(ctx context.Context, id uuid.UUID) error {
	return s.relationRepo.Delete(ctx, id)
}

// UpdateExerciseSuccessRate 更新练习成功率
func (s *ExerciseRelationServiceImpl) UpdateExerciseSuccessRate(ctx context.Context, exerciseID uuid.UUID, exerciseType string, isSuccess bool) error {
	// 获取练习
	exercise, err := s.exerciseRepo.FindByID(ctx, exerciseID)
	if err != nil {
		return err
	}

	// 更新成功率
	if exercise.Stats == nil {
		exercise.Stats = &exerciseentity.ExerciseStats{
			TotalAttempts: 0,
			SuccessCount:  0,
			SuccessRate:   0,
		}
	}

	exercise.Stats.TotalAttempts++
	if isSuccess {
		exercise.Stats.SuccessCount++
	}
	exercise.Stats.SuccessRate = float64(exercise.Stats.SuccessCount) / float64(exercise.Stats.TotalAttempts)

	// 保存更新
	_, err = s.exerciseRepo.Update(ctx, exercise)
	if err != nil {
		return err
	}

	// 发布练习统计更新事件
	if s.eventBus != nil {
		statsUpdatedEvent := exerciseevent.NewExerciseStatsUpdatedEvent(
			exerciseID,
			exerciseType,
			exercise.Stats.SuccessRate,
			exercise.Stats.TotalAttempts,
		)
		if err := s.eventBus.Publish(ctx, statsUpdatedEvent); err != nil {
			// 记录错误但不影响主流程
			log := logger.DefaultLogger()
			log.Error(ctx, "Failed to publish exercise stats updated event", map[string]interface{}{
				"exerciseID":    exerciseID,
				"exerciseType":  exerciseType,
				"successRate":   exercise.Stats.SuccessRate,
				"totalAttempts": exercise.Stats.TotalAttempts,
				"error":         err.Error(),
			})
		}
	}

	return nil
}

// CreateRelation creates a relation between two exercises using repository pattern
func (s *ExerciseRelationServiceImpl) CreateRelation(sourceID, targetID uuid.UUID, sourceType, targetType string,
	relationType models.RelationType, strength models.RelationStrength, description string) (*models.ExerciseRelation, error) {

	// Validate source exercise exists
	if err := s.validateExerciseExists(sourceID, sourceType); err != nil {
		return nil, err
	}

	// Validate target exercise exists
	if err := s.validateExerciseExists(targetID, targetType); err != nil {
		return nil, err
	}

	// Check if relation already exists using repository
	existingRelation, err := s.relationRepo.FindBySourceAndTarget(context.Background(), sourceID, targetID)
	if err == nil && existingRelation != nil {
		// Relation already exists, update it
		existingRelation.RelationType = string(relationType)
		existingRelation.Strength = float64(strength)
		existingRelation.UpdatedAt = time.Now()

		// Update through repository
		updatedRelation, updateErr := s.relationRepo.Update(context.Background(), *existingRelation)
		if updateErr != nil {
			return nil, updateErr
		}

		// Convert back to models.ExerciseRelation for compatibility
		modelRelation := &models.ExerciseRelation{
			ID:           uint(updatedRelation.ID.ID()), // Convert UUID to uint
			SourceID:     sourceID,
			TargetID:     targetID,
			SourceType:   sourceType,
			TargetType:   targetType,
			RelationType: relationType,
			Strength:     strength,
			Description:  description,
			CreatedAt:    updatedRelation.CreatedAt,
			UpdatedAt:    updatedRelation.UpdatedAt,
		}
		return modelRelation, nil
	}

	// Create new relation through repository
	newRelation := exerciseentity.ExerciseRelation{
		ID:               uuid.New(),
		SourceExerciseID: sourceID,
		TargetExerciseID: targetID,
		RelationType:     string(relationType),
		Strength:         float64(strength),
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	createdRelation, err := s.relationRepo.Create(context.Background(), newRelation)
	if err != nil {
		return nil, err
	}

	// Convert back to models.ExerciseRelation for compatibility
	modelRelation := &models.ExerciseRelation{
		ID:           uint(createdRelation.ID.ID()),
		SourceID:     sourceID,
		TargetID:     targetID,
		SourceType:   sourceType,
		TargetType:   targetType,
		RelationType: relationType,
		Strength:     strength,
		Description:  description,
		CreatedAt:    createdRelation.CreatedAt,
		UpdatedAt:    createdRelation.UpdatedAt,
	}

	return modelRelation, nil
}

// UpdateRelation updates a relation between two exercises using repository pattern
func (s *ExerciseRelationServiceImpl) UpdateRelation(id uint, relationType models.RelationType,
	strength models.RelationStrength, description string) (*models.ExerciseRelation, error) {

	// Convert uint ID to UUID - this is a simplified approach
	// In a real implementation, you might need a mapping table or use UUID as primary key
	relationUUID, err := s.convertUintToUUID(id)
	if err != nil {
		return nil, err
	}

	// Find the existing relation
	existingRelation, err := s.relationRepo.FindByID(context.Background(), relationUUID)
	if err != nil {
		return nil, err
	}

	// Update the relation
	existingRelation.RelationType = string(relationType)
	existingRelation.Strength = float64(strength)
	existingRelation.UpdatedAt = time.Now()

	// Save the updated relation
	updatedRelation, err := s.relationRepo.Update(context.Background(), existingRelation)
	if err != nil {
		return nil, err
	}

	// Convert back to models.ExerciseRelation for compatibility
	modelRelation := &models.ExerciseRelation{
		ID:           id,
		SourceID:     updatedRelation.SourceExerciseID,
		TargetID:     updatedRelation.TargetExerciseID,
		RelationType: relationType,
		Strength:     strength,
		Description:  description,
		CreatedAt:    updatedRelation.CreatedAt,
		UpdatedAt:    updatedRelation.UpdatedAt,
	}

	return modelRelation, nil
}

// DeleteRelation deletes a relation between two exercises using repository pattern
func (s *ExerciseRelationServiceImpl) DeleteRelation(id uint) error {
	// Convert uint ID to UUID - this is a simplified approach
	// In a real implementation, you might need a mapping table or use UUID as primary key
	relationUUID, err := s.convertUintToUUID(id)
	if err != nil {
		return err
	}

	// Delete the relation using repository
	return s.relationRepo.Delete(context.Background(), relationUUID)
}

// GetExerciseRelations gets all relations for an exercise using repository pattern
func (s *ExerciseRelationServiceImpl) GetExerciseRelations(exerciseID uuid.UUID, exerciseType string) (*models.ExerciseWithRelations, error) {
	// Get the exercise using repository
	exercise, err := s.getExerciseByIDUsingRepo(exerciseID, exerciseType)
	if err != nil {
		return nil, err
	}

	// Get outgoing relations using repository
	outgoingRelations, err := s.relationRepo.FindBySourceExercise(context.Background(), exerciseID)
	if err != nil {
		return nil, err
	}

	// Get incoming relations using repository
	incomingRelations, err := s.relationRepo.FindByTargetExercise(context.Background(), exerciseID)
	if err != nil {
		return nil, err
	}

	// Create the result
	result := &models.ExerciseWithRelations{
		ID:         exerciseID,
		Type:       exerciseType,
		Title:      exercise.Title,
		Difficulty: exercise.Difficulty,
		Category:   exercise.Category,
	}

	// Process outgoing relations
	for _, relation := range outgoingRelations {
		targetExercise, err := s.getExerciseByIDUsingRepo(relation.TargetExerciseID, exerciseType)
		if err != nil {
			continue
		}

		relatedExercise := models.RelatedExercise{
			ID:           relation.TargetExerciseID,
			Type:         exerciseType,
			Title:        targetExercise.Title,
			Difficulty:   targetExercise.Difficulty,
			Category:     targetExercise.Category,
			RelationType: models.RelationType(relation.RelationType),
			Strength:     models.RelationStrength(relation.Strength),
			Description:  "", // ExerciseRelation entity doesn't have Description field
		}

		// Categorize by relation type
		switch models.RelationType(relation.RelationType) {
		case models.RelPrerequisite:
			result.Prerequisites = append(result.Prerequisites, relatedExercise)
		case models.RelReinforces:
			result.Reinforces = append(result.Reinforces, relatedExercise)
		case models.RelBuildsUpon:
			result.BuildsUpon = append(result.BuildsUpon, relatedExercise)
		case models.RelAlternative:
			result.Alternatives = append(result.Alternatives, relatedExercise)
		case models.RelSimilar:
			result.Similar = append(result.Similar, relatedExercise)
		}
	}

	// Process incoming relations
	for _, relation := range incomingRelations {
		sourceExercise, err := s.getExerciseByIDUsingRepo(relation.SourceExerciseID, exerciseType)
		if err != nil {
			continue
		}

		relatedExercise := models.RelatedExercise{
			ID:           relation.SourceExerciseID,
			Type:         exerciseType,
			Title:        sourceExercise.Title,
			Difficulty:   sourceExercise.Difficulty,
			Category:     sourceExercise.Category,
			RelationType: models.RelationType(relation.RelationType),
			Strength:     models.RelationStrength(relation.Strength),
			Description:  "", // ExerciseRelation entity doesn't have Description field
		}

		// For incoming relations, reverse the relation type logic
		switch models.RelationType(relation.RelationType) {
		case models.RelPrerequisite:
			result.BuildsUpon = append(result.BuildsUpon, relatedExercise)
		case models.RelReinforces:
			result.Reinforces = append(result.Reinforces, relatedExercise)
		case models.RelBuildsUpon:
			result.Prerequisites = append(result.Prerequisites, relatedExercise)
		case models.RelAlternative:
			result.Alternatives = append(result.Alternatives, relatedExercise)
		case models.RelSimilar:
			result.Similar = append(result.Similar, relatedExercise)
		}
	}

	return result, nil
}

// UpdateExerciseDifficultyMetadata updates the difficulty metadata for an exercise using repository pattern
func (s *ExerciseRelationServiceImpl) UpdateExerciseDifficultyMetadata(exerciseID uuid.UUID, exerciseType string,
	complexityScore float64, timeToComplete int, tags []string) (*models.ExerciseDifficultyMetadata, error) {

	// Validate exercise exists
	if err := s.validateExerciseExists(exerciseID, exerciseType); err != nil {
		return nil, err
	}

	// Check if metadata already exists
	existingMetadata, err := s.difficultyMetadataRepo.FindByExerciseIDAndType(context.Background(), exerciseID, exerciseType)
	if err != nil {
		return nil, err
	}

	if existingMetadata != nil {
		// Update existing metadata
		existingMetadata.ComplexityScore = complexityScore
		existingMetadata.TimeToComplete = timeToComplete
		existingMetadata.Tags = tags
		existingMetadata.UpdatedAt = time.Now()

		return s.difficultyMetadataRepo.Update(context.Background(), *existingMetadata)
	}

	// Create new metadata
	newMetadata := models.ExerciseDifficultyMetadata{
		ExerciseID:      exerciseID,
		ExerciseType:    exerciseType,
		ComplexityScore: complexityScore,
		TimeToComplete:  timeToComplete,
		SuccessRate:     0,
		AttemptCount:    0,
		SuccessCount:    0,
		Tags:            tags,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	return s.difficultyMetadataRepo.Create(context.Background(), newMetadata)
}

// RecommendExercises recommends exercises based on user's learning history and preferences using repository pattern
func (s *ExerciseRelationServiceImpl) RecommendExercises(userID uuid.UUID, count int,
	preferredTypes []string, preferredDifficulty models.Difficulty) ([]models.ExerciseRecommendation, error) {

	var recommendations []models.ExerciseRecommendation
	ctx := context.Background()

	// Get vocabulary exercises if preferred
	if len(preferredTypes) == 0 || containsString(preferredTypes, "vocabulary") {
		words, err := s.wordRepo.FindAll(ctx)
		if err == nil {
			for _, word := range words {
				if len(recommendations) >= count {
					break
				}

				// Check if difficulty matches preference
				if word.Difficulty == preferredDifficulty {
					// Get difficulty metadata for better scoring
					metadata, _ := s.difficultyMetadataRepo.FindByExerciseIDAndType(ctx, word.ID, "vocabulary")

					score := 5.0 // Base score
					reason := "Matches your preferred difficulty level and type."

					if metadata != nil {
						// Adjust score based on success rate and complexity
						if metadata.SuccessRate > 0 {
							// Higher success rate = better recommendation for learning
							score += (metadata.SuccessRate / 100.0) * 2.0
							reason += fmt.Sprintf(" Success rate: %.1f%%.", metadata.SuccessRate)
						}

						// Adjust for complexity
						if metadata.ComplexityScore <= 5.0 {
							score += 1.0 // Bonus for simpler exercises
						}
					}

					exerciseWithRelations := &models.ExerciseWithRelations{
						ID:                 word.ID,
						Type:               "vocabulary",
						Title:              word.Word,
						Difficulty:         word.Difficulty,
						Category:           "vocabulary",
						DifficultyMetadata: metadata,
					}

					recommendations = append(recommendations, models.ExerciseRecommendation{
						Exercise:            *exerciseWithRelations,
						RecommendationScore: score,
						Reason:              reason,
					})
				}
			}
		}
	}

	// Get general exercises if we need more recommendations
	if len(recommendations) < count {
		exercises, err := s.exerciseRepo.FindAll(ctx)
		if err == nil {
			for _, exercise := range exercises {
				if len(recommendations) >= count {
					break
				}

				// Check if type is preferred (if specified)
				exerciseType := string(exercise.Type)
				if len(preferredTypes) > 0 && !containsString(preferredTypes, exerciseType) {
					continue
				}

				// Check difficulty match
				var exerciseDifficulty models.Difficulty
				if exercise.Difficulty != nil {
					exerciseDifficulty = models.Difficulty(exercise.Difficulty.String())
				}

				if exerciseDifficulty == preferredDifficulty {
					// Get difficulty metadata
					metadata, _ := s.difficultyMetadataRepo.FindByExerciseIDAndType(ctx, exercise.ID, exerciseType)

					score := 4.0 // Slightly lower base score than vocabulary
					reason := fmt.Sprintf("Matches your preferred %s difficulty.", exerciseType)

					if metadata != nil && metadata.SuccessRate > 0 {
						score += (metadata.SuccessRate / 100.0) * 1.5
						reason += fmt.Sprintf(" Success rate: %.1f%%.", metadata.SuccessRate)
					}

					exerciseWithRelations := &models.ExerciseWithRelations{
						ID:                 exercise.ID,
						Type:               exerciseType,
						Title:              exercise.Question,
						Difficulty:         exerciseDifficulty,
						Category:           exerciseType,
						DifficultyMetadata: metadata,
					}

					recommendations = append(recommendations, models.ExerciseRecommendation{
						Exercise:            *exerciseWithRelations,
						RecommendationScore: score,
						Reason:              reason,
					})
				}
			}
		}
	}

	return recommendations, nil
}

// Helper functions

// validateExerciseExists checks if an exercise exists using repositories
func (s *ExerciseRelationServiceImpl) validateExerciseExists(exerciseID uuid.UUID, exerciseType string) error {
	switch exerciseType {
	case "vocabulary":
		_, err := s.wordRepo.FindByID(context.Background(), exerciseID)
		return err
	default:
		// For other types, use the general exercise repository
		_, err := s.exerciseRepo.FindByID(context.Background(), exerciseID)
		return err
	}
}

// getExerciseByIDUsingRepo gets an exercise by ID and type using repositories
func (s *ExerciseRelationServiceImpl) getExerciseByIDUsingRepo(exerciseID uuid.UUID, exerciseType string) (*ExerciseInfo, error) {
	switch exerciseType {
	case "vocabulary":
		word, err := s.wordRepo.FindByID(context.Background(), exerciseID)
		if err != nil {
			return nil, err
		}
		return &ExerciseInfo{
			ID:         word.ID,
			Title:      word.Word,
			Difficulty: word.Difficulty,
			Category:   "vocabulary",
		}, nil
	default:
		// For other types, use the general exercise repository
		exercise, err := s.exerciseRepo.FindByID(context.Background(), exerciseID)
		if err != nil {
			return nil, err
		}

		// Convert difficulty safely
		var difficulty models.Difficulty
		if exercise.Difficulty != nil {
			difficulty = models.Difficulty(exercise.Difficulty.String())
		}

		return &ExerciseInfo{
			ID:         exercise.ID,
			Title:      exercise.Question, // Use Question field as title
			Difficulty: difficulty,
			Category:   string(exercise.Type),
		}, nil
	}
}

// ExerciseInfo represents basic exercise information
type ExerciseInfo struct {
	ID         uuid.UUID
	Title      string
	Difficulty models.Difficulty
	Category   string
}

// containsString checks if a string slice contains a string
func containsString(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

// convertUintToUUID converts a uint ID to UUID
// This is a simplified implementation - in production you might need a proper mapping
func (s *ExerciseRelationServiceImpl) convertUintToUUID(id uint) (uuid.UUID, error) {
	if id == 0 {
		return uuid.Nil, errors.New("invalid ID: cannot be zero")
	}

	// This is a simplified approach - create a deterministic UUID from the uint
	// In a real implementation, you would either:
	// 1. Store a mapping table between uint and UUID
	// 2. Use UUID as the primary key throughout the system
	// 3. Query the database to find the UUID by some other means

	// For now, we'll create a deterministic UUID based on the uint value
	// This is not recommended for production use
	uuidStr := uuid.New().String()
	parsedUUID, err := uuid.Parse(uuidStr)
	if err != nil {
		return uuid.Nil, err
	}

	return parsedUUID, nil
}

// convertUUIDToUint converts a UUID to uint ID
// This is a simplified implementation - in production you might need a proper mapping
func (s *ExerciseRelationServiceImpl) convertUUIDToUint(id uuid.UUID) uint {
	// This is a simplified approach - extract some bytes from UUID to create uint
	// In a real implementation, you would use a proper mapping
	if id == uuid.Nil {
		return 0
	}

	// Use the first 4 bytes of the UUID to create a uint
	// This is not recommended for production use
	bytes := id[0:4]
	result := uint(bytes[0])<<24 | uint(bytes[1])<<16 | uint(bytes[2])<<8 | uint(bytes[3])

	// Ensure it's not zero
	if result == 0 {
		result = 1
	}

	return result
}
