package impl

import (
	"context"
	"errors"
	"languagelearning/domain/core/event"
	evaluationevent "languagelearning/domain/evaluation/event"
	learningevent "languagelearning/domain/learning/event"
	"languagelearning/domain/learning/repository"
	"languagelearning/domain/learning/service"
	"languagelearning/models"
	"log"
	"time"

	"github.com/google/uuid"
)

// PersonalizedLearningServiceImpl handles personalized learning functionality using repositories
type PersonalizedLearningServiceImpl struct {
	learningPathRepo     repository.ModelsLearningPathRepository
	evaluationRepo       repository.EvaluationRepository
	evaluationResultRepo repository.EvaluationResultRepository
	lessonRepo           repository.ModelsLessonRepository
	wordRepo             repository.WordRepository
	eventBus             event.EventBus
}

// NewPersonalizedLearningService creates a new personalized learning service
func NewPersonalizedLearningService(
	learningPathRepo repository.ModelsLearningPathRepository,
	evaluationRepo repository.EvaluationRepository,
	evaluationResultRepo repository.EvaluationResultRepository,
	lessonRepo repository.ModelsLessonRepository,
	wordRepo repository.WordRepository,
	eventBus event.EventBus,
) service.PersonalizedLearningService {
	return &PersonalizedLearningServiceImpl{
		learningPathRepo:     learningPathRepo,
		evaluationRepo:       evaluationRepo,
		evaluationResultRepo: evaluationResultRepo,
		lessonRepo:           lessonRepo,
		wordRepo:             wordRepo,
		eventBus:             eventBus,
	}
}

// CreatePersonalizedLearningPath creates a personalized learning path based on evaluation results
func (s *PersonalizedLearningServiceImpl) CreatePersonalizedLearningPath(userID, evaluationID uuid.UUID) (*models.LearningPath, error) {
	ctx := context.Background()

	// Get the evaluation result
	evalResult, err := s.evaluationResultRepo.FindByEvaluationIDAndUserID(ctx, evaluationID, userID)
	if err != nil {
		log.Printf("Failed to find evaluation result: evalID=%s, userID=%s, error=%v", evaluationID, userID, err)
		return nil, errors.New("evaluation result not found")
	}

	// Determine user level based on evaluation score
	level := s.determineUserLevel(evalResult.OverallScore)

	// Identify focus areas based on evaluation performance
	focusAreas := s.identifyFocusAreas(evalResult)

	// Create the learning path
	path := models.LearningPath{
		ID:                uuid.New(),
		UserID:            userID,
		Title:             s.generatePathTitle(level),
		Description:       s.generatePathDescription(level, focusAreas),
		Level:             level,
		FocusAreas:        focusAreas,
		EstimatedDuration: s.calculateEstimatedDuration(level, focusAreas),
		Status:            models.PathActive,
		Progress:          0,
		EvaluationID:      evaluationID,
		StartDate:         time.Now(),
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Save the learning path
	createdPath, err := s.learningPathRepo.Create(ctx, path)
	if err != nil {
		log.Printf("Failed to create personalized learning path: userID=%s, error=%v", userID, err)
		return nil, errors.New("failed to create learning path")
	}

	// Publish learning path created event
	if s.eventBus != nil {
		pathCreatedEvent := learningevent.NewLearningPathUpdatedEvent(
			createdPath.ID, userID, nil, nil,
		)
		if err := s.eventBus.Publish(ctx, pathCreatedEvent); err != nil {
			log.Printf("Failed to publish learning path created event: %v", err)
			// Don't return error, as the main operation succeeded
		}
	}

	log.Printf("Created personalized learning path: pathID=%s, userID=%s, level=%s", createdPath.ID, userID, level)
	return createdPath, nil
}

// determineUserLevel determines the appropriate level based on evaluation score
func (s *PersonalizedLearningServiceImpl) determineUserLevel(score int) models.LessonLevel {
	switch {
	case score >= 80:
		return models.LessonAdvanced
	case score >= 60:
		return models.LessonIntermediate
	default:
		return models.LessonBeginner
	}
}

// identifyFocusAreas identifies areas that need focus based on evaluation results
func (s *PersonalizedLearningServiceImpl) identifyFocusAreas(evalResult *models.EvaluationResult) []string {
	var focusAreas []string

	// Simple logic: if overall score is below 80%, focus on all basic areas
	if evalResult.OverallScore < 80 {
		focusAreas = []string{"vocabulary", "grammar"}
	}

	// If score is very low, add listening practice
	if evalResult.OverallScore < 60 {
		focusAreas = append(focusAreas, "listening")
	}

	// If no specific areas identified, use general areas
	if len(focusAreas) == 0 {
		focusAreas = []string{"vocabulary", "grammar", "listening", "speaking"}
	}

	return focusAreas
}

// generatePathTitle generates a title for the learning path
func (s *PersonalizedLearningServiceImpl) generatePathTitle(level models.LessonLevel) string {
	switch level {
	case models.LessonBeginner:
		return "個人化初級英語學習路徑"
	case models.LessonIntermediate:
		return "個人化中級英語學習路徑"
	case models.LessonAdvanced:
		return "個人化高級英語學習路徑"
	default:
		return "個人化英語學習路徑"
	}
}

// generatePathDescription generates a description for the learning path
func (s *PersonalizedLearningServiceImpl) generatePathDescription(level models.LessonLevel, focusAreas []string) string {
	baseDescription := "根據您的評估結果量身定制的學習路徑。"

	switch level {
	case models.LessonBeginner:
		baseDescription += "專注於建立英語基礎，包括基本詞彙和語法結構。"
	case models.LessonIntermediate:
		baseDescription += "提升您的英語技能，加強聽說讀寫能力。"
	case models.LessonAdvanced:
		baseDescription += "精進您的英語運用能力，達到流利溝通水平。"
	}

	if len(focusAreas) > 0 {
		baseDescription += "重點關注：" + joinFocusAreas(focusAreas) + "。"
	}

	return baseDescription
}

// joinFocusAreas joins focus areas with Chinese names
func joinFocusAreas(areas []string) string {
	var chineseAreas []string
	for _, area := range areas {
		switch area {
		case "vocabulary":
			chineseAreas = append(chineseAreas, "詞彙")
		case "grammar":
			chineseAreas = append(chineseAreas, "語法")
		case "listening":
			chineseAreas = append(chineseAreas, "聽力")
		case "speaking":
			chineseAreas = append(chineseAreas, "口語")
		default:
			chineseAreas = append(chineseAreas, area)
		}
	}

	if len(chineseAreas) == 0 {
		return ""
	}
	if len(chineseAreas) == 1 {
		return chineseAreas[0]
	}
	if len(chineseAreas) == 2 {
		return chineseAreas[0] + "和" + chineseAreas[1]
	}

	result := ""
	for i, area := range chineseAreas {
		if i == len(chineseAreas)-1 {
			result += "和" + area
		} else if i == 0 {
			result += area
		} else {
			result += "、" + area
		}
	}
	return result
}

// calculateEstimatedDuration calculates estimated duration based on level and focus areas
func (s *PersonalizedLearningServiceImpl) calculateEstimatedDuration(level models.LessonLevel, focusAreas []string) int {
	baseDuration := 14 // 2 weeks

	// Adjust based on level
	switch level {
	case models.LessonBeginner:
		baseDuration = 21 // 3 weeks
	case models.LessonIntermediate:
		baseDuration = 28 // 4 weeks
	case models.LessonAdvanced:
		baseDuration = 35 // 5 weeks
	}

	// Add time for each focus area
	baseDuration += len(focusAreas) * 3

	return baseDuration
}

// GetPersonalizedRecommendations gets personalized lesson recommendations for a user
func (s *PersonalizedLearningServiceImpl) GetPersonalizedRecommendations(userID uuid.UUID) ([]models.Lesson, error) {
	ctx := context.Background()

	// Get user's active learning paths
	activePaths, err := s.learningPathRepo.FindActiveByUserID(ctx, userID)
	if err != nil {
		log.Printf("Failed to find active learning paths for user %s: %v", userID, err)
		return nil, errors.New("failed to get user's learning paths")
	}

	var recommendations []models.Lesson

	// Get recommendations based on active learning paths
	for _, path := range activePaths {
		// Get lessons for this path's level and focus areas
		for _, area := range path.FocusAreas {
			lessons, err := s.lessonRepo.FindByLevelAndCategory(ctx, path.Level, area)
			if err != nil {
				log.Printf("Failed to find lessons for level %s and category %s: %v", path.Level, area, err)
				continue
			}

			// Add up to 2 lessons per area
			maxLessons := 2
			if len(lessons) > maxLessons {
				lessons = lessons[:maxLessons]
			}
			recommendations = append(recommendations, lessons...)
		}
	}

	// If no active paths, provide general beginner recommendations
	if len(activePaths) == 0 {
		lessons, err := s.lessonRepo.FindByLevelWithLimit(ctx, models.LessonBeginner, 5)
		if err != nil {
			log.Printf("Failed to find beginner lessons: %v", err)
			return nil, errors.New("failed to get recommendations")
		}
		recommendations = lessons
	}

	// Limit total recommendations
	maxRecommendations := 10
	if len(recommendations) > maxRecommendations {
		recommendations = recommendations[:maxRecommendations]
	}

	log.Printf("Generated %d personalized recommendations for user %s", len(recommendations), userID)
	return recommendations, nil
}

// UpdateLearningPathBasedOnProgress updates a learning path based on user progress
func (s *PersonalizedLearningServiceImpl) UpdateLearningPathBasedOnProgress(userID, pathID uuid.UUID) error {
	ctx := context.Background()

	// Get the learning path
	path, err := s.learningPathRepo.FindByUserIDWithLessons(ctx, userID, pathID)
	if err != nil {
		log.Printf("Failed to find learning path: userID=%s, pathID=%s, error=%v", userID, pathID, err)
		return errors.New("learning path not found")
	}

	// Simple logic: if progress is good (>80%), we don't need to adjust
	if path.Progress >= 80 {
		log.Printf("Learning path progress is good, no adjustment needed: pathID=%s, progress=%d", pathID, path.Progress)
		return nil
	}

	// If progress is slow (<50% after some time), we could add easier lessons
	// For now, just log the situation
	if path.Progress < 50 {
		log.Printf("Learning path progress is slow, consider adding easier content: pathID=%s, progress=%d", pathID, path.Progress)
		// TODO: Implement logic to add easier lessons or adjust difficulty
	}

	return nil
}

// CreateInitialAssessmentForNewUser creates an initial assessment for a new user
func (s *PersonalizedLearningServiceImpl) CreateInitialAssessmentForNewUser(userID uuid.UUID) (*models.Evaluation, error) {
	ctx := context.Background()

	// Check if user already has an initial assessment
	existingEvals, err := s.evaluationRepo.FindByUserIDAndType(ctx, userID, models.EvalPlacement)
	if err == nil && len(existingEvals) > 0 {
		// Return the first existing assessment
		return &existingEvals[0], nil
	}

	// Create a new initial assessment
	evaluation := models.Evaluation{
		ID:             uuid.New(),
		UserID:         userID,
		Type:           models.EvalPlacement,
		Title:          "初始語言能力評估",
		Description:    "這個評估將幫助我們了解您的語言水平，並為您創建個性化的學習計劃。",
		TotalQuestions: 20, // Simplified: 20 questions
		PassingScore:   60,
		Duration:       30, // 30 minutes
		IsCompleted:    false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Create simple assessment sections
	sections := s.createInitialAssessmentSections()
	evaluation.Sections = sections

	// Save the evaluation
	createdEval, err := s.evaluationRepo.Create(ctx, evaluation)
	if err != nil {
		log.Printf("Failed to create initial assessment: userID=%s, error=%v", userID, err)
		return nil, errors.New("failed to create initial assessment")
	}

	// Publish evaluation started event
	if s.eventBus != nil {
		evalStartedEvent := evaluationevent.NewEvaluationStartedEvent(createdEval.ID, userID)
		if err := s.eventBus.Publish(ctx, evalStartedEvent); err != nil {
			log.Printf("Failed to publish evaluation started event: %v", err)
			// Don't return error, as the main operation succeeded
		}
	}

	log.Printf("Created initial assessment: evalID=%s, userID=%s", createdEval.ID, userID)
	return createdEval, nil
}

// createInitialAssessmentSections creates sections for the initial assessment
func (s *PersonalizedLearningServiceImpl) createInitialAssessmentSections() []models.EvalSection {
	var sections []models.EvalSection

	// Vocabulary section
	vocabSection := models.EvalSection{
		Title:  "詞彙",
		Skill:  "vocabulary",
		Weight: 30,
	}
	vocabSection.Questions = s.createSimpleQuestions("vocabulary", 6)
	sections = append(sections, vocabSection)

	// Grammar section
	grammarSection := models.EvalSection{
		Title:  "語法",
		Skill:  "grammar",
		Weight: 30,
	}
	grammarSection.Questions = s.createSimpleQuestions("grammar", 6)
	sections = append(sections, grammarSection)

	// Listening section
	listeningSection := models.EvalSection{
		Title:  "聽力",
		Skill:  "listening",
		Weight: 25,
	}
	listeningSection.Questions = s.createSimpleQuestions("listening", 5)
	sections = append(sections, listeningSection)

	// Speaking section
	speakingSection := models.EvalSection{
		Title:  "口語",
		Skill:  "speaking",
		Weight: 15,
	}
	speakingSection.Questions = s.createSimpleQuestions("speaking", 3)
	sections = append(sections, speakingSection)

	return sections
}

// createSimpleQuestions creates simple questions for a skill area
func (s *PersonalizedLearningServiceImpl) createSimpleQuestions(skill string, count int) []models.EvalQuestion {
	var questions []models.EvalQuestion

	for i := 0; i < count; i++ {
		question := models.EvalQuestion{
			Type:          "multiple-choice",
			Content:       getSimpleQuestionContent(skill, i+1),
			Options:       getSimpleQuestionOptions(skill),
			CorrectAnswer: getSimpleQuestionAnswer(skill),
			Points:        5,
		}
		questions = append(questions, question)
	}

	return questions
}

// CreateExerciseSetFromAssessment creates a personalized exercise set based on assessment results
func (s *PersonalizedLearningServiceImpl) CreateExerciseSetFromAssessment(userID uuid.UUID, evaluationID uuid.UUID) (*models.LearningPath, error) {
	// This is essentially the same as CreatePersonalizedLearningPath
	return s.CreatePersonalizedLearningPath(userID, evaluationID)
}

// UpdateExerciseSetBasedOnResults updates a learning path based on exercise results
func (s *PersonalizedLearningServiceImpl) UpdateExerciseSetBasedOnResults(userID uuid.UUID, pathID uuid.UUID) error {
	// This is essentially the same as UpdateLearningPathBasedOnProgress
	return s.UpdateLearningPathBasedOnProgress(userID, pathID)
}

// GetPersonalizedLearningStatus gets the status of a user's personalized learning
func (s *PersonalizedLearningServiceImpl) GetPersonalizedLearningStatus(userID uuid.UUID) (map[string]interface{}, error) {
	ctx := context.Background()

	// Get the user's initial assessment
	initialAssessments, err := s.evaluationRepo.FindByUserIDAndType(ctx, userID, models.EvalPlacement)
	hasInitialAssessment := err == nil && len(initialAssessments) > 0

	// Get the user's active learning paths
	activePaths, err := s.learningPathRepo.FindActiveByUserID(ctx, userID)
	hasActiveLearningPath := err == nil && len(activePaths) > 0

	// Get the user's completed learning paths
	// For now, we'll use a simplified approach since FindCompletedByUserID doesn't exist
	allPaths, err := s.learningPathRepo.FindByUserID(ctx, userID)
	completedCount := 0
	if err == nil {
		for _, path := range allPaths {
			if path.Status == models.PathCompleted {
				completedCount++
			}
		}
	}

	// Get pending assessments
	pendingAssessments, err := s.evaluationRepo.FindByUserIDAndType(ctx, userID, models.EvalProgress)
	hasPendingAssessment := false
	var pendingAssessmentID *uuid.UUID
	if err == nil {
		for _, assessment := range pendingAssessments {
			if !assessment.IsCompleted {
				hasPendingAssessment = true
				pendingAssessmentID = &assessment.ID
				break
			}
		}
	}

	// Create the status
	status := map[string]interface{}{
		"hasInitialAssessment":       hasInitialAssessment,
		"initialAssessmentID":        nil,
		"initialAssessmentStatus":    "not_started",
		"hasActiveLearningPath":      hasActiveLearningPath,
		"activeLearningPathID":       nil,
		"activeLearningPathProgress": 0,
		"completedLearningPaths":     completedCount,
		"hasPendingAssessment":       hasPendingAssessment,
		"pendingAssessmentID":        pendingAssessmentID,
	}

	// Fill in details if available
	if hasInitialAssessment {
		assessment := initialAssessments[0]
		status["initialAssessmentID"] = assessment.ID
		if assessment.IsCompleted {
			status["initialAssessmentStatus"] = "completed"
		} else {
			status["initialAssessmentStatus"] = "in_progress"
		}
	}

	if hasActiveLearningPath {
		path := activePaths[0]
		status["activeLearningPathID"] = path.ID
		status["activeLearningPathProgress"] = path.Progress
	}

	log.Printf("Retrieved personalized learning status for user %s", userID)
	return status, nil
}

// Helper functions are defined in helpers.go
