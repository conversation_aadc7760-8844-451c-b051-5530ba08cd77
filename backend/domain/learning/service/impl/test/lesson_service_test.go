package test

import (
	"context"
	"languagelearning/domain/learning/entity"
	"languagelearning/domain/learning/service/impl"
	models "languagelearning/models"
	"languagelearning/domain/repository"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	coreEvent "languagelearning/domain/core/event"
)

// MockLessonRepository 模拟LessonRepository接口
type MockLessonRepository struct {
	mock.Mock
}

func (m *MockLessonRepository) FindByID(ctx context.Context, id uuid.UUID) (*entity.Lesson, error) {
	args := m.Called(ctx, id)
	ret0, _ := args.Get(0).(*entity.Lesson)
	return ret0, args.Error(1)
}

func (m *MockLessonRepository) FindAll(ctx context.Context) ([]entity.Lesson, error) {
	args := m.Called(ctx)
	return args.Get(0).([]entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) Create(ctx context.Context, entity entity.Lesson) (entity.Lesson, error) {
	args := m.Called(ctx, entity)
	return args.Get(0).(entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) Update(ctx context.Context, entity entity.Lesson) (entity.Lesson, error) {
	args := m.Called(ctx, entity)
	return args.Get(0).(entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLessonRepository) Count(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockLessonRepository) FindPage(ctx context.Context, pageable repository.Pageable) (repository.Page[entity.Lesson], error) {
	args := m.Called(ctx, pageable)
	return args.Get(0).(repository.Page[entity.Lesson]), args.Error(1)
}

func (m *MockLessonRepository) Search(ctx context.Context, query string) ([]entity.Lesson, error) {
	args := m.Called(ctx, query)
	return args.Get(0).([]entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) SearchPage(ctx context.Context, query string, pageable repository.Pageable) (repository.Page[entity.Lesson], error) {
	args := m.Called(ctx, query, pageable)
	return args.Get(0).(repository.Page[entity.Lesson]), args.Error(1)
}

func (m *MockLessonRepository) FindByLanguage(ctx context.Context, language string) ([]*entity.Lesson, error) {
	args := m.Called(ctx, language)
	return args.Get(0).([]*entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) FindByLevel(ctx context.Context, level entity.LessonLevel) ([]*entity.Lesson, error) {
	args := m.Called(ctx, level)
	return args.Get(0).([]*entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) FindByCategory(ctx context.Context, category entity.LessonCategory) ([]*entity.Lesson, error) {
	args := m.Called(ctx, category)
	return args.Get(0).([]*entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) FindByTags(ctx context.Context, tags []string) ([]*entity.Lesson, error) {
	args := m.Called(ctx, tags)
	return args.Get(0).([]*entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) FindByAuthor(ctx context.Context, authorID uuid.UUID) ([]*entity.Lesson, error) {
	args := m.Called(ctx, authorID)
	return args.Get(0).([]*entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) GetPublishedLessons(ctx context.Context) ([]*entity.Lesson, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) GetRelatedLessons(ctx context.Context, lessonID uuid.UUID) ([]*entity.Lesson, error) {
	args := m.Called(ctx, lessonID)
	return args.Get(0).([]*entity.Lesson), args.Error(1)
}

func (m *MockLessonRepository) AddExercise(ctx context.Context, lessonID, exerciseID uuid.UUID) error {
	args := m.Called(ctx, lessonID, exerciseID)
	return args.Error(0)
}

// MockLessonProgressRepository 模拟LessonProgressRepository接口
type MockLessonProgressRepository struct {
	mock.Mock
}

func (m *MockLessonProgressRepository) FindByID(ctx context.Context, id uuid.UUID) (models.LessonProgress, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(models.LessonProgress), args.Error(1)
}

func (m *MockLessonProgressRepository) FindAll(ctx context.Context) ([]models.LessonProgress, error) {
	args := m.Called(ctx)
	return args.Get(0).([]models.LessonProgress), args.Error(1)
}

func (m *MockLessonProgressRepository) Create(ctx context.Context, entity models.LessonProgress) (models.LessonProgress, error) {
	args := m.Called(ctx, entity)
	return args.Get(0).(models.LessonProgress), args.Error(1)
}

func (m *MockLessonProgressRepository) Update(ctx context.Context, entity models.LessonProgress) (models.LessonProgress, error) {
	args := m.Called(ctx, entity)
	return args.Get(0).(models.LessonProgress), args.Error(1)
}

func (m *MockLessonProgressRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLessonProgressRepository) Count(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockLessonProgressRepository) FindByUserAndLesson(ctx context.Context, userID, lessonID uuid.UUID) (*models.LessonProgress, error) {
	args := m.Called(ctx, userID, lessonID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.LessonProgress), args.Error(1)
}

func (m *MockLessonProgressRepository) FindByUser(ctx context.Context, userID uuid.UUID) ([]*models.LessonProgress, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*models.LessonProgress), args.Error(1)
}

func (m *MockLessonProgressRepository) GetCompletedLessons(ctx context.Context, userID uuid.UUID) ([]*models.LessonProgress, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*models.LessonProgress), args.Error(1)
}

func (m *MockLessonProgressRepository) GetInProgressLessons(ctx context.Context, userID uuid.UUID) ([]*models.LessonProgress, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*models.LessonProgress), args.Error(1)
}

func (m *MockLessonProgressRepository) UpdateProgress(ctx context.Context, progress *models.LessonProgress) error {
	args := m.Called(ctx, progress)
	return args.Error(0)
}

func (m *MockLessonProgressRepository) MarkAsCompleted(ctx context.Context, userID, lessonID uuid.UUID) error {
	args := m.Called(ctx, userID, lessonID)
	return args.Error(0)
}

// MockEventBus 模拟EventBus接口
type MockEventBus struct {
	mock.Mock
}

func (m *MockEventBus) Publish(ctx context.Context, e interface{}) error {
	args := m.Called(ctx, e)
	return args.Error(0)
}

func (m *MockEventBus) Subscribe(eventType string, handler coreEvent.EventHandler) error {
	args := m.Called(eventType, handler)
	return args.Error(0)
}

func (m *MockEventBus) Unsubscribe(eventType string, handler coreEvent.EventHandler) error {
	args := m.Called(eventType, handler)
	return args.Error(0)
}

// TestLessonService_GetLessonByID 测试获取课程
func TestLessonService_GetLessonByID(t *testing.T) {
	// 准备模拟存储库
	mockLessonRepo := new(MockLessonRepository)
	mockProgressRepo := new(MockLessonProgressRepository)
	mockEventBus := new(MockEventBus)

	// 创建服务
	service := impl.NewLessonService(mockLessonRepo, mockProgressRepo, mockEventBus)

	// 准备测试数据
	ctx := context.Background()
	id := uuid.New()
	now := time.Now()

	expectedLesson := entity.Lesson{
		ID:          id,
		Title:       "Test Lesson",
		Description: "Test Description",
		Level:       entity.Beginner,
		Category:    entity.Grammar,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// 配置模拟行为
	mockLessonRepo.On("FindByID", ctx, id).Return(expectedLesson, nil)

	// 调用测试方法
	result, err := service.GetLessonByID(ctx, id)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedLesson.ID, result.ID)
	assert.Equal(t, expectedLesson.Title, result.Title)

	// 验证模拟调用
	mockLessonRepo.AssertExpectations(t)
}

// TestLessonService_CreateLesson 测试创建课程
func TestLessonService_CreateLesson(t *testing.T) {
	// 准备模拟存储库
	mockLessonRepo := new(MockLessonRepository)
	mockProgressRepo := new(MockLessonProgressRepository)
	mockEventBus := new(MockEventBus)

	// 创建服务
	service := impl.NewLessonService(mockLessonRepo, mockProgressRepo, mockEventBus)

	// 准备测试数据
	ctx := context.Background()
	id := uuid.New()
	now := time.Now()

	lessonToCreate := &entity.Lesson{
		Title:       "New Lesson",
		Description: "New Description",
		Level:       entity.Intermediate,
		Category:    entity.Vocabulary,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	createdLesson := entity.Lesson{
		ID:          id,
		Title:       "New Lesson",
		Description: "New Description",
		Level:       entity.Intermediate,
		Category:    entity.Vocabulary,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// 配置模拟行为
	mockLessonRepo.On("Create", ctx, *lessonToCreate).Return(createdLesson, nil)

	// 调用测试方法
	result, err := service.CreateLesson(ctx, lessonToCreate)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, createdLesson.ID, result.ID)
	assert.Equal(t, createdLesson.Title, result.Title)

	// 验证模拟调用
	mockLessonRepo.AssertExpectations(t)
}

// TestLessonService_GetUserLessonProgress 测试获取用户的课程进度
func TestLessonService_GetUserLessonProgress(t *testing.T) {
	// 准备模拟存储库
	mockLessonRepo := new(MockLessonRepository)
	mockProgressRepo := new(MockLessonProgressRepository)
	mockEventBus := new(MockEventBus)

	// 创建服务
	service := impl.NewLessonService(mockLessonRepo, mockProgressRepo, mockEventBus)

	// 准备测试数据
	ctx := context.Background()
	userID := uuid.New()
	lessonID := uuid.New()

	progress := &models.LessonProgress{
		ID:          uuid.New(),
		UserID:      userID,
		LessonID:    lessonID,
		Progress:    50,
		Completed:   false,
		CompletedDate: nil,
	}

	// 配置模拟行为
	mockProgressRepo.On("FindByUserAndLesson", ctx, userID, lessonID).Return(progress, nil)

	// 调用测试方法
	result, err := service.GetUserLessonProgress(ctx, userID, lessonID)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, progress.ID, result.ID)
	assert.Equal(t, progress.UserID, result.UserID)
	assert.Equal(t, progress.LessonID, result.LessonID)
	assert.Equal(t, progress.Progress, result.Progress)
	assert.Equal(t, progress.Completed, result.Completed)
	assert.Equal(t, progress.CompletedDate, result.CompletedDate)

	// 验证模拟调用
	mockProgressRepo.AssertExpectations(t)
}

// TestLessonService_CreateNewUserLessonProgress 测试为用户创建新的课程进度
func TestLessonService_CreateNewUserLessonProgress(t *testing.T) {
	// 准备模拟存储库
	mockLessonRepo := new(MockLessonRepository)
	mockProgressRepo := new(MockLessonProgressRepository)
	mockEventBus := new(MockEventBus)

	// 创建服务
	service := impl.NewLessonService(mockLessonRepo, mockProgressRepo, mockEventBus)

	// 准备测试数据
	ctx := context.Background()
	userID := uuid.New()
	lessonID := uuid.New()

	// 没有找到进度，应该创建一个新的
	mockProgressRepo.On("FindByUserAndLesson", ctx, userID, lessonID).Return(nil, nil)

	// 新创建的进度
	newProgress := models.LessonProgress{
		ID:          uuid.New(),
		UserID:      userID,
		LessonID:    lessonID,
		Progress:    0,
		Completed:   false,
		CompletedDate: nil,
	}

	// 模拟创建行为
	mockProgressRepo.On("Create", ctx, mock.AnythingOfType("models.LessonProgress")).Return(newProgress, nil)

	// 调用测试方法
	result, err := service.GetUserLessonProgress(ctx, userID, lessonID)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, newProgress.ID, result.ID)
	assert.Equal(t, newProgress.UserID, result.UserID)
	assert.Equal(t, newProgress.LessonID, result.LessonID)

	// 验证模拟调用
	mockProgressRepo.AssertExpectations(t)
}

// TestLessonService_CompleteLessonProgress 测试完成课程
func TestLessonService_CompleteLessonProgress(t *testing.T) {
	// 准备模拟存储库
	mockLessonRepo := new(MockLessonRepository)
	mockProgressRepo := new(MockLessonProgressRepository)
	mockEventBus := new(MockEventBus)

	// 创建服务
	service := impl.NewLessonService(mockLessonRepo, mockProgressRepo, mockEventBus)

	// 准备测试数据
	ctx := context.Background()
	userID := uuid.New()
	lessonID := uuid.New()

	// 配置模拟行为
	mockProgressRepo.On("MarkAsCompleted", ctx, userID, lessonID).Return(nil)

	// 调用测试方法
	err := service.CompleteLessonProgress(ctx, userID, lessonID)

	// 验证结果
	assert.NoError(t, err)

	// 验证模拟调用
	mockProgressRepo.AssertExpectations(t)
}
