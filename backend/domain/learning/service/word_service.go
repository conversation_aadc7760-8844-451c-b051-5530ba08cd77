package service

import (
	"languagelearning/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// WordService defines the interface for word-related operations
type WordService interface {
	GetWords(userID uuid.UUID, category, difficulty string) ([]models.UserWordResponse, error)
	GetWordDetail(userID, wordID uuid.UUID) (*models.UserWordResponse, error)
	MarkWordAsLearned(userID, wordID uuid.UUID) (gin.H, error)
	MarkWordAsFavorite(userID, wordID uuid.UUID, isFavorite bool) (gin.H, error)
}
