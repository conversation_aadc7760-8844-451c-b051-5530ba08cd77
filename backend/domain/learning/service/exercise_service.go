package service

import (
	"context"
	exerciseentity "languagelearning/domain/exercise/entity"
	"languagelearning/domain/repository"
	"languagelearning/models"

	"github.com/google/uuid"
)

// ExerciseService 练习服务接口
type ExerciseService interface {
	// Basic CRUD operations
	GetExerciseByID(ctx context.Context, id uuid.UUID) (*exerciseentity.Exercise, error)
	GetExercises(ctx context.Context, pageable repository.Pageable, filters map[string]interface{}) (repository.Page[exerciseentity.Exercise], error)
	CreateExercise(ctx context.Context, exercise *exerciseentity.Exercise) (*exerciseentity.Exercise, error)
	UpdateExercise(ctx context.Context, exercise *exerciseentity.Exercise) (*exerciseentity.Exercise, error)
	DeleteExercise(ctx context.Context, id uuid.UUID) error

	// Publishing operations
	PublishExercise(ctx context.Context, id uuid.UUID) error
	UnpublishExercise(ctx context.Context, id uuid.UUID) error

	// Exercise type specific operations
	GetExercisesByType(ctx context.Context, exerciseType exerciseentity.ExerciseType) ([]*exerciseentity.Exercise, error)
	GetExercisesByDifficulty(ctx context.Context, difficulty exerciseentity.ExerciseDifficulty) ([]*exerciseentity.Exercise, error)
	GetExercisesByLanguage(ctx context.Context, language string) ([]*exerciseentity.Exercise, error)

	// Search and random operations
	SearchExercises(ctx context.Context, query string, pageable repository.Pageable) (repository.Page[exerciseentity.Exercise], error)
	GetRandomExercises(ctx context.Context, count int, filters map[string]interface{}) ([]*exerciseentity.Exercise, error)

	// Exercise attempt operations
	SubmitExerciseAttempt(ctx context.Context, attempt *exerciseentity.ExerciseAttempt) (*exerciseentity.ExerciseAttempt, error)
	GetUserExerciseAttempts(ctx context.Context, userID uuid.UUID) ([]*exerciseentity.ExerciseAttempt, error)
	GetUserExerciseStats(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error)
	CheckExerciseAnswer(ctx context.Context, exerciseID uuid.UUID, userAnswer string) (bool, string, error)

	// Exercise relation operations
	GetRelatedExercises(ctx context.Context, exerciseID uuid.UUID, relationType string) ([]*exerciseentity.Exercise, error)
	CreateExerciseRelation(ctx context.Context, relation *exerciseentity.ExerciseRelation) (*exerciseentity.ExerciseRelation, error)
	UpdateExerciseRelation(ctx context.Context, relation *exerciseentity.ExerciseRelation) (*exerciseentity.ExerciseRelation, error)
	DeleteExerciseRelation(ctx context.Context, id uuid.UUID) error

	// Note: Specific exercise type operations have been removed as they used direct GORM calls.
	// Use the generic methods above with appropriate filters instead:
	// - GetExercisesByType() for type-specific exercises
	// - SubmitExerciseAttempt() for submitting answers
}

// ExerciseRecommendationService 练习推荐服务接口
type ExerciseRecommendationService interface {
	// GetRecommendedExercises 获取推荐的练习
	GetRecommendedExercises(ctx context.Context, userID uuid.UUID, count int) ([]*exerciseentity.Exercise, error)

	// GetExercisesForReview 获取需要复习的练习
	GetExercisesForReview(ctx context.Context, userID uuid.UUID, count int) ([]*exerciseentity.Exercise, error)

	// GetNextExerciseSet 获取下一组练习
	GetNextExerciseSet(ctx context.Context, userID uuid.UUID, count int, filters map[string]interface{}) ([]*exerciseentity.Exercise, error)

	// GeneratePersonalizedExerciseSet 生成个性化练习集
	GeneratePersonalizedExerciseSet(ctx context.Context, userID uuid.UUID, count int, focusAreas []string) ([]*exerciseentity.Exercise, error)
}

// ExerciseRelationService 练习关系服务接口
type ExerciseRelationService interface {
	CreateRelation(sourceID, targetID uuid.UUID, sourceType, targetType string,
		relationType models.RelationType, strength models.RelationStrength, description string) (*models.ExerciseRelation, error)

	UpdateRelation(id uint, relationType models.RelationType,
		strength models.RelationStrength, description string) (*models.ExerciseRelation, error)

	GetExerciseRelations(exerciseID uuid.UUID, exerciseType string) (*models.ExerciseWithRelations, error)

	DeleteRelation(id uint) error

	// GetRelatedExercises 获取相关练习
	GetRelatedExercises(ctx context.Context, exerciseID uuid.UUID, relationType string) ([]*exerciseentity.Exercise, error)

	// UpdateExerciseSuccessRate 更新练习成功率
	UpdateExerciseSuccessRate(ctx context.Context, exerciseID uuid.UUID, exerciseType string, isSuccess bool) error

	UpdateExerciseDifficultyMetadata(exerciseID uuid.UUID, exerciseType string,
		complexityScore float64, timeToComplete int, tags []string) (*models.ExerciseDifficultyMetadata, error)

	RecommendExercises(userID uuid.UUID, count int,
		preferredTypes []string, preferredDifficulty models.Difficulty) ([]models.ExerciseRecommendation, error)
}
