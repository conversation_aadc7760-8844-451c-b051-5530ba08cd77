package service

import (
	"languagelearning/models"

	"github.com/google/uuid"
)

// PracticeService defines the interface for practice-related operations
type PracticeService interface {
	// GetPracticeHistory retrieves the current user's practice session history
	GetPracticeHistory(userID uuid.UUID) ([]models.PracticeSession, error)

	// GetRecommendedPractice retrieves personalized practice recommendations for the current user
	GetRecommendedPractice(userID uuid.UUID) ([]models.RecommendedPractice, error)

	// SavePracticeSession saves a completed practice session for the current user
	SavePracticeSession(userID uuid.UUID, practiceType string, duration, score int) (*models.PracticeSession, error)
}
