package service

import (
	"languagelearning/models"

	"github.com/google/uuid"
)

type PersonalizedLearningService interface {
	CreateInitialAssessmentForNewUser(userID uuid.UUID) (*models.Evaluation, error)
	CreateExerciseSetFromAssessment(userID uuid.UUID, evaluationID uuid.UUID) (*models.LearningPath, error)
	UpdateExerciseSetBasedOnResults(userID uuid.UUID, pathID uuid.UUID) error
	GetPersonalizedLearningStatus(userID uuid.UUID) (map[string]interface{}, error)
}
