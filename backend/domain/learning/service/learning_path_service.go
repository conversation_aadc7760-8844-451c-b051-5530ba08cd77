package service

import (
	"languagelearning/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LearningPathService 學習路徑服務接口
type LearningPathService interface {

	// CreateLearningPath 創建學習路徑
	CreateLearningPath(userID uuid.UUID, title, description, level string, focusAreas []string, estimatedDuration int, evaluationID uuid.UUID) (*models.LearningPath, error)

	// UpdateLearningPath 更新學習路徑
	UpdateLearningPath(userID, pathID uuid.UUID, title, description, status, level string, focusAreas []string, estimatedDuration int) (*models.LearningPath, error)

	// DeleteLearningPath 刪除學習路徑
	DeleteLearningPath(userID, pathID uuid.UUID) error

	// AdjustLearningPathBasedOnAssessment 調整學習路徑基於評估
	AdjustLearningPathBasedOnAssessment(userID, pathID uuid.UUID, evalID uuid.UUID) error

	CreateLearningPathFromRecommendation(userID, evalID uuid.UUID) (*models.LearningPath, error)

	SchedulePeriodicAssessment(userID, pathID uuid.UUID) (*models.Evaluation, error)

	GetLearningPaths(userID uuid.UUID, status string) ([]models.LearningPathSummary, error)

	AddLessonToPath(userID, pathID uuid.UUID, lessonID uuid.UUID, order int, isRequired bool) (*models.LearningPathLesson, error)

	GetRecommendedLearningPaths(userID, evalID uuid.UUID) ([]models.LearningPathRecommendation, error)

	GetLearningPathDetail(userID, pathID uuid.UUID) (*models.LearningPath, error)

	RecommendNextLearningPath(userID, pathID uuid.UUID) (*models.LearningPathRecommendation, error)

	GetNextExercise(userID, pathID uuid.UUID) (gin.H, error)

	CompleteExercise(userID, pathID uuid.UUID, lessonIDStr string) (*models.LearningPath, error)

	AutoUpdateLearningPathProgress(userID, pathID uuid.UUID) error
	AutoUpdateLessonCompletion(userID, pathID uuid.UUID) error
	UpdateExerciseSetBasedOnResults(userID, pathID uuid.UUID) error
}
