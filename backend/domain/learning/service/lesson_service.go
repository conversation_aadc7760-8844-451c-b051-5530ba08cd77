package service

import (
	"context"
	entity "languagelearning/domain/learning/entity"
	"languagelearning/domain/repository"

	"github.com/google/uuid"
)

// LessonService 课程服务接口
type LessonService interface {
	// GetLessonByID 通过ID获取课程
	GetLessonByID(ctx context.Context, id uuid.UUID) (*entity.Lesson, error)

	// GetLessons 获取课程列表，支持分页和过滤
	GetLessons(ctx context.Context, pageable repository.Pageable, filters map[string]interface{}) (repository.Page[entity.Lesson], error)

	// CreateLesson 创建新课程
	CreateLesson(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error)

	// UpdateLesson 更新课程
	UpdateLesson(ctx context.Context, lesson *entity.Lesson) (*entity.Lesson, error)

	// DeleteLesson 删除课程
	DeleteLesson(ctx context.Context, id uuid.UUID) error

	// PublishLesson 发布课程
	PublishLesson(ctx context.Context, id uuid.UUID) error

	// UnpublishLesson 取消发布课程
	UnpublishLesson(ctx context.Context, id uuid.UUID) error

	// GetLessonsByLanguage 获取指定语言的课程
	GetLessonsByLanguage(ctx context.Context, language string) ([]*entity.Lesson, error)

	// GetLessonsByLevel 获取指定级别的课程
	GetLessonsByLevel(ctx context.Context, level entity.LessonLevel) ([]*entity.Lesson, error)

	// GetLessonsByCategory 获取指定类别的课程
	GetLessonsByCategory(ctx context.Context, category entity.LessonCategory) ([]*entity.Lesson, error)

	// SearchLessons 搜索课程
	SearchLessons(ctx context.Context, query string, pageable repository.Pageable) (repository.Page[entity.Lesson], error)

	// GetRelatedLessons 获取相关课程
	GetRelatedLessons(ctx context.Context, lessonID uuid.UUID) ([]*entity.Lesson, error)

	// GetUserLessonProgress 获取用户的课程进度
	GetUserLessonProgress(ctx context.Context, userID, lessonID uuid.UUID) (*entity.LessonProgress, error)

	// UpdateLessonProgress 更新课程进度
	UpdateLessonProgress(ctx context.Context, progress *entity.LessonProgress) (*entity.LessonProgress, error)

	// CompleteLessonProgress 完成课程
	CompleteLessonProgress(ctx context.Context, userID, lessonID uuid.UUID) error

	// GetUserLessonProgressList 获取用户的课程进度列表
	GetUserLessonProgressList(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error)

	// GetUserCompletedLessons 获取用户已完成的课程
	GetUserCompletedLessons(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error)

	// GetUserInProgressLessons 获取用户正在进行的课程
	GetUserInProgressLessons(ctx context.Context, userID uuid.UUID) ([]*entity.LessonProgress, error)

	// GetFavoriteLessons 获取用户收藏的课程
	GetFavoriteLessons(ctx context.Context, userID uuid.UUID) ([]*entity.Lesson, error)

	// ToggleFavoriteLesson 切换课程收藏状态
	ToggleFavoriteLesson(ctx context.Context, userID uuid.UUID, lessonID string, isFavorite bool) (map[string]bool, error)
}
