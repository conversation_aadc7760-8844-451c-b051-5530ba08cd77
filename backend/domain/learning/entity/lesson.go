package entity

import (
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// LessonType 課程類型
type LessonType string

const (
	// GrammarLesson 語法課程
	GrammarLesson LessonType = "grammar"
	// VocabularyLesson 詞彙課程
	VocabularyLesson LessonType = "vocabulary"
	// ListeningLesson 聽力課程
	ListeningLesson LessonType = "listening"
	// SpeakingLesson 口語課程
	SpeakingLesson LessonType = "speaking"
	// ReadingLesson 閱讀課程
	ReadingLesson LessonType = "reading"
	// WritingLesson 寫作課程
	WritingLesson LessonType = "writing"
	// ComprehensiveLesson 綜合課程
	ComprehensiveLesson LessonType = "comprehensive"
)

// LessonStatus 課程狀態
type LessonStatus string

const (
	// DraftStatus 草稿狀態
	DraftStatus LessonStatus = "draft"
	// ReviewStatus 審核狀態
	ReviewStatus LessonStatus = "review"
	// PublishedStatus 已發布狀態
	PublishedStatus LessonStatus = "published"
	// ArchivedStatus 已歸檔狀態
	ArchivedStatus LessonStatus = "archived"
)

// ResourceType 資源類型
type ResourceType string

const (
	// VideoResource 視頻資源
	VideoResource ResourceType = "video"
	// AudioResource 音頻資源
	AudioResource ResourceType = "audio"
	// DocumentResource 文檔資源
	DocumentResource ResourceType = "document"
	// ImageResource 圖片資源
	ImageResource ResourceType = "image"
	// LinkResource 鏈接資源
	LinkResource ResourceType = "link"
)

// LessonLevel defines the difficulty level of a lesson
type LessonLevel string

// LessonCategory defines the category of a lesson
type LessonCategory string

// LessonProgress represents a user's progress in a lesson
type LessonProgress struct {
	ID             uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	UserID         uuid.UUID  `json:"userId" gorm:"type:uuid;not null"`
	LessonID       uuid.UUID  `json:"lessonId" gorm:"type:uuid;not null"`
	Progress       int        `json:"progress" gorm:"default:0"` // e.g., percentage 0-100
	Completed      bool       `json:"completed" gorm:"default:false"`
	Score          int        `json:"score" gorm:"default:0"`
	StartedAt      time.Time  `json:"startedAt,omitempty"`
	CompletedAt    *time.Time `json:"completedAt,omitempty"` // Pointer to allow null
	LastAccessedAt time.Time  `json:"lastAccessedAt,omitempty"`
	Notes          string     `json:"notes,omitempty" gorm:"type:text"`
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      time.Time  `json:"updatedAt"`
}

// Resource 課程資源
type Resource struct {
	ID          uuid.UUID    `json:"id"`
	Type        ResourceType `json:"type"`
	URL         string       `json:"url"`
	Title       string       `json:"title"`
	Description string       `json:"description"`
	CreatedAt   time.Time    `json:"createdAt"`
	UpdatedAt   time.Time    `json:"updatedAt"`
}

// NewResource 創建新的課程資源
func NewResource(resourceType ResourceType, url, title, description string) (*Resource, error) {
	if !resourceType.IsValid() {
		return nil, fmt.Errorf("invalid resource type: %s", resourceType)
	}
	if url == "" {
		return nil, errors.New("resource URL is required")
	}
	if title == "" {
		return nil, errors.New("resource title is required")
	}

	now := time.Now()
	return &Resource{
		ID:          uuid.New(),
		Type:        resourceType,
		URL:         url,
		Title:       title,
		Description: description,
		CreatedAt:   now,
		UpdatedAt:   now,
	}, nil
}

// IsValid 檢查資源類型是否有效
func (t ResourceType) IsValid() bool {
	switch t {
	case VideoResource, AudioResource, DocumentResource, ImageResource, LinkResource:
		return true
	default:
		return false
	}
}

// Lesson 課程實體
type Lesson struct {
	ID            uuid.UUID           `json:"id"`
	Title         string              `json:"title"`
	Description   string              `json:"description"`
	Type          LessonType          `json:"type"`
	Level         string              `json:"level"`
	Difficulty    *ExerciseDifficulty `json:"difficulty"`
	Content       string              `json:"content"`
	Duration      *Duration           `json:"duration"`
	Prerequisites []uuid.UUID         `json:"prerequisites,omitempty"`
	Objectives    []string            `json:"objectives,omitempty"`
	Tags          []string            `json:"tags,omitempty"`
	Status        LessonStatus        `json:"status"`
	AuthorID      uuid.UUID           `json:"authorId"`
	Exercises     []interface{}       `json:"exercises,omitempty"` // TODO: Use proper exercise entity
	Resources     []*Resource         `json:"resources,omitempty"`
	IsPublished   bool                `json:"isPublished"`
	PublishedAt   *time.Time          `json:"publishedAt,omitempty"`
	CreatedAt     time.Time           `json:"createdAt"`
	UpdatedAt     time.Time           `json:"updatedAt"`
}

// NewLesson 創建新的課程實體
func NewLesson(
	title string,
	description string,
	lessonType LessonType,
	level string,
	difficulty *ExerciseDifficulty,
	content string,
	authorID uuid.UUID,
) (*Lesson, error) {
	if !lessonType.IsValid() {
		return nil, fmt.Errorf("invalid lesson type: %s", lessonType)
	}
	if difficulty == nil {
		return nil, errors.New("lesson difficulty is required")
	}

	now := time.Now()
	return &Lesson{
		ID:          uuid.New(),
		Title:       title,
		Description: description,
		Type:        lessonType,
		Level:       level,
		Difficulty:  difficulty,
		Content:     content,
		Status:      DraftStatus,
		AuthorID:    authorID,
		CreatedAt:   now,
		UpdatedAt:   now,
	}, nil
}

// IsValid 檢查課程類型是否有效
func (t LessonType) IsValid() bool {
	switch t {
	case GrammarLesson, VocabularyLesson, ListeningLesson,
		SpeakingLesson, ReadingLesson, WritingLesson,
		ComprehensiveLesson:
		return true
	default:
		return false
	}
}

// Validate 驗證課程實體
func (l *Lesson) Validate() error {
	if l.Title == "" {
		return errors.New("lesson title is required")
	}
	if l.Description == "" {
		return errors.New("lesson description is required")
	}
	if !l.Type.IsValid() {
		return fmt.Errorf("invalid lesson type: %s", l.Type)
	}
	if l.Difficulty == nil {
		return errors.New("lesson difficulty is required")
	}
	if l.Content == "" {
		return errors.New("lesson content is required")
	}
	return nil
}

// AddExercise 添加練習到課程
func (l *Lesson) AddExercise(exercise interface{}) error {
	if exercise == nil {
		return errors.New("exercise cannot be nil")
	}
	if l.Status == ArchivedStatus {
		return errors.New("cannot add exercise to archived lesson")
	}
	l.Exercises = append(l.Exercises, exercise)
	l.UpdatedAt = time.Now()
	return nil
}

// RemoveExercise 從課程中移除練習
func (l *Lesson) RemoveExercise(exerciseID uuid.UUID) error {
	if l.Status == ArchivedStatus {
		return errors.New("cannot remove exercise from archived lesson")
	}

	// TODO: Implement proper exercise ID comparison when exercise entity is properly defined
	for i := range l.Exercises {
		// For now, we'll remove by index since we can't access exercise.ID
		// This is a temporary solution until proper exercise entity integration
		l.Exercises = append(l.Exercises[:i], l.Exercises[i+1:]...)
		l.UpdatedAt = time.Now()
		return nil
	}
	return errors.New("exercise not found in lesson")
}

// AddResource 添加資源到課程
func (l *Lesson) AddResource(resource *Resource) error {
	if resource == nil {
		return errors.New("resource cannot be nil")
	}
	if l.Status == ArchivedStatus {
		return errors.New("cannot add resource to archived lesson")
	}
	l.Resources = append(l.Resources, resource)
	l.UpdatedAt = time.Now()
	return nil
}

// RemoveResource 從課程中移除資源
func (l *Lesson) RemoveResource(resourceID uuid.UUID) error {
	if l.Status == ArchivedStatus {
		return errors.New("cannot remove resource from archived lesson")
	}

	for i, resource := range l.Resources {
		if resource.ID == resourceID {
			l.Resources = append(l.Resources[:i], l.Resources[i+1:]...)
			l.UpdatedAt = time.Now()
			return nil
		}
	}
	return errors.New("resource not found in lesson")
}

// Publish 發布課程
func (l *Lesson) Publish() error {
	if err := l.Validate(); err != nil {
		return fmt.Errorf("cannot publish invalid lesson: %w", err)
	}
	if len(l.Exercises) == 0 {
		return errors.New("cannot publish lesson without exercises")
	}
	l.Status = PublishedStatus
	l.UpdatedAt = time.Now()
	return nil
}

// Archive 歸檔課程
func (l *Lesson) Archive() {
	l.Status = ArchivedStatus
	l.UpdatedAt = time.Now()
}

// SetDuration 設置課程時長
func (l *Lesson) SetDuration(minutes int) error {
	duration, err := NewDuration(minutes)
	if err != nil {
		return err
	}
	l.Duration = duration
	l.UpdatedAt = time.Now()
	return nil
}

// AddPrerequisite 添加前置課程
func (l *Lesson) AddPrerequisite(lessonID uuid.UUID) error {
	if l.Status == ArchivedStatus {
		return errors.New("cannot modify prerequisites of archived lesson")
	}
	for _, id := range l.Prerequisites {
		if id == lessonID {
			return errors.New("prerequisite already exists")
		}
	}
	l.Prerequisites = append(l.Prerequisites, lessonID)
	l.UpdatedAt = time.Now()
	return nil
}

// RemovePrerequisite 移除前置課程
func (l *Lesson) RemovePrerequisite(lessonID uuid.UUID) error {
	if l.Status == ArchivedStatus {
		return errors.New("cannot modify prerequisites of archived lesson")
	}
	for i, id := range l.Prerequisites {
		if id == lessonID {
			l.Prerequisites = append(l.Prerequisites[:i], l.Prerequisites[i+1:]...)
			l.UpdatedAt = time.Now()
			return nil
		}
	}
	return errors.New("prerequisite not found")
}

// AddObjective 添加學習目標
func (l *Lesson) AddObjective(objective string) error {
	if l.Status == ArchivedStatus {
		return errors.New("cannot modify objectives of archived lesson")
	}
	if objective == "" {
		return errors.New("objective cannot be empty")
	}
	for _, obj := range l.Objectives {
		if obj == objective {
			return errors.New("objective already exists")
		}
	}
	l.Objectives = append(l.Objectives, objective)
	l.UpdatedAt = time.Now()
	return nil
}

// RemoveObjective 移除學習目標
func (l *Lesson) RemoveObjective(objective string) error {
	if l.Status == ArchivedStatus {
		return errors.New("cannot modify objectives of archived lesson")
	}
	for i, obj := range l.Objectives {
		if obj == objective {
			l.Objectives = append(l.Objectives[:i], l.Objectives[i+1:]...)
			l.UpdatedAt = time.Now()
			return nil
		}
	}
	return errors.New("objective not found")
}

// AddTag 添加標籤
func (l *Lesson) AddTag(tag string) error {
	if l.Status == ArchivedStatus {
		return errors.New("cannot modify tags of archived lesson")
	}
	if tag == "" {
		return errors.New("tag cannot be empty")
	}
	for _, t := range l.Tags {
		if t == tag {
			return errors.New("tag already exists")
		}
	}
	l.Tags = append(l.Tags, tag)
	l.UpdatedAt = time.Now()
	return nil
}

// RemoveTag 移除標籤
func (l *Lesson) RemoveTag(tag string) error {
	if l.Status == ArchivedStatus {
		return errors.New("cannot modify tags of archived lesson")
	}
	for i, t := range l.Tags {
		if t == tag {
			l.Tags = append(l.Tags[:i], l.Tags[i+1:]...)
			l.UpdatedAt = time.Now()
			return nil
		}
	}
	return errors.New("tag not found")
}
