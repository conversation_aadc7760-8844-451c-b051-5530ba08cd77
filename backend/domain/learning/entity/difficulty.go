package entity

// Difficulty 表示難度級別
type Difficulty string

const (
	// Easy 簡單難度
	Easy Difficulty = "easy"
	// Medium 中等難度
	Medium Difficulty = "medium"
	// Hard 困難難度
	Hard Difficulty = "hard"
	// Expert 專家難度
	Expert Difficulty = "expert"
)

// String 返回難度級別的字符串表示
func (d Difficulty) String() string {
	return string(d)
}

// IsValid 檢查難度級別是否有效
func (d Difficulty) IsValid() bool {
	switch d {
	case Easy, Medium, Hard, Expert:
		return true
	default:
		return false
	}
}
