package entity

import (
	"errors"
	"fmt"
	"time"
)

// Duration 表示時間長度的值對象
type Duration struct {
	minutes int
}

// NewDuration 創建一個新的時間長度值對象
func NewDuration(minutes int) (*Duration, error) {
	if minutes < 0 {
		return nil, errors.New("duration cannot be negative")
	}
	return &Duration{minutes: minutes}, nil
}

// Minutes 獲取分鐘數
func (d Duration) Minutes() int {
	return d.minutes
}

// Hours 獲取小時數
func (d Duration) Hours() float64 {
	return float64(d.minutes) / 60.0
}

// String 返回字符串表示
func (d Duration) String() string {
	if d.minutes < 60 {
		return fmt.Sprintf("%d分鐘", d.minutes)
	}
	hours := d.minutes / 60
	remainingMinutes := d.minutes % 60
	if remainingMinutes == 0 {
		return fmt.Sprintf("%d小時", hours)
	}
	return fmt.Sprintf("%d小時%d分鐘", hours, remainingMinutes)
}

// Progress 表示進度的值對象
type Progress struct {
	value int // 0-100
}

// NewProgress 創建一個新的進度值對象
func NewProgress(value int) (*Progress, error) {
	if value < 0 || value > 100 {
		return nil, errors.New("progress must be between 0 and 100")
	}
	return &Progress{value: value}, nil
}

// Value 獲取進度值
func (p Progress) Value() int {
	return p.value
}

// IsComplete 判斷是否完成
func (p Progress) IsComplete() bool {
	return p.value == 100
}

// String 返回字符串表示
func (p Progress) String() string {
	return fmt.Sprintf("%d%%", p.value)
}

// Score 表示分數的值對象
type Score struct {
	value     int
	maxValue  int
	timestamp time.Time
}

// NewScore 創建一個新的分數值對象
func NewScore(value, maxValue int) (*Score, error) {
	if value < 0 || maxValue <= 0 || value > maxValue {
		return nil, errors.New("invalid score value or max value")
	}
	return &Score{
		value:     value,
		maxValue:  maxValue,
		timestamp: time.Now(),
	}, nil
}

// Value 獲取分數值
func (s Score) Value() int {
	return s.value
}

// MaxValue 獲取最大分數值
func (s Score) MaxValue() int {
	return s.maxValue
}

// Percentage 獲取百分比
func (s Score) Percentage() float64 {
	return float64(s.value) / float64(s.maxValue) * 100
}

// Timestamp 獲取時間戳
func (s Score) Timestamp() time.Time {
	return s.timestamp
}

// String 返回字符串表示
func (s Score) String() string {
	return fmt.Sprintf("%d/%d (%.1f%%)", s.value, s.maxValue, s.Percentage())
}

// Language 表示語言的值對象
type Language struct {
	code       string // ISO 639-1 語言代碼
	name       string // 語言名稱
	nativeName string // 本地語言名稱
}

// NewLanguage 創建一個新的語言值對象
func NewLanguage(code, name, nativeName string) (*Language, error) {
	if len(code) != 2 {
		return nil, errors.New("language code must be 2 characters (ISO 639-1)")
	}
	return &Language{
		code:       code,
		name:       name,
		nativeName: nativeName,
	}, nil
}

// Code 獲取語言代碼
func (l Language) Code() string {
	return l.code
}

// Name 獲取語言名稱
func (l Language) Name() string {
	return l.name
}

// NativeName 獲取本地語言名稱
func (l Language) NativeName() string {
	return l.nativeName
}

// String 返回字符串表示
func (l Language) String() string {
	return fmt.Sprintf("%s (%s)", l.name, l.nativeName)
}

// ExerciseDifficulty 表示練習難度的值對象
type ExerciseDifficulty struct {
	level     Difficulty
	threshold int // 用於計算的閾值
}

// NewExerciseDifficulty 創建一個新的練習難度值對象
func NewExerciseDifficulty(level Difficulty) (*ExerciseDifficulty, error) {
	var threshold int
	switch level {
	case Easy:
		threshold = 1
	case Medium:
		threshold = 2
	case Hard:
		threshold = 3
	case Expert:
		threshold = 4
	default:
		return nil, errors.New("invalid difficulty level")
	}
	return &ExerciseDifficulty{
		level:     level,
		threshold: threshold,
	}, nil
}

// Level 獲取難度級別
func (d ExerciseDifficulty) Level() Difficulty {
	return d.level
}

// Threshold 獲取難度閾值
func (d ExerciseDifficulty) Threshold() int {
	return d.threshold
}

// IsHarderThan 判斷是否比另一個難度更難
func (d ExerciseDifficulty) IsHarderThan(other ExerciseDifficulty) bool {
	return d.threshold > other.threshold
}

// String 返回字符串表示
func (d ExerciseDifficulty) String() string {
	return string(d.level)
}

// ToDifficulty 轉換為 Difficulty 類型
func (d ExerciseDifficulty) ToDifficulty() Difficulty {
	return d.level
}

// MasteryLevel 表示精通程度級別
// MasteryLevel is a string type for lesson mastery
// e.g. beginner, intermediate, advanced, master
//
//go:generate stringer -type=MasteryLevel
type MasteryLevel string

const (
	MasteryBeginner     MasteryLevel = "beginner"
	MasteryIntermediate MasteryLevel = "intermediate"
	MasteryAdvanced     MasteryLevel = "advanced"
	MasteryMaster       MasteryLevel = "master"
)

// String returns the string representation of the mastery level
func (m MasteryLevel) String() string {
	return string(m)
}

// IsValid checks if the mastery level is valid
func (m MasteryLevel) IsValid() bool {
	switch m {
	case MasteryBeginner, MasteryIntermediate, MasteryAdvanced, MasteryMaster:
		return true
	default:
		return false
	}
}
