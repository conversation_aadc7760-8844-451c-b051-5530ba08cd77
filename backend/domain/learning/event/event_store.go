package event

import (
	"context"
	"encoding/json"
	"time"

	coreevent "languagelearning/domain/core/event"

	"github.com/google/uuid"
	amqp "github.com/rabbitmq/amqp091-go"
)

// LearningEventStore 學習領域事件存儲
type LearningEventStore struct {
	*coreevent.DefaultEventReconstructor
	connection *amqp.Connection
	channel    *amqp.Channel
	exchange   string
	queue      string
}

// NewLearningEventStore 創建學習領域事件存儲
func NewLearningEventStore(url, exchange, queue string) (*LearningEventStore, error) {
	conn, err := amqp.Dial(url)
	if err != nil {
		return nil, err
	}

	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, err
	}

	// 聲明交換機
	err = ch.ExchangeDeclare(
		exchange, // name
		"topic",  // type
		true,     // durable
		false,    // auto-deleted
		false,    // internal
		false,    // no-wait
		nil,      // arguments
	)
	if err != nil {
		ch.Close()
		conn.Close()
		return nil, err
	}

	// 聲明隊列
	q, err := ch.QueueDeclare(
		queue, // name
		true,  // durable
		false, // delete when unused
		false, // exclusive
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		ch.Close()
		conn.Close()
		return nil, err
	}

	// 綁定隊列到交換機
	err = ch.QueueBind(
		q.Name,   // queue name
		"#",      // routing key
		exchange, // exchange
		false,    // no-wait
		nil,      // arguments
	)
	if err != nil {
		ch.Close()
		conn.Close()
		return nil, err
	}

	return &LearningEventStore{
		DefaultEventReconstructor: coreevent.NewDefaultEventReconstructor(),
		connection:                conn,
		channel:                   ch,
		exchange:                  exchange,
		queue:                     queue,
	}, nil
}

// Save 實現 EventStore 接口的 Save 方法
func (s *LearningEventStore) Save(ctx context.Context, event coreevent.Event) error {
	// 生成事件 ID
	eventID := uuid.New()

	storedEvent := coreevent.StoredEvent{
		ID:            eventID,
		Type:          event.GetEventType(),
		AggregateID:   event.GetAggregateID(),
		AggregateType: event.GetAggregateType(),
		Version:       1, // 初始版本為 1
		CreatedAt:     event.GetEventTime(),
	}

	// 序列化事件數據
	data, err := json.Marshal(event.Payload())
	if err != nil {
		return err
	}
	storedEvent.Data = data

	// 序列化存儲的事件
	body, err := json.Marshal(storedEvent)
	if err != nil {
		return err
	}

	// 發布消息到 RabbitMQ
	return s.channel.PublishWithContext(ctx,
		s.exchange,           // exchange
		event.GetEventType(), // routing key
		false,                // mandatory
		false,                // immediate
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // 持久化消息
			Timestamp:    time.Now(),
			MessageId:    eventID.String(),
			Headers: amqp.Table{
				"aggregate_id":   storedEvent.AggregateID.String(),
				"aggregate_type": storedEvent.AggregateType,
				"version":        storedEvent.Version,
			},
		},
	)
}

// GetEvents 實現 EventStore 接口的 GetEvents 方法
func (s *LearningEventStore) GetEvents(ctx context.Context, aggregateID uuid.UUID) ([]coreevent.Event, error) {
	// 創建臨時隊列用於查詢
	q, err := s.channel.QueueDeclare(
		"",    // name (empty for random name)
		false, // durable
		true,  // delete when unused
		true,  // exclusive
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		return nil, err
	}
	defer s.channel.QueueDelete(q.Name, false, false, false)

	// 綁定隊列到交換機，使用聚合根ID作為路由鍵
	err = s.channel.QueueBind(
		q.Name,               // queue name
		aggregateID.String(), // routing key
		s.exchange,           // exchange
		false,                // no-wait
		nil,                  // arguments
	)
	if err != nil {
		return nil, err
	}

	var events []coreevent.Event
	msgs, err := s.channel.Consume(
		q.Name, // queue
		"",     // consumer
		true,   // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		return nil, err
	}

	// 設置超時
	done := make(chan bool)
	go func() {
		for msg := range msgs {
			var storedEvent coreevent.StoredEvent
			if err := json.Unmarshal(msg.Body, &storedEvent); err != nil {
				continue
			}
			// 將存儲的事件轉換為領域事件
			event, err := s.Reconstruct(storedEvent.Type, storedEvent.Data)
			if err != nil {
				continue
			}
			events = append(events, event)
		}
		done <- true
	}()

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-done:
		return events, nil
	}
}

// GetEventsByType 實現 EventStore 接口的 GetEventsByType 方法
func (s *LearningEventStore) GetEventsByType(ctx context.Context, eventType string) ([]coreevent.Event, error) {
	// 創建臨時隊列用於查詢
	q, err := s.channel.QueueDeclare(
		"",    // name (empty for random name)
		false, // durable
		true,  // delete when unused
		true,  // exclusive
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		return nil, err
	}
	defer s.channel.QueueDelete(q.Name, false, false, false)

	// 綁定隊列到交換機，使用事件類型作為路由鍵
	err = s.channel.QueueBind(
		q.Name,     // queue name
		eventType,  // routing key
		s.exchange, // exchange
		false,      // no-wait
		nil,        // arguments
	)
	if err != nil {
		return nil, err
	}

	var events []coreevent.Event
	msgs, err := s.channel.Consume(
		q.Name, // queue
		"",     // consumer
		true,   // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		return nil, err
	}

	// 設置超時
	done := make(chan bool)
	go func() {
		for msg := range msgs {
			var storedEvent coreevent.StoredEvent
			if err := json.Unmarshal(msg.Body, &storedEvent); err != nil {
				continue
			}
			// 將存儲的事件轉換為領域事件
			event, err := s.Reconstruct(storedEvent.Type, storedEvent.Data)
			if err != nil {
				continue
			}
			events = append(events, event)
		}
		done <- true
	}()

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-done:
		return events, nil
	}
}

// GetEventsByTimeRange 實現 EventStore 接口的 GetEventsByTimeRange 方法
func (s *LearningEventStore) GetEventsByTimeRange(ctx context.Context, start, end time.Time) ([]coreevent.Event, error) {
	// 創建臨時隊列用於查詢
	q, err := s.channel.QueueDeclare(
		"",    // name (empty for random name)
		false, // durable
		true,  // delete when unused
		true,  // exclusive
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		return nil, err
	}
	defer s.channel.QueueDelete(q.Name, false, false, false)

	// 綁定隊列到交換機，使用通配符路由鍵
	err = s.channel.QueueBind(
		q.Name,     // queue name
		"#",        // routing key (matches all)
		s.exchange, // exchange
		false,      // no-wait
		nil,        // arguments
	)
	if err != nil {
		return nil, err
	}

	var events []coreevent.Event
	msgs, err := s.channel.Consume(
		q.Name, // queue
		"",     // consumer
		true,   // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		return nil, err
	}

	// 設置超時
	done := make(chan bool)
	go func() {
		for msg := range msgs {
			var storedEvent coreevent.StoredEvent
			if err := json.Unmarshal(msg.Body, &storedEvent); err != nil {
				continue
			}
			// 檢查事件時間是否在指定範圍內
			if storedEvent.CreatedAt.Before(start) || storedEvent.CreatedAt.After(end) {
				continue
			}
			// 將存儲的事件轉換為領域事件
			event, err := s.Reconstruct(storedEvent.Type, storedEvent.Data)
			if err != nil {
				continue
			}
			events = append(events, event)
		}
		done <- true
	}()

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-done:
		return events, nil
	}
}

// Close 關閉事件存儲
func (s *LearningEventStore) Close() error {
	if err := s.channel.Close(); err != nil {
		return err
	}
	return s.connection.Close()
}
