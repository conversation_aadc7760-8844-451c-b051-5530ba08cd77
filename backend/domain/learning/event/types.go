package event

import (
	"time"

	achievemententity "languagelearning/domain/achievement/entity"
	coreevent "languagelearning/domain/core/event"
	learningentity "languagelearning/domain/learning/entity"
	userentity "languagelearning/domain/user/entity"

	"github.com/google/uuid"
)

// ==================== 基礎接口定義 ====================

// EventHandler 事件處理器接口
type EventHandler = coreevent.EventHandler

// EventSubscriber 事件訂閱者接口
type EventSubscriber = coreevent.EventSubscriber

// EventBus 事件總線接口
type EventBus = coreevent.EventBus

// LearningEvent 學習領域事件接口
type LearningEvent interface {
	coreevent.Event
	// LearningEventType 返回學習事件類型
	LearningEventType() string
}

// LearningEventHandler 學習領域事件處理器接口
type LearningEventHandler interface {
	EventHandler
	// HandleLearningEvent 處理學習領域事件
	HandleLearningEvent(event LearningEvent) error
}

// ==================== 基礎事件實現 ====================

// BaseLearningEvent 學習領域基礎事件實現
type BaseLearningEvent struct {
	*coreevent.BaseEvent
	LearningType string `json:"learningType"`
}

// NewBaseLearningEvent 創建學習領域基礎事件
func NewBaseLearningEvent(eventType string, aggregateID uuid.UUID, aggregateType string, learningType string, data interface{}) *BaseLearningEvent {
	return &BaseLearningEvent{
		BaseEvent:    coreevent.NewBaseEvent(eventType, aggregateID, aggregateType, data),
		LearningType: learningType,
	}
}

// LearningEventType 實現 LearningEvent 接口
func (e *BaseLearningEvent) LearningEventType() string {
	return e.LearningType
}

// ==================== 具體事件定義 ====================

// LearningProgressUpdatedEvent 學習進度更新事件
type LearningProgressUpdatedEvent struct {
	*coreevent.BaseEvent
	UserID       uuid.UUID
	Progress     *learningentity.Progress
	Level        int
	Experience   int
	UpdatedAt    time.Time
	StreakDays   int
	TotalLessons int
}

// NewLearningProgressUpdatedEvent 創建學習進度更新事件
func NewLearningProgressUpdatedEvent(userID uuid.UUID, progress *learningentity.Progress, level, experience, streakDays, totalLessons int) *LearningProgressUpdatedEvent {
	now := time.Now()
	return &LearningProgressUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"learning_progress.updated",
			userID,
			"learning_progress",
			map[string]interface{}{
				"userID":       userID,
				"progress":     progress,
				"level":        level,
				"experience":   experience,
				"updatedAt":    now,
				"streakDays":   streakDays,
				"totalLessons": totalLessons,
			},
		),
		UserID:       userID,
		Progress:     progress,
		Level:        level,
		Experience:   experience,
		UpdatedAt:    now,
		StreakDays:   streakDays,
		TotalLessons: totalLessons,
	}
}

// LearningPathUpdatedEvent 學習路徑更新事件
type LearningPathUpdatedEvent struct {
	*coreevent.BaseEvent
	PathID    uuid.UUID
	UserID    uuid.UUID
	Progress  *learningentity.Progress
	Lessons   []interface{} // TODO: Use proper lesson entity after cleanup
	UpdatedAt time.Time
}

// NewLearningPathUpdatedEvent 創建學習路徑更新事件
func NewLearningPathUpdatedEvent(pathID, userID uuid.UUID, progress *learningentity.Progress, lessons []interface{}) *LearningPathUpdatedEvent {
	now := time.Now()
	return &LearningPathUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"learning_path.updated",
			pathID,
			"learning_path",
			map[string]interface{}{
				"pathID":    pathID,
				"userID":    userID,
				"progress":  progress,
				"lessons":   lessons,
				"updatedAt": now,
			},
		),
		PathID:    pathID,
		UserID:    userID,
		Progress:  progress,
		Lessons:   lessons,
		UpdatedAt: now,
	}
}

// LearningGoalAchievedEvent 學習目標達成事件
type LearningGoalAchievedEvent struct {
	*coreevent.BaseEvent
	UserID      uuid.UUID
	GoalID      uuid.UUID
	AchievedAt  time.Time
	Description string
	Reward      int // 獎勵經驗值
}

// NewLearningGoalAchievedEvent 創建學習目標達成事件
func NewLearningGoalAchievedEvent(userID, goalID uuid.UUID, description string, reward int) *LearningGoalAchievedEvent {
	now := time.Now()
	return &LearningGoalAchievedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"learning_goal.achieved",
			userID,
			"learning_goal",
			map[string]interface{}{
				"userID":      userID,
				"goalID":      goalID,
				"achievedAt":  now,
				"description": description,
				"reward":      reward,
			},
		),
		UserID:      userID,
		GoalID:      goalID,
		AchievedAt:  now,
		Description: description,
		Reward:      reward,
	}
}

// UserRegisteredEvent 用戶註冊事件
type UserRegisteredEvent struct {
	*coreevent.BaseEvent
	User      *userentity.User
	CreatedAt time.Time
}

// UserLearningPreferenceUpdatedEvent 用戶學習偏好更新事件
type UserLearningPreferenceUpdatedEvent struct {
	*coreevent.BaseEvent
	UserID     uuid.UUID
	Preference *userentity.LearningPreference
	UpdatedAt  time.Time
}

// AchievementUnlockedEvent 成就解鎖事件
type AchievementUnlockedEvent struct {
	*coreevent.BaseEvent
	Achievement *achievemententity.Achievement
	UserID      uuid.UUID
	UnlockedAt  time.Time
}

// UserLevelUpEvent 用戶等級提升事件
type UserLevelUpEvent struct {
	*coreevent.BaseEvent
	UserID    uuid.UUID
	OldLevel  int
	NewLevel  int
	UpdatedAt time.Time
}

// EvaluationCompletedEvent 評估完成事件
type EvaluationCompletedEvent struct {
	*coreevent.BaseEvent
	UserID       uuid.UUID
	EvaluationID uuid.UUID
	Score        *learningentity.Score
	CompletedAt  time.Time
	Feedback     string
}

// NotificationSentEvent 通知發送事件
type NotificationSentEvent struct {
	*coreevent.BaseEvent
	UserID         uuid.UUID
	NotificationID uuid.UUID
	SentAt         time.Time
	Channel        string // email, push, in-app
}

// NotificationReadEvent 通知已讀事件
type NotificationReadEvent struct {
	*coreevent.BaseEvent
	UserID         uuid.UUID
	NotificationID uuid.UUID
	ReadAt         time.Time
}

// ExerciseAttemptSubmittedEvent 練習提交事件
type ExerciseAttemptSubmittedEvent struct {
	*coreevent.BaseEvent
	UserID      uuid.UUID
	ExerciseID  uuid.UUID
	AttemptID   uuid.UUID
	SubmittedAt time.Time
}

// NewExerciseAttemptSubmittedEvent 創建練習提交事件
func NewExerciseAttemptSubmittedEvent(userID, exerciseID, attemptID uuid.UUID) *ExerciseAttemptSubmittedEvent {
	now := time.Now()
	return &ExerciseAttemptSubmittedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"exercise.attempt.submitted",
			exerciseID,
			"exercise",
			map[string]interface{}{
				"userID":      userID,
				"exerciseID":  exerciseID,
				"attemptID":   attemptID,
				"submittedAt": now,
			},
		),
		UserID:      userID,
		ExerciseID:  exerciseID,
		AttemptID:   attemptID,
		SubmittedAt: now,
	}
}

// LearningPathProgressUpdatedEvent 學習路徑進度更新事件
type LearningPathProgressUpdatedEvent struct {
	*coreevent.BaseEvent
	UserID       uuid.UUID
	PathID       uuid.UUID
	Progress     *learningentity.Progress
	UpdatedAt    time.Time
	Level        int
	Experience   int
	StreakDays   int
	TotalLessons int
}

// NewLearningPathProgressUpdatedEvent 創建學習路徑進度更新事件
func NewLearningPathProgressUpdatedEvent(pathID, userID uuid.UUID, progress *learningentity.Progress, level, experience, streakDays, totalLessons int) *LearningPathProgressUpdatedEvent {
	now := time.Now()
	return &LearningPathProgressUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"learning_path.progress.updated",
			pathID,
			"learning_path",
			map[string]interface{}{
				"pathID":       pathID,
				"userID":       userID,
				"progress":     progress,
				"updatedAt":    now,
				"level":        level,
				"experience":   experience,
				"streakDays":   streakDays,
				"totalLessons": totalLessons,
			},
		),
		UserID:       userID,
		PathID:       pathID,
		Progress:     progress,
		UpdatedAt:    now,
		Level:        level,
		Experience:   experience,
		StreakDays:   streakDays,
		TotalLessons: totalLessons,
	}
}

// LessonCompletedEvent 課程完成事件
type LessonCompletedEvent struct {
	*coreevent.BaseEvent
	LessonID     uuid.UUID
	UserID       uuid.UUID
	CompletedAt  time.Time
	Score        *learningentity.Score
	Duration     *learningentity.Duration
	MasteryLevel string
}

// NewLessonCompletedEvent 創建課程完成事件
func NewLessonCompletedEvent(lessonID, userID uuid.UUID, score *learningentity.Score, duration *learningentity.Duration, masteryLevel string) *LessonCompletedEvent {
	now := time.Now()
	return &LessonCompletedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"lesson.completed",
			lessonID,
			"lesson",
			map[string]interface{}{
				"lessonID":     lessonID,
				"userID":       userID,
				"completedAt":  now,
				"score":        score,
				"duration":     duration,
				"masteryLevel": masteryLevel,
			},
		),
		LessonID:     lessonID,
		UserID:       userID,
		CompletedAt:  now,
		Score:        score,
		Duration:     duration,
		MasteryLevel: masteryLevel,
	}
}

// ExerciseAttemptedEvent 練習嘗試事件
type ExerciseAttemptedEvent struct {
	*coreevent.BaseEvent
	ExerciseID  uuid.UUID
	UserID      uuid.UUID
	AttemptedAt time.Time
	Score       *learningentity.Score
	Duration    *learningentity.Duration
}

// NewExerciseAttemptedEvent 創建練習嘗試事件
func NewExerciseAttemptedEvent(exerciseID, userID uuid.UUID, score *learningentity.Score, duration *learningentity.Duration) *ExerciseAttemptedEvent {
	now := time.Now()
	return &ExerciseAttemptedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"exercise.attempted",
			exerciseID,
			"exercise",
			map[string]interface{}{
				"exerciseID":  exerciseID,
				"userID":      userID,
				"attemptedAt": now,
				"score":       score,
				"duration":    duration,
			},
		),
		ExerciseID:  exerciseID,
		UserID:      userID,
		AttemptedAt: now,
		Score:       score,
		Duration:    duration,
	}
}

// ExerciseDifficultyChangedEvent 練習難度變更事件
type ExerciseDifficultyChangedEvent struct {
	ExerciseID    uint
	UserID        uint
	ChangedAt     time.Time
	OldDifficulty *learningentity.ExerciseDifficulty
	NewDifficulty *learningentity.ExerciseDifficulty
	Reason        string
}

func (e ExerciseDifficultyChangedEvent) EventName() string {
	return "exercise.difficulty_changed"
}

func (e ExerciseDifficultyChangedEvent) OccurredAt() time.Time {
	return e.ChangedAt
}

// UserProgressMilestoneEvent 用戶進度里程碑事件
type UserProgressMilestoneEvent struct {
	UserID      uint
	Milestone   string
	ReachedAt   time.Time
	Progress    *learningentity.Progress
	Description string
}

func (e UserProgressMilestoneEvent) EventName() string {
	return "user.progress_milestone"
}

func (e UserProgressMilestoneEvent) OccurredAt() time.Time {
	return e.ReachedAt
}
