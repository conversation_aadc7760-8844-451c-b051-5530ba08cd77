package handlers

import (
	"context"
	"fmt"
	"log"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/learning/event"
	notificationentity "languagelearning/domain/notification/entity"
)

// LearningPathHandler 處理學習路徑相關事件
type LearningPathHandler struct {
	*BaseHandler
}

// NewLearningPathHandler 創建學習路徑處理器
func NewLearningPathHandler(base *BaseHandler) *LearningPathHandler {
	return &LearningPathHandler{
		BaseHandler: base,
	}
}

// Handle 實現 EventHandler 接口
func (h *LearningPathHandler) Handle(e coreevent.Event) error {
	switch evt := e.(type) {
	case *event.LearningPathUpdatedEvent:
		return h.handleLearningPathUpdated(evt)
	case *event.LearningPathProgressUpdatedEvent:
		return h.handleLearningPathProgressUpdated(evt)
	default:
		return fmt.Errorf("unsupported event type: %T", e)
	}
}

// handleLearningPathUpdated 處理學習路徑更新事件
func (h *LearningPathHandler) handleLearningPathUpdated(evt *event.LearningPathUpdatedEvent) error {
	log.Printf("處理學習路徑更新事件：PathID=%s, UserID=%s",
		evt.PathID, evt.UserID)

	// 發送學習路徑更新通知
	ctx := context.Background()
	title := "學習路徑更新"
	content := "您的學習路徑已更新，包含新的課程內容！"

	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLearning,
		title,
		content,
		map[string]interface{}{
			"pathID":    evt.PathID,
			"updatedAt": evt.UpdatedAt,
		},
	)

	if err != nil {
		log.Printf("發送學習路徑更新通知失敗: %v", err)
		return err
	}

	return nil
}

// handleLearningPathProgressUpdated 處理學習路徑進度更新事件
func (h *LearningPathHandler) handleLearningPathProgressUpdated(evt *event.LearningPathProgressUpdatedEvent) error {
	log.Printf("處理學習路徑進度更新事件：PathID=%s, UserID=%s, Level=%d",
		evt.PathID, evt.UserID, evt.Level)

	// 發送進度更新通知
	ctx := context.Background()
	title := "學習路徑進度更新"
	content := fmt.Sprintf("您在學習路徑中的進度已更新！等級：%d，經驗值：%d", evt.Level, evt.Experience)

	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLearning,
		title,
		content,
		map[string]interface{}{
			"pathID":       evt.PathID,
			"level":        evt.Level,
			"experience":   evt.Experience,
			"streakDays":   evt.StreakDays,
			"totalLessons": evt.TotalLessons,
		},
	)

	if err != nil {
		log.Printf("發送學習路徑進度通知失敗: %v", err)
		return err
	}

	return nil
}
