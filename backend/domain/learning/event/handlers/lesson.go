package handlers

import (
	"context"
	"fmt"
	"log"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/learning/event"
	"languagelearning/domain/notification/entity"
)

// LessonHandler 處理課程相關事件
type LessonHandler struct {
	*BaseHandler
}

// NewLessonHandler 創建課程處理器
func NewLessonHandler(base *BaseHandler) *LessonHandler {
	return &LessonHandler{
		BaseHandler: base,
	}
}

// Handle 實現 EventHandler 接口
func (h *LessonHandler) Handle(e coreevent.Event) error {
	switch evt := e.(type) {
	case *event.LessonCompletedEvent:
		return h.handleLessonCompleted(evt)
	default:
		return fmt.Errorf("unsupported event type: %T", e)
	}
}

// handleLessonCompleted 處理課程完成事件
func (h *LessonHandler) handleLessonCompleted(evt *event.LessonCompletedEvent) error {
	log.Printf("處理課程完成事件：LessonID=%s, UserID=%s, Time=%s",
		evt.LessonID, evt.UserID, evt.CompletedAt)

	// 發送課程完成通知
	ctx := context.Background()
	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		entity.NotificationTypeAchievement,
		"課程完成",
		fmt.Sprintf("恭喜你完成了課程！掌握程度：%s", evt.MasteryLevel),
		map[string]interface{}{
			"lessonID":     evt.LessonID,
			"score":        evt.Score,
			"duration":     evt.Duration,
			"masteryLevel": evt.MasteryLevel,
		},
	)

	return err
}
