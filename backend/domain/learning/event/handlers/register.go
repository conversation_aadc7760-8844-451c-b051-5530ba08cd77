package handlers

import (
	"fmt"

	"languagelearning/domain/learning/event"
	"languagelearning/domain/notification/service"
)

// RegisterAllHandlers 註冊所有事件處理器
func RegisterAllHandlers(bus event.EventBus, notificationService service.NotificationService) error {
	// 創建基礎處理器
	base := NewBaseHandler(notificationService)

	// 註冊學習進度處理器
	progressHandler := NewLearningProgressHandler(base)
	if err := bus.Subscribe("learning_progress.updated", progressHandler); err != nil {
		return fmt.Errorf("failed to register learning progress handler: %w", err)
	}

	// 註冊學習路徑處理器
	pathHandler := NewLearningPathHandler(base)
	if err := bus.Subscribe("learning_path.updated", pathHandler); err != nil {
		return fmt.Errorf("failed to register learning path handler: %w", err)
	}

	// 註冊課程處理器
	lessonHandler := NewLessonHandler(base)
	if err := bus.Subscribe("lesson.completed", lessonHandler); err != nil {
		return fmt.Errorf("failed to register lesson handler: %w", err)
	}

	// 註冊練習處理器
	exerciseHandler := NewExerciseHandler(base)
	if err := bus.Subscribe("exercise.attempted", exerciseHandler); err != nil {
		return fmt.Errorf("failed to register exercise handler: %w", err)
	}

	return nil
}
