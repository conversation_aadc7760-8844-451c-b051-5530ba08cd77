package handlers

import (
	"context"
	"fmt"
	"log"

	coreevent "languagelearning/domain/core/event"
	exerciseevent "languagelearning/domain/exercise/event"
	"languagelearning/domain/learning/event"
	notificationentity "languagelearning/domain/notification/entity"
)

// ExerciseHandler 處理練習相關事件
type ExerciseHandler struct {
	*BaseHandler
}

// NewExerciseHandler 創建練習處理器
func NewExerciseHandler(base *BaseHandler) *ExerciseHandler {
	return &ExerciseHandler{
		BaseHandler: base,
	}
}

// Handle 實現 EventHandler 接口
func (h *ExerciseHandler) Handle(e coreevent.Event) error {
	switch evt := e.(type) {
	case *exerciseevent.ExerciseAttemptedEvent:
		return h.handleExerciseAttempted(evt)
	case *event.ExerciseAttemptedEvent:
		return h.handleLearningExerciseAttempted(evt)
	default:
		return fmt.Errorf("unsupported event type: %T", e)
	}
}

// handleExerciseAttempted 處理練習嘗試事件
func (h *ExerciseHandler) handleExerciseAttempted(evt *exerciseevent.ExerciseAttemptedEvent) error {
	log.Printf("處理練習嘗試事件：ExerciseID=%s, UserID=%s, Time=%s",
		evt.ExerciseID, evt.UserID, evt.EventTime)

	// 根據練習結果發送不同通知
	ctx := context.Background()
	var title, content string
	var isCorrect bool
	if evt.Score != nil {
		isCorrect = evt.Score.Value() == evt.Score.MaxValue()
	} else {
		isCorrect = false // Assume nil score is not correct
	}

	if isCorrect {
		title = "練習正確"
		content = fmt.Sprintf("恭喜你完成了練習！得分：%d", evt.Score.Value)
	} else {
		title = "練習需要改進"
		content = fmt.Sprintf("繼續加油！當前得分：%d", evt.Score.Value)
	}

	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLesson,
		title,
		content,
		map[string]interface{}{
			"exerciseID": evt.ExerciseID,
			"score":      evt.Score,
			"duration":   evt.Duration,
			"isCorrect":  isCorrect,
		},
	)

	return err
}

// handleLearningExerciseAttempted 處理學習領域練習嘗試事件
func (h *ExerciseHandler) handleLearningExerciseAttempted(evt *event.ExerciseAttemptedEvent) error {
	log.Printf("處理學習練習嘗試事件：ExerciseID=%s, UserID=%s, Time=%s",
		evt.ExerciseID, evt.UserID, evt.AttemptedAt)

	// 根據練習結果發送不同通知
	ctx := context.Background()
	var title, content string
	var isCorrect bool
	if evt.Score != nil {
		isCorrect = evt.Score.Value() == evt.Score.MaxValue()
	} else {
		isCorrect = false // Assume nil score is not correct
	}

	if isCorrect {
		title = "練習正確"
		content = fmt.Sprintf("恭喜你完成了練習！得分：%d", evt.Score.Value())
	} else {
		title = "練習需要改進"
		content = fmt.Sprintf("繼續加油！當前得分：%d", evt.Score.Value())
	}

	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLesson,
		title,
		content,
		map[string]interface{}{
			"exerciseID": evt.ExerciseID,
			"score":      evt.Score,
			"duration":   evt.Duration,
			"isCorrect":  isCorrect,
		},
	)

	return err
}
