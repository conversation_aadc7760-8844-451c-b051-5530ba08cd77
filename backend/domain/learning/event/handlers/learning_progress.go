package handlers

import (
	"context"
	"fmt"
	"log"

	coreevent "languagelearning/domain/core/event"
	"languagelearning/domain/learning/event"
	notificationentity "languagelearning/domain/notification/entity"
)

// LearningProgressHandler 處理學習進度相關事件
type LearningProgressHandler struct {
	*BaseHandler
}

// NewLearningProgressHandler 創建學習進度處理器
func NewLearningProgressHandler(base *BaseHandler) *LearningProgressHandler {
	return &LearningProgressHandler{
		BaseHandler: base,
	}
}

// Handle 實現 EventHandler 接口
func (h *LearningProgressHandler) Handle(e coreevent.Event) error {
	switch evt := e.(type) {
	case *event.LearningProgressUpdatedEvent:
		return h.handleLearningProgressUpdated(evt)
	default:
		return fmt.Errorf("unsupported event type: %T", e)
	}
}

// handleLearningProgressUpdated 處理學習進度更新事件
func (h *LearningProgressHandler) handleLearningProgressUpdated(evt *event.LearningProgressUpdatedEvent) error {
	log.Printf("處理學習進度更新事件：UserID=%s, Level=%d, Experience=%d",
		evt.UserID, evt.Level, evt.Experience)

	// 發送進度更新通知
	ctx := context.Background()
	title := "學習進度更新"
	content := fmt.Sprintf("恭喜！您已達到等級 %d，經驗值：%d", evt.Level, evt.Experience)

	if evt.StreakDays > 0 {
		content += fmt.Sprintf("，連續學習 %d 天！", evt.StreakDays)
	}

	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLearning,
		title,
		content,
		map[string]interface{}{
			"level":        evt.Level,
			"experience":   evt.Experience,
			"streakDays":   evt.StreakDays,
			"totalLessons": evt.TotalLessons,
		},
	)

	if err != nil {
		log.Printf("發送學習進度通知失敗: %v", err)
		return err
	}

	return nil
}
