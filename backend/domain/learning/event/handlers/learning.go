package handlers

import (
	"context"
	"fmt"
	"log"

	"languagelearning/domain/learning/event"
	notificationentity "languagelearning/domain/notification/entity"
)

// LearningHandler 處理學習相關事件
type LearningHandler struct {
	*BaseHandler
}

// NewLearningHandler 創建學習處理器
func NewLearningHandler(base *BaseHandler) *LearningHandler {
	return &LearningHandler{
		BaseHandler: base,
	}
}

// Handle 實現 EventHandler 接口
func (h *LearningHandler) Handle(e event.Event) error {
	switch evt := e.(type) {
	case *event.LearningProgressUpdatedEvent:
		return h.handleProgressUpdated(evt)
	case *event.LearningPathUpdatedEvent:
		return h.handlePathUpdated(evt)
	default:
		return fmt.Errorf("unsupported event type: %T", e)
	}
}

// handleProgressUpdated 處理進度更新事件
func (h *LearningHandler) handleProgressUpdated(evt *event.LearningProgressUpdatedEvent) error {
	log.Printf("處理進度更新事件：UserID=%s, Progress=%d%%, Time=%s",
		evt.UserID, evt.Progress.Value(), evt.EventTime)

	ctx := context.Background()
	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLesson,
		"學習進度更新",
		fmt.Sprintf("你的學習進度已更新至 %d%%", evt.Progress.Value()),
		map[string]interface{}{
			"progress": evt.Progress.Value(),
			"time":     evt.EventTime,
		},
	)

	return err
}

// handlePathUpdated 處理路徑更新事件
func (h *LearningHandler) handlePathUpdated(evt *event.LearningPathUpdatedEvent) error {
	log.Printf("處理路徑更新事件：UserID=%s, PathID=%s, Progress=%d%%, Time=%s",
		evt.UserID, evt.PathID, evt.Progress.Value(), evt.EventTime)

	ctx := context.Background()
	_, err := h.notificationService.CreateNotification(
		ctx,
		evt.UserID,
		notificationentity.NotificationTypeLesson,
		"學習路徑更新",
		fmt.Sprintf("你的學習路徑進度已更新至 %d%%", evt.Progress.Value()),
		map[string]interface{}{
			"pathID":   evt.PathID,
			"progress": evt.Progress.Value(),
			"time":     evt.EventTime,
		},
	)

	return err
}
