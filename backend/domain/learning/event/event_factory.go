package event

import (
	"encoding/json"
	"fmt"
	"time"

	coreevent "languagelearning/domain/core/event"
	exerciseentity "languagelearning/domain/exercise/entity"
	exerciseevent "languagelearning/domain/exercise/event"
	"languagelearning/domain/learning/entity"
	lessonevent "languagelearning/domain/learning/lesson/event"

	"github.com/google/uuid"
)

// RegisterEventFactories 註冊所有領域事件的重建工廠
func RegisterEventFactories(reconstructor *coreevent.DefaultEventReconstructor) {
	// 用戶相關
	reconstructor.RegisterEventFactory("user.registered", func(data json.RawMessage) (coreevent.Event, error) {
		var event UserRegisteredEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	reconstructor.RegisterEventFactory("user.learning_preference.updated", func(data json.RawMessage) (coreevent.Event, error) {
		var event UserLearningPreferenceUpdatedEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	// 成就相關
	reconstructor.RegisterEventFactory("achievement.unlocked", func(data json.RawMessage) (coreevent.Event, error) {
		var event AchievementUnlockedEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	reconstructor.RegisterEventFactory("user.level_up", func(data json.RawMessage) (coreevent.Event, error) {
		var event UserLevelUpEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	// 評估相關
	reconstructor.RegisterEventFactory("evaluation.completed", func(data json.RawMessage) (coreevent.Event, error) {
		var event EvaluationCompletedEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	// 通知相關
	reconstructor.RegisterEventFactory("notification.sent", func(data json.RawMessage) (coreevent.Event, error) {
		var event NotificationSentEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	reconstructor.RegisterEventFactory("notification.read", func(data json.RawMessage) (coreevent.Event, error) {
		var event NotificationReadEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	// 學習相關
	reconstructor.RegisterEventFactory("lesson.completed", func(data json.RawMessage) (coreevent.Event, error) {
		var event lessonevent.LessonCompletedEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	reconstructor.RegisterEventFactory("exercise.attempted", func(data json.RawMessage) (coreevent.Event, error) {
		var event exerciseevent.ExerciseAttemptedEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})

	reconstructor.RegisterEventFactory("learning_path.progress.updated", func(data json.RawMessage) (coreevent.Event, error) {
		var event LearningPathProgressUpdatedEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return &event, nil
	})
}

// LearningEventReconstructor 學習事件重構器
type LearningEventReconstructor struct {
	*coreevent.DefaultEventReconstructor
}

// NewLearningEventReconstructor 創建學習事件重構器
func NewLearningEventReconstructor() *LearningEventReconstructor {
	return &LearningEventReconstructor{
		DefaultEventReconstructor: coreevent.NewDefaultEventReconstructor(),
	}
}

// Reconstruct 重構事件
func (r *LearningEventReconstructor) Reconstruct(eventType string, data map[string]interface{}) (coreevent.Event, error) {
	switch eventType {
	case "lesson.completed":
		return r.reconstructLessonCompleted(data)
	case "exercise.attempted":
		return r.reconstructExerciseAttempted(data)
	case "learning_path.updated":
		return r.reconstructLearningPathUpdated(data)
	case "learning_progress.updated":
		return r.reconstructLearningProgressUpdated(data)
	default:
		return nil, fmt.Errorf("unsupported event type: %s", eventType)
	}
}

// reconstructLessonCompleted 重構課程完成事件
func (r *LearningEventReconstructor) reconstructLessonCompleted(data map[string]interface{}) (*lessonevent.LessonCompletedEvent, error) {
	lessonID, err := uuid.Parse(data["lessonID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid lesson ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	// Create a progress value object for the event
	progress, err := entity.NewProgress(100) // 100% complete
	if err != nil {
		return nil, fmt.Errorf("failed to create progress: %v", err)
	}

	return lessonevent.NewLessonCompletedEvent(lessonID, userID, progress), nil
}

// reconstructExerciseAttempted 重構練習嘗試事件
func (r *LearningEventReconstructor) reconstructExerciseAttempted(data map[string]interface{}) (*exerciseevent.ExerciseAttemptedEvent, error) {
	exerciseID, err := uuid.Parse(data["exerciseID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid exercise ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	score, err := entity.NewScore(int(data["score"].(float64)), int(data["totalScore"].(float64)))
	if err != nil {
		return nil, fmt.Errorf("invalid score: %v", err)
	}

	duration, err := entity.NewDuration(int(data["durationMinutes"].(float64)))
	if err != nil {
		return nil, fmt.Errorf("invalid duration: %v", err)
	}

	difficultyStr := data["difficulty"].(string)
	difficulty, err := entity.NewExerciseDifficulty(entity.Difficulty(difficultyStr))
	if err != nil {
		return nil, fmt.Errorf("invalid difficulty: %v", err)
	}

	// Convert learning/entity types to exercise/entity types
	// We need to create new instances of exercise domain types with the same values
	var exerciseScore *exerciseentity.Score
	if score != nil {
		s, err := exerciseentity.NewScore(score.Value(), score.MaxValue())
		if err != nil || s == nil {
			// If we can't create a valid score, return an error
			return nil, fmt.Errorf("failed to create exercise score: %v", err)
		}
		exerciseScore = s
	}

	var exerciseDuration *exerciseentity.Duration
	if duration != nil {
		d, err := exerciseentity.NewDuration(duration.Minutes())
		if err != nil || d == nil {
			return nil, fmt.Errorf("failed to create exercise duration: %v", err)
		}
		exerciseDuration = d
	}

	var exerciseDifficulty *exerciseentity.ExerciseDifficulty
	if difficulty != nil {
		d, err := exerciseentity.NewExerciseDifficulty(difficulty.Level().String())
		if err != nil || d == nil {
			return nil, fmt.Errorf("failed to create exercise difficulty: %v", err)
		}
		exerciseDifficulty = d
	}

	// Create a new exercise attempted event
	// Note: The function expects value types, but we have pointers
	// We need to ensure we're not passing nil pointers
	if exerciseScore == nil || exerciseDuration == nil || exerciseDifficulty == nil {
		return nil, fmt.Errorf("failed to convert all required fields: score=%v, duration=%v, difficulty=%v",
			exerciseScore != nil, exerciseDuration != nil, exerciseDifficulty != nil)
	}

	// Since NewExerciseAttemptedEvent expects value types, we need to dereference the pointers
	return exerciseevent.NewExerciseAttemptedEvent(
		exerciseID,
		userID,
		*exerciseScore,
		*exerciseDuration,
		*exerciseDifficulty,
		data["isCorrect"].(bool),
	), nil
}

// reconstructLearningPathUpdated 重構學習路徑更新事件
func (r *LearningEventReconstructor) reconstructLearningPathUpdated(data map[string]interface{}) (*LearningPathUpdatedEvent, error) {
	pathID, err := uuid.Parse(data["pathID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid path ID: %v", err)
	}

	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	updatedAt, err := time.Parse(time.RFC3339, data["updatedAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid updated at time: %v", err)
	}

	progress, err := entity.NewProgress(int(data["progress"].(float64)))
	if err != nil {
		return nil, fmt.Errorf("invalid progress: %v", err)
	}

	lessons := make([]interface{}, 0)
	if lessonsData, ok := data["lessons"].([]interface{}); ok {
		for _, lessonData := range lessonsData {
			lessonMap, ok := lessonData.(map[string]interface{})
			if !ok {
				continue
			}

			lessonID, err := uuid.Parse(lessonMap["id"].(string))
			if err != nil {
				continue
			}

			// Create lesson using the lesson entity
			// TODO: Fix lesson creation with proper types
			// For now, skip lesson creation to avoid compilation errors
			lessons = append(lessons, map[string]interface{}{
				"id":          lessonID,
				"title":       lessonMap["title"].(string),
				"description": lessonMap["description"].(string),
				"level":       lessonMap["level"].(string),
				"category":    lessonMap["category"].(string),
			})
		}
	}

	return &LearningPathUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"learning_path.updated",
			pathID,
			"learning_path",
			data,
		),
		PathID:    pathID,
		UserID:    userID,
		Progress:  progress,
		Lessons:   lessons,
		UpdatedAt: updatedAt,
	}, nil
}

// reconstructLearningProgressUpdated 重構學習進度更新事件
func (r *LearningEventReconstructor) reconstructLearningProgressUpdated(data map[string]interface{}) (*LearningProgressUpdatedEvent, error) {
	userID, err := uuid.Parse(data["userID"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	updatedAt, err := time.Parse(time.RFC3339, data["updatedAt"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid updated at time: %v", err)
	}

	progress, err := entity.NewProgress(int(data["progress"].(float64)))
	if err != nil {
		return nil, fmt.Errorf("invalid progress: %v", err)
	}

	return &LearningProgressUpdatedEvent{
		BaseEvent: coreevent.NewBaseEvent(
			"learning_progress.updated",
			userID,
			"learning_progress",
			data,
		),
		UserID:       userID,
		Progress:     progress,
		Level:        int(data["level"].(float64)),
		Experience:   int(data["experience"].(float64)),
		UpdatedAt:    updatedAt,
		StreakDays:   int(data["streakDays"].(float64)),
		TotalLessons: int(data["totalLessons"].(float64)),
	}, nil
}
