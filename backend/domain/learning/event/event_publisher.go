package event

import (
	"context"
	"sync"

	coreevent "languagelearning/domain/core/event"
)

// LearningEventPublisher 學習領域事件發布者
type LearningEventPublisher struct {
	*coreevent.DefaultEventBus
	learningHandlers map[string][]LearningEventHandler
	mu               sync.RWMutex
}

// NewLearningEventPublisher 創建學習領域事件發布者
func NewLearningEventPublisher() *LearningEventPublisher {
	return &LearningEventPublisher{
		DefaultEventBus:  coreevent.NewDefaultEventBus(),
		learningHandlers: make(map[string][]LearningEventHandler),
	}
}

// PublishLearningEvent 發布學習領域事件
func (p *LearningEventPublisher) PublishLearningEvent(ctx context.Context, event LearningEvent) error {
	// 發布到核心事件總線
	if err := p.Publish(ctx, event); err != nil {
		return err
	}

	// 發布到學習領域事件處理器
	p.mu.RLock()
	defer p.mu.RUnlock()

	handlers := p.learningHandlers[event.LearningEventType()]
	for _, handler := range handlers {
		// 在 goroutine 中異步執行處理器
		go func(h LearningEventHandler, ev LearningEvent) {
			_ = h.HandleLearningEvent(ev) // 忽略錯誤，或者可以添加錯誤處理邏輯
		}(handler, event)
	}
	return nil
}

// SubscribeLearningEvent 訂閱學習領域事件
func (p *LearningEventPublisher) SubscribeLearningEvent(eventType string, handler LearningEventHandler) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if _, ok := p.learningHandlers[eventType]; !ok {
		p.learningHandlers[eventType] = make([]LearningEventHandler, 0)
	}
	p.learningHandlers[eventType] = append(p.learningHandlers[eventType], handler)
	return nil
}

// UnsubscribeLearningEvent 取消訂閱學習領域事件
func (p *LearningEventPublisher) UnsubscribeLearningEvent(eventType string, handler LearningEventHandler) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if handlers, ok := p.learningHandlers[eventType]; ok {
		for i, h := range handlers {
			if h == handler {
				p.learningHandlers[eventType] = append(handlers[:i], handlers[i+1:]...)
				return nil
			}
		}
	}
	return nil
}
