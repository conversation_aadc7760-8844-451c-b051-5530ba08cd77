package event

import (
	"fmt"
	"log"

	"languagelearning/domain/core/event"
)

// LessonProgressHandler 課程進度更新事件處理器
type LessonProgressHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 學習路徑服務
	// - 用戶服務
}

// NewLessonProgressHandler 創建課程進度更新事件處理器
func NewLessonProgressHandler() *LessonProgressHandler {
	return &LessonProgressHandler{}
}

// Handle 處理課程進度更新事件
func (h *LessonProgressHandler) Handle(e event.Event) error {
	progressEvent, ok := e.(*LessonProgressUpdatedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶學習統計
	// 2. 更新學習路徑進度
	// 3. 檢查是否需要發送進度提醒
	// 4. 更新用戶學習時間統計

	// Progress is a value object with a Value() method
	var progressValue int
	if progressEvent.Progress != nil {
		progressValue = progressEvent.Progress.Value()
	}

	log.Printf("Lesson progress updated: %s by user %s, progress: %d%%",
		progressEvent.LessonID,
		progressEvent.UserID,
		progressValue,
	)
	return nil
}

// RegisterLessonProgressHandler 註冊課程進度更新事件處理器
func RegisterLessonProgressHandler(bus event.EventBus) error {
	handler := NewLessonProgressHandler()
	return bus.Subscribe("lesson.progress.updated", handler)
}
