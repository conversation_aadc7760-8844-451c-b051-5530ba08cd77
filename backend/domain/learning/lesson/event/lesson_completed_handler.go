package event

import (
	"fmt"
	"log"

	"languagelearning/domain/core/event"
)

// LessonCompletedHandler 課程完成事件處理器
type LessonCompletedHandler struct {
	// TODO: 添加需要的依賴，例如：
	// - 成就服務
	// - 通知服務
	// - 學習路徑服務
	// - 用戶服務
}

// NewLessonCompletedHandler 創建課程完成事件處理器
func NewLessonCompletedHandler() *LessonCompletedHandler {
	return &LessonCompletedHandler{}
}

// Handle 處理課程完成事件
func (h *LessonCompletedHandler) Handle(e event.Event) error {
	completedEvent, ok := e.(*LessonCompletedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: %T", e)
	}

	// TODO: 實現事件處理邏輯，例如：
	// 1. 更新用戶學習統計
	// 2. 檢查並解鎖相關成就
	// 3. 發送完成通知
	// 4. 更新學習路徑進度
	// 5. 推薦相關課程

	log.Printf("Lesson completed: %s by user %s", completedEvent.LessonID, completedEvent.UserID)
	return nil
}

// RegisterLessonCompletedHandler 註冊課程完成事件處理器
func RegisterLessonCompletedHandler(bus event.EventBus) error {
	handler := NewLessonCompletedHandler()
	return bus.Subscribe("lesson.completed", handler)
}
