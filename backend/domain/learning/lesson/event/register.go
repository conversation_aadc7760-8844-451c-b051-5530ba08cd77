package event

import (
	"fmt"

	"languagelearning/domain/core/event"
)

// RegisterLessonEventHandlers 註冊所有課程相關的事件處理器
func RegisterLessonEventHandlers(bus event.EventBus) error {
	// 註冊課程完成事件處理器
	if err := RegisterLessonCompletedHandler(bus); err != nil {
		return fmt.Errorf("failed to register lesson completed handler: %w", err)
	}

	// 註冊課程進度更新事件處理器
	if err := RegisterLessonProgressHandler(bus); err != nil {
		return fmt.Errorf("failed to register lesson progress handler: %w", err)
	}

	// TODO: 註冊其他課程相關的事件處理器
	// - 課程創建事件處理器
	// - 課程更新事件處理器

	return nil
}
