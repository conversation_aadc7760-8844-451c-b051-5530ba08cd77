package event

import (
	"time"

	"languagelearning/domain/core/event"
	lesson "languagelearning/domain/learning/entity"

	"github.com/google/uuid"
)

// LessonCreatedEvent 課程創建事件
type LessonCreatedEvent struct {
	*event.BaseEvent
	Lesson *lesson.Lesson `json:"lesson"`
}

// NewLessonCreatedEvent 創建課程創建事件
func NewLessonCreatedEvent(lesson *lesson.Lesson) *LessonCreatedEvent {
	return &LessonCreatedEvent{
		BaseEvent: event.NewBaseEvent(
			"lesson.created",
			lesson.ID,
			"lesson",
			lesson,
		),
		Lesson: lesson,
	}
}

// LessonUpdatedEvent 課程更新事件
type LessonUpdatedEvent struct {
	*event.BaseEvent
	Lesson *lesson.Lesson `json:"lesson"`
}

// NewLessonUpdatedEvent 創建課程更新事件
func NewLessonUpdatedEvent(lesson *lesson.Lesson) *LessonUpdatedEvent {
	return &LessonUpdatedEvent{
		BaseEvent: event.NewBaseEvent(
			"lesson.updated",
			lesson.ID,
			"lesson",
			lesson,
		),
		Lesson: lesson,
	}
}

// LessonProgressUpdatedEvent 課程進度更新事件
type LessonProgressUpdatedEvent struct {
	*event.BaseEvent
	LessonID  uuid.UUID        `json:"lesson_id"`
	UserID    uuid.UUID        `json:"user_id"`
	Progress  *lesson.Progress `json:"progress"`
	UpdatedAt time.Time        `json:"updated_at"`
}

// NewLessonProgressUpdatedEvent 創建課程進度更新事件
func NewLessonProgressUpdatedEvent(lessonID, userID uuid.UUID, progress *lesson.Progress) *LessonProgressUpdatedEvent {
	return &LessonProgressUpdatedEvent{
		BaseEvent: event.NewBaseEvent(
			"lesson.progress.updated",
			lessonID,
			"lesson",
			progress,
		),
		LessonID:  lessonID,
		UserID:    userID,
		Progress:  progress,
		UpdatedAt: time.Now(),
	}
}

// LessonCompletedEvent 課程完成事件
type LessonCompletedEvent struct {
	*event.BaseEvent
	LessonID    uuid.UUID        `json:"lesson_id"`
	UserID      uuid.UUID        `json:"user_id"`
	Progress    *lesson.Progress `json:"progress"`
	CompletedAt time.Time        `json:"completed_at"`
}

// NewLessonCompletedEvent 創建課程完成事件
func NewLessonCompletedEvent(lessonID, userID uuid.UUID, progress *lesson.Progress) *LessonCompletedEvent {
	return &LessonCompletedEvent{
		BaseEvent: event.NewBaseEvent(
			"lesson.completed",
			lessonID,
			"lesson",
			progress,
		),
		LessonID:    lessonID,
		UserID:      userID,
		Progress:    progress,
		CompletedAt: time.Now(),
	}
}
