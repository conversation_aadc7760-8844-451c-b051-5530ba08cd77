package lesson

import (
	"time"

	"github.com/google/uuid"
)

// LessonStatus 課程狀態
type LessonStatus string

const (
	// Draft 草稿狀態
	Draft LessonStatus = "draft"
	// Published 已發布狀態
	Published LessonStatus = "published"
	// Archived 已歸檔狀態
	Archived LessonStatus = "archived"
)

// LessonType 課程類型
type LessonType string

const (
	// GrammarLesson 語法課程
	GrammarLesson LessonType = "grammar"
	// VocabularyLesson 詞彙課程
	VocabularyLesson LessonType = "vocabulary"
	// SpeakingLesson 口語課程
	SpeakingLesson LessonType = "speaking"
	// ListeningLesson 聽力課程
	ListeningLesson LessonType = "listening"
	// ReadingLesson 閱讀課程
	ReadingLesson LessonType = "reading"
	// WritingLesson 寫作課程
	WritingLesson LessonType = "writing"
	// PronunciationLesson 發音課程
	PronunciationLesson LessonType = "pronunciation"
	// ConversationLesson 會話課程
	ConversationLesson LessonType = "conversation"
	// CultureLesson 文化課程
	CultureLesson LessonType = "culture"
)

// ExerciseDifficulty 練習難度
type ExerciseDifficulty struct {
	Level Level `json:"level"`
}

// NewExerciseDifficulty 創建練習難度
func NewExerciseDifficulty(level Level) *ExerciseDifficulty {
	return &ExerciseDifficulty{
		Level: level,
	}
}

// Resource 課程資源
type Resource struct {
	ID          uuid.UUID `json:"id"`
	Type        string    `json:"type"`
	URL         string    `json:"url"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// NewResource 創建新資源
func NewResource(resourceType, url, title, description string) (*Resource, error) {
	now := time.Now()
	return &Resource{
		ID:          uuid.New(),
		Type:        resourceType,
		URL:         url,
		Title:       title,
		Description: description,
		CreatedAt:   now,
		UpdatedAt:   now,
	}, nil
}
