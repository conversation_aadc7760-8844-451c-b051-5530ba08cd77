package lesson

import (
	"fmt"
)

// Language 語言值對象
type Language struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// NewLanguage 創建語言值對象
func NewLanguage(code, name string) *Language {
	return &Language{
		Code: code,
		Name: name,
	}
}

// Duration 時長值對象
type Duration struct {
	Minutes int `json:"minutes"`
}

// NewDuration 創建時長值對象
func NewDuration(minutes int) (*Duration, error) {
	if minutes <= 0 {
		return nil, fmt.Errorf("duration must be positive")
	}
	return &Duration{
		Minutes: minutes,
	}, nil
}

// ProgressValue 進度值對象
type ProgressValue struct {
	Value int `json:"value"`
}

// NewProgressValue 創建進度值對象
func NewProgressValue(value int) (*ProgressValue, error) {
	if value < 0 || value > 100 {
		return nil, fmt.Errorf("progress value must be between 0 and 100")
	}
	return &ProgressValue{
		Value: value,
	}, nil
}

// IsComplete 檢查是否完成
func (p *ProgressValue) IsComplete() bool {
	return p.Value >= 100
}
