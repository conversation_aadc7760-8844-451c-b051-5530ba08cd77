package controllers

import (
	"languagelearning/models"
	"languagelearning/utils/response"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SaveProgressRequest represents the request to save assessment progress
type SaveProgressRequest struct {
	EvaluationID    string `json:"evaluationId" binding:"required"`
	CurrentSection  int    `json:"currentSection" binding:"required,min=0"`
	CurrentQuestion int    `json:"currentQuestion" binding:"required,min=0"`
	SessionToken    string `json:"sessionToken" binding:"required"`
}

// GetProgressRequest represents the request to get assessment progress
type GetProgressRequest struct {
	EvaluationID string `json:"evaluationId" binding:"required"`
	SessionToken string `json:"sessionToken" binding:"required"`
}

// @Summary Save assessment progress
// @Description Save the user's progress in an assessment to allow resuming later
// @Tags Assessment
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body SaveProgressRequest true "Progress details"
// @Success 200 {object} utils.Response "Progress saved successfully"
// @Failure 400 {object} utils.Response "Invalid request body"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Evaluation not found"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/assessments/progress [post]
func SaveAssessmentProgress(c *gin.Context) {
	// Get the request body
	var req SaveProgressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Parse the evaluation ID
	evalID, err := uuid.Parse(req.EvaluationID)
	if err != nil {
		response.ValidationError(c, "Invalid evaluation ID", map[string]string{"evaluationId": "Invalid UUID format"})
		return
	}

	// Check if the evaluation exists and belongs to the user
	var evaluation models.Evaluation
	result := models.DB.Where("id = ? AND user_id = ?", evalID, userID).First(&evaluation)
	if result.Error != nil {
		response.NotFound(c, "Evaluation not found")
		return
	}

	// Check if a progress record already exists
	var progress models.AssessmentProgress
	result = models.DB.Where("user_id = ? AND evaluation_id = ? AND session_token = ?",
		userID, evalID, req.SessionToken).First(&progress)

	if result.Error != nil {
		// Create a new progress record
		progress = models.AssessmentProgress{
			UserID:          userID.(uuid.UUID),
			EvaluationID:    evalID,
			CurrentSection:  req.CurrentSection,
			CurrentQuestion: req.CurrentQuestion,
			SessionToken:    req.SessionToken,
			LastUpdated:     time.Now(),
			CreatedAt:       time.Now(),
		}

		if err := models.DB.Create(&progress).Error; err != nil {
			response.InternalError(c, "Failed to save assessment progress")
			return
		}
	} else {
		// Update the existing progress record
		progress.CurrentSection = req.CurrentSection
		progress.CurrentQuestion = req.CurrentQuestion
		progress.LastUpdated = time.Now()

		if err := models.DB.Save(&progress).Error; err != nil {
			response.InternalError(c, "Failed to update assessment progress")
			return
		}
	}

	response.Success(c, http.StatusOK, progress.ToResponse(), "Assessment progress saved successfully")
}

// @Summary Get assessment progress
// @Description Get the user's progress in an assessment to allow resuming
// @Tags Assessment
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param evaluationId query string true "Evaluation ID"
// @Param sessionToken query string true "Session token"
// @Success 200 {object} utils.Response "Progress retrieved successfully"
// @Failure 400 {object} utils.Response "Invalid request parameters"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Progress not found"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/assessments/progress [get]
func GetAssessmentProgress(c *gin.Context) {
	// Get the query parameters
	evalIDStr := c.Query("evaluationId")
	sessionToken := c.Query("sessionToken")

	if evalIDStr == "" || sessionToken == "" {
		response.ValidationError(c, "Missing required parameters",
			map[string]string{"evaluationId": "Required", "sessionToken": "Required"})
		return
	}

	// Parse the evaluation ID
	evalID, err := uuid.Parse(evalIDStr)
	if err != nil {
		response.ValidationError(c, "Invalid evaluation ID",
			map[string]string{"evaluationId": "Invalid UUID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Find the progress record
	var progress models.AssessmentProgress
	result := models.DB.Where("user_id = ? AND evaluation_id = ? AND session_token = ?",
		userID, evalID, sessionToken).First(&progress)

	if result.Error != nil {
		response.NotFound(c, "Assessment progress not found")
		return
	}

	response.Success(c, http.StatusOK, progress.ToResponse(), "Assessment progress retrieved successfully")
}
