package controllers

import (
	"languagelearning/domain/learning/service"
	"languagelearning/utils/response"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// PersonalizedLearningController handles the personalized learning functionality
type PersonalizedLearningController struct {
	Service service.PersonalizedLearningService
}

// NewPersonalizedLearningController creates a new personalized learning controller
func NewPersonalizedLearningController(service service.PersonalizedLearningService) *PersonalizedLearningController {
	return &PersonalizedLearningController{
		Service: service,
	}
}

// @Summary Get personalized learning status
// @Description Get the current status of a user's personalized learning journey
// @Tags Personalized Learning
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "Personalized learning status retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/personalized-learning/status [get]
func (c *PersonalizedLearningController) GetPersonalizedLearningStatus(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the status
	status, err := c.Service.GetPersonalizedLearningStatus(userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, "Failed to get personalized learning status")
		return
	}

	response.Success(ctx, http.StatusOK, status, "Personalized learning status retrieved successfully")
}

// @Summary Initiate personalized learning
// @Description Start the personalized learning process for a new user by creating an initial assessment
// @Tags Personalized Learning
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "Initial assessment created successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/personalized-learning/initiate [post]
func (c *PersonalizedLearningController) InitiatePersonalizedLearning(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Create initial assessment
	assessment, err := c.Service.CreateInitialAssessmentForNewUser(userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, "Failed to create initial assessment")
		return
	}

	response.Success(ctx, http.StatusOK, assessment, "Initial assessment created successfully")
}

// Note: The following methods have been moved to learning_path_controller.go:
// - CreateExerciseSetFromAssessment
// - UpdateExerciseSetBasedOnResults
// - GetNextExercise
// - CompleteExercise
