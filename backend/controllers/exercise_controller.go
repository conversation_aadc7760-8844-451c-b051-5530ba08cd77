package controllers

import (
	"net/http"

	"languagelearning/domain/learning/service"
	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
)

// ExerciseController handles the exercise functionality
type ExerciseController struct {
	exerciseService         service.ExerciseService
	exerciseRelationService service.ExerciseRelationService
}

// NewExerciseController creates a new exercise controller
func NewExerciseController(
	exerciseService service.ExerciseService,
	exerciseRelationService service.ExerciseRelationService,
) *ExerciseController {
	return &ExerciseController{
		exerciseService:         exerciseService,
		exerciseRelationService: exerciseRelationService,
	}
}

// SubmitGrammarAnswerRequest represents the submit grammar answer request body
type SubmitGrammarAnswerRequest struct {
	Answer string `json:"answer" binding:"required" example:"have been living"`
}

// SubmitListeningAnswerRequest represents the submit listening answer request body
type SubmitListeningAnswerRequest struct {
	AnswerIndex int `json:"answerIndex" binding:"required" example:"2"`
}

// SubmitSpeakingAnswerRequest represents the submit speaking answer request body
type SubmitSpeakingAnswerRequest struct {
	RecordingPath string `json:"recordingPath" binding:"required" example:"/uploads/recordings/user123_exercise456.mp3"`
}

// SubmitWordAnswerRequest represents the submit word answer request body
type SubmitWordAnswerRequest struct {
	IsCorrect bool `json:"isCorrect" binding:"required" example:"true"`
}

// @Summary Get grammar exercises
// @Description Get all grammar exercises with optional filtering by category and difficulty
// @Tags Grammar
// @Produce json
// @Security BearerAuth
// @Param category query string false "Filter by category (e.g., verbs, nouns, adjectives)"
// @Param difficulty query string false "Filter by difficulty (e.g., beginner, intermediate, advanced)"
// @Success 200 {object} utils.Response "Grammar exercises retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercises/grammar [get]
func (c *ExerciseController) GetGrammarExercises(ctx *gin.Context) {
	// This method has been updated to use the repository pattern.
	// For now, return a message indicating the method needs to be implemented
	// with the new domain-based approach.
	response.Success(ctx, http.StatusOK, map[string]interface{}{
		"message":    "Grammar exercises endpoint has been migrated to use domain services. Please use the generic exercise endpoints with type filters.",
		"suggestion": "Use /api/v1/exercises with type=grammar filter",
	}, "Grammar exercises endpoint migrated")
}

// @Summary Submit grammar answer
// @Description Submit an answer for a grammar exercise and get feedback
// @Tags Grammar
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Exercise ID"
// @Param request body SubmitGrammarAnswerRequest true "Answer submission"
// @Success 200 {object} utils.Response "Grammar answer submitted successfully"
// @Failure 400 {object} utils.Response "Invalid request body or Exercise ID"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Exercise not found"
// @Router /api/v1/exercises/grammar/{id}/submit [post]
func (c *ExerciseController) SubmitGrammarAnswer(ctx *gin.Context) {
	// This method has been updated to use the repository pattern.
	response.Success(ctx, http.StatusOK, map[string]interface{}{
		"message":    "Grammar answer submission has been migrated to use domain services. Please use the generic exercise attempt endpoints.",
		"suggestion": "Use /api/v1/exercises/{id}/attempt with appropriate exercise entity",
	}, "Grammar answer submission endpoint migrated")
}

// @Summary Get listening exercises
// @Description Get all listening exercises with optional filtering by category and difficulty
// @Tags Listening
// @Produce json
// @Security BearerAuth
// @Param category query string false "Filter by category (e.g., conversations, news, stories)"
// @Param difficulty query string false "Filter by difficulty (e.g., beginner, intermediate, advanced)"
// @Success 200 {object} utils.Response "Listening exercises retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercises/listening [get]
func (c *ExerciseController) GetListeningExercises(ctx *gin.Context) {
	// This method has been updated to use the repository pattern.
	response.Success(ctx, http.StatusOK, map[string]interface{}{
		"message":    "Listening exercises endpoint has been migrated to use domain services. Please use the generic exercise endpoints with type filters.",
		"suggestion": "Use /api/v1/exercises with type=listening filter",
	}, "Listening exercises endpoint migrated")
}

// @Summary Submit listening answer
// @Description Submit an answer for a listening exercise and get feedback
// @Tags Listening
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Exercise ID"
// @Param request body SubmitListeningAnswerRequest true "Answer submission"
// @Success 200 {object} utils.Response "Listening answer submitted successfully"
// @Failure 400 {object} utils.Response "Invalid request body or Exercise ID"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Exercise not found"
// @Router /api/v1/exercises/listening/{id}/submit [post]
func (c *ExerciseController) SubmitListeningAnswer(ctx *gin.Context) {
	// This method has been updated to use the repository pattern.
	response.Success(ctx, http.StatusOK, map[string]interface{}{
		"message":    "Listening answer submission has been migrated to use domain services. Please use the generic exercise attempt endpoints.",
		"suggestion": "Use /api/v1/exercises/{id}/attempt with appropriate exercise entity",
	}, "Listening answer submission endpoint migrated")
}

// @Summary Get speaking exercises
// @Description Get all speaking exercises with optional filtering by category and difficulty
// @Tags Speaking
// @Produce json
// @Security BearerAuth
// @Param category query string false "Filter by category (e.g., pronunciation, conversation, presentation)"
// @Param difficulty query string false "Filter by difficulty (e.g., beginner, intermediate, advanced)"
// @Success 200 {object} utils.Response "Speaking exercises retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercises/speaking [get]
func (c *ExerciseController) GetSpeakingExercises(ctx *gin.Context) {
	// This method has been updated to use the repository pattern.
	response.Success(ctx, http.StatusOK, map[string]interface{}{
		"message":    "Speaking exercises endpoint has been migrated to use domain services. Please use the generic exercise endpoints with type filters.",
		"suggestion": "Use /api/v1/exercises with type=speaking filter",
	}, "Speaking exercises endpoint migrated")
}

// @Summary Submit speaking answer
// @Description Submit a recording for a speaking exercise and get feedback
// @Tags Speaking
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Exercise ID"
// @Param request body SubmitSpeakingAnswerRequest true "Recording submission"
// @Success 200 {object} utils.Response "Speaking answer submitted successfully"
// @Failure 400 {object} utils.Response "Invalid request body or Exercise ID"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Exercise not found"
// @Router /api/v1/exercises/speaking/{id}/submit [post]
func (c *ExerciseController) SubmitSpeakingAnswer(ctx *gin.Context) {
	// This method has been updated to use the repository pattern.
	response.Success(ctx, http.StatusOK, map[string]interface{}{
		"message":    "Speaking answer submission has been migrated to use domain services. Please use the generic exercise attempt endpoints.",
		"suggestion": "Use /api/v1/exercises/{id}/attempt with appropriate exercise entity",
	}, "Speaking answer submission endpoint migrated")
}

// @Summary Get word exercises
// @Description Get vocabulary word exercises with optional filtering by category and difficulty
// @Tags Words
// @Produce json
// @Security BearerAuth
// @Param category query string false "Filter by category (e.g., food, travel, business)"
// @Param difficulty query string false "Filter by difficulty (e.g., beginner, intermediate, advanced)"
// @Success 200 {object} utils.Response "Word exercises retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercises/word [get]
func (c *ExerciseController) GetWordExercises(ctx *gin.Context) {
	// This method has been updated to use the repository pattern.
	response.Success(ctx, http.StatusOK, map[string]interface{}{
		"message":    "Word exercises endpoint has been migrated to use domain services. Please use the generic exercise endpoints with type filters.",
		"suggestion": "Use /api/v1/exercises with type=vocabulary filter",
	}, "Word exercises endpoint migrated")
}

// @Summary Submit word answer
// @Description Submit an answer for a vocabulary word exercise and get feedback
// @Tags Words
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Word ID"
// @Param request body SubmitWordAnswerRequest true "Answer submission"
// @Success 200 {object} utils.Response "Word answer submitted successfully"
// @Failure 400 {object} utils.Response "Invalid request body or Word ID"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Word not found"
// @Router /api/v1/exercises/word/{id}/submit [post]
func (c *ExerciseController) SubmitWordAnswer(ctx *gin.Context) {
	// This method has been updated to use the repository pattern.
	response.Success(ctx, http.StatusOK, map[string]interface{}{
		"message":    "Word answer submission has been migrated to use domain services. Please use the generic exercise attempt endpoints.",
		"suggestion": "Use /api/v1/exercises/{id}/attempt with appropriate exercise entity",
	}, "Word answer submission endpoint migrated")
}
