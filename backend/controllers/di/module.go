package di

import (
	"go.uber.org/dig"
	"languagelearning/controllers"
	v1 "languagelearning/controllers/v1"
	coredi "languagelearning/domain/core/di"
)

// ControllerModule 控制器模塊
type ControllerModule struct {
	coredi.BaseModule
}

// NewControllerModule 創建控制器模塊
func NewControllerModule() coredi.Module {
	return &ControllerModule{
		BaseModule: coredi.NewBaseModule(
			"controller",
			[]string{"core", "user", "learning", "achievement", "auth", "notification", "evaluation"}, // 依賴所有領域模塊
			[]coredi.ModuleFeature{coredi.FeatureBasic},
		),
	}
}

// Register 註冊控制器模塊的所有依賴
func (m *ControllerModule) Register(container *dig.Container) error {
	// 註冊課程控制器
	if err := container.Provide(controllers.NewLessonController); err != nil {
		return err
	}

	// 註冊練習關係控制器
	if err := container.Provide(controllers.NewExerciseRelationController); err != nil {
		return err
	}

	// 註冊用戶控制器
	if err := container.Provide(controllers.NewUserController); err != nil {
		return err
	}

	// 註冊認證控制器
	if err := container.Provide(controllers.NewAuthController); err != nil {
		return err
	}

	// 註冊練習控制器
	if err := container.Provide(controllers.NewExerciseController); err != nil {
		return err
	}

	// 註冊通知控制器
	if err := container.Provide(controllers.NewNotificationController); err != nil {
		return err
	}

	// 註冊個性化學習控制器
	if err := container.Provide(controllers.NewPersonalizedLearningController); err != nil {
		return err
	}

	// 註冊學習路徑控制器
	if err := container.Provide(controllers.NewLearningPathController); err != nil {
		return err
	}

	// 註冊成就控制器
	if err := container.Provide(controllers.NewAchievementController); err != nil {
		return err
	}

	// 註冊評估控制器
	if err := container.Provide(controllers.NewEvaluationController); err != nil {
		return err
	}

	// 註冊單詞控制器
	if err := container.Provide(controllers.NewWordController); err != nil {
		return err
	}

	// 註冊練習控制器
	if err := container.Provide(controllers.NewPracticeController); err != nil {
		return err
	}

	// 註冊API路由
	if err := container.Provide(v1.NewAPIRouter); err != nil {
		return err
	}

	return nil
}
