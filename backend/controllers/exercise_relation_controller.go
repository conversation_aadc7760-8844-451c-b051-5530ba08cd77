package controllers

import (
	learningService "languagelearning/domain/learning/service"
	"languagelearning/models"
	"languagelearning/utils/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ExerciseRelationController handles the exercise relation functionality
type ExerciseRelationController struct {
	Service learningService.ExerciseRelationService
}

// NewExerciseRelationController creates a new exercise relation controller
func NewExerciseRelationController(service learningService.ExerciseRelationService) *ExerciseRelationController {
	return &ExerciseRelationController{
		Service: service,
	}
}

// CreateRelationRequest represents the request to create a relation
type CreateRelationRequest struct {
	SourceID     string `json:"sourceId" binding:"required" example:"550e8400-e29b-41d4-a716-************"`
	TargetID     string `json:"targetId" binding:"required" example:"550e8400-e29b-41d4-a716-************"`
	SourceType   string `json:"sourceType" binding:"required" example:"grammar"`        // grammar, vocabulary, listening, speaking
	TargetType   string `json:"targetType" binding:"required" example:"vocabulary"`     // grammar, vocabulary, listening, speaking
	RelationType string `json:"relationType" binding:"required" example:"prerequisite"` // prerequisite, related, similar
	Strength     int    `json:"strength" binding:"required,min=1,max=3" example:"2"`    // 1=weak, 2=medium, 3=strong
	Description  string `json:"description" example:"Grammar concept needed to understand this vocabulary"`
}

// UpdateRelationRequest represents the request to update a relation
type UpdateRelationRequest struct {
	RelationType string `json:"relationType" binding:"required" example:"related"`   // prerequisite, related, similar
	Strength     int    `json:"strength" binding:"required,min=1,max=3" example:"3"` // 1=weak, 2=medium, 3=strong
	Description  string `json:"description" example:"Updated relationship description"`
}

// UpdateDifficultyMetadataRequest represents the request to update difficulty metadata
type UpdateDifficultyMetadataRequest struct {
	ComplexityScore float64  `json:"complexityScore" binding:"required,min=1,max=10" example:"7.5"` // 1-10 scale
	TimeToComplete  int      `json:"timeToComplete" binding:"required,min=1" example:"300"`         // in seconds
	Tags            []string `json:"tags" example:"[\"grammar\", \"verbs\", \"past-tense\"]"`
}

// @Summary Create exercise relation
// @Description Create a relationship between two exercises (e.g., prerequisite, related)
// @Tags Exercise Relations
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateRelationRequest true "Relation details"
// @Success 201 {object} utils.Response "Relation created successfully"
// @Failure 400 {object} utils.Response "Invalid request body or validation errors"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercise-relations [post]
func (c *ExerciseRelationController) CreateRelation(ctx *gin.Context) {
	// Get the request body
	var req CreateRelationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid request body")
		return
	}

	// Parse UUIDs
	sourceID, err := uuid.Parse(req.SourceID)
	if err != nil {
		response.ValidationError(ctx, "Invalid source ID", map[string]string{"sourceId": "Invalid UUID format"})
		return
	}

	targetID, err := uuid.Parse(req.TargetID)
	if err != nil {
		response.ValidationError(ctx, "Invalid target ID", map[string]string{"targetId": "Invalid UUID format"})
		return
	}

	// Create relation
	relation, err := c.Service.CreateRelation(
		sourceID,
		targetID,
		req.SourceType,
		req.TargetType,
		models.RelationType(req.RelationType),
		models.RelationStrength(req.Strength),
		req.Description,
	)

	if err != nil {
		response.InternalError(ctx, "Failed to create relation: "+err.Error())
		return
	}

	response.Success(ctx, http.StatusCreated, relation, "Relation created successfully")
}

// @Summary Update exercise relation
// @Description Update an existing relationship between two exercises
// @Tags Exercise Relations
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Relation ID"
// @Param request body UpdateRelationRequest true "Updated relation details"
// @Success 200 {object} utils.Response "Relation updated successfully"
// @Failure 400 {object} utils.Response "Invalid request body or validation errors"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercise-relations/{id} [put]
func (c *ExerciseRelationController) UpdateRelation(ctx *gin.Context) {
	// Get the relation ID from the URL
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ValidationError(ctx, "Invalid relation ID", map[string]string{"id": "Invalid ID format"})
		return
	}

	// Get the request body
	var req UpdateRelationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid request body")
		return
	}

	// Update relation
	relation, err := c.Service.UpdateRelation(
		uint(id),
		models.RelationType(req.RelationType),
		models.RelationStrength(req.Strength),
		req.Description,
	)

	if err != nil {
		response.InternalError(ctx, "Failed to update relation: "+err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, relation, "Relation updated successfully")
}

// @Summary Delete exercise relation
// @Description Delete an existing relationship between two exercises
// @Tags Exercise Relations
// @Produce json
// @Security BearerAuth
// @Param id path int true "Relation ID"
// @Success 200 {object} utils.Response "Relation deleted successfully"
// @Failure 400 {object} utils.Response "Invalid relation ID"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercise-relations/{id} [delete]
func (c *ExerciseRelationController) DeleteRelation(ctx *gin.Context) {
	// Get the relation ID from the URL
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ValidationError(ctx, "Invalid relation ID", map[string]string{"id": "Invalid ID format"})
		return
	}

	// Delete relation
	if err := c.Service.DeleteRelation(uint(id)); err != nil {
		response.InternalError(ctx, "Failed to delete relation: "+err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, nil, "Relation deleted successfully")
}

// @Summary Get exercise relations
// @Description Get all relationships for a specific exercise
// @Tags Exercise Relations
// @Produce json
// @Security BearerAuth
// @Param id path string true "Exercise ID"
// @Param type query string true "Exercise type (grammar, vocabulary, listening, speaking)"
// @Success 200 {object} utils.Response "Relations retrieved successfully"
// @Failure 400 {object} utils.Response "Invalid exercise ID or type"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercise-relations/{id} [get]
func (c *ExerciseRelationController) GetExerciseRelations(ctx *gin.Context) {
	// Get the exercise ID from the URL
	exerciseIDStr := ctx.Param("id")
	exerciseID, err := uuid.Parse(exerciseIDStr)
	if err != nil {
		response.ValidationError(ctx, "Invalid exercise ID", map[string]string{"id": "Invalid UUID format"})
		return
	}

	// Get the exercise type from the query parameters
	exerciseType := ctx.Query("type")
	if exerciseType == "" {
		response.ValidationError(ctx, "Exercise type is required", map[string]string{"type": "Exercise type is required"})
		return
	}

	// Get relations
	exerciseWithRelations, err := c.Service.GetExerciseRelations(exerciseID, exerciseType)
	if err != nil {
		response.InternalError(ctx, "Failed to get relations: "+err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, exerciseWithRelations, "Relations retrieved successfully")
}

// @Summary Update exercise difficulty metadata
// @Description Update the difficulty metadata for a specific exercise
// @Tags Exercise Relations
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Exercise ID"
// @Param type query string true "Exercise type (grammar, vocabulary, listening, speaking)"
// @Param request body UpdateDifficultyMetadataRequest true "Difficulty metadata"
// @Success 200 {object} utils.Response "Difficulty metadata updated successfully"
// @Failure 400 {object} utils.Response "Invalid exercise ID, type, or request body"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercise-relations/{id}/difficulty [put]
func (c *ExerciseRelationController) UpdateExerciseDifficultyMetadata(ctx *gin.Context) {
	// Get the exercise ID from the URL
	exerciseIDStr := ctx.Param("id")
	exerciseID, err := uuid.Parse(exerciseIDStr)
	if err != nil {
		response.ValidationError(ctx, "Invalid exercise ID", map[string]string{"id": "Invalid UUID format"})
		return
	}

	// Get the exercise type from the query parameters
	exerciseType := ctx.Query("type")
	if exerciseType == "" {
		response.ValidationError(ctx, "Exercise type is required", map[string]string{"type": "Exercise type is required"})
		return
	}

	// Get the request body
	var req UpdateDifficultyMetadataRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid request body")
		return
	}

	// Update difficulty metadata
	metadata, err := c.Service.UpdateExerciseDifficultyMetadata(
		exerciseID,
		exerciseType,
		req.ComplexityScore,
		req.TimeToComplete,
		req.Tags,
	)

	if err != nil {
		response.InternalError(ctx, "Failed to update difficulty metadata: "+err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, metadata, "Difficulty metadata updated successfully")
}

// @Summary Recommend exercises
// @Description Get personalized exercise recommendations based on user's learning history and preferences
// @Tags Exercise Relations
// @Produce json
// @Security BearerAuth
// @Param count query int false "Number of recommendations to return (default: 5)"
// @Param type query []string false "Preferred exercise types (can specify multiple)" collectionFormat(multi)
// @Param difficulty query string false "Preferred difficulty level (easy, medium, hard) (default: medium)"
// @Success 200 {object} utils.Response "Recommendations retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/exercise-relations/recommend [get]
func (c *ExerciseRelationController) RecommendExercises(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get query parameters
	countStr := ctx.DefaultQuery("count", "5")
	count, err := strconv.Atoi(countStr)
	if err != nil || count < 1 {
		count = 5
	}

	preferredTypes := ctx.QueryArray("type")
	preferredDifficultyStr := ctx.DefaultQuery("difficulty", "medium")
	preferredDifficulty := models.Medium // Default to medium difficulty

	switch preferredDifficultyStr {
	case "easy":
		preferredDifficulty = models.Easy
	case "hard":
		preferredDifficulty = models.Hard
	}

	// Get recommendations
	recommendations, err := c.Service.RecommendExercises(
		userID.(uuid.UUID),
		count,
		preferredTypes,
		preferredDifficulty,
	)

	if err != nil {
		response.InternalError(ctx, "Failed to get recommendations: "+err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, recommendations, "Recommendations retrieved successfully")
}
