package controllers

import (
	"context"
	"fmt"
	"net/http"

	"languagelearning/domain/user/entity"
	"languagelearning/domain/user/service"
	"languagelearning/models"
	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UpdateUserProfileRequest represents the update user profile request body
type UpdateUserProfileRequest struct {
	Bio       string      `json:"bio,omitempty"`
	Location  string      `json:"location,omitempty"`
	Website   string      `json:"website,omitempty"`
	Following []uuid.UUID `json:"following,omitempty"`
	Followers []uuid.UUID `json:"followers,omitempty"`
}

// UpdateUserSettingsRequest represents the update user settings request body
type UpdateUserSettingsRequest struct {
	NotificationsEnabled *bool   `json:"notificationsEnabled" example:"true"`
	DarkModeEnabled      *bool   `json:"darkModeEnabled" example:"false"`
	DailyGoal            *int    `json:"dailyGoal" example:"5"`
	PreferredLanguage    *string `json:"preferredLanguage" example:"en-US"`
}

// UserController handles user-related requests
type UserController struct {
	UserService service.UserService
}

// NewUserController creates a new UserController
func NewUserController(userService service.UserService) *UserController {
	return &UserController{UserService: userService}
}

// @Summary Get user profile
// @Description Get the current user's profile information
// @Tags User
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=models.User} "User profile retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "User not found"
// @Router /api/v1/user/profile [get]
func (uc *UserController) GetUserProfile(c *gin.Context) {
	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID := userID.(uuid.UUID)

	user, err := uc.UserService.GetUserProfile(context.Background(), userUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "User not found")
		} else {
			response.InternalError(c, "Failed to retrieve user profile")
		}
		return
	}

	response.Success(c, http.StatusOK, user, "User profile retrieved successfully")
}

// @Summary Update user profile
// @Description Update the current user's profile information
// @Tags User
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body user.UpdateUserProfileRequest true "User profile information to update"
// @Success 200 {object} utils.Response{data=models.User} "User profile updated successfully"
// @Failure 400 {object} utils.Response "Invalid request body or validation errors"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "User not found"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/user/profile [put]
func (uc *UserController) UpdateUserProfile(c *gin.Context) {
	var req UpdateUserProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	userProfile := &entity.UserProfile{
		Bio:       req.Bio,
		Location:  req.Location,
		Website:   req.Website,
		Following: req.Following,
		Followers: req.Followers,
	}

	user, err := uc.UserService.UpdateUserProfile(context.Background(), userUUID, userProfile)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "User not found")
		} else if err == models.ErrEmailAlreadyExists {
			response.ValidationError(c, "Email already exists", map[string]string{"email": "This email is already in use"})
		} else {
			response.InternalError(c, "Failed to update user profile")
		}
		return
	}

	response.Success(c, http.StatusOK, user, "User profile updated successfully")
}

// @Summary Get user settings
// @Description Get the current user's settings
// @Tags User
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=models.UserSettings} "User settings retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "User settings not found"
// @Router /api/v1/user/settings [get]
func (uc *UserController) GetUserSettings(c *gin.Context) {
	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	settings, err := uc.UserService.GetUserSettings(context.Background(), userUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "User settings not found")
		} else {
			response.InternalError(c, "Failed to retrieve user settings")
		}
		return
	}

	response.Success(c, http.StatusOK, settings, "User settings retrieved successfully")
}

// @Summary Update user settings
// @Description Update the current user's settings
// @Tags User
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body UpdateUserSettingsRequest true "User settings to update"
// @Success 200 {object} utils.Response{data=models.UserSettings} "User settings updated successfully"
// @Failure 400 {object} utils.Response "Invalid request body"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/user/settings [put]
func (uc *UserController) UpdateUserSettings(c *gin.Context) {
	var req UpdateUserSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	settings, err := uc.UserService.UpdateUserSettings(context.Background(), userUUID, &entity.UserSettings{
		NotificationsEnabled: *req.NotificationsEnabled,
		DarkModeEnabled:      *req.DarkModeEnabled,
		DailyGoal:            *req.DailyGoal,
		PreferredLanguage:    *req.PreferredLanguage,
	})
	if err != nil {
		response.InternalError(c, "Failed to update user settings")
		return
	}

	response.Success(c, http.StatusOK, settings, "User settings updated successfully")
}

// @Summary Get user statistics
// @Description Get the current user's learning statistics
// @Tags User
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "User stats retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "User not found"
// @Router /api/v1/user/stats [get]
func (uc *UserController) GetUserStats(c *gin.Context) {
	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	stats, err := uc.UserService.GetUserStats(context.Background(), userUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "User not found")
		} else {
			response.InternalError(c, "Failed to retrieve user stats")
		}
		return
	}

	response.Success(c, http.StatusOK, stats, "User stats retrieved successfully")
}

// @Summary Get learning streak
// @Description Get the current user's learning streak
// @Tags User
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=map[string]int} "Learning streak retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "User not found"
// @Router /api/v1/user/streak [get]
func (uc *UserController) GetLearningStreak(c *gin.Context) {
	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	streak, err := uc.UserService.GetLearningStreak(context.Background(), userUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "User not found")
		} else {
			response.InternalError(c, "Failed to retrieve learning streak")
		}
		return
	}

	response.Success(c, http.StatusOK, gin.H{"streak": streak}, "Learning streak retrieved successfully")
}

// @Summary Get progress report
// @Description Get the current user's progress report for a specific period (week, month, year)
// @Tags User
// @Produce json
// @Security BearerAuth
// @Param period path string true "Period (week, month, year)" Enums(week, month, year)
// @Success 200 {object} utils.Response{data=models.ProgressReport} "Progress report retrieved successfully"
// @Failure 400 {object} utils.Response "Invalid period"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Router /api/v1/user/progress/{period} [get]
func (uc *UserController) GetProgressReport(c *gin.Context) {
	period := c.Param("period")
	if period == "" {
		response.ValidationError(c, "Period is required", map[string]string{"period": "Period parameter is required"})
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	report, err := uc.UserService.GetProgressReport(context.Background(), userUUID, period)
	if err != nil {
		if err == models.ErrInvalidPeriod {
			response.ValidationError(c, "Invalid period", map[string]string{"period": "Period must be one of: week, month, year"})
		} else {
			response.InternalError(c, "Failed to retrieve progress report")
		}
		return
	}

	response.Success(c, http.StatusOK, report, "Progress report retrieved successfully")
}

// @Summary Get user word list
// @Description Get the current user's vocabulary word list
// @Tags User
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=[]models.UserWordResponse} "User word list retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/user/words [get]
func (uc *UserController) GetUserWordList(c *gin.Context) {
	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	wordList, err := uc.UserService.GetUserWordList(context.Background(), userUUID)
	if err != nil {
		response.InternalError(c, "Failed to retrieve user word list")
		return
	}

	response.Success(c, http.StatusOK, wordList, "User word list retrieved successfully")
}

// @Summary Get user achievements
// @Description Get the current user's achievements
// @Tags User
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=[]models.UserAchievementResponse} "User achievements retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/user/achievements [get]
func (uc *UserController) GetUserAchievements(c *gin.Context) {
	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	achievements, err := uc.UserService.GetUserAchievements(context.Background(), userUUID)
	if err != nil {
		response.InternalError(c, "Failed to retrieve user achievements")
		return
	}

	response.Success(c, http.StatusOK, achievements, "User achievements retrieved successfully")
}

// GetMe returns the current user's profile
// @Summary Get current user profile
// @Description Get the profile of the currently authenticated user
// @Tags user
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.UserResponse
// @Failure 401 {object} models.AppError
// @Failure 500 {object} models.AppError
// @Router /api/v1/user/me [get]
func (uc *UserController) GetMe(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	// Convert userID to UUID
	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID format"})
		return
	}

	// Get user from service
	user, err := uc.UserService.GetUserByID(c.Request.Context(), userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get user profile"})
		return
	}

	//debug print user
	fmt.Println(user)

	// Convert to response model
	response.Success(c, http.StatusOK, user, "User profile retrieved successfully")
}
