package controllers

import (
	evaluationSvc "languagelearning/domain/evaluation/service"
	"languagelearning/models"
	"languagelearning/utils/response"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type EvaluationController struct {
	evalService evaluationSvc.EvaluationService
	// Achievement controller is now injected via DI
}

func NewEvaluationController(evalService evaluationSvc.EvaluationService) *EvaluationController {
	return &EvaluationController{evalService: evalService}
}

// EvaluationRequest represents the request to create or update an evaluation
type EvaluationRequest struct {
	Type         models.EvaluationType `json:"type" binding:"required"`
	Title        string                `json:"title" binding:"required"`
	Description  string                `json:"description"`
	PassingScore int                   `json:"passingScore" binding:"required,min=0,max=100"`
	Duration     int                   `json:"duration" binding:"required,min=1"`
	Sections     []SectionRequest      `json:"sections" binding:"required,dive"`
}

// SectionRequest represents the request to create or update a section
type SectionRequest struct {
	Title     string            `json:"title" binding:"required"`
	Skill     string            `json:"skill" binding:"required"`
	Weight    int               `json:"weight" binding:"required,min=1,max=100"`
	Questions []QuestionRequest `json:"questions" binding:"required,dive"`
}

// QuestionRequest represents the request to create or update a question
type QuestionRequest struct {
	Type          string   `json:"type" binding:"required"`
	Content       string   `json:"content" binding:"required"`
	Options       []string `json:"options"`
	CorrectAnswer string   `json:"correctAnswer" binding:"required"`
	Points        int      `json:"points" binding:"required,min=1"`
}

// AnswerRequest represents the request to submit an answer
type AnswerRequest struct {
	QuestionID      uint   `json:"questionId" binding:"required"`
	Answer          string `json:"answer" binding:"required"`
	SessionToken    string `json:"sessionToken" binding:"required"`
	CurrentSection  int    `json:"currentSection" binding:"required,min=0"`
	CurrentQuestion int    `json:"currentQuestion" binding:"required,min=0"`
}

// GetAvailableEvaluations returns evaluations available to the user
func (ac *EvaluationController) GetAvailableEvaluations(c *gin.Context) {
	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Get query parameters for filtering
	evalType := c.Query("type")

	// Get the evaluations using the service
	evaluations, err := ac.evalService.GetEvaluations(userID.(uuid.UUID), evalType)
	if err != nil {
		response.InternalError(c, "Failed to retrieve evaluations")
		return
	}

	response.Success(c, http.StatusOK, evaluations, "Evaluations retrieved successfully")
}

// GetEvaluationDetails returns details of a specific evaluation
func (ac *EvaluationController) GetEvaluationDetails(c *gin.Context) {
	// Get the evaluation ID from the URL
	evalID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		response.ValidationError(c, "Invalid evaluation ID", map[string]string{"id": "Invalid evaluation ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Get the evaluation details using the service
	evaluation, err := ac.evalService.GetEvaluationDetails(userID.(uuid.UUID), evalID)
	if err != nil {
		response.NotFound(c, "Evaluation not found")
		return
	}

	response.Success(c, http.StatusOK, evaluation, "Evaluation retrieved successfully")
}

// CreateEvaluation creates a new evaluation
func (ac *EvaluationController) CreateEvaluation(c *gin.Context) {
	// Get the request body
	var req EvaluationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Convert SectionRequest to models.EvalSection
	sections := make([]models.EvalSection, len(req.Sections))
	for i, sectionReq := range req.Sections {
		sections[i] = models.EvalSection{
			Title:  sectionReq.Title,
			Skill:  sectionReq.Skill,
			Weight: sectionReq.Weight,
		}
		questions := make([]models.EvalQuestion, len(sectionReq.Questions))
		for j, questionReq := range sectionReq.Questions {
			questions[j] = models.EvalQuestion{
				Type:          questionReq.Type,
				Content:       questionReq.Content,
				Options:       questionReq.Options,
				CorrectAnswer: questionReq.CorrectAnswer,
				Points:        questionReq.Points,
			}
		}
		sections[i].Questions = questions
	}

	// Create the evaluation using the service
	evaluation, err := ac.evalService.CreateEvaluation(
		userID.(uuid.UUID),
		req.Type,
		req.Title,
		req.Description,
		req.PassingScore,
		req.Duration,
		sections, // Pass the converted sections
	)
	if err != nil {
		response.InternalError(c, "Failed to create evaluation")
		return
	}

	response.Success(c, http.StatusCreated, evaluation, "Evaluation created successfully")
}

// StartEvaluationRequest represents the request to start an evaluation
type StartEvaluationRequest struct {
	SessionToken string `json:"sessionToken" binding:"required"`
}

// StartEvaluation starts an evaluation for a user
func (ac *EvaluationController) StartEvaluation(c *gin.Context) {
	// Get the evaluation ID from the URL
	evalID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		response.ValidationError(c, "Invalid evaluation ID", map[string]string{"id": "Invalid evaluation ID format"})
		return
	}

	// Get the request body
	var req StartEvaluationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Start the evaluation using the service
	evaluation, progressResponse, err := ac.evalService.StartEvaluation(
		userID.(uuid.UUID),
		evalID,
		req.SessionToken,
	)
	if err != nil {
		response.InternalError(c, "Failed to start evaluation")
		return
	}

	// Prepare the response
	responseData := gin.H{
		"evaluation": evaluation,
		"progress":   progressResponse,
	}

	response.Success(c, http.StatusOK, responseData, "Evaluation started successfully")
}

// SubmitEvaluationAnswer submits an answer for an evaluation question
func (ac *EvaluationController) SubmitEvaluationAnswer(c *gin.Context) {
	// Get the evaluation ID from the URL
	evalID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		response.ValidationError(c, "Invalid evaluation ID", map[string]string{"id": "Invalid evaluation ID format"})
		return
	}

	// Get the request body
	var req AnswerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Submit the answer using the service
	isCorrect, progressResponse, err := ac.evalService.SubmitEvaluationAnswer(
		userID.(uuid.UUID),
		evalID,
		req.QuestionID,
		req.Answer,
		req.SessionToken,
		req.CurrentSection,
		req.CurrentQuestion,
	)
	if err != nil {
		response.InternalError(c, "Failed to submit answer")
		return
	}

	response.Success(c, http.StatusOK, gin.H{
		"isCorrect": isCorrect,
		"progress":  progressResponse,
	}, "Answer submitted successfully")
}

// CompleteEvaluation completes an evaluation and generates results
func (ac *EvaluationController) CompleteEvaluation(c *gin.Context) {
	// Get the evaluation ID from the URL
	evalID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		response.ValidationError(c, "Invalid evaluation ID", map[string]string{"id": "Invalid evaluation ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Complete the evaluation using the service
	evalResult, evalType, err := ac.evalService.CompleteEvaluation(userID.(uuid.UUID), evalID) // Receive evalType
	if err != nil {
		response.InternalError(c, "Failed to complete evaluation")
		return
	}

	// If this is a placement evaluation, automatically create a personalized learning path
	if evalType == models.EvalPlacement { // Use the returned evalType
		// Check if personalized learning service is available
		// This part might need adjustment based on how global services are handled with DI
		// if global.PersonalizedLearningService != nil {
		// 	// Create a personalized learning path
		// 	learningPath, err := global.PersonalizedLearningService.CreateExerciseSetFromAssessment(userID.(uuid.UUID), evalID)
		// 	if err == nil && learningPath != nil {
		// 		// Add learning path to the response
		// 		response := gin.H{
		// 			"evaluationResult": evalResult,
		// 			"learningPath":     learningPath,
		// 		}
		// 		response.Success(c, http.StatusOK, response, "Evaluation completed and learning path created successfully")
		// 		return
		// 	}
		// }
	}

	response.Success(c, http.StatusOK, evalResult, "Evaluation completed successfully")
}

// GetEvaluationResults returns results of a completed evaluation
func (ac *EvaluationController) GetEvaluationResults(c *gin.Context) {
	// Get the evaluation ID from the URL
	evalID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		response.ValidationError(c, "Invalid evaluation ID", map[string]string{"id": "Invalid evaluation ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Get the evaluation results using the service
	evalResult, err := ac.evalService.GetEvaluationResult(userID.(uuid.UUID), evalID)
	if err != nil {
		response.NotFound(c, "Evaluation result not found")
		return
	}

	response.Success(c, http.StatusOK, evalResult, "Evaluation result retrieved successfully")
}

// GetUserEvaluationHistory returns a user's evaluation history
func (ac *EvaluationController) GetUserEvaluationHistory(c *gin.Context) {
	// Get the user ID from the context
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Get the evaluation history using the service
	results, err := ac.evalService.GetUserEvaluationHistory(userID.(uuid.UUID))
	if err != nil {
		response.InternalError(c, "Failed to retrieve evaluation history")
		return
	}

	response.Success(c, http.StatusOK, results, "Evaluation history retrieved successfully")
}

// Helper functions

// generateFeedback generates feedback based on the score
func generateFeedback(score int, passingScore int) string {
	if score >= 90 {
		return "Excellent work! You have demonstrated a strong understanding of the material."
	} else if score >= 80 {
		return "Great job! You have a good grasp of the material."
	} else if score >= passingScore {
		return "Good work! You have passed the evaluation, but there is room for improvement."
	} else if score >= passingScore-10 {
		return "You were close to passing. With a little more practice, you'll be able to pass next time."
	} else {
		return "You need more practice before attempting this evaluation again."
	}
}

// calculatePointsForEvaluation calculates points based on evaluation type and score
func calculatePointsForEvaluation(evalType models.EvaluationType, score int) int {
	basePoints := 0

	switch evalType {
	case models.EvalPlacement:
		basePoints = 50
	case models.EvalProgress:
		basePoints = 100
	case models.EvalSkill:
		basePoints = 150
	case models.EvalCertificate:
		basePoints = 300
	}

	// Adjust points based on score
	return (basePoints * score) / 100
}
