package controllers

import (
	"net/http"

	learningService "languagelearning/domain/learning/service"
	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// WordController handles the word functionality
type WordController struct {
	wordService           learningService.WordService
	achievementController *AchievementController
}

// NewWordController creates a new word controller
func NewWordController(wordService learningService.WordService, achievementController *AchievementController) *WordController {
	return &WordController{
		wordService:           wordService,
		achievementController: achievementController,
	}
}

// @Summary Get vocabulary words
// @Description Get all vocabulary words with optional filtering by category and difficulty
// @Tags Words
// @Produce json
// @Security BearerAuth
// @Param category query string false "Filter by category (e.g., food, travel, business)"
// @Param difficulty query string false "Filter by difficulty (e.g., beginner, intermediate, advanced)"
// @Success 200 {object} utils.Response "Words retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/words [get]
func (c *WordController) GetWords(ctx *gin.Context) {
	// Get query parameters for filtering
	category := ctx.Query("category")
	difficulty := ctx.Query("difficulty")

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the words
	words, err := c.wordService.GetWords(userID.(uuid.UUID), category, difficulty)
	if err != nil {
		response.InternalError(ctx, "Failed to retrieve words")
		return
	}

	response.Success(ctx, http.StatusOK, words, "Words retrieved successfully")
}

// @Summary Get word detail
// @Description Get detailed information about a specific vocabulary word
// @Tags Words
// @Produce json
// @Security BearerAuth
// @Param id path string true "Word ID"
// @Success 200 {object} utils.Response "Word retrieved successfully"
// @Failure 400 {object} utils.Response "Invalid word ID"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Word not found"
// @Router /api/v1/words/{id} [get]
func (c *WordController) GetWordDetail(ctx *gin.Context) {
	// Get the word ID from the URL
	wordID := ctx.Param("id")
	if wordID == "" {
		response.ValidationError(ctx, "Word ID is required", map[string]string{"id": "Word ID is required"})
		return
	}

	// Parse the word ID
	id, err := uuid.Parse(wordID)
	if err != nil {
		response.ValidationError(ctx, "Invalid word ID", map[string]string{"id": "Invalid UUID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the word detail
	wordDetail, err := c.wordService.GetWordDetail(userID.(uuid.UUID), id)
	if err != nil {
		response.NotFound(ctx, "Word not found")
		return
	}

	response.Success(ctx, http.StatusOK, wordDetail, "Word retrieved successfully")
}

// @Summary Mark word as learned
// @Description Mark a vocabulary word as learned for the current user
// @Tags Words
// @Produce json
// @Security BearerAuth
// @Param id path string true "Word ID"
// @Success 200 {object} utils.Response "Word marked as learned successfully"
// @Failure 400 {object} utils.Response "Invalid word ID"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Word not found"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/words/{id}/learned [put]
func (c *WordController) MarkWordAsLearned(ctx *gin.Context) {
	// Get the word ID from the URL
	wordID := ctx.Param("id")
	if wordID == "" {
		response.ValidationError(ctx, "Word ID is required", map[string]string{"id": "Word ID is required"})
		return
	}

	// Parse the word ID
	id, err := uuid.Parse(wordID)
	if err != nil {
		response.ValidationError(ctx, "Invalid word ID", map[string]string{"id": "Invalid UUID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Mark the word as learned
	responseData, err := c.wordService.MarkWordAsLearned(userID.(uuid.UUID), id)
	if err != nil {
		response.NotFound(ctx, "Word not found")
		return
	}

	// Check and update achievements
	if c.achievementController != nil {
		c.achievementController.CheckAndUpdateAchievements(userID.(uuid.UUID))
	}

	response.Success(ctx, http.StatusOK, responseData, "Word marked as learned successfully")
}
