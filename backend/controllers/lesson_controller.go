package controllers

import (
	"net/http"
	"strconv"
	"time"

	"languagelearning/domain/learning/entity"
	"languagelearning/domain/learning/service"
	baseRepo "languagelearning/domain/repository"
	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LessonProgressResponse defines the structure for lesson progress API responses.
// Temporary definition, should be moved to a DTO package.
type LessonProgressResponse struct {
	ID            uuid.UUID  `json:"id"`
	UserID        uuid.UUID  `json:"userId"`
	LessonID      uuid.UUID  `json:"lessonId"`
	Progress      int        `json:"progress"`
	IsCompleted   bool       `json:"isCompleted"`
	CompletedDate *time.Time `json:"completedDate,omitempty"`
	CreatedAt     time.Time  `json:"createdAt"`
	UpdatedAt     time.Time  `json:"updatedAt"`
}

// UpdateLessonProgressRequest defines the structure for updating lesson progress.
// Temporary definition, should be moved to a DTO package.
type UpdateLessonProgressRequest struct {
	Progress    int  `json:"progress" binding:"min=0,max=100"`
	IsCompleted bool `json:"isCompleted"`
}

// LessonController 课程控制器
type LessonController struct {
	lessonService service.LessonService
}

// NewLessonController 创建课程控制器
func NewLessonController(lessonService service.LessonService) *LessonController {
	return &LessonController{
		lessonService: lessonService,
	}
}

// ToggleFavoriteLessonRequest represents the toggle favorite lesson request body
type ToggleFavoriteLessonRequest struct {
	IsFavorite bool `json:"isFavorite" binding:"required" example:"true"`
}

// GetLessons 获取课程列表
func (c *LessonController) GetLessons(ctx *gin.Context) {
	page, size := c.ExtractPaginationParams(ctx)

	// 创建分页请求
	pageable := baseRepo.PageRequest{
		Page: page,
		Size: size,
	}

	// 解析排序参数
	if sortParam := ctx.Query("sort"); sortParam != "" {
		pageable.Sort = []string{sortParam}
	}

	// 调用服务
	result, err := c.lessonService.GetLessons(ctx, pageable, nil)
	if err != nil {
		response.InternalError(ctx, "Failed to get lessons")
		return
	}

	response.Success(ctx, http.StatusOK, result, "Lessons retrieved successfully")
}

// GetLessonByID 获取指定ID的课程
func (c *LessonController) GetLessonByID(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID") // Corrected response call
		return
	}

	// 调用服务
	lesson, err := c.lessonService.GetLessonByID(ctx, id)
	if err != nil {
		response.NotFound(ctx, "Lesson not found")
		return
	}

	response.Success(ctx, http.StatusOK, lesson, "Lesson retrieved successfully")
}

// @Summary Get favorite lessons
// @Description Get the current user's favorite lessons
// @Tags Lessons
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "Favorite lessons retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/lessons/favorites [get]
func (c *LessonController) GetFavoriteLessons(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the favorite lessons
	favoriteLessons, err := c.lessonService.GetFavoriteLessons(ctx, userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, "Failed to retrieve favorite lessons")
		return
	}

	response.Success(ctx, http.StatusOK, favoriteLessons, "Favorite lessons retrieved successfully")
}

// @Summary Toggle favorite lesson
// @Description Mark or unmark a lesson as favorite for the current user
// @Tags Lessons
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Lesson ID"
// @Param request body ToggleFavoriteLessonRequest true "Favorite status"
// @Success 200 {object} utils.Response{data=map[string]bool} "Lesson favorite status updated successfully"
// @Failure 400 {object} utils.Response "Invalid request body or Lesson ID is required"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Lesson not found"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/lessons/{id}/favorite [patch]
func (c *LessonController) ToggleFavoriteLesson(ctx *gin.Context) {
	// Get the lesson ID from the URL
	lessonID := ctx.Param("id")
	if lessonID == "" {
		response.ValidationError(ctx, "Lesson ID is required", map[string]string{"id": "Lesson ID is required"})
		return
	}

	// Get the request body
	var req ToggleFavoriteLessonRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Toggle the favorite status
	responseData, err := c.lessonService.ToggleFavoriteLesson(ctx, userID.(uuid.UUID), lessonID, req.IsFavorite)
	if err != nil {
		response.NotFound(ctx, "Lesson not found")
		return
	}

	response.Success(ctx, http.StatusOK, responseData, "Lesson favorite status updated successfully")
}

// CreateLesson 创建新课程
func (c *LessonController) CreateLesson(ctx *gin.Context) {
	// 解析请求体
	var lesson entity.Lesson
	if err := ctx.ShouldBindJSON(&lesson); err != nil {
		response.BadRequest(ctx, "Invalid lesson data") // Corrected response call
		return
	}

	// 调用服务
	createdLesson, err := c.lessonService.CreateLesson(ctx, &lesson)
	if err != nil {
		response.InternalError(ctx, "Failed to create lesson")
		return
	}

	response.Success(ctx, http.StatusCreated, createdLesson, "Lesson created successfully")
}

// UpdateLesson 更新课程
func (c *LessonController) UpdateLesson(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID") // Corrected response call
		return
	}

	// 解析请求体
	var lesson entity.Lesson
	if err := ctx.ShouldBindJSON(&lesson); err != nil {
		response.BadRequest(ctx, "Invalid lesson data") // Corrected response call
		return
	}

	// 设置ID
	lesson.ID = id

	// 调用服务
	updatedLesson, err := c.lessonService.UpdateLesson(ctx, &lesson)
	if err != nil {
		response.InternalError(ctx, "Failed to update lesson")
		return
	}

	response.Success(ctx, http.StatusOK, updatedLesson, "Lesson updated successfully")
}

// DeleteLesson 删除课程
func (c *LessonController) DeleteLesson(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID") // Corrected response call
		return
	}

	// 调用服务
	err = c.lessonService.DeleteLesson(ctx, id)
	if err != nil {
		response.InternalError(ctx, "Failed to delete lesson")
		return
	}

	response.Success(ctx, http.StatusOK, nil, "Lesson deleted successfully")
}

// PublishLesson 发布课程
func (c *LessonController) PublishLesson(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID") // Corrected response call
		return
	}

	// 调用服务
	err = c.lessonService.PublishLesson(ctx, id)
	if err != nil {
		response.InternalError(ctx, "Failed to publish lesson")
		return
	}

	response.Success(ctx, http.StatusOK, nil, "Lesson published successfully")
}

// UnpublishLesson 取消发布课程
func (c *LessonController) UnpublishLesson(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID") // Corrected response call
		return
	}

	// 调用服务
	err = c.lessonService.UnpublishLesson(ctx, id)
	if err != nil {
		response.InternalError(ctx, "Failed to unpublish lesson")
		return
	}

	response.Success(ctx, http.StatusOK, nil, "Lesson unpublished successfully")
}

// SearchLessons 搜索课程
func (c *LessonController) SearchLessons(ctx *gin.Context) {
	// 提取查询参数
	query := ctx.Query("q")
	if query == "" {
		response.BadRequest(ctx, "Search query is required") // Corrected response call
		return
	}

	// 提取分页参数
	page, size := c.ExtractPaginationParams(ctx)
	pageable := baseRepo.PageRequest{
		Page: page,
		Size: size,
	}

	// 解析排序参数
	if sortParam := ctx.Query("sort"); sortParam != "" {
		pageable.Sort = []string{sortParam}
	}

	// 调用服务
	result, err := c.lessonService.SearchLessons(ctx, query, pageable)
	if err != nil {
		response.InternalError(ctx, "Failed to search lessons")
		return
	}

	response.Success(ctx, http.StatusOK, result, "Search completed successfully")
}

// GetLessonsByLanguage 获取指定语言的课程
func (c *LessonController) GetLessonsByLanguage(ctx *gin.Context) {
	language := ctx.Param("language")
	if language == "" {
		response.BadRequest(ctx, "Language parameter is required") // Corrected response call
		return
	}

	// 调用服务
	lessons, err := c.lessonService.GetLessonsByLanguage(ctx, language)
	if err != nil {
		response.InternalError(ctx, "Failed to get lessons by language")
		return
	}

	response.Success(ctx, http.StatusOK, lessons, "Lessons retrieved successfully")
}

// GetLessonsByLevel 获取指定级别的课程
func (c *LessonController) GetLessonsByLevel(ctx *gin.Context) {
	levelParam := ctx.Param("level")
	if levelParam == "" {
		response.BadRequest(ctx, "Level parameter is required") // Corrected response call
		return
	}

	level := entity.LessonLevel(levelParam)

	// 调用服务
	lessons, err := c.lessonService.GetLessonsByLevel(ctx, level)
	if err != nil {
		response.InternalError(ctx, "Failed to get lessons by level")
		return
	}

	response.Success(ctx, http.StatusOK, lessons, "Lessons retrieved successfully")
}

// GetLessonsByCategory 获取指定类别的课程
func (c *LessonController) GetLessonsByCategory(ctx *gin.Context) {
	categoryParam := ctx.Param("category")
	if categoryParam == "" {
		response.BadRequest(ctx, "Category parameter is required") // Corrected response call
		return
	}

	category := entity.LessonCategory(categoryParam)

	// 调用服务
	lessons, err := c.lessonService.GetLessonsByCategory(ctx, category)
	if err != nil {
		response.InternalError(ctx, "Failed to get lessons by category")
		return
	}

	response.Success(ctx, http.StatusOK, lessons, "Lessons retrieved successfully")
}

// GetRelatedLessons 获取相关课程
func (c *LessonController) GetRelatedLessons(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID") // Corrected response call
		return
	}

	// 调用服务
	lessons, err := c.lessonService.GetRelatedLessons(ctx, id)
	if err != nil {
		response.InternalError(ctx, "Failed to get related lessons")
		return
	}

	response.Success(ctx, http.StatusOK, lessons, "Related lessons retrieved successfully")
}

// GetLessonProgress 获取用户的课程进度
func (c *LessonController) GetLessonProgress(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID") // Corrected response call
		return
	}

	// 获取用户ID (通常从JWT令牌获取，这里简化处理)
	userIDStr := ctx.GetHeader("X-User-ID")
	if userIDStr == "" {
		response.BadRequest(ctx, "User ID is required") // Corrected response call
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(ctx, "Invalid user ID") // Corrected response call
		return
	}

	// 调用服务
	progress, err := c.lessonService.GetUserLessonProgress(ctx, userID, id)
	if err != nil {
		response.InternalError(ctx, "Failed to get lesson progress")
		return
	}

	// Map to response DTO, assuming LessonProgressResponse has similar fields
	responseData := &LessonProgressResponse{
		ID:            progress.ID,
		UserID:        progress.UserID,
		LessonID:      progress.LessonID,
		Progress:      progress.Progress,
		IsCompleted:   progress.Completed,
		CompletedDate: progress.CompletedAt,
		CreatedAt:     progress.CreatedAt,
		UpdatedAt:     progress.UpdatedAt,
	}
	response.Success(ctx, http.StatusOK, responseData, "Lesson progress retrieved successfully")
}

// UpdateLessonProgress 更新课程进度
func (c *LessonController) UpdateLessonProgress(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID")
		return
	}

	// 获取用户ID (通常从JWT令牌获取，这里简化处理)
	userIDStr := ctx.GetHeader("X-User-ID")
	if userIDStr == "" {
		response.BadRequest(ctx, "User ID is required")
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(ctx, "Invalid user ID")
		return
	}

	// 解析请求体
	var progressData UpdateLessonProgressRequest
	if err := ctx.ShouldBindJSON(&progressData); err != nil {
		response.BadRequest(ctx, "Invalid progress data")
		return
	}

	// 获取现有进度
	progress, err := c.lessonService.GetUserLessonProgress(ctx, userID, id)
	if err != nil {
		response.InternalError(ctx, "Failed to get lesson progress")
		return
	}

	// 创建新的进度值对象
	newProgress, err := entity.NewProgress(progressData.Progress)
	if err != nil {
		response.BadRequest(ctx, "Invalid progress value")
		return
	}

	// 更新进度
	progress.Progress = newProgress.Value() // Use .Value() to get the int
	progress.Completed = progressData.IsCompleted

	// 调用服务
	updatedProgress, err := c.lessonService.UpdateLessonProgress(ctx, progress)
	if err != nil {
		response.InternalError(ctx, "Failed to update lesson progress")
		return
	}

	// Map to response DTO, assuming LessonProgressResponse has similar fields
	responseData := &LessonProgressResponse{
		ID:            updatedProgress.ID,
		UserID:        updatedProgress.UserID,
		LessonID:      updatedProgress.LessonID,
		Progress:      updatedProgress.Progress,
		IsCompleted:   updatedProgress.Completed,
		CompletedDate: updatedProgress.CompletedAt,
		CreatedAt:     updatedProgress.CreatedAt,
		UpdatedAt:     updatedProgress.UpdatedAt,
	}
	response.Success(ctx, http.StatusOK, responseData, "Lesson progress updated successfully")
}

// CompleteLesson 完成课程
func (c *LessonController) CompleteLesson(ctx *gin.Context) {
	// 解析ID参数
	idParam := ctx.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(ctx, "Invalid lesson ID") // Corrected response call
		return
	}

	// 获取用户ID (通常从JWT令牌获取，这里简化处理)
	userIDStr := ctx.GetHeader("X-User-ID")
	if userIDStr == "" {
		response.BadRequest(ctx, "User ID is required") // Corrected response call
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(ctx, "Invalid user ID") // Corrected response call
		return
	}

	// 调用服务
	err = c.lessonService.CompleteLessonProgress(ctx, userID, id)
	if err != nil {
		response.InternalError(ctx, "Failed to complete lesson")
		return
	}

	response.Success(ctx, http.StatusOK, nil, "Lesson completed successfully")
}

// GetUserLessonProgressList 获取用户的课程进度列表
func (c *LessonController) GetUserLessonProgressList(ctx *gin.Context) {
	// 获取用户ID (通常从JWT令牌获取，这里简化处理)
	userIDStr := ctx.GetHeader("X-User-ID")
	if userIDStr == "" {
		response.BadRequest(ctx, "User ID is required") // Corrected response call
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(ctx, "Invalid user ID") // Corrected response call
		return
	}

	// 调用服务
	progressList, err := c.lessonService.GetUserLessonProgressList(ctx, userID)
	if err != nil {
		response.InternalError(ctx, "Failed to get lesson progress list")
		return
	}

	response.Success(ctx, http.StatusOK, progressList, "Lesson progress list retrieved successfully")
}

// GetCompletedLessons 获取用户已完成的课程
func (c *LessonController) GetCompletedLessons(ctx *gin.Context) {
	// 获取用户ID (通常从JWT令牌获取，这里简化处理)
	userIDStr := ctx.GetHeader("X-User-ID")
	if userIDStr == "" {
		response.BadRequest(ctx, "User ID is required") // Corrected response call
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(ctx, "Invalid user ID") // Corrected response call
		return
	}

	// 调用服务
	completedLessons, err := c.lessonService.GetUserCompletedLessons(ctx, userID)
	if err != nil {
		response.InternalError(ctx, "Failed to get completed lessons")
		return
	}

	response.Success(ctx, http.StatusOK, completedLessons, "Completed lessons retrieved successfully")
}

// GetInProgressLessons 获取用户正在进行的课程
func (c *LessonController) GetInProgressLessons(ctx *gin.Context) {
	// 获取用户ID (通常从JWT令牌获取，这里简化处理)
	userIDStr := ctx.GetHeader("X-User-ID")
	if userIDStr == "" {
		response.BadRequest(ctx, "User ID is required") // Corrected response call
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequest(ctx, "Invalid user ID") // Corrected response call
		return
	}

	// 调用服务
	inProgressLessons, err := c.lessonService.GetUserInProgressLessons(ctx, userID)
	if err != nil {
		response.InternalError(ctx, "Failed to get in-progress lessons")
		return
	}

	response.Success(ctx, http.StatusOK, inProgressLessons, "In-progress lessons retrieved successfully")
}

// ExtractPaginationParams extracts pagination parameters from the context
func (c *LessonController) ExtractPaginationParams(ctx *gin.Context) (int, int) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil || page <= 0 {
		page = 1
	}
	size, err := strconv.Atoi(ctx.DefaultQuery("size", "10"))
	if err != nil || size <= 0 {
		size = 10
	}
	return page, size
}
