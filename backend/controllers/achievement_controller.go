package controllers

import (
	"net/http"

	achievementSvc "languagelearning/domain/achievement/service"

	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AchievementController handles the achievement functionality
type AchievementController struct {
	achievementService achievementSvc.AchievementService
}

// NewAchievementController creates a new achievement controller
func NewAchievementController(achievementService achievementSvc.AchievementService) *AchievementController {
	return &AchievementController{
		achievementService: achievementService,
	}
}

// @Summary Get achievements
// @Description Get all achievements with user progress information
// @Tags Achievements
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "Achievements retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/achievements [get]
func (c *AchievementController) GetAchievements(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the achievements
	achievements, err := c.achievementService.GetAchievements(userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, "Failed to retrieve achievements")
		return
	}

	response.Success(ctx, http.StatusOK, achievements, "Achievements retrieved successfully")
}

// @Summary Claim achievement reward
// @Description Claim the reward points for an unlocked achievement
// @Tags Achievements
// @Produce json
// @Security BearerAuth
// @Param id path string true "Achievement ID"
// @Success 200 {object} utils.Response "Achievement reward claimed successfully"
// @Failure 400 {object} utils.Response "Invalid achievement ID or achievement not unlocked or reward already claimed"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "Achievement not found or user achievement not found"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/achievements/{id}/claim [patch]
func (c *AchievementController) ClaimAchievementReward(ctx *gin.Context) {
	// Get the achievement ID from the URL
	achievementID := ctx.Param("id")
	if achievementID == "" {
		response.ValidationError(ctx, "Achievement ID is required", map[string]string{"id": "Achievement ID is required"})
		return
	}

	// Parse the achievement ID
	id, err := uuid.Parse(achievementID)
	if err != nil {
		response.ValidationError(ctx, "Invalid achievement ID", map[string]string{"id": "Invalid UUID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Claim the achievement reward
	responseData, err := c.achievementService.ClaimAchievementReward(userID.(uuid.UUID), id)
	if err != nil {
		response.InternalError(ctx, "Failed to claim achievement reward")
		return
	}

	response.Success(ctx, http.StatusOK, responseData, "Achievement reward claimed successfully")
}

// CheckAndUpdateAchievements checks and updates a user's achievements
// This is a helper function that would be called after certain actions
func (c *AchievementController) CheckAndUpdateAchievements(userID uuid.UUID) {
	c.achievementService.CheckAndUpdateAchievements(userID)
}
