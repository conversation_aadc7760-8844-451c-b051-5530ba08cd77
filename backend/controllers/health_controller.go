package controllers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HealthController 健康检查控制器
type HealthController struct {
	db *gorm.DB
}

// NewHealthController 创建健康检查控制器
func NewHealthController(db *gorm.DB) *HealthController {
	return &HealthController{db: db}
}

// HealthCheck 健康检查端点
func (h *HealthController) HealthCheck(c *gin.Context) {
	status := "healthy"
	checks := make(map[string]interface{})
	
	// 检查数据库连接
	sqlDB, err := h.db.DB()
	if err != nil {
		status = "unhealthy"
		checks["database"] = map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		}
	} else {
		if err := sqlDB.Ping(); err != nil {
			status = "unhealthy"
			checks["database"] = map[string]interface{}{
				"status": "error",
				"error":  err.Error(),
			}
		} else {
			checks["database"] = map[string]interface{}{
				"status": "healthy",
			}
		}
	}
	
	// 添加系统信息
	checks["timestamp"] = time.Now().UTC()
	checks["version"] = "1.0.0" // 从配置或环境变量获取
	
	httpStatus := http.StatusOK
	if status == "unhealthy" {
		httpStatus = http.StatusServiceUnavailable
	}
	
	c.JSON(httpStatus, gin.H{
		"status": status,
		"checks": checks,
	})
}

// ReadinessCheck 就绪检查端点
func (h *HealthController) ReadinessCheck(c *gin.Context) {
	// 检查应用是否准备好接收流量
	c.JSON(http.StatusOK, gin.H{
		"status": "ready",
		"timestamp": time.Now().UTC(),
	})
}
