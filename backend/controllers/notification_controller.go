package controllers

import (
	"net/http"
	"strconv"

	"languagelearning/domain/notification/entity"
	notificationSvc "languagelearning/domain/notification/service"
	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// NotificationController handles notification-related requests
type NotificationController struct {
	notificationService notificationSvc.NotificationService
}

// NewNotificationController creates a new notification controller
func NewNotificationController(notificationService notificationSvc.NotificationService) *NotificationController {
	return &NotificationController{
		notificationService: notificationService,
	}
}

// @Summary Get user notifications
// @Description Get all notifications for the current user with optional filtering by status
// @Tags Notifications
// @Produce json
// @Security BearerAuth
// @Param status query string false "Filter by status (unread, read, archived)"
// @Param limit query int false "Number of notifications to return (default: 10)"
// @Param offset query int false "Offset for pagination (default: 0)"
// @Success 200 {object} utils.Response "Notifications retrieved successfully"
// @Failure 401 {object} utils.Response "Unauthorized"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/notifications [get]
func (c *NotificationController) GetNotifications(ctx *gin.Context) {
	// Get user ID from context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "Unauthorized")
		return
	}

	// Parse query parameters
	status := ctx.Query("status")
	var statusFilter *entity.NotificationStatus
	if status != "" {
		notificationStatus := entity.NotificationStatus(status)
		statusFilter = &notificationStatus
	}

	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	offset, _ := strconv.Atoi(ctx.DefaultQuery("offset", "0"))

	// Get notifications
	notifications, count, err := c.notificationService.GetUserNotifications(ctx.Request.Context(), userID.(uuid.UUID), statusFilter, limit, offset)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Return response
	response.Success(ctx, http.StatusOK, gin.H{
		"data": notifications,
		"meta": gin.H{
			"total":  count,
			"limit":  limit,
			"offset": offset,
		},
	}, "Notifications retrieved successfully")
}

// @Summary Mark notification as read
// @Description Mark a specific notification as read for the current user
// @Tags Notifications
// @Produce json
// @Security BearerAuth
// @Param id path string true "Notification ID"
// @Success 200 {object} utils.Response "Notification marked as read"
// @Failure 400 {object} utils.Response "Invalid notification ID"
// @Failure 401 {object} utils.Response "Unauthorized"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/notifications/{id}/read [patch]
func (c *NotificationController) MarkNotificationAsRead(ctx *gin.Context) {
	// Get user ID from context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "Unauthorized")
		return
	}

	// Get notification ID from URL
	notificationID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid notification ID", map[string]string{"id": "Invalid notification ID format"})
		return
	}

	// Mark notification as read
	err = c.notificationService.MarkNotificationAsRead(ctx.Request.Context(), notificationID, userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Return success response
	response.Success(ctx, http.StatusOK, nil, "Notification marked as read")
}

// @Summary Mark all notifications as read
// @Description Mark all notifications for the current user as read
// @Tags Notifications
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "All notifications marked as read"
// @Failure 401 {object} utils.Response "Unauthorized"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/notifications/read-all [patch]
func (c *NotificationController) MarkAllNotificationsAsRead(ctx *gin.Context) {
	// Get user ID from context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "Unauthorized")
		return
	}

	// Mark all notifications as read
	err := c.notificationService.MarkAllNotificationsAsRead(ctx.Request.Context(), userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Return success response
	response.Success(ctx, http.StatusOK, nil, "All notifications marked as read")
}

// @Summary Archive notification
// @Description Archive a specific notification for the current user
// @Tags Notifications
// @Produce json
// @Security BearerAuth
// @Param id path string true "Notification ID"
// @Success 200 {object} utils.Response "Notification archived"
// @Failure 400 {object} utils.Response "Invalid notification ID"
// @Failure 401 {object} utils.Response "Unauthorized"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/notifications/{id}/archive [patch]
func (c *NotificationController) ArchiveNotification(ctx *gin.Context) {
	// Get user ID from context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "Unauthorized")
		return
	}

	// Get notification ID from URL
	notificationID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid notification ID", map[string]string{"id": "Invalid notification ID format"})
		return
	}

	// Archive notification
	err = c.notificationService.ArchiveNotification(ctx.Request.Context(), notificationID, userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Return success response
	response.Success(ctx, http.StatusOK, nil, "Notification archived")
}

// @Summary Get notification preferences
// @Description Get the notification preferences for the current user
// @Tags Notifications
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "Notification preferences retrieved successfully"
// @Failure 401 {object} utils.Response "Unauthorized"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/notifications/preferences [get]
func (c *NotificationController) GetNotificationPreferences(ctx *gin.Context) {
	// Get user ID from context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "Unauthorized")
		return
	}

	// Get notification preferences
	preferences, err := c.notificationService.GetUserNotificationPreferences(ctx.Request.Context(), userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Return response
	response.Success(ctx, http.StatusOK, preferences, "Notification preferences retrieved successfully")
}

// @Summary Update notification preferences
// @Description Update the notification preferences for the current user
// @Tags Notifications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param preferences body entity.NotificationPreference true "Notification preferences"
// @Success 200 {object} utils.Response "Notification preferences updated"
// @Failure 400 {object} utils.Response "Invalid notification preferences"
// @Failure 401 {object} utils.Response "Unauthorized"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/notifications/preferences [put]
func (c *NotificationController) UpdateNotificationPreferences(ctx *gin.Context) {
	// Get user ID from context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "Unauthorized")
		return
	}

	// Parse request body
	var preferences entity.NotificationPreference
	if err := ctx.ShouldBindJSON(&preferences); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid notification preferences")
		return
	}

	// Update notification preferences
	err := c.notificationService.UpdateUserNotificationPreferences(ctx.Request.Context(), userID.(uuid.UUID), &preferences)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Return success response
	response.Success(ctx, http.StatusOK, nil, "Notification preferences updated")
}
