package controllers

import (
	"net/http"

	authService "languagelearning/domain/auth/service"
	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthController handles authentication-related requests
type AuthController struct {
	authService authService.AuthService
}

// NewAuthController creates a new auth controller
func NewAuthController(authService authService.AuthService) *AuthController {
	return &AuthController{authService: authService}
}

// LoginRequest represents the login request body
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest represents the registration request body
type RegisterRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// ResetPasswordRequest represents the password reset request body
type ResetPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// ChangePasswordRequest represents the change password request body
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required,min=6"`
}

// RefreshTokenRequest represents the refresh token request body
type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken" binding:"required"`
}

// TokenResponse represents the token response body
type TokenResponse struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	TokenType    string `json:"tokenType"`
	ExpiresIn    int    `json:"expiresIn"`
}

// @Summary Login user
// @Description Authenticate a user and return a JWT token
// @Tags Auth
// @Accept json
// @Produce json
// @Param request body LoginRequest true "Login credentials"
// @Success 200 {object} utils.Response{data=models.UserResponse} "Login successful"
// @Failure 400 {object} utils.Response "Invalid request body"
// @Failure 401 {object} utils.Response "Invalid username or password"
// @Failure 500 {object} utils.Response "Server error"
// @Router /auth/login [post]
func (ac *AuthController) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	token, err := ac.authService.Login(req.Username, req.Password)
	if err != nil {
		response.InternalError(c, "Failed to login")
		return
	}

	response.Success(c, http.StatusOK, token, "Login successful")
}

// @Summary Register user
// @Description Register a new user and return a JWT token
// @Tags Auth
// @Accept json
// @Produce json
// @Param request body RegisterRequest true "Registration information"
// @Success 201 {object} utils.Response{data=models.UserResponse} "Registration successful"
// @Failure 400 {object} utils.Response "Invalid request body or validation errors"
// @Failure 500 {object} utils.Response "Server error"
// @Router /auth/register [post]
func (ac *AuthController) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	user, err := ac.authService.Register(req.Email, req.Password, req.Username)
	if err != nil {
		response.InternalError(c, "Failed to register user")
		return
	}

	response.Success(c, http.StatusCreated, user, "Registration successful")
}

// @Summary Logout user
// @Description Logout the current user (client-side token removal)
// @Tags Auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "Logout successful"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Router /auth/logout [delete]
func (ac *AuthController) Logout(c *gin.Context) {
	response.Success(c, http.StatusOK, nil, "Logout successful")
}

// @Summary Reset password
// @Description Send a password reset link to the user's email
// @Tags Auth
// @Accept json
// @Produce json
// @Param request body ResetPasswordRequest true "Email for password reset"
// @Success 200 {object} utils.Response "If your email is registered, you will receive a password reset link"
// @Failure 400 {object} utils.Response "Invalid request body"
// @Router /auth/reset-password [post]
func (ac *AuthController) ResetPassword(c *gin.Context) {
	var req ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	if err := ac.authService.RequestPasswordReset(req.Email); err != nil {
		response.InternalError(c, "Failed to process password reset")
		return
	}

	response.Success(c, http.StatusOK, nil, "If your email is registered, you will receive a password reset link")
}

// @Summary Change password
// @Description Change the current user's password
// @Tags Auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body ChangePasswordRequest true "Old and new password"
// @Success 200 {object} utils.Response "Password changed successfully"
// @Failure 400 {object} utils.Response "Invalid request body"
// @Failure 401 {object} utils.Response "User not authenticated or invalid old password"
// @Failure 404 {object} utils.Response "User not found"
// @Failure 500 {object} utils.Response "Server error"
// @Router /auth/change-password [put]
func (ac *AuthController) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		response.InternalError(c, "Invalid user ID")
		return
	}

	if err := ac.authService.ChangePassword(userUUID, req.OldPassword, req.NewPassword); err != nil {
		response.InternalError(c, "Failed to change password")
		return
	}

	response.Success(c, http.StatusOK, nil, "Password changed successfully")
}

// @Summary Refresh token
// @Description Refresh the access token using a refresh token
// @Tags Auth
// @Accept json
// @Produce json
// @Param request body RefreshTokenRequest true "Refresh token"
// @Success 200 {object} utils.Response{data=TokenResponse} "Token refreshed successfully"
// @Failure 400 {object} utils.Response "Invalid request body"
// @Failure 401 {object} utils.Response "Invalid refresh token"
// @Failure 500 {object} utils.Response "Server error"
// @Router /auth/refresh-token [post]
func (ac *AuthController) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(c, err, "Invalid request body")
		return
	}

	userID, err := ac.authService.ValidateToken(req.RefreshToken)
	if err != nil {
		response.Unauthorized(c, "Failed to validate token")
		return
	}

	accessToken, err := ac.authService.RefreshToken(userID)
	if err != nil {
		response.Unauthorized(c, "Failed to refresh token")
		return
	}

	tokenResponse := TokenResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
	}

	response.Success(c, http.StatusOK, tokenResponse, "Token refreshed successfully")
}
