package controllers

import (
	evaluationService "languagelearning/domain/evaluation/service"
	learningService "languagelearning/domain/learning/service"
	"languagelearning/utils/response"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LearningPathController handles all learning path related functionality
type LearningPathController struct {
	learningPathService learningService.LearningPathService
	evaluationService   evaluationService.EvaluationService
}

// NewLearningPathController creates a new learning path controller
func NewLearningPathController(
	learningPathService learningService.LearningPathService,
	evaluationService evaluationService.EvaluationService,
) *LearningPathController {
	return &LearningPathController{
		learningPathService: learningPathService,
		evaluationService:   evaluationService,
	}
}

// CreateLearningPathRequest represents the request to create a learning path
type CreateLearningPathRequest struct {
	Title             string   `json:"title" binding:"required"`
	Description       string   `json:"description"`
	EvaluationID      string   `json:"evaluationId"`
	Level             string   `json:"level" binding:"required"`
	FocusAreas        []string `json:"focusAreas" binding:"required"`
	EstimatedDuration int      `json:"estimatedDuration" binding:"required,min=1"`
}

// AddLessonToPathRequest represents the request to add a lesson to a learning path
type AddLessonToPathRequest struct {
	LessonID   string `json:"lessonId" binding:"required"`
	Order      int    `json:"order" binding:"required,min=1"`
	IsRequired bool   `json:"isRequired"`
}

// UpdateLearningPathRequest represents the request to update a learning path
type UpdateLearningPathRequest struct {
	Title             string   `json:"title"`
	Description       string   `json:"description"`
	Status            string   `json:"status"`
	Level             string   `json:"level"`
	FocusAreas        []string `json:"focusAreas"`
	EstimatedDuration int      `json:"estimatedDuration" binding:"min=1"`
}

// GetLearningPaths returns all learning paths for a user
func (c *LearningPathController) GetLearningPaths(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get query parameters for filtering
	status := ctx.Query("status")

	// Get the learning paths
	summaries, err := c.learningPathService.GetLearningPaths(userID.(uuid.UUID), status)
	if err != nil {
		response.InternalError(ctx, "Failed to retrieve learning paths")
		return
	}

	response.Success(ctx, http.StatusOK, summaries, "Learning paths retrieved successfully")
}

// GetLearningPathDetail returns a specific learning path
func (c *LearningPathController) GetLearningPathDetail(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the learning path
	path, err := c.learningPathService.GetLearningPathDetail(userID.(uuid.UUID), pathID)
	if err != nil {
		response.NotFound(ctx, "Learning path not found")
		return
	}

	response.Success(ctx, http.StatusOK, path, "Learning path retrieved successfully")
}

// CreateLearningPath creates a new learning path
func (c *LearningPathController) CreateLearningPath(ctx *gin.Context) {
	// Get the request body
	var req CreateLearningPathRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Parse evaluation ID if provided
	var evaluationID uuid.UUID
	if req.EvaluationID != "" {
		var err error
		evaluationID, err = uuid.Parse(req.EvaluationID)
		if err != nil {
			response.ValidationError(ctx, "Invalid evaluation ID", map[string]string{"evaluationId": "Invalid evaluation ID format"})
			return
		}
	}

	// Create the learning path
	path, err := c.learningPathService.CreateLearningPath(
		userID.(uuid.UUID),
		req.Title,
		req.Description,
		req.Level,
		req.FocusAreas,
		req.EstimatedDuration,
		evaluationID,
	)
	if err != nil {
		response.InternalError(ctx, "Failed to create learning path")
		return
	}

	response.Success(ctx, http.StatusCreated, path, "Learning path created successfully")
}

// AddLessonToPath adds a lesson to a learning path
func (c *LearningPathController) AddLessonToPath(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the request body
	var req AddLessonToPathRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	lessonID, err := uuid.Parse(req.LessonID)
	if err != nil {
		response.ValidationError(ctx, "Invalid lesson ID", map[string]string{"lessonId": "Invalid lesson ID format"})
		return
	}

	// Add the lesson to the path
	pathLesson, err := c.learningPathService.AddLessonToPath(
		userID.(uuid.UUID),
		pathID,
		lessonID,
		req.Order,
		req.IsRequired,
	)
	if err != nil {
		response.InternalError(ctx, "Failed to add lesson to learning path")
		return
	}

	response.Success(ctx, http.StatusOK, pathLesson, "Lesson added to path successfully")
}

// UpdateLearningPath updates a learning path
func (c *LearningPathController) UpdateLearningPath(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the request body
	var req UpdateLearningPathRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Update the learning path
	path, err := c.learningPathService.UpdateLearningPath(
		userID.(uuid.UUID),
		pathID,
		req.Title,
		req.Description,
		req.Status,
		req.Level,
		req.FocusAreas,
		req.EstimatedDuration,
	)
	if err != nil {
		response.NotFound(ctx, "Learning path not found")
		return
	}

	response.Success(ctx, http.StatusOK, path, "Learning path updated successfully")
}

// DeleteLearningPath deletes a learning path
func (c *LearningPathController) DeleteLearningPath(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Delete the learning path
	err = c.learningPathService.DeleteLearningPath(userID.(uuid.UUID), pathID)
	if err != nil {
		response.NotFound(ctx, "Learning path not found")
		return
	}

	response.Success(ctx, http.StatusOK, nil, "Learning path deleted successfully")
}

// GetRecommendedLearningPaths returns recommended learning paths based on evaluation results
func (c *LearningPathController) GetRecommendedLearningPaths(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the evaluation ID from the query parameters
	evalIDStr := ctx.Query("evaluationId")
	if evalIDStr == "" {
		// If no evaluation ID is provided, use the most recent completed evaluation
		evalResults, err := c.evaluationService.GetUserEvaluationHistory(userID.(uuid.UUID))
		if err != nil {
			response.InternalError(ctx, "Failed to retrieve evaluation results")
			return
		}

		if len(evalResults) == 0 {
			response.NotFound(ctx, "No completed evaluations found")
			return
		}

		evalIDStr = evalResults[0].EvaluationID.String()
	}

	// Parse the evaluation ID
	evalID, err := uuid.Parse(evalIDStr)
	if err != nil {
		response.ValidationError(ctx, "Invalid evaluation ID", map[string]string{"evaluationId": "Invalid evaluation ID format"})
		return
	}

	// Get the recommended learning paths
	recommendations, err := c.learningPathService.GetRecommendedLearningPaths(userID.(uuid.UUID), evalID)
	if err != nil {
		response.NotFound(ctx, "Evaluation result not found")
		return
	}

	response.Success(ctx, http.StatusOK, recommendations, "Recommendations generated successfully")
}

// CreateLearningPathFromRecommendation creates a learning path from a recommendation
func (c *LearningPathController) CreateLearningPathFromRecommendation(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the evaluation ID from the query parameters
	evalIDStr := ctx.Query("evaluationId")
	if evalIDStr == "" {
		response.ValidationError(ctx, "Evaluation ID is required", map[string]string{"evaluationId": "Evaluation ID is required"})
		return
	}

	// Parse the evaluation ID
	evalID, err := uuid.Parse(evalIDStr)
	if err != nil {
		response.ValidationError(ctx, "Invalid evaluation ID", map[string]string{"evaluationId": "Invalid evaluation ID format"})
		return
	}

	// Create a learning path from the recommendation
	path, err := c.learningPathService.CreateLearningPathFromRecommendation(userID.(uuid.UUID), evalID)
	if err != nil {
		response.InternalError(ctx, "Failed to create learning path from recommendation")
		return
	}

	response.Success(ctx, http.StatusCreated, path, "Learning path created successfully")
}

// UpdateLearningPathProgress updates the progress of a learning path
func (c *LearningPathController) UpdateLearningPathProgress(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Update the learning path progress
	if err := c.learningPathService.AutoUpdateLearningPathProgress(userID.(uuid.UUID), pathID); err != nil {
		response.InternalError(ctx, "Failed to update learning path progress")
		return
	}

	// Get the updated learning path
	path, err := c.learningPathService.GetLearningPathDetail(userID.(uuid.UUID), pathID)
	if err != nil {
		response.NotFound(ctx, "Learning path not found")
		return
	}

	response.Success(ctx, http.StatusOK, path, "Learning path progress updated successfully")
}

// UpdateLessonCompletion updates the completion status of lessons in a learning path
func (c *LearningPathController) UpdateLessonCompletion(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Update the lesson completion status
	if err := c.learningPathService.AutoUpdateLessonCompletion(userID.(uuid.UUID), pathID); err != nil {
		response.InternalError(ctx, "Failed to update lesson completion status")
		return
	}

	// Get the updated learning path
	path, err := c.learningPathService.GetLearningPathDetail(userID.(uuid.UUID), pathID)
	if err != nil {
		response.NotFound(ctx, "Learning path not found")
		return
	}

	response.Success(ctx, http.StatusOK, path, "Lesson completion status updated successfully")
}

// SchedulePeriodicAssessment schedules a periodic assessment for a user
func (c *LearningPathController) SchedulePeriodicAssessment(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Schedule a periodic assessment
	evaluation, err := c.learningPathService.SchedulePeriodicAssessment(userID.(uuid.UUID), pathID)
	if err != nil {
		response.InternalError(ctx, "Failed to schedule periodic assessment")
		return
	}

	response.Success(ctx, http.StatusOK, evaluation, "Periodic assessment scheduled successfully")
}

// AdjustLearningPathBasedOnAssessment adjusts a learning path based on assessment results
func (c *LearningPathController) AdjustLearningPathBasedOnAssessment(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the evaluation ID from the query parameters
	evalIDStr := ctx.Query("evaluationId")
	if evalIDStr == "" {
		response.ValidationError(ctx, "Evaluation ID is required", map[string]string{"evaluationId": "Evaluation ID is required"})
		return
	}

	// Parse the evaluation ID
	evalID, err := uuid.Parse(evalIDStr)
	if err != nil {
		response.ValidationError(ctx, "Invalid evaluation ID", map[string]string{"evaluationId": "Invalid evaluation ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Adjust the learning path based on assessment results
	if err := c.learningPathService.AdjustLearningPathBasedOnAssessment(userID.(uuid.UUID), pathID, evalID); err != nil {
		response.InternalError(ctx, "Failed to adjust learning path")
		return
	}

	// Get the updated learning path
	path, err := c.learningPathService.GetLearningPathDetail(userID.(uuid.UUID), pathID)
	if err != nil {
		response.NotFound(ctx, "Learning path not found")
		return
	}

	response.Success(ctx, http.StatusOK, path, "Learning path adjusted successfully")
}

// RecommendNextLearningPath recommends the next learning path after completing the current one
func (c *LearningPathController) RecommendNextLearningPath(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Recommend the next learning path
	recommendation, err := c.learningPathService.RecommendNextLearningPath(userID.(uuid.UUID), pathID)
	if err != nil {
		response.InternalError(ctx, "Failed to recommend next learning path")
		return
	}

	response.Success(ctx, http.StatusOK, recommendation, "Next learning path recommended successfully")
}

// CreateNextLearningPath creates the next learning path after completing the current one
func (c *LearningPathController) CreateNextLearningPath(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Recommend the next learning path
	recommendation, err := c.learningPathService.RecommendNextLearningPath(userID.(uuid.UUID), pathID)
	if err != nil {
		response.InternalError(ctx, "Failed to recommend next learning path")
		return
	}

	// Create a learning path from the recommendation
	path, err := c.learningPathService.CreateLearningPath(
		userID.(uuid.UUID),
		recommendation.Title,
		recommendation.Description,
		string(recommendation.Level),
		recommendation.FocusAreas,
		recommendation.EstimatedDuration,
		uuid.Nil,
	)
	if err != nil {
		response.InternalError(ctx, "Failed to create learning path")
		return
	}

	// Add lessons to the path
	for i, lesson := range recommendation.SampleLessons {
		_, err := c.learningPathService.AddLessonToPath(
			userID.(uuid.UUID),
			path.ID,
			lesson.ID,
			i+1,
			true,
		)
		if err != nil {
			response.InternalError(ctx, "Failed to add lessons to path")
			return
		}
	}

	// Get the complete learning path with lessons
	completePath, err := c.learningPathService.GetLearningPathDetail(userID.(uuid.UUID), path.ID)
	if err != nil {
		response.NotFound(ctx, "Learning path not found")
		return
	}

	response.Success(ctx, http.StatusCreated, completePath, "Next learning path created successfully")
}

// GetNextExercise gets the next exercise for a user in a learning path
func (c *LearningPathController) GetNextExercise(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the next exercise
	responseData, err := c.learningPathService.GetNextExercise(userID.(uuid.UUID), pathID)
	if err != nil {
		response.InternalError(ctx, "Failed to retrieve next exercise")
		return
	}

	response.Success(ctx, http.StatusOK, responseData, "Next exercise retrieved successfully")
}

// CompleteExercise marks an exercise as completed in a learning path
func (c *LearningPathController) CompleteExercise(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the lesson ID from the URL
	lessonIDStr := ctx.Param("lessonId")

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Mark the exercise as completed
	learningPath, err := c.learningPathService.CompleteExercise(userID.(uuid.UUID), pathID, lessonIDStr)
	if err != nil {
		response.InternalError(ctx, "Failed to mark exercise as completed")
		return
	}

	response.Success(ctx, http.StatusOK, learningPath, "Exercise completed successfully")
}

// UpdateExerciseSetBasedOnResults updates a learning path based on exercise results
func (c *LearningPathController) UpdateExerciseSetBasedOnResults(ctx *gin.Context) {
	// Get the learning path ID from the URL
	pathID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.ValidationError(ctx, "Invalid learning path ID", map[string]string{"id": "Invalid learning path ID format"})
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Update exercise set
	if err := c.learningPathService.UpdateExerciseSetBasedOnResults(userID.(uuid.UUID), pathID); err != nil {
		response.InternalError(ctx, "Failed to update exercise set")
		return
	}

	// Get the updated learning path
	path, err := c.learningPathService.GetLearningPathDetail(userID.(uuid.UUID), pathID)
	if err != nil {
		response.NotFound(ctx, "Learning path not found")
		return
	}

	response.Success(ctx, http.StatusOK, path, "Exercise set updated successfully")
}
