package v1

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// BaseController 所有控制器的基础类
type BaseController struct{}

// Response API标准响应结构
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   interface{} `json:"error,omitempty"`
}

// NewSuccessResponse 创建成功响应
func (c *BaseController) NewSuccessResponse(data interface{}, message string) Response {
	return Response{
		Success: true,
		Message: message,
		Data:    data,
	}
}

// NewErrorResponse 创建错误响应
func (c *BaseController) NewErrorResponse(err error, message string) Response {
	return Response{
		Success: false,
		Message: message,
		Error:   err.Error(),
	}
}

// RespondWithSuccess 返回成功响应
func (c *BaseController) RespondWithSuccess(ctx *gin.Context, statusCode int, data interface{}, message string) {
	ctx.JSON(statusCode, c.NewSuccessResponse(data, message))
}

// RespondWithError 返回错误响应
func (c *BaseController) RespondWithError(ctx *gin.Context, statusCode int, err error, message string) {
	ctx.JSON(statusCode, c.NewErrorResponse(err, message))
}

// RespondOK 返回成功的200响应
func (c *BaseController) RespondOK(ctx *gin.Context, data interface{}, message string) {
	c.RespondWithSuccess(ctx, http.StatusOK, data, message)
}

// RespondCreated 返回成功的201响应
func (c *BaseController) RespondCreated(ctx *gin.Context, data interface{}, message string) {
	c.RespondWithSuccess(ctx, http.StatusCreated, data, message)
}

// RespondBadRequest 返回400错误响应
func (c *BaseController) RespondBadRequest(ctx *gin.Context, err error, message string) {
	c.RespondWithError(ctx, http.StatusBadRequest, err, message)
}

// RespondNotFound 返回404错误响应
func (c *BaseController) RespondNotFound(ctx *gin.Context, err error, message string) {
	c.RespondWithError(ctx, http.StatusNotFound, err, message)
}

// RespondInternalError 返回500错误响应
func (c *BaseController) RespondInternalError(ctx *gin.Context, err error, message string) {
	c.RespondWithError(ctx, http.StatusInternalServerError, err, message)
}

// ExtractPaginationParams 提取分页参数
func (c *BaseController) ExtractPaginationParams(ctx *gin.Context) (page, size int) {
	// 默认值
	page = 0
	size = 10

	// 尝试从查询参数中提取
	pageParam := ctx.DefaultQuery("page", "0")
	sizeParam := ctx.DefaultQuery("size", "10")

	// 尝试转换为整数
	if pageInt, err := strconv.Atoi(pageParam); err == nil {
		page = pageInt
	}

	if sizeInt, err := strconv.Atoi(sizeParam); err == nil {
		size = sizeInt
	}

	// 限制size的最大值
	if size > 100 {
		size = 100
	}

	return page, size
}
