package v1

import (
	"languagelearning/controllers"
	"languagelearning/middleware" // Assuming middleware is needed for authenticated/admin routes
	"log"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"     // Assuming swagger is needed
	ginSwagger "github.com/swaggo/gin-swagger" // Assuming swagger is needed
)

// APIRouter API路由注册器
type APIRouter struct {
	AuthController                 *controllers.AuthController
	UserController                 *controllers.UserController
	LessonController               *controllers.LessonController
	NotificationController         *controllers.NotificationController
	ExerciseRelationController     *controllers.ExerciseRelationController
	PersonalizedLearningController *controllers.PersonalizedLearningController
	LearningPathController         *controllers.LearningPathController
	AchievementController          *controllers.AchievementController
	EvaluationController           *controllers.EvaluationController
	ExerciseController             *controllers.ExerciseController
	WordController                 *controllers.WordController
	PracticeController             *controllers.PracticeController
	// Add other controllers here as they are integrated
}

// NewAPIRouter 创建一个新的API路由注册器
func NewAPIRouter(
	authController *controllers.AuthController,
	userController *controllers.UserController,
	lessonController *controllers.LessonController,
	notificationController *controllers.NotificationController,
	exerciseRelationController *controllers.ExerciseRelationController,
	personalizedLearningController *controllers.PersonalizedLearningController,
	learningPathController *controllers.LearningPathController,
	achievementController *controllers.AchievementController,
	evaluationController *controllers.EvaluationController,
	exerciseController *controllers.ExerciseController,
	wordController *controllers.WordController,
	practiceController *controllers.PracticeController,
	// Add other controllers here as they are integrated
) *APIRouter {
	return &APIRouter{
		AuthController:                 authController,
		UserController:                 userController,
		LessonController:               lessonController,
		NotificationController:         notificationController,
		ExerciseRelationController:     exerciseRelationController,
		PersonalizedLearningController: personalizedLearningController,
		LearningPathController:         learningPathController,
		AchievementController:          achievementController,
		EvaluationController:           evaluationController,
		ExerciseController:             exerciseController,
		WordController:                 wordController,
		PracticeController:             practiceController,
		// Assign other controllers here
	}
}

// RegisterRoutes 注册所有API路由
func (r *APIRouter) RegisterRoutes(router *gin.Engine) {
	log.Printf("Registering API routes...")

	// Health checks - 暂时注释掉，等待健康检查控制器集成
	// router.GET("/health", healthController.HealthCheck)
	// router.GET("/readiness", healthController.ReadinessCheck)
	// router.GET("/liveness", healthController.HealthCheck)

	// Swagger
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler)) // Assuming swagger will be needed

	// API版本前缀
	apiV1 := router.Group("/api/v1")

	// 公开路由
	public := apiV1.Group("")
	r.registerPublicRoutes(public)

	// 需要认证的路由
	authenticated := apiV1.Group("")
	authenticated.Use(middleware.JWTAuth()) // Uncomment middleware
	r.registerAuthenticatedRoutes(authenticated)

	// 需要管理员权限的路由
	admin := apiV1.Group("/admin")
	// 暂时注释掉中间件, 后续实现
	// admin.Use(middleware.JWTAuth()) // Removed middleware.AdminAuth()
	r.registerAdminRoutes(admin)

	log.Printf("Successfully registered all API routes")
}

// registerPublicRoutes 注册公开的API路由
func (r *APIRouter) registerPublicRoutes(router *gin.RouterGroup) {
	// Auth routes
	auth := router.Group("/auth")
	{
		auth.POST("/login", r.AuthController.Login)
		auth.POST("/register", r.AuthController.Register)
		auth.POST("/reset-password", r.AuthController.ResetPassword)
	}

	// User routes (public parts)
	// Currently no public user routes defined in controllers/registry.go

	// Lesson routes (read-only)
	lessons := router.Group("/lessons")
	{
		lessons.GET("", r.LessonController.GetLessons)
		lessons.GET("/:id", r.LessonController.GetLessonByID) // Changed GetLessonDetail to GetLessonByID
		// Add other public lesson routes from controllers/registry.go if any
	}

	// Exercise routes (read-only)
	// Currently commented out in controllers/v1/router.go
}

// registerAuthenticatedRoutes 注册需要认证的API路由
func (r *APIRouter) registerAuthenticatedRoutes(router *gin.RouterGroup) {
	// Auth routes (protected parts)
	auth := router.Group("/auth")
	{
		auth.DELETE("/logout", r.AuthController.Logout)
		auth.PUT("/change-password", r.AuthController.ChangePassword)
	}

	// User routes
	user := router.Group("/user")
	{
		user.GET("/me", r.UserController.GetMe)
		user.GET("/profile", r.UserController.GetUserProfile)
		user.PUT("/profile", r.UserController.UpdateUserProfile)
		user.GET("/settings", r.UserController.GetUserSettings)
		user.PUT("/settings", r.UserController.UpdateUserSettings)
		user.GET("/stats", r.UserController.GetUserStats)
		user.GET("/streak", r.UserController.GetLearningStreak)
		user.GET("/progress/:period", r.UserController.GetProgressReport)
		user.GET("/words", r.UserController.GetUserWordList)
		user.GET("/achievements", r.UserController.GetUserAchievements)
	}

	// Notification routes
	notifications := router.Group("/notifications")
	{
		notifications.GET("", r.NotificationController.GetNotifications)
		notifications.PATCH("/:id/read", r.NotificationController.MarkNotificationAsRead)
		notifications.PATCH("/read-all", r.NotificationController.MarkAllNotificationsAsRead)
		notifications.PATCH("/:id/archive", r.NotificationController.ArchiveNotification)
		notifications.GET("/preferences", r.NotificationController.GetNotificationPreferences)
		notifications.PUT("/preferences", r.NotificationController.UpdateNotificationPreferences)
	}

	// Lesson routes
	lessons := router.Group("/lessons")
	{
		// Lesson progress related routes
		lessons.GET("/:id/progress", r.LessonController.GetLessonProgress)
		lessons.PUT("/:id/progress", r.LessonController.UpdateLessonProgress)
		// TODO: Implement or relocate GetFavoriteLessons and ToggleFavoriteLesson
		// lessons.GET("/favorites", r.LessonController.GetFavoriteLessons)
		// lessons.PATCH("/:id/favorite", r.LessonController.ToggleFavoriteLesson)
		// Add other authenticated lesson routes from controllers/registry.go if any
	}

	// Personalized Learning routes
	personalized := router.Group("/personalized-learning")
	{
		personalized.GET("/status", r.PersonalizedLearningController.GetPersonalizedLearningStatus)
		personalized.POST("/initiate", r.PersonalizedLearningController.InitiatePersonalizedLearning)
	}

	// Exercise Relation routes
	relations := router.Group("/exercise-relations")
	{
		relations.POST("", r.ExerciseRelationController.CreateRelation)
		relations.PUT("/:id", r.ExerciseRelationController.UpdateRelation)
		relations.DELETE("/:id", r.ExerciseRelationController.DeleteRelation)
		relations.GET("/exercise/:id", r.ExerciseRelationController.GetExerciseRelations)
		relations.PUT("/exercise/:id/difficulty", r.ExerciseRelationController.UpdateExerciseDifficultyMetadata)
		relations.GET("/recommendations", r.ExerciseRelationController.RecommendExercises)
	}

	// Learning Path routes
	learningPath := router.Group("/learning-paths")
	{
		learningPath.GET("", r.LearningPathController.GetLearningPaths)
		learningPath.GET("/:id", r.LearningPathController.GetLearningPathDetail)
		learningPath.POST("", r.LearningPathController.CreateLearningPath)
		learningPath.POST("/:id/lessons", r.LearningPathController.AddLessonToPath)
		learningPath.PUT("/:id", r.LearningPathController.UpdateLearningPath)
		learningPath.DELETE("/:id", r.LearningPathController.DeleteLearningPath)
		learningPath.GET("/recommendations", r.LearningPathController.GetRecommendedLearningPaths)
		learningPath.POST("/recommendations/create", r.LearningPathController.CreateLearningPathFromRecommendation)
		learningPath.POST("/:id/schedule-assessment", r.LearningPathController.SchedulePeriodicAssessment)
		learningPath.POST("/:id/adjust", r.LearningPathController.AdjustLearningPathBasedOnAssessment)
		learningPath.GET("/:id/next", r.LearningPathController.RecommendNextLearningPath)
		learningPath.POST("/:id/next/create", r.LearningPathController.CreateNextLearningPath)
		learningPath.POST("/:id/complete-exercise/:lessonId", r.LearningPathController.CompleteExercise)
		learningPath.GET("/:id/next-exercise", r.LearningPathController.GetNextExercise)
		learningPath.POST("/:id/update", r.LearningPathController.UpdateExerciseSetBasedOnResults)
		learningPath.POST("/:id/update-progress", r.LearningPathController.UpdateLearningPathProgress)
		learningPath.POST("/:id/update-lessons", r.LearningPathController.UpdateLessonCompletion)
	}

	// Evaluation routes
	router.GET("/evaluations", r.EvaluationController.GetAvailableEvaluations)
	router.GET("/evaluations/:id", r.EvaluationController.GetEvaluationDetails)
	router.POST("/evaluations", r.EvaluationController.CreateEvaluation)
	router.POST("/evaluations/:id/start", r.EvaluationController.StartEvaluation)
	router.POST("/evaluations/:id/answer", r.EvaluationController.SubmitEvaluationAnswer)
	router.POST("/evaluations/:id/complete", r.EvaluationController.CompleteEvaluation)
	router.GET("/evaluations/:id/results", r.EvaluationController.GetEvaluationResults)
	router.GET("/evaluations/history", r.EvaluationController.GetUserEvaluationHistory)

	// Achievement routes
	achievements := router.Group("/achievements")
	{
		achievements.GET("", r.AchievementController.GetAchievements)
		achievements.PATCH("/:id/claim", r.AchievementController.ClaimAchievementReward)
	}

	// Word routes
	router.GET("/words", r.WordController.GetWords)
	router.GET("/words/:id", r.WordController.GetWordDetail)
	router.PUT("/words/:id/learned", r.WordController.MarkWordAsLearned)

	// Practice routes
	router.GET("/practice/history", r.PracticeController.GetPracticeHistory)
	router.GET("/practice/recommended", r.PracticeController.GetRecommendedPractice)
	router.POST("/practice/session", r.PracticeController.SavePracticeSession)

	// Exercise routes
	router.GET("/grammar/exercises", r.ExerciseController.GetGrammarExercises)
	router.POST("/grammar/exercises/:id/submit", r.ExerciseController.SubmitGrammarAnswer)
	router.GET("/speaking/exercises", r.ExerciseController.GetSpeakingExercises)
	router.POST("/speaking/exercises/:id/submit", r.ExerciseController.SubmitSpeakingAnswer)
	router.GET("/listening/exercises", r.ExerciseController.GetListeningExercises)
	router.POST("/listening/exercises/:id/submit", r.ExerciseController.SubmitListeningAnswer)
	router.GET("/word/exercises", r.ExerciseController.GetWordExercises)
	router.POST("/word/exercises/:id/submit", r.ExerciseController.SubmitWordAnswer)
}

// registerAdminRoutes 注册需要管理员权限的API路由
func (r *APIRouter) registerAdminRoutes(router *gin.RouterGroup) {
	// Lesson management routes
	// TODO: Implement or relocate admin lesson routes
	// lessons := router.Group("/lessons")
	// {
	// lessons.POST("", r.LessonController.CreateLesson)
	// lessons.PUT("/:id", r.LessonController.UpdateLesson)
	// lessons.DELETE("/:id", r.LessonController.DeleteLesson)
	// Add other admin lesson routes from controllers/registry.go if any
	// }

	// Exercise management routes
	// Currently commented out in controllers/v1/router.go
}
