package controllers

import (
	"net/http"

	learningService "languagelearning/domain/learning/service"
	"languagelearning/utils/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// PracticeController handles the practice functionality
type PracticeController struct {
	practiceService       learningService.PracticeService
	achievementController *AchievementController
}

// NewPracticeController creates a new practice controller
func NewPracticeController(practiceService learningService.PracticeService, achievementController *AchievementController) *PracticeController {
	return &PracticeController{
		practiceService:       practiceService,
		achievementController: achievementController,
	}
}

// SavePracticeSessionRequest represents the save practice session request body
type SavePracticeSessionRequest struct {
	Type     string `json:"type" binding:"required" example:"vocabulary"`        // vocabulary, grammar, listening, speaking, mixed
	Duration int    `json:"duration" binding:"required,min=1" example:"300"`     // in seconds
	Score    int    `json:"score" binding:"required,min=0,max=100" example:"85"` // percentage score
}

// @Summary Get practice history
// @Description Get the current user's practice session history
// @Tags Practice
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "Practice history retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/practice/history [get]
func (c *PracticeController) GetPracticeHistory(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the practice history
	sessions, err := c.practiceService.GetPracticeHistory(userID.(uuid.UUID))
	if err != nil {
		response.InternalError(ctx, "Failed to retrieve practice history")
		return
	}

	response.Success(ctx, http.StatusOK, sessions, "Practice history retrieved successfully")
}

// @Summary Get recommended practice
// @Description Get personalized practice recommendations for the current user
// @Tags Practice
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "Recommended practice retrieved successfully"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 404 {object} utils.Response "User not found"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/practice/recommended [get]
func (c *PracticeController) GetRecommendedPractice(ctx *gin.Context) {
	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Get the recommended practice
	recommendations, err := c.practiceService.GetRecommendedPractice(userID.(uuid.UUID))
	if err != nil {
		response.NotFound(ctx, "User not found")
		return
	}

	response.Success(ctx, http.StatusOK, recommendations, "Recommended practice retrieved successfully")
}

// @Summary Save practice session
// @Description Save a completed practice session for the current user
// @Tags Practice
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body SavePracticeSessionRequest true "Practice session details"
// @Success 200 {object} utils.Response "Practice session saved successfully"
// @Failure 400 {object} utils.Response "Invalid request body"
// @Failure 401 {object} utils.Response "User not authenticated"
// @Failure 500 {object} utils.Response "Server error"
// @Router /api/v1/practice/session [post]
func (c *PracticeController) SavePracticeSession(ctx *gin.Context) {
	// Get the request body
	var req SavePracticeSessionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.HandleValidationErrors(ctx, err, "Invalid request body")
		return
	}

	// Get the user ID from the context
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Unauthorized(ctx, "User not authenticated")
		return
	}

	// Save the practice session
	session, err := c.practiceService.SavePracticeSession(userID.(uuid.UUID), req.Type, req.Duration, req.Score)
	if err != nil {
		response.InternalError(ctx, "Failed to save practice session")
		return
	}

	response.Success(ctx, http.StatusOK, session, "Practice session saved successfully")
}
