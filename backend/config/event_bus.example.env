# 事件总线配置示例
# 支持的类型: memory, redis, rabbitmq, kafka

# ===========================================
# 基础配置
# ===========================================

# 事件总线类型 (memory|redis|rabbitmq|kafka)
EVENT_BUS_TYPE=memory

# 重试配置
EVENT_BUS_RETRY_ATTEMPTS=3
EVENT_BUS_RETRY_DELAY=1s

# 批处理配置
EVENT_BUS_BATCH_SIZE=100
EVENT_BUS_FLUSH_INTERVAL=5s

# ===========================================
# Redis 配置 (当 EVENT_BUS_TYPE=redis 时使用)
# ===========================================

# Redis 连接配置
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Redis Stream 配置
REDIS_STREAM_KEY=events
REDIS_CONSUMER_GROUP=event-handlers
REDIS_CONSUMER_NAME=handler-1

# Redis 连接池配置
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5
REDIS_MAX_RETRIES=3
REDIS_DIAL_TIMEOUT=5s
REDIS_READ_TIMEOUT=3s
REDIS_WRITE_TIMEOUT=3s

# ===========================================
# RabbitMQ 配置 (当 EVENT_BUS_TYPE=rabbitmq 时使用)
# ===========================================

# RabbitMQ 连接配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_EXCHANGE=events
RABBITMQ_CONNECTION_NAME=event-bus

# RabbitMQ 队列配置
RABBITMQ_QUEUE_DURABLE=true
RABBITMQ_QUEUE_AUTO_DELETE=false
RABBITMQ_QUEUE_EXCLUSIVE=false

# RabbitMQ 消息配置
RABBITMQ_MESSAGE_PERSISTENT=true
RABBITMQ_PREFETCH_COUNT=10

# RabbitMQ 连接配置
RABBITMQ_HEARTBEAT=10s

# ===========================================
# Kafka 配置 (当 EVENT_BUS_TYPE=kafka 时使用)
# ===========================================

# Kafka 连接配置
KAFKA_BROKERS=localhost:9092
KAFKA_TOPIC=events

# Kafka 生产者配置
KAFKA_PRODUCER_BATCH_SIZE=100
KAFKA_PRODUCER_BATCH_TIMEOUT=10ms
KAFKA_PRODUCER_COMPRESSION=gzip
KAFKA_PRODUCER_MAX_RETRIES=3
KAFKA_PRODUCER_REQUIRED_ACKS=1

# Kafka 消费者配置
KAFKA_CONSUMER_GROUP_ID=event-handlers
KAFKA_CONSUMER_START_OFFSET=-1
KAFKA_CONSUMER_COMMIT_INTERVAL=1s
KAFKA_CONSUMER_SESSION_TIMEOUT=10s
KAFKA_CONSUMER_HEARTBEAT_TIMEOUT=3s
KAFKA_CONSUMER_MAX_BYTES=10485760

# ===========================================
# 使用示例
# ===========================================

# 1. 使用内存事件总线（默认，无持久化）
# EVENT_BUS_TYPE=memory

# 2. 使用 Redis 事件总线
# EVENT_BUS_TYPE=redis
# REDIS_ADDR=localhost:6379
# REDIS_PASSWORD=your_password
# REDIS_DB=0

# 3. 使用 RabbitMQ 事件总线
# EVENT_BUS_TYPE=rabbitmq
# RABBITMQ_URL=amqp://user:password@localhost:5672/vhost

# 4. 使用 Kafka 事件总线
# EVENT_BUS_TYPE=kafka
# KAFKA_BROKERS=localhost:9092,localhost:9093,localhost:9094
# KAFKA_TOPIC=events

# ===========================================
# Docker Compose 示例
# ===========================================

# 如果使用 Docker Compose，可以这样配置：

# Redis:
# EVENT_BUS_TYPE=redis
# REDIS_ADDR=redis:6379

# RabbitMQ:
# EVENT_BUS_TYPE=rabbitmq
# RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/

# Kafka:
# EVENT_BUS_TYPE=kafka
# KAFKA_BROKERS=kafka:9092

# ===========================================
# 生产环境建议
# ===========================================

# 1. 对于高可用性要求，建议使用 Kafka 或 RabbitMQ
# 2. 对于简单场景，Redis 是一个很好的选择
# 3. 内存事件总线仅适用于开发和测试环境
# 4. 在生产环境中，请确保设置适当的重试和超时配置
# 5. 考虑使用集群模式以提高可用性

# ===========================================
# 监控和调试
# ===========================================

# 启用详细日志记录
LOG_LEVEL=debug

# 事件总线健康检查端点
# GET /health/eventbus

# 事件总线统计信息端点
# GET /stats/eventbus
