package config

import (
	"os"
	"strconv"
	"time"
)

// Config 集中化应用配置结构
type Config struct {
	// 服务器配置
	Server ServerConfig

	// 数据库配置
	Database DatabaseConfig

	// JWT认证配置
	JWT JWTConfig

	// API配置
	API APIConfig

	// RabbitMQ 配置
	RabbitMQ RabbitMQConfig

	// 邮件服务配置
	Email EmailConfig

	// SendGrid 配置
	SendGridAPIKey string
}

// ServerConfig 服务器相关配置
type ServerConfig struct {
	Port    string
	GinMode string
}

// DatabaseConfig 数据库相关配置
type DatabaseConfig struct {
	URL         string
	MaxIdleConn int
	MaxOpenConn int
}

// JWTConfig JWT认证相关配置
type JWTConfig struct {
	Secret          string
	ExpirationHours int
}

// APIConfig API相关配置
type APIConfig struct {
	BaseURL string
}

// RabbitMQConfig RabbitMQ相关配置
type RabbitMQConfig struct {
	Host     string
	Port     string
	User     string
	Password string
}

// EmailConfig 邮件服务相关配置
type EmailConfig struct {
	FromName    string
	FromAddress string
}

// LoadConfig 从环境变量加载配置
func LoadConfig() *Config {
	expirationHours, _ := strconv.Atoi(getEnv("JWT_EXPIRATION_HOURS", "24"))
	dbMaxIdleConn, _ := strconv.Atoi(getEnv("DATABASE_MAX_IDLE_CONN", "10"))
	dbMaxOpenConn, _ := strconv.Atoi(getEnv("DATABASE_MAX_OPEN_CONN", "100"))

	return &Config{
		Server: ServerConfig{
			Port:    getEnv("PORT", "8080"),
			GinMode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			URL:         getEnv("DATABASE_URL", "postgres://postgres:postgres@localhost:5432/languagelearning?sslmode=disable"),
			MaxIdleConn: dbMaxIdleConn,
			MaxOpenConn: dbMaxOpenConn,
		},
		JWT: JWTConfig{
			Secret:          getEnv("JWT_SECRET", "your-secret-key-change-this-in-production"),
			ExpirationHours: expirationHours,
		},
		API: APIConfig{
			BaseURL: getEnv("API_BASE_URL", "http://localhost:8080"),
		},
		RabbitMQ: RabbitMQConfig{
			Host:     getEnv("RABBITMQ_HOST", "localhost"),
			Port:     getEnv("RABBITMQ_PORT", "5672"),
			User:     getEnv("RABBITMQ_USER", "guest"),
			Password: getEnv("RABBITMQ_PASSWORD", "guest"),
		},
		Email: EmailConfig{
			FromName:    getEnv("EMAIL_FROM_NAME", "Language Learning App"),
			FromAddress: getEnv("EMAIL_FROM_ADDRESS", "<EMAIL>"),
		},
		SendGridAPIKey: getEnv("SENDGRID_API_KEY", ""),
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetJWTExpirationDuration 获取JWT过期时间
func (c *Config) GetJWTExpirationDuration() time.Duration {
	return time.Duration(c.JWT.ExpirationHours) * time.Hour
}
