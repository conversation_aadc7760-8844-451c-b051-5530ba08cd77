FROM golang:1.23-alpine

# Install development tools
RUN apk add --no-cache git curl make gcc libc-dev

# Set working directory
WORKDIR /app

# Install air for hot reloading (optional)
RUN go install github.com/cosmtrek/air@v1.49.0

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download || (rm -f go.sum && go mod tidy)

# Copy the source code
COPY . .

# Expose port
EXPOSE 8080

# Command to run the application with hot reloading
# If Air fails, fall back to running the app directly
CMD ["sh", "-c", "air -c .air.toml || go run cmd/api/main.go"]
