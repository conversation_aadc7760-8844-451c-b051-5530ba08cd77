#!/bin/sh

echo "Fixing dependencies..."

# Remove go.sum to start fresh
rm -f go.sum

# Download all required dependencies
go get github.com/gin-contrib/cors
go get github.com/gin-gonic/gin
go get github.com/golang-jwt/jwt/v5
go get github.com/google/uuid
go get github.com/joho/godotenv
go get golang.org/x/crypto/bcrypt
go get gorm.io/driver/postgres
go get gorm.io/gorm
go get gorm.io/gorm/logger

# Tidy up the go.mod file
go mod tidy

echo "Dependencies fixed successfully!"
