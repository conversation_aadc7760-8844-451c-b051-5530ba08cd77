🔍 分析Lesson重复定义使用情况...

📊 Lesson定义分析报告
===================================================

🏗️  发现 4 个Lesson定义:

1. models/lesson.go
   包名: models
   字段数: 16
   方法数: 3
   主要字段: ID, Title, Description, Content, Language
   主要方法: BeforeCreate, ToLearningEntity, FromLearningEntity

2. domain/lesson/entity/lesson.go
   包名: entity
   字段数: 15
   方法数: 10
   主要字段: ID, Title, Description, Content, Language
   主要方法: Validate, IsPublished, CanBePublished

3. domain/learning/entity/lesson.go
   包名: entity
   字段数: 19
   方法数: 14
   主要字段: ID, Title, Description, Type, Level
   主要方法: Validate, AddExercise, RemoveExercise

4. domain/learning/lesson/entity/lesson.go
   包名: lesson
   字段数: 18
   方法数: 7
   主要字段: ID, Title, Description, Content, Level
   主要方法: SetDuration, SetLanguage, GetStatus

📈 发现 993 个Lesson使用:

📁 按文件统计使用情况:
   cmd/reset/main.go: 3次
   domain/learning/service/adaptive_learning_service.go: 1次
   cmd/seed/main.go: 3次
   domain/learning/lesson/event/lesson_completed_handler.go: 1次
   domain/learning/path/event/learning_path_handlers.go: 1次
   domain/user/di/module.go: 3次
   domain/achievement/service/impl/achievement_service.go: 8次
   domain/lesson/event/types.go: 2次
   controllers/auth_controller.go: 2次
   controllers/evaluation_controller.go: 3次
   domain/exercise/event/exercise_events.go: 2次
   domain/learning/event/handlers/learning_path.go: 3次
   domain/learning/service/practice_service.go: 1次
   domain/user/event/user_events.go: 3次
   domain/evaluation/repository/evaluation_repository.go: 2次
   controllers/exercise_relation_controller.go: 3次
   domain/notification/di/module.go: 3次
   domain/user/event/user_handlers.go: 1次
   seeds/grammar_seed.go: 2次
   domain/achievement/entity/evaluation.go: 1次
   domain/learning/event/handlers/register.go: 2次
   seeds/english_listening_seed.go: 2次
   controllers/learning_path_controller.go: 3次
   domain/evaluation/service/impl/evaluation_service.go: 5次
   domain/learning/lesson/event/lesson_progress_handler.go: 1次
   domain/learning/repository/impl/evaluation_repository_impl.go: 2次
   domain/learning/repository/impl/lesson_repository_impl.go: 154次
   domain/learning/service/personalized_learning_service.go: 1次
   seeds/helpers/exercise_helper.go: 1次
   domain/learning/path/event/learning_path_events.go: 8次
   domain/learning/repository/evaluation_repository.go: 1次
   domain/learning/service/impl/adaptive_learning_service_impl.go: 5次
   domain/learning/service/lesson_service.go: 26次
   domain/user/service/impl/user_service_impl.go: 6次
   controllers/user_controller.go: 4次
   domain/achievement/repository/impl/user_achievement_repository_impl.go: 2次
   domain/achievement/service/achievement_service.go: 1次
   domain/core/event/handlers/base.go: 1次
   domain/learning/repository/lesson_repository.go: 36次
   domain/lesson/event/lesson_event.go: 2次
   domain/user/repository/impl/transaction_manager_impl.go: 1次
   migrations/language_migration.go: 1次
   domain/learning/service/impl/learning_path_service_impl.go: 7次
   controllers/lesson_controller.go: 8次
   domain/achievement/event/achievement_handlers.go: 1次
   domain/core/di/core_module.go: 3次
   domain/evaluation/event/evaluation_events.go: 2次
   seeds/english_grammar_seed.go: 2次
   domain/learning/lesson/event/register.go: 1次
   domain/learning/repository/word_repository.go: 1次
   domain/notification/repository/impl/notification_repository.go: 2次
   scripts/verify_repo_pattern.go: 2次
   domain/learning/service/impl/word_service.go: 5次
   domain/achievement/di/module.go: 2次
   domain/learning/entity/lesson.go: 17次
   domain/learning/repository/impl/models_lesson_repository_impl.go: 50次
   models/learning_path.go: 3次
   models/user.go: 1次
   scripts/seed_vocabulary.go: 2次
   tests/repository_pattern_test.go: 3次
   domain/learning/di/module.go: 3次
   controllers/notification_controller.go: 3次
   di/container.go: 22次
   domain/auth/repository/impl/auth_repository_impl.go: 3次
   domain/learning/repository/learning_path_repository.go: 2次
   domain/lesson/service/lesson_service.go: 31次
   cmd/debug/main.go: 1次
   controllers/personalized_learning_controller.go: 2次
   domain/core/event/types.go: 1次
   migrations/migrations.go: 5次
   domain/exercise/event/exercise_handlers.go: 1次
   domain/notification/event/event_factory.go: 2次
   domain/notification/repository/notification_repository.go: 1次
   domain/notification/service/impl/notification_service.go: 5次
   utils/response/response.go: 1次
   domain/lesson/event/handlers/lesson.go: 3次
   seeds/language_seed.go: 1次
   seeds/english_speaking_seed.go: 2次
   cmd/worker/main.go: 11次
   controllers/achievement_controller.go: 2次
   domain/learning/event/event_publisher.go: 1次
   controllers/di/module.go: 3次
   domain/learning/repository/difficulty_metadata_repository.go: 1次
   domain/learning/repository/impl/word_repository_impl.go: 2次
   domain/learning/service/learning_path_service.go: 1次
   domain/learning/service/impl/personalized_learning_service_impl.go: 10次
   domain/learning/service/impl/practice_service.go: 3次
   middleware/jwt.go: 2次
   middleware/logging_middleware.go: 1次
   models/word.go: 1次
   scripts/run_seed.go: 2次
   scripts/test_vocabulary.go: 2次
   utils/validator.go: 1次
   cmd/migrate/main.go: 3次
   domain/auth/di/module.go: 2次
   domain/learning/path/entity/learning_path.go: 4次
   domain/learning/repository/impl/lesson_progress_repository_impl.go: 3次
   domain/learning/repository/models_learning_path_repository.go: 1次
   domain/learning/repository/models_lesson_repository.go: 27次
   domain/learning/service/impl/lesson_service_impl.go: 40次
   seeds/vocabulary_seed.go: 4次
   domain/learning/repository/impl/practice_repository_impl.go: 2次
   domain/notification/event/notification_handlers.go: 1次
   models/lesson.go: 14次
   seeds/seed.go: 16次
   domain/evaluation/service/evaluation_service_interface.go: 1次
   domain/learning/event/handlers/learning.go: 2次
   domain/lesson/event/handlers/base.go: 1次
   domain/lesson/repository/lesson_repository.go: 31次
   domain/lesson/service/lesson_service_impl.go: 31次
   domain/user/repository/impl/user_stats_repository_impl.go: 3次
   seeds/listening_seed.go: 2次
   tests/controllers/health_controller_test.go: 2次
   domain/learning/event/handlers/base.go: 1次
   domain/learning/event/types.go: 4次
   domain/user/service/user_service.go: 1次
   migrations/assessment_progress_migration.go: 1次
   tests/utils/test_utils.go: 1次
   domain/learning/repository/impl/exercise_attempt_repository_impl.go: 3次
   domain/learning/repository/practice_repository.go: 1次
   domain/notification/service/notification_service.go: 1次
   tests/smoke_test.go: 2次
   controllers/v1/router.go: 2次
   di/modular_container.go: 16次
   domain/learning/event/handlers/exercise.go: 4次
   domain/learning/lesson/entity/lesson.go: 10次
   domain/learning/repository/impl/exercise_relation_repository_impl.go: 2次
   domain/learning/repository/impl/exercise_repository_impl.go: 5次
   domain/user/repository/impl/user_repository.go: 3次
   seeds/english_word_seed.go: 1次
   domain/achievement/event/achievement_events.go: 2次
   domain/auth/repository/auth_repository.go: 2次
   domain/evaluation/di/module.go: 2次
   domain/learning/repository/exercise_repository.go: 2次
   domain/learning/service/exercise_service.go: 3次
   seeds/word_seed.go: 1次
   domain/learning/service/impl/test/lesson_service_test.go: 71次
   domain/lesson/entity/lesson.go: 11次
   controllers/practice_controller.go: 2次
   domain/auth/service/impl/auth_service.go: 6次
   domain/exercise/entity/exercise.go: 2次
   domain/learning/event/event_store.go: 1次
   domain/learning/repository/impl/difficulty_metadata_repository_impl.go: 2次
   domain/learning/service/impl/exercise_relation_service_impl.go: 7次
   controllers/health_controller.go: 2次
   controllers/word_controller.go: 2次
   domain/achievement/repository/impl/achievement_repository_impl.go: 2次
   models/setup.go: 1次
   seeds/evaluation_seed.go: 1次
   seeds/speaking_seed.go: 2次
   cmd/docs/models.go: 1次
   controllers/assessment_progress_controller.go: 2次
   controllers/exercise_controller.go: 2次
   domain/learning/repository/impl/models_learning_path_repository_impl.go: 2次
   domain/notification/event/notification_events.go: 2次
   domain/user/event/event_factory.go: 2次
   seeds/tag_seed.go: 1次
   domain/learning/event/event_factory.go: 6次
   cmd/api/main.go: 6次
   cmd/demo/modular_di_demo.go: 2次
   domain/achievement/event/event_factory.go: 2次
   domain/achievement/repository/user_achievement_repository.go: 1次
   domain/learning/event/handlers/learning_progress.go: 3次
   domain/learning/event/handlers/lesson.go: 3次
   examples/event_bus_usage.go: 1次
   cmd/worker/test_events.go: 5次
   domain/auth/service/auth_service.go: 1次
   domain/learning/lesson/event/lesson_events.go: 14次
   domain/learning/service/impl/exercise_service_impl.go: 6次
   domain/learning/service/word_service.go: 1次
   domain/user/repository/user_repository.go: 1次

📦 按导入路径统计:
   languagelearning/domain/auth/repository: 2个文件
   languagelearning/config: 8个文件
   languagelearning/controllers/v1: 4个文件
   languagelearning/domain/learning/event/handlers: 3个文件
   languagelearning/controllers: 5个文件
   languagelearning/domain/evaluation/di: 1个文件
   languagelearning/domain/user/di: 1个文件
   languagelearning/domain/learning/repository: 21个文件
   languagelearning/domain/user/entity: 19个文件
   languagelearning/domain/user/service: 2个文件
   languagelearning/domain/achievement/event: 3个文件
   languagelearning/domain/exercise/event: 7个文件
   languagelearning/domain/achievement/service: 2个文件
   languagelearning/domain/repository: 13个文件
   languagelearning/domain/learning/service/impl: 3个文件
   languagelearning/domain/learning/di: 1个文件
   languagelearning/domain/lesson/event: 1个文件
   languagelearning/domain/user/event: 4个文件
   languagelearning/seeds/helpers: 8个文件
   languagelearning/domain/notification/event: 4个文件
   languagelearning/domain/notification/service: 9个文件
   languagelearning/domain/user/repository/impl: 5个文件
   languagelearning/domain/auth/service/impl: 2个文件
   languagelearning/domain/core/event: 46个文件
   languagelearning/domain/evaluation/entity: 2个文件
   languagelearning/tests/utils: 1个文件
   languagelearning/domain/notification/entity: 13个文件
   languagelearning/domain/notification/repository/impl: 2个文件
   languagelearning/domain/achievement/di: 1个文件
   languagelearning/domain/exercise/entity: 9个文件
   languagelearning/domain/learning/path/entity: 2个文件
   languagelearning/domain/learning/entity: 14个文件
   languagelearning/domain/core/di: 10个文件
   languagelearning/seeds: 4个文件
   languagelearning/domain/learning/service: 15个文件
   languagelearning/domain/evaluation/service/impl: 2个文件
   languagelearning/controllers/di: 1个文件
   languagelearning/domain/evaluation/event: 2个文件
   languagelearning/domain/evaluation/service: 3个文件
   languagelearning/domain/learning/repository/impl: 3个文件
   languagelearning/domain/achievement/entity: 3个文件
   languagelearning/domain/notification/repository: 2个文件
   languagelearning/domain/learning/event: 12个文件
   languagelearning/domain/user/service/impl: 2个文件
   languagelearning/domain/notification/service/impl: 2个文件
   languagelearning/utils/errors: 1个文件
   languagelearning/migrations: 2个文件
   languagelearning/domain/lesson/entity: 4个文件
   languagelearning/domain/lesson/repository: 2个文件
   languagelearning/middleware: 5个文件
   languagelearning/domain/learning/lesson/event: 5个文件
   languagelearning/domain/auth/repository/impl: 1个文件
   languagelearning/domain/notification/di: 1个文件
   languagelearning/di: 5个文件
   languagelearning/domain/achievement/service/impl: 2个文件
   languagelearning/domain/auth/di: 1个文件
   languagelearning/domain/core/entity: 3个文件
   languagelearning/utils/response: 16个文件
   languagelearning/domain/achievement/repository/impl: 1个文件
   languagelearning/domain/learning/lesson/entity: 5个文件
   languagelearning/domain/core: 3个文件
   languagelearning/domain/user/repository: 8个文件
   languagelearning/models: 74个文件
   languagelearning/utils/logger: 6个文件
   languagelearning/domain/auth/service: 2个文件

💡 清理建议
===================================================

🎯 推荐的清理策略:

📋 models/lesson.go:
   复杂度: 19 (字段: 16, 方法: 3)
   使用次数: 171
   建议: 保留作为数据库模型，简化业务逻辑

📋 domain/lesson/entity/lesson.go:
   复杂度: 25 (字段: 15, 方法: 10)
   使用次数: 85
   建议: 作为主要业务实体保留

📋 domain/learning/entity/lesson.go:
   复杂度: 33 (字段: 19, 方法: 14)
   使用次数: 91
   建议: 作为主要业务实体保留

📋 domain/learning/lesson/entity/lesson.go:
   复杂度: 25 (字段: 18, 方法: 7)
   使用次数: 27
   建议: 作为主要业务实体保留

🚀 推荐的实施步骤:
1. 保留 models/lesson.go 作为数据库模型
2. 选择最完善的domain实体作为主要业务实体
3. 创建转换方法在repository层
4. 逐步迁移所有引用
5. 移除重复定义
