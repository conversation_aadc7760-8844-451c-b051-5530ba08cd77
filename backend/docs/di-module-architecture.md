# 依賴注入模塊化架構文檔

## 概述

本文檔描述了語言學習系統的依賴注入(DI)模塊化架構。該架構旨在提供一個靈活、可擴展且易於維護的依賴管理系統，遵循領域驅動設計(DDD)原則，使各個領域更加自治。

## 核心概念

### 模塊 (Module)

模塊是依賴注入系統的基本單位，代表一個功能相關的組件集合。每個模塊負責註冊自己的依賴，並聲明與其他模塊的關係。

模塊接口定義：
```go
type Module interface {
    // 返回模塊名稱
    Name() string
    // 返回此模塊依賴的其他模塊名稱
    Dependencies() []string
    // 返回此模塊支持的功能特性
    Features() []ModuleFeature
    // 註冊模塊到容器
    Register(container *dig.Container) error
}
```

### 功能特性 (Feature)

功能特性是模塊支持的能力標記，用於實現按需加載。系統可以根據配置啟用或禁用特定功能。

基本功能特性：
- `FeatureBasic`: 基本功能，默認啟用
- `FeatureAdvanced`: 進階功能，可選啟用
- `FeatureExperimental`: 實驗性功能，通常在開發環境啟用

### 模塊註冊表 (Registry)

模塊註冊表管理所有可用模塊，負責：
- 註冊模塊
- 管理功能特性的啟用/禁用
- 解析模塊依賴關係
- 檢測循環依賴
- 確定模塊加載順序

## 架構設計

### 目錄結構

```
/domain
  /core
    /di
      module.go       # 定義模塊接口和基類
      registry.go     # 模塊註冊表實現
  /learning
    /di
      module.go       # 學習領域模塊
  /user
    /di
      module.go       # 用戶領域模塊
  # 其他領域...
/controllers
  /di
    module.go         # 控制器模塊
/di
  container.go        # 主容器實現
```

### 依賴流程

1. 應用啟動時創建主容器
2. 註冊核心組件（配置、數據庫連接等）
3. 註冊所有可用模塊到註冊表
4. 解析模塊依賴關係，檢測循環依賴
5. 按依賴順序加載模塊
6. 註冊事件處理器
7. 容器構建完成，應用啟動

## 模塊實現指南

### 創建新模塊

1. 在相應領域目錄下創建 `di/module.go` 文件
2. 實現 `Module` 接口
3. 提供 `NewXXXModule()` 工廠函數
4. 在主容器的 `RegisterModules()` 方法中註冊模塊

### 模塊實現示例

```go
// domain/learning/di/module.go
package di

import (
    "go.uber.org/dig"
    coredi "languagelearning/domain/core/di"
    "languagelearning/domain/learning/repository/impl"
    svcimpl "languagelearning/domain/learning/service/impl"
)

// LearningModule 學習領域的DI模塊
type LearningModule struct {
    coredi.BaseModule
}

// NewLearningModule 創建學習領域模塊
func NewLearningModule() coredi.Module {
    return &LearningModule{
        BaseModule: coredi.NewBaseModule(
            "learning",
            []string{"event"},
            []coredi.ModuleFeature{coredi.FeatureBasic},
        ),
    }
}

// Register 註冊學習領域的所有依賴
func (m *LearningModule) Register(container *dig.Container) error {
    // 註冊存儲庫和服務...
    return nil
}
```

## 使用指南

### 在 main.go 中初始化容器

```go
// 創建DI容器
container := di.NewContainer()

// 啟用特定功能（可選）
container.EnableFeature(coredi.FeatureAdvanced)

// 構建容器
if err := container.Build(); err != nil {
    log.Fatalf("Failed to build DI container: %v", err)
}

// 使用容器啟動應用
container.Invoke(func(router *v1.APIRouter) {
    // 設置路由並啟動服務器
})
```

### 啟用/禁用功能

```go
// 啟用進階功能
container.EnableFeature(coredi.FeatureAdvanced)

// 禁用實驗性功能
container.DisableFeature(coredi.FeatureExperimental)
```

## 最佳實踐

1. **模塊命名** - 使用有意義的名稱，通常是領域名稱
2. **依賴聲明** - 明確聲明模塊依賴，避免隱式依賴
3. **功能分級** - 合理使用功能特性標記，區分基本功能和高級功能
4. **避免循環依賴** - 設計時避免模塊間的循環依賴
5. **保持模塊內聚** - 一個模塊應該只包含緊密相關的組件
6. **接口優先** - 註冊依賴時優先使用接口而非具體實現

## 擴展點

1. **條件註冊** - 根據配置或環境變量條件性註冊某些依賴
2. **插件系統** - 擴展為支持動態加載的插件系統
3. **健康檢查** - 為每個模塊添加健康檢查功能
4. **指標收集** - 收集模塊加載和性能指標
5. **熱重載** - 支持某些模塊的運行時重載

## 故障排除

### 常見問題

1. **循環依賴錯誤**
   - 錯誤: `循環依賴檢測到: module_name`
   - 解決: 重新設計模塊依賴關係，可能需要引入中間模塊或事件機制

2. **依賴解析失敗**
   - 錯誤: `failed to register module X: failed to provide Y`
   - 解決: 檢查依賴是否正確註冊，或者依賴的模塊是否已啟用

3. **功能缺失**
   - 問題: 某些功能未生效
   - 解決: 檢查相關功能特性是否已啟用 `container.EnableFeature(feature)`