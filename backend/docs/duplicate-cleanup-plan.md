# 重复定义清理计划

## 📊 分析结果总结

基于代码分析，发现以下重复定义情况：

### Lesson 重复定义 (4个)
1. **models/lesson.go** - 数据库模型 (使用次数: 170, 复杂度: 19)
2. **domain/lesson/entity/lesson.go** - 课程领域实体 (使用次数: 84, 复杂度: 25)
3. **domain/learning/entity/lesson.go** - 学习领域实体 (使用次数: 90, 复杂度: 33)
4. **domain/learning/lesson/entity/lesson.go** - 学习课程子模块 (使用次数: 27, 复杂度: 25)

## 🎯 清理策略

### 阶段1: 创建统一的领域实体 (本周)

#### 1.1 选择主要实体
- **保留**: `domain/learning/entity/lesson.go` (最复杂，功能最完整)
- **作为**: 主要业务实体
- **原因**: 复杂度最高(33)，使用次数多(90)，功能最完整

#### 1.2 保留数据库模型
- **保留**: `models/lesson.go`
- **作为**: 数据库持久化模型
- **原因**: 使用次数最多(170)，GORM相关

#### 1.3 移除重复实体
- **移除**: `domain/lesson/entity/lesson.go`
- **移除**: `domain/learning/lesson/entity/lesson.go`
- **策略**: 逐步迁移引用到主要实体

### 阶段2: 创建转换层 (下周)

#### 2.1 Repository转换
```go
// 在repository层处理模型转换
type LessonRepository interface {
    // 返回领域实体
    FindByID(id uuid.UUID) (*entity.Lesson, error)
    Save(lesson *entity.Lesson) error
}

type lessonRepositoryImpl struct {
    db *gorm.DB
}

func (r *lessonRepositoryImpl) FindByID(id uuid.UUID) (*entity.Lesson, error) {
    var model models.Lesson
    err := r.db.First(&model, "id = ?", id).Error
    if err != nil {
        return nil, err
    }
    return model.ToEntity(), nil // 转换方法
}
```

#### 2.2 转换方法
- 在 `models/lesson.go` 中添加 `ToEntity()` 方法
- 在 `domain/learning/entity/lesson.go` 中添加 `ToModel()` 方法

### 阶段3: 迁移引用 (第3-4周)

#### 3.1 迁移优先级
1. **高优先级**: Repository层 (154次使用)
2. **中优先级**: Service层 (40次使用)
3. **低优先级**: Controller层 (8次使用)

#### 3.2 迁移步骤
1. 更新所有import语句
2. 更新类型声明
3. 更新方法调用
4. 运行测试验证

### 阶段4: 清理和验证 (第5周)

#### 4.1 移除文件
- 删除 `domain/lesson/entity/lesson.go`
- 删除 `domain/learning/lesson/entity/lesson.go`
- 删除相关的空目录

#### 4.2 验证
- 编译检查
- 测试运行
- 功能验证

## 🛠️ 实施工具

### 自动化脚本
```bash
# 1. 查找和替换import
find . -name "*.go" -exec sed -i 's|domain/lesson/entity|domain/learning/entity|g' {} \;

# 2. 查找使用情况
grep -r "lesson\.Lesson" --include="*.go" .

# 3. 验证编译
go build ./...
```

### 测试策略
- 每个阶段后运行完整测试套件
- 重点测试Lesson相关功能
- 性能回归测试

## 📈 成功指标

- [ ] 重复定义从4个减少到2个 (models + domain)
- [ ] 所有测试通过
- [ ] 编译无错误
- [ ] 功能无回归
- [ ] 代码覆盖率不降低

## ⚠️ 风险控制

### 风险识别
1. **破坏现有功能**: 迁移过程中可能引入bug
2. **测试覆盖不足**: 当前测试覆盖率较低
3. **依赖关系复杂**: 多层依赖可能遗漏

### 缓解措施
1. **分阶段实施**: 每次只改变一小部分
2. **备份代码**: 每个阶段前创建分支
3. **增加测试**: 先补充关键路径测试
4. **回滚计划**: 准备快速回滚方案

## 📅 时间计划

| 阶段 | 时间 | 任务 | 交付物 |
|------|------|------|--------|
| 1 | 第1周 | 创建统一实体 | 新的转换方法 |
| 2 | 第2周 | Repository转换层 | 转换接口实现 |
| 3 | 第3-4周 | 迁移引用 | 更新所有引用 |
| 4 | 第5周 | 清理验证 | 删除重复文件 |

## 🔄 后续优化

清理完成后的进一步优化：
1. 统一Exercise相关重复定义
2. 清理User相关重复定义
3. 优化值对象使用
4. 建立防止重复的规范
