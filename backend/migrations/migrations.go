package migrations

import (
	"log"

	"languagelearning/models"

	"gorm.io/gorm"
)

// Migrate runs all database migrations
func Migrate(db *gorm.DB) {
	log.Println("Running database migrations...")

	// First migrate user and language models
	err := db.AutoMigrate(
		&models.User{},
		&models.UserSettings{},
		&models.Language{},
		&models.UserLanguage{},
	)
	if err != nil {
		log.Fatalf("Failed to migrate user and language models: %v", err)
	}

	// Run language migrations to set up default language and update existing tables
	MigrateLanguages(db)

	// Run exercise progress migrations to create missing tables
	MigrateExerciseProgress(db)

	// Run assessment progress migrations
	MigrateAssessmentProgress(db)

	// Now migrate the rest of the models
	err = db.AutoMigrate(
		&models.Exercise{},
		&models.GrammarExercise{},
		&models.ListeningExercise{},
		&models.ListeningQuestion{},
		&models.SpeakingExercise{},
		&models.Lesson{},
		&models.LessonProgress{},
		&models.LessonExercise{},
		&models.Tag{},
		&models.ExerciseTag{},
		&models.Achievement{},
		&models.UserAchievement{},
		&models.UserStats{},
		&models.UserProfile{},
		&models.Social{},
		&models.Word{},
		&models.UserWord{},
		&models.PracticeSession{},
		// Evaluation models
		&models.Evaluation{},
		&models.EvalSection{},
		&models.EvalQuestion{},
		&models.EvaluationResult{},
		&models.SectionScore{},
		// Learning path models
		&models.LearningPath{},
		&models.LearningPathLesson{},
		// Exercise relation models
		&models.ExerciseRelation{},
		&models.ExerciseDifficultyMetadata{},
		// Notification models
		&models.Notification{},
		&models.NotificationPreference{},
		&models.EmailTemplate{},
		&models.EmailLog{},
		// Assessment progress model
		&models.AssessmentProgress{},
	)
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	log.Println("Database migrations completed successfully")
}

// CreateIndexes creates database indexes for better performance
func CreateIndexes(db *gorm.DB) {
	log.Println("Creating database indexes...")

	// User indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")

	// Exercise indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_grammar_exercises_difficulty ON grammar_exercises(difficulty)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_grammar_exercises_category ON grammar_exercises(category)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_listening_exercises_difficulty ON listening_exercises(difficulty)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_listening_exercises_category ON listening_exercises(category)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_speaking_exercises_difficulty ON speaking_exercises(difficulty)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_speaking_exercises_category ON speaking_exercises(category)")

	// Lesson indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lessons_category ON lessons(category)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lessons_level ON lessons(level)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lessons_difficulty ON lessons(difficulty)")

	// Lesson progress indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lesson_progress_user_id ON lesson_progresses(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lesson_progress_lesson_id ON lesson_progresses(lesson_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lesson_progress_is_completed ON lesson_progresses(is_completed)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lesson_progress_is_favorite ON lesson_progresses(is_favorite)")

	// Achievement indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_achievements_type ON achievements(type)")

	// User achievement indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON user_achievements(achievement_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_achievements_is_unlocked ON user_achievements(is_unlocked)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_achievements_reward_claimed ON user_achievements(reward_claimed)")

	// Word indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_words_difficulty ON words(difficulty)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_words_category ON words(category)")

	// User word indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_words_user_id ON user_words(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_words_word_id ON user_words(word_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_words_is_learned ON user_words(is_learned)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_words_is_favorite ON user_words(is_favorite)")

	// Practice session indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_practice_sessions_user_id ON practice_sessions(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_practice_sessions_type ON practice_sessions(type)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_practice_sessions_created_at ON practice_sessions(created_at)")

	// Evaluation indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_evaluations_user_id ON evaluations(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_evaluations_type ON evaluations(type)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_evaluations_is_completed ON evaluations(is_completed)")

	// Evaluation section indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_eval_sections_evaluation_id ON eval_sections(evaluation_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_eval_sections_skill ON eval_sections(skill)")

	// Evaluation question indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_eval_questions_section_id ON eval_questions(section_id)")

	// Evaluation result indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_evaluation_results_user_id ON evaluation_results(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_evaluation_results_evaluation_id ON evaluation_results(evaluation_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_evaluation_results_is_passed ON evaluation_results(is_passed)")

	// Section score indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_section_scores_result_id ON section_scores(result_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_section_scores_skill ON section_scores(skill)")

	// Learning path indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_learning_paths_user_id ON learning_paths(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_learning_paths_status ON learning_paths(status)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_learning_paths_level ON learning_paths(level)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_learning_paths_evaluation_id ON learning_paths(evaluation_id)")

	// Learning path lesson indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_learning_path_lessons_learning_path_id ON learning_path_lessons(learning_path_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_learning_path_lessons_lesson_id ON learning_path_lessons(lesson_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_learning_path_lessons_is_completed ON learning_path_lessons(is_completed)")

	// Exercise relation indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_relations_source_id ON exercise_relations(source_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_relations_target_id ON exercise_relations(target_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_relations_source_type ON exercise_relations(source_type)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_relations_target_type ON exercise_relations(target_type)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_relations_relation_type ON exercise_relations(relation_type)")

	// Exercise difficulty metadata indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_difficulty_metadata_exercise_id ON exercise_difficulty_metadata(exercise_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_difficulty_metadata_exercise_type ON exercise_difficulty_metadata(exercise_type)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_difficulty_metadata_complexity_score ON exercise_difficulty_metadata(complexity_score)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_difficulty_metadata_success_rate ON exercise_difficulty_metadata(success_rate)")

	// Language indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_languages_code ON languages(code)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_languages_is_active ON languages(is_active)")

	// User language indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_languages_user_id ON user_languages(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_languages_language_id ON user_languages(language_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_languages_is_learning ON user_languages(is_learning)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_languages_is_native ON user_languages(is_native)")

	// Assessment progress indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_assessment_progress_user_id ON assessment_progresses(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_assessment_progress_evaluation_id ON assessment_progresses(evaluation_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_assessment_progress_session_token ON assessment_progresses(session_token)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_assessment_progress_last_updated ON assessment_progresses(last_updated)")

	// Exercise language indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_grammar_exercises_language_id ON grammar_exercises(language_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_listening_exercises_language_id ON listening_exercises(language_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_speaking_exercises_language_id ON speaking_exercises(language_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_words_language_id ON words(language_id)")

	// Lesson exercise indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lesson_exercises_lesson_id ON lesson_exercises(lesson_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_lesson_exercises_exercise_id ON lesson_exercises(exercise_id)")

	// Tag indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name)")

	// Exercise tag indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_tags_exercise_id ON exercise_tags(exercise_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_exercise_tags_tag_id ON exercise_tags(tag_id)")

	log.Println("Database indexes created successfully")
}

// DropAllTables drops all tables in the database
// WARNING: This will delete all data
func DropAllTables(db *gorm.DB) {
	log.Println("WARNING: Dropping all tables...")

	// Drop tables in reverse order of dependencies
	db.Migrator().DropTable(
		&models.EmailLog{},
		&models.EmailTemplate{},
		&models.NotificationPreference{},
		&models.Notification{},
		&models.AssessmentProgress{},
		&models.ExerciseDifficultyMetadata{},
		&models.ExerciseRelation{},
		&models.ExerciseTag{},
		&models.Tag{},
		&models.SectionScore{},
		&models.EvaluationResult{},
		&models.EvalQuestion{},
		&models.EvalSection{},
		&models.Evaluation{},
		&models.LearningPathLesson{},
		&models.LearningPath{},
		&models.PracticeSession{},
		&models.UserWord{},
		&models.Word{},
		&models.UserAchievement{},
		&models.Achievement{},
		&models.LessonProgress{},
		&models.LessonExercise{},
		&models.Lesson{},
		&models.ListeningQuestion{},
		&models.ListeningExercise{},
		&models.SpeakingExercise{},
		&models.GrammarExercise{},
		&models.Exercise{},
		&models.UserStats{},
		&models.UserProfile{},
		&models.Social{},
		&models.UserLanguage{},
		&models.UserSettings{},
		&models.User{},
		&models.Language{},
	)

	log.Println("All tables dropped successfully")
}

// Reset drops all tables and runs migrations
func Reset(db *gorm.DB) {
	DropAllTables(db)
	Migrate(db)
	CreateIndexes(db)
}
