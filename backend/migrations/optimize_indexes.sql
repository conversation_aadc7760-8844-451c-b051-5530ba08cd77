-- 数据库索引优化脚本
-- 执行前请确保在生产环境使用 CONCURRENTLY 选项

-- =====================================================
-- 高频查询索引优化
-- =====================================================

-- 用户相关索引优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active 
ON users(email) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_recent 
ON users(last_login_at DESC) WHERE is_active = true AND last_login_at > NOW() - INTERVAL '30 days';

-- 练习相关索引优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_type_difficulty_published 
ON exercises(type, difficulty) WHERE is_published = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_lang_diff_type 
ON exercises(language_id, difficulty, type) WHERE is_published = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_author_published 
ON exercises(author_id, is_published, created_at DESC);

-- 学习路径索引优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_paths_user_status 
ON learning_paths(user_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_paths_completion 
ON learning_paths(user_id, completion_percentage, updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_paths_recent 
ON learning_paths(user_id, updated_at DESC) WHERE status = 'active' AND updated_at > NOW() - INTERVAL '7 days';

-- 课程进度索引优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_progress_user_lesson 
ON lesson_progress(user_id, lesson_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_progress_completion 
ON lesson_progress(user_id, completed, progress DESC);

-- 课程相关索引优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lessons_difficulty_lang 
ON lessons(difficulty, language_id, is_published) WHERE is_published = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lessons_author_published 
ON lessons(author_id, is_published, created_at DESC);

-- =====================================================
-- 评估和练习会话索引优化
-- =====================================================

-- 评估结果索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_evaluation_results_user_completed 
ON evaluation_results(user_id, is_passed, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_evaluations_incomplete 
ON evaluations(user_id, created_at DESC) WHERE is_completed = false;

-- 练习会话索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_user_time 
ON practice_sessions(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_high_score 
ON practice_sessions(user_id, score DESC) WHERE score >= 80;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_type_time 
ON practice_sessions(type, created_at DESC);

-- =====================================================
-- 用户数据索引优化
-- =====================================================

-- 单词学习索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_words_user_learned 
ON user_words(user_id, is_learned);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_words_user_favorite 
ON user_words(user_id, is_favorite) WHERE is_favorite = true;

-- 成就索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievements_user_unlocked 
ON user_achievements(user_id, is_unlocked, unlocked_at DESC);

-- 用户统计索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_stats_activity 
ON user_stats(user_id, last_active DESC, total_points DESC);

-- 通知索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_status_time 
ON notifications(user_id, is_read, created_at DESC);

-- =====================================================
-- 关系表索引优化
-- =====================================================

-- 练习关系索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercise_relations_type_strength 
ON exercise_relations(relation_type, strength DESC);

-- 学习路径课程关系索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_lessons_path_order 
ON learning_path_lessons(learning_path_id, order_index);

-- 课程练习关系索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_exercises_lesson_order 
ON lesson_exercises(lesson_id, order_index);

-- =====================================================
-- 全文搜索索引
-- =====================================================

-- 练习内容搜索索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_content_search 
ON exercises USING gin(to_tsvector('english', title || ' ' || description || ' ' || content));

-- 课程内容搜索索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lessons_content_search 
ON lessons USING gin(to_tsvector('english', title || ' ' || description || ' ' || content));

-- 单词搜索索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_words_search 
ON words USING gin(to_tsvector('english', word || ' ' || translation || ' ' || definition));

-- =====================================================
-- 时间序列索引优化
-- =====================================================

-- 按时间分区的索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_daily 
ON practice_sessions(date_trunc('day', created_at), user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievements_monthly 
ON user_achievements(date_trunc('month', unlocked_at)) WHERE is_unlocked = true;

-- =====================================================
-- 统计和分析索引
-- =====================================================

-- 用户活跃度分析索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_registration_cohort 
ON users(date_trunc('month', created_at), is_active);

-- 练习难度分析索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_difficulty_stats 
ON exercises(difficulty, type) WHERE is_published = true;

-- 学习进度分析索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_progress_analytics 
ON lesson_progress(lesson_id, completed, avg(progress)) WHERE completed = true;

-- =====================================================
-- 性能监控查询
-- =====================================================

-- 查看索引使用情况
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
-- FROM pg_stat_user_indexes 
-- ORDER BY idx_scan DESC;

-- 查看未使用的索引
-- SELECT schemaname, tablename, indexname, idx_scan 
-- FROM pg_stat_user_indexes 
-- WHERE idx_scan = 0 AND indexname NOT LIKE '%_pkey';

-- 查看表大小和索引大小
-- SELECT 
--     schemaname,
--     tablename,
--     pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size,
--     pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as indexes_size
-- FROM pg_tables 
-- WHERE schemaname = 'public'
-- ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- =====================================================
-- 维护命令
-- =====================================================

-- 更新表统计信息 (建议定期执行)
-- ANALYZE users;
-- ANALYZE exercises;
-- ANALYZE lessons;
-- ANALYZE learning_paths;
-- ANALYZE lesson_progress;
-- ANALYZE evaluation_results;
-- ANALYZE practice_sessions;
-- ANALYZE user_words;
-- ANALYZE user_achievements;

-- 重建索引 (如果需要)
-- REINDEX INDEX CONCURRENTLY idx_users_email_active;

-- =====================================================
-- 索引优化说明
-- =====================================================

/*
1. 高频查询索引:
   - 针对最常用的查询模式创建索引
   - 包括用户登录、练习筛选、学习进度等

2. 复合索引:
   - 多字段组合索引，优化复杂查询
   - 字段顺序按选择性排列

3. 部分索引:
   - 只对满足特定条件的行创建索引
   - 减少索引大小，提高性能

4. 全文搜索索引:
   - 使用 GIN 索引支持全文搜索
   - 提升搜索功能性能

5. 时间序列索引:
   - 优化按时间范围的查询
   - 支持分析和报表功能

预期性能提升:
- 用户查询: 60-80% 提升
- 练习筛选: 70-90% 提升  
- 学习路径: 50-70% 提升
- 全文搜索: 80-95% 提升
- 整体API: 40-60% 提升
*/
