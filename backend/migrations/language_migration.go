package migrations

import (
	"log"
	"time"

	"languagelearning/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MigrateLanguages creates the languages table and adds language_id column to exercise tables
func MigrateLanguages(db *gorm.DB) {
	// Create languages table
	if err := db.AutoMigrate(&models.Language{}); err != nil {
		log.Fatalf("Failed to migrate languages table: %v", err)
	}
	log.Println("Languages table migrated successfully")

	// Create user languages table
	if err := db.AutoMigrate(&models.UserLanguage{}); err != nil {
		log.Fatalf("Failed to migrate user languages table: %v", err)
	}
	log.Println("User languages table migrated successfully")

	// Create default English language if it doesn't exist
	var defaultLanguage models.Language
	result := db.Where("code = ?", "en").First(&defaultLanguage)
	if result.Error != nil {
		defaultLanguage = models.Language{
			ID:          uuid.New(),
			Code:        "en",
			Name:        "English",
			NativeName:  "English",
			Description: "English is a West Germanic language",
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		if err := db.Create(&defaultLanguage).Error; err != nil {
			log.Fatalf("Failed to create default language: %v", err)
		}
		log.Println("Created default English language")
	}

	// Check if grammar_exercises table exists
	var hasGrammarExercisesTable bool
	if err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'grammar_exercises')").Scan(&hasGrammarExercisesTable).Error; err != nil {
		log.Fatalf("Failed to check if grammar_exercises table exists: %v", err)
	}

	// Handle grammar exercises language_id column if table exists
	if hasGrammarExercisesTable {
		var hasLanguageID bool
		if err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='grammar_exercises' AND column_name='language_id')").Scan(&hasLanguageID).Error; err != nil {
			log.Fatalf("Failed to check if language_id column exists: %v", err)
		}

		if !hasLanguageID {
			// Column doesn't exist, add it as nullable
			if err := db.Exec("ALTER TABLE grammar_exercises ADD COLUMN language_id uuid").Error; err != nil {
				log.Fatalf("Failed to add language_id column to grammar exercises table: %v", err)
			}
			log.Println("Added language_id column to grammar exercises table")
		}
	} else {
		log.Println("Grammar exercises table does not exist yet, skipping language_id column addition")
	}

	// Get default language (reuse the one we created earlier)
	result = db.Where("code = ?", "en").First(&defaultLanguage)
	if result.Error != nil {
		log.Fatalf("Default language not found: %v", result.Error)
	}

	// Update existing rows with default language if table exists
	if hasGrammarExercisesTable {
		if err := db.Exec("UPDATE grammar_exercises SET language_id = ? WHERE language_id IS NULL", defaultLanguage.ID).Error; err != nil {
			log.Fatalf("Failed to update grammar exercises with default language: %v", err)
		}
		log.Println("Updated grammar exercises with default language")
	}

	// Check if column is nullable if table exists
	if hasGrammarExercisesTable {
		var isNullable string
		if err := db.Raw("SELECT is_nullable FROM information_schema.columns WHERE table_name='grammar_exercises' AND column_name='language_id'").Scan(&isNullable).Error; err != nil {
			log.Fatalf("Failed to check if language_id column is nullable: %v", err)
		}

		if isNullable == "YES" {
			// Make the column non-nullable
			if err := db.Exec("ALTER TABLE grammar_exercises ALTER COLUMN language_id SET NOT NULL").Error; err != nil {
				log.Fatalf("Failed to make language_id column non-nullable: %v", err)
			}
			log.Println("Made language_id column non-nullable in grammar exercises table")
		}
	}

	// Check if listening_exercises table exists
	var hasListeningExercisesTable bool
	if err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'listening_exercises')").Scan(&hasListeningExercisesTable).Error; err != nil {
		log.Fatalf("Failed to check if listening_exercises table exists: %v", err)
	}

	// Handle listening exercises language_id column if table exists
	if hasListeningExercisesTable {
		var hasListeningLanguageID bool
		if err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='listening_exercises' AND column_name='language_id')").Scan(&hasListeningLanguageID).Error; err != nil {
			log.Fatalf("Failed to check if language_id column exists in listening_exercises: %v", err)
		}

		if !hasListeningLanguageID {
			// Column doesn't exist, add it as nullable
			if err := db.Exec("ALTER TABLE listening_exercises ADD COLUMN language_id uuid").Error; err != nil {
				log.Fatalf("Failed to add language_id column to listening exercises table: %v", err)
			}
			log.Println("Added language_id column to listening exercises table")
		}
	} else {
		log.Println("Listening exercises table does not exist yet, skipping language_id column addition")
	}

	// Update existing rows with default language if table exists
	if hasListeningExercisesTable {
		if err := db.Exec("UPDATE listening_exercises SET language_id = ? WHERE language_id IS NULL", defaultLanguage.ID).Error; err != nil {
			log.Fatalf("Failed to update listening exercises with default language: %v", err)
		}
		log.Println("Updated listening exercises with default language")
	}

	// Check if column is nullable if table exists
	if hasListeningExercisesTable {
		var isListeningNullable string
		if err := db.Raw("SELECT is_nullable FROM information_schema.columns WHERE table_name='listening_exercises' AND column_name='language_id'").Scan(&isListeningNullable).Error; err != nil {
			log.Fatalf("Failed to check if language_id column is nullable in listening_exercises: %v", err)
		}

		if isListeningNullable == "YES" {
			// Make the column non-nullable
			if err := db.Exec("ALTER TABLE listening_exercises ALTER COLUMN language_id SET NOT NULL").Error; err != nil {
				log.Fatalf("Failed to make language_id column non-nullable in listening_exercises: %v", err)
			}
			log.Println("Made language_id column non-nullable in listening exercises table")
		}
	}

	// Check if speaking_exercises table exists
	var hasSpeakingExercisesTable bool
	if err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'speaking_exercises')").Scan(&hasSpeakingExercisesTable).Error; err != nil {
		log.Fatalf("Failed to check if speaking_exercises table exists: %v", err)
	}

	// Handle speaking exercises language_id column if table exists
	if hasSpeakingExercisesTable {
		var hasSpeakingLanguageID bool
		if err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='speaking_exercises' AND column_name='language_id')").Scan(&hasSpeakingLanguageID).Error; err != nil {
			log.Fatalf("Failed to check if language_id column exists in speaking_exercises: %v", err)
		}

		if !hasSpeakingLanguageID {
			// Column doesn't exist, add it as nullable
			if err := db.Exec("ALTER TABLE speaking_exercises ADD COLUMN language_id uuid").Error; err != nil {
				log.Fatalf("Failed to add language_id column to speaking exercises table: %v", err)
			}
			log.Println("Added language_id column to speaking exercises table")
		}
	} else {
		log.Println("Speaking exercises table does not exist yet, skipping language_id column addition")
	}

	// Update existing rows with default language if table exists
	if hasSpeakingExercisesTable {
		if err := db.Exec("UPDATE speaking_exercises SET language_id = ? WHERE language_id IS NULL", defaultLanguage.ID).Error; err != nil {
			log.Fatalf("Failed to update speaking exercises with default language: %v", err)
		}
		log.Println("Updated speaking exercises with default language")
	}

	// Check if column is nullable if table exists
	if hasSpeakingExercisesTable {
		var isSpeakingNullable string
		if err := db.Raw("SELECT is_nullable FROM information_schema.columns WHERE table_name='speaking_exercises' AND column_name='language_id'").Scan(&isSpeakingNullable).Error; err != nil {
			log.Fatalf("Failed to check if language_id column is nullable in speaking_exercises: %v", err)
		}

		if isSpeakingNullable == "YES" {
			// Make the column non-nullable
			if err := db.Exec("ALTER TABLE speaking_exercises ALTER COLUMN language_id SET NOT NULL").Error; err != nil {
				log.Fatalf("Failed to make language_id column non-nullable in speaking_exercises: %v", err)
			}
			log.Println("Made language_id column non-nullable in speaking exercises table")
		}
	}

	// Check if words table exists
	var hasWordsTable bool
	if err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'words')").Scan(&hasWordsTable).Error; err != nil {
		log.Fatalf("Failed to check if words table exists: %v", err)
	}

	// Handle words language_id column if table exists
	if hasWordsTable {
		var hasWordsLanguageID bool
		if err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='words' AND column_name='language_id')").Scan(&hasWordsLanguageID).Error; err != nil {
			log.Fatalf("Failed to check if language_id column exists in words: %v", err)
		}

		if !hasWordsLanguageID {
			// Column doesn't exist, add it as nullable
			if err := db.Exec("ALTER TABLE words ADD COLUMN language_id uuid").Error; err != nil {
				log.Fatalf("Failed to add language_id column to words table: %v", err)
			}
			log.Println("Added language_id column to words table")
		}
	} else {
		log.Println("Words table does not exist yet, skipping language_id column addition")
	}

	// Update existing rows with default language if table exists
	if hasWordsTable {
		if err := db.Exec("UPDATE words SET language_id = ? WHERE language_id IS NULL", defaultLanguage.ID).Error; err != nil {
			log.Fatalf("Failed to update words with default language: %v", err)
		}
		log.Println("Updated words with default language")
	}

	// Check if column is nullable if table exists
	if hasWordsTable {
		var isWordsNullable string
		if err := db.Raw("SELECT is_nullable FROM information_schema.columns WHERE table_name='words' AND column_name='language_id'").Scan(&isWordsNullable).Error; err != nil {
			log.Fatalf("Failed to check if language_id column is nullable in words: %v", err)
		}

		if isWordsNullable == "YES" {
			// Make the column non-nullable
			if err := db.Exec("ALTER TABLE words ALTER COLUMN language_id SET NOT NULL").Error; err != nil {
				log.Fatalf("Failed to make language_id column non-nullable in words: %v", err)
			}
			log.Println("Made language_id column non-nullable in words table")
		}
	}

	// Add foreign key constraints if tables exist
	if hasGrammarExercisesTable {
		if err := db.Exec("ALTER TABLE grammar_exercises ADD CONSTRAINT fk_grammar_exercises_language FOREIGN KEY (language_id) REFERENCES languages(id)").Error; err != nil {
			log.Printf("Warning: Failed to add foreign key constraint to grammar exercises table: %v", err)
		}
	}

	if hasListeningExercisesTable {
		if err := db.Exec("ALTER TABLE listening_exercises ADD CONSTRAINT fk_listening_exercises_language FOREIGN KEY (language_id) REFERENCES languages(id)").Error; err != nil {
			log.Printf("Warning: Failed to add foreign key constraint to listening exercises table: %v", err)
		}
	}

	if hasSpeakingExercisesTable {
		if err := db.Exec("ALTER TABLE speaking_exercises ADD CONSTRAINT fk_speaking_exercises_language FOREIGN KEY (language_id) REFERENCES languages(id)").Error; err != nil {
			log.Printf("Warning: Failed to add foreign key constraint to speaking exercises table: %v", err)
		}
	}

	if hasWordsTable {
		if err := db.Exec("ALTER TABLE words ADD CONSTRAINT fk_words_language FOREIGN KEY (language_id) REFERENCES languages(id)").Error; err != nil {
			log.Printf("Warning: Failed to add foreign key constraint to words table: %v", err)
		}
	}

	log.Println("Language migrations completed successfully")
}
