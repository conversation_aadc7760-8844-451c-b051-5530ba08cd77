-- 基于实际数据库结构的优化索引脚本
-- 根据执行结果调整，只包含存在的字段和表

-- =====================================================
-- 成功创建的索引 (已验证)
-- =====================================================

-- 用户相关索引 (已成功)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active ON users(email) WHERE is_active = true;

-- 学习路径索引 (已成功)  
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_paths_user_status ON learning_paths(user_id, status);

-- 评估索引 (已成功)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_evaluations_incomplete ON evaluations(user_id, created_at DESC) WHERE is_completed = false;

-- 练习会话索引 (已成功)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_user_time ON practice_sessions(user_id, created_at DESC);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_high_score ON practice_sessions(user_id, score DESC) WHERE score >= 80;
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_type_time ON practice_sessions(type, created_at DESC);

-- 用户单词索引 (已成功)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_words_user_learned ON user_words(user_id, is_learned);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_words_user_favorite ON user_words(user_id, is_favorite) WHERE is_favorite = true;

-- 练习关系索引 (已成功)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercise_relations_type_strength ON exercise_relations(relation_type, strength DESC);

-- 全文搜索索引 (已成功)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lessons_content_search ON lessons USING gin(to_tsvector('english', title || ' ' || description || ' ' || content));
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_words_search ON words USING gin(to_tsvector('english', word || ' ' || translation || ' ' || definition));

-- =====================================================
-- 基于实际表结构的新索引
-- =====================================================

-- 基于实际exercises表结构的索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_type_difficulty 
ON exercises(type, difficulty);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_language_difficulty 
ON exercises(language_id, difficulty) WHERE language_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_category_difficulty 
ON exercises(category, difficulty) WHERE category IS NOT NULL;

-- 基于实际lessons表结构的索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lessons_difficulty_language 
ON lessons(difficulty, language_id) WHERE language_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lessons_status_created 
ON lessons(status, created_at DESC);

-- 基于lesson_progresses表的索引 (注意表名是复数)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_progresses_user_lesson 
ON lesson_progresses(user_id, lesson_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_progresses_completion 
ON lesson_progresses(user_id, completed, progress DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_progresses_user_completed 
ON lesson_progresses(user_id, completed_date DESC) WHERE completed = true;

-- 评估结果索引 (基于实际字段)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_evaluation_results_user_passed 
ON evaluation_results(user_id, is_passed);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_evaluation_results_evaluation_user 
ON evaluation_results(evaluation_id, user_id);

-- 用户成就索引 (基于实际字段)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievements_user_unlocked 
ON user_achievements(user_id, is_unlocked);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievements_achievement_unlocked 
ON user_achievements(achievement_id, is_unlocked) WHERE is_unlocked = true;

-- 通知索引 (基于实际字段)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_created 
ON notifications(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_type_created 
ON notifications(type, created_at DESC);

-- 学习路径课程关系索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_lessons_path_lesson 
ON learning_path_lessons(learning_path_id, lesson_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_lessons_lesson_path 
ON learning_path_lessons(lesson_id, learning_path_id);

-- 课程练习关系索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_exercises_lesson_exercise 
ON lesson_exercises(lesson_id, exercise_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_exercises_exercise_lesson 
ON lesson_exercises(exercise_id, lesson_id);

-- =====================================================
-- 大表优化索引 (基于表大小分析)
-- =====================================================

-- grammar_exercises表优化 (2.6GB)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_grammar_exercises_difficulty_category_lang 
ON grammar_exercises(difficulty, category, language_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_grammar_exercises_created_difficulty 
ON grammar_exercises(created_at DESC, difficulty);

-- lesson_exercises表优化 (1.9GB)  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_exercises_lesson_created 
ON lesson_exercises(lesson_id, created_at DESC);

-- exercise_tags表优化 (909MB)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercise_tags_tag_exercise 
ON exercise_tags(tag_id, exercise_id);

-- practice_sessions表优化 (24MB)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_user_score_time 
ON practice_sessions(user_id, score DESC, created_at DESC);

-- =====================================================
-- 性能监控索引
-- =====================================================

-- 用户活跃度监控
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_login_active 
ON users(last_login_at DESC) WHERE is_active = true;

-- 练习完成率监控
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_completion_rate 
ON practice_sessions(type, score) WHERE score IS NOT NULL;

-- 学习路径进度监控
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_paths_progress_monitoring 
ON learning_paths(status, updated_at DESC);

-- =====================================================
-- 搜索优化索引
-- =====================================================

-- 练习搜索 (基于实际字段)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_question_search 
ON exercises USING gin(to_tsvector('english', question || ' ' || COALESCE(instruction, '') || ' ' || COALESCE(explanation, '')));

-- 语法练习搜索
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_grammar_exercises_content_search 
ON grammar_exercises USING gin(to_tsvector('english', question || ' ' || COALESCE(explanation, '')));

-- =====================================================
-- 分析和报表索引
-- =====================================================

-- 用户学习统计
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_practice_sessions_user_daily_stats 
ON practice_sessions(user_id, DATE(created_at), type);

-- 练习难度分布
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exercises_difficulty_type_stats 
ON exercises(difficulty, type);

-- 学习路径完成统计
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_progresses_completion_stats 
ON lesson_progresses(lesson_id, completed) WHERE completed = true;

-- =====================================================
-- 外键关系优化索引
-- =====================================================

-- 确保所有外键都有索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_words_word_id 
ON user_words(word_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievements_achievement_id 
ON user_achievements(achievement_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_evaluation_results_evaluation_id 
ON evaluation_results(evaluation_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_eval_sections_evaluation_id 
ON eval_sections(evaluation_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_eval_questions_section_id 
ON eval_questions(section_id);

-- =====================================================
-- 维护和监控查询
-- =====================================================

-- 查看索引使用情况
/*
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
*/

-- 查看表和索引大小
/*
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size('public.'||tablename)) as table_size,
    pg_size_pretty(pg_indexes_size('public.'||tablename)) as indexes_size,
    ROUND(100.0 * pg_indexes_size('public.'||tablename) / pg_total_relation_size('public.'||tablename), 1) as index_ratio
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size('public.'||tablename) DESC;
*/

-- 查看慢查询
/*
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
*/

-- =====================================================
-- 索引优化总结
-- =====================================================

/*
优化重点:
1. 大表索引优化 (grammar_exercises, lesson_exercises, exercise_tags)
2. 高频查询路径优化 (用户查询, 练习筛选, 学习进度)
3. 外键关系索引确保
4. 全文搜索性能提升
5. 分析和报表查询优化

预期性能提升:
- 用户相关查询: 60-80% 提升
- 练习筛选查询: 70-90% 提升  
- 学习进度查询: 50-70% 提升
- 搜索功能: 80-95% 提升
- 大表查询: 40-60% 提升

监控建议:
1. 定期检查 pg_stat_user_indexes 索引使用情况
2. 监控 pg_stat_statements 慢查询
3. 每周执行 ANALYZE 更新统计信息
4. 监控索引膨胀和碎片化
*/
