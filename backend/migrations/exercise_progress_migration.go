package migrations

import (
	"log"

	"gorm.io/gorm"
)

// MigrateExerciseProgress creates the exercise progress tables if they don't exist
func MigrateExerciseProgress(db *gorm.DB) {
	// Check if grammar_exercise_progress table exists
	var hasGrammarProgressTable bool
	db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'grammar_exercise_progress')").Scan(&hasGrammarProgressTable)

	if !hasGrammarProgressTable {
		log.Println("Creating grammar_exercise_progress table...")
		db.Exec(`
			CREATE TABLE grammar_exercise_progress (
				id SERIAL PRIMARY KEY,
				user_id UUID NOT NULL,
				exercise_id UUID NOT NULL,
				is_completed BOOLEAN DEFAULT false,
				score INT DEFAULT 0,
				completed_at TIMESTAMP,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
			)
		`)
		db.Exec("CREATE INDEX idx_grammar_exercise_progress_user_id ON grammar_exercise_progress(user_id)")
		db.Exec("CREATE INDEX idx_grammar_exercise_progress_exercise_id ON grammar_exercise_progress(exercise_id)")
		db.Exec("CREATE INDEX idx_grammar_exercise_progress_is_completed ON grammar_exercise_progress(is_completed)")
	}

	// Check if listening_exercise_progress table exists
	var hasListeningProgressTable bool
	db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'listening_exercise_progress')").Scan(&hasListeningProgressTable)

	if !hasListeningProgressTable {
		log.Println("Creating listening_exercise_progress table...")
		db.Exec(`
			CREATE TABLE listening_exercise_progress (
				id SERIAL PRIMARY KEY,
				user_id UUID NOT NULL,
				exercise_id UUID NOT NULL,
				is_completed BOOLEAN DEFAULT false,
				score INT DEFAULT 0,
				completed_at TIMESTAMP,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
			)
		`)
		db.Exec("CREATE INDEX idx_listening_exercise_progress_user_id ON listening_exercise_progress(user_id)")
		db.Exec("CREATE INDEX idx_listening_exercise_progress_exercise_id ON listening_exercise_progress(exercise_id)")
		db.Exec("CREATE INDEX idx_listening_exercise_progress_is_completed ON listening_exercise_progress(is_completed)")
	}

	// Check if speaking_exercise_progress table exists
	var hasSpeakingProgressTable bool
	db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'speaking_exercise_progress')").Scan(&hasSpeakingProgressTable)

	if !hasSpeakingProgressTable {
		log.Println("Creating speaking_exercise_progress table...")
		db.Exec(`
			CREATE TABLE speaking_exercise_progress (
				id SERIAL PRIMARY KEY,
				user_id UUID NOT NULL,
				exercise_id UUID NOT NULL,
				is_completed BOOLEAN DEFAULT false,
				score INT DEFAULT 0,
				completed_at TIMESTAMP,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
			)
		`)
		db.Exec("CREATE INDEX idx_speaking_exercise_progress_user_id ON speaking_exercise_progress(user_id)")
		db.Exec("CREATE INDEX idx_speaking_exercise_progress_exercise_id ON speaking_exercise_progress(exercise_id)")
		db.Exec("CREATE INDEX idx_speaking_exercise_progress_is_completed ON speaking_exercise_progress(is_completed)")
	}

	log.Println("Exercise progress tables migration completed")
}
