version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: languagelearning-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: languagelearning
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Go API Service (Simple version without Air)
  api:
    image: golang:1.21-alpine
    container_name: languagelearning-api
    working_dir: /app
    environment:
      - PORT=8080
      - GIN_MODE=debug
      - JWT_SECRET=your-secret-key-change-this-in-production
      - JWT_EXPIRATION_HOURS=24
      - DATABASE_URL=******************************************/languagelearning?sslmode=disable
      - API_BASE_URL=http://localhost:8080
    ports:
      - "8080:8080"
    volumes:
      - .:/app
    depends_on:
      postgres:
        condition: service_healthy
    command: sh -c "chmod +x ./fix-deps.sh && ./fix-deps.sh && go run main.go -migrate && go run main.go"
    restart: unless-stopped

  # PgAdmin (optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: languagelearning-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
