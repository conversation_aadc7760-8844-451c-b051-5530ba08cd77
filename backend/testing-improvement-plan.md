# 測試改進計劃 - 語言學習系統

## 🎯 目標概述

將測試覆蓋率從當前的30%提升到80%+，建立完整的測試金字塔，確保代碼質量和重構安全性。

## 📊 當前測試狀況分析

### 現有測試類型
- ✅ **E2E測試**: `tests/api_test.sh`, `tests/adaptive_learning_test.sh`
- ✅ **煙霧測試**: `tests/smoke_test.go`
- ⚠️ **單元測試**: 僅有少量示例 (`domain/learning/service/impl/test/`)
- ❌ **集成測試**: 缺失
- ❌ **性能測試**: 缺失

### 測試覆蓋率分析
```
當前覆蓋率: ~30%
├── Controllers: ~20%
├── Services: ~40%
├── Repositories: ~10%
└── Domain Logic: ~50%
```

## 🏗️ 測試金字塔設計

```
        /\
       /  \
      / UI \     E2E Tests (10%)
     /______\    - API flow tests
    /        \   - User journey tests
   / Contract \  Integration Tests (20%)
  /____________\ - Repository tests
 /              \ - Service integration
/   Unit Tests   \ Unit Tests (70%)
\________________/ - Domain logic
                   - Business rules
```

## 📋 Week 1: 測試基礎設施建設

### Day 1-2: 環境搭建

#### 1. 安裝測試依賴
```bash
go get github.com/stretchr/testify/assert
go get github.com/stretchr/testify/mock
go get github.com/stretchr/testify/suite
go get github.com/golang/mock/gomock
go get github.com/DATA-DOG/go-sqlmock
```

#### 2. 創建測試目錄結構
```
tests/
├── unit/                 # 單元測試
│   ├── domain/
│   ├── services/
│   └── repositories/
├── integration/          # 集成測試
│   ├── api/
│   ├── database/
│   └── services/
├── e2e/                 # 端到端測試
├── fixtures/            # 測試數據
├── mocks/              # Mock對象
└── utils/              # 測試工具
```

#### 3. 測試配置文件
創建 `tests/config/test_config.go`:
```go
package config

import (
    "languagelearning/config"
)

func GetTestConfig() *config.Config {
    return &config.Config{
        Database: config.DatabaseConfig{
            URL: "postgres://test:test@localhost:5433/test_db",
        },
        JWT: config.JWTConfig{
            Secret: "test-secret",
            ExpirationHours: 1,
        },
    }
}
```

### Day 3-5: 核心業務邏輯測試

#### 1. User Service 單元測試
創建 `tests/unit/services/user_service_test.go`:

**測試覆蓋範圍**:
- ✅ 用戶創建
- ✅ 用戶認證
- ✅ 密碼驗證
- ✅ 用戶更新
- ✅ 錯誤處理

**測試用例示例**:
```go
func TestUserService_CreateUser(t *testing.T) {
    tests := []struct {
        name    string
        input   *entity.User
        setup   func(*MockUserRepository)
        want    *entity.User
        wantErr bool
    }{
        {
            name: "successful_creation",
            input: &entity.User{
                Username: "testuser",
                Email:    "<EMAIL>",
                Password: "password123",
            },
            setup: func(repo *MockUserRepository) {
                repo.On("Create", mock.Anything, mock.Anything).
                    Return(entity.User{ID: uuid.New()}, nil)
            },
            wantErr: false,
        },
        // 更多測試用例...
    }
}
```

#### 2. Learning Service 單元測試
**測試覆蓋範圍**:
- ✅ 學習路徑創建
- ✅ 練習生成
- ✅ 進度追蹤
- ✅ 自適應學習邏輯

#### 3. Auth Service 單元測試
**測試覆蓋範圍**:
- ✅ 登錄驗證
- ✅ JWT生成和驗證
- ✅ 權限檢查
- ✅ 令牌刷新

### Day 6-7: Mock對象和測試工具

#### 1. 生成Mock對象
```bash
# 為Repository接口生成Mock
mockgen -source=domain/user/repository/user_repository.go \
        -destination=tests/mocks/mock_user_repository.go

# 為Service接口生成Mock
mockgen -source=domain/user/service/user_service.go \
        -destination=tests/mocks/mock_user_service.go
```

#### 2. 測試工具函數
創建 `tests/utils/test_helpers.go`:
```go
package utils

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "languagelearning/domain/user/entity"
)

// CreateTestUser 創建測試用戶
func CreateTestUser(t *testing.T) *entity.User {
    return &entity.User{
        ID:       uuid.New(),
        Username: "testuser",
        Email:    "<EMAIL>",
        IsActive: true,
    }
}

// AssertUserEqual 比較用戶對象
func AssertUserEqual(t *testing.T, expected, actual *entity.User) {
    assert.Equal(t, expected.Username, actual.Username)
    assert.Equal(t, expected.Email, actual.Email)
    assert.Equal(t, expected.IsActive, actual.IsActive)
}
```

## 📋 Week 2: Repository和集成測試

### Day 8-10: Repository層測試

#### 1. 數據庫測試設置
創建 `tests/integration/database/setup.go`:
```go
package database

import (
    "testing"
    "gorm.io/gorm"
    "languagelearning/models"
)

func SetupTestDB(t *testing.T) *gorm.DB {
    // 使用內存數據庫或測試數據庫
    db := setupInMemoryDB()
    
    // 運行遷移
    err := db.AutoMigrate(&models.User{}, &models.Exercise{})
    require.NoError(t, err)
    
    return db
}

func CleanupTestDB(t *testing.T, db *gorm.DB) {
    // 清理測試數據
}
```

#### 2. Repository集成測試
創建 `tests/integration/repositories/user_repository_test.go`:

**測試覆蓋範圍**:
- ✅ CRUD操作
- ✅ 查詢條件
- ✅ 事務處理
- ✅ 錯誤處理

### Day 11-12: Service集成測試

#### 1. Service層集成測試
測試Service與Repository的集成:
```go
func TestUserService_Integration(t *testing.T) {
    // 使用真實的Repository實現
    db := SetupTestDB(t)
    defer CleanupTestDB(t, db)
    
    repo := impl.NewUserRepository(db)
    service := impl.NewUserService(repo, mockEventBus)
    
    // 測試完整的業務流程
}
```

### Day 13-14: API集成測試

#### 1. Controller集成測試
創建 `tests/integration/api/user_controller_test.go`:

**測試覆蓋範圍**:
- ✅ HTTP請求處理
- ✅ 認證和授權
- ✅ 請求驗證
- ✅ 響應格式

```go
func TestUserController_CreateUser(t *testing.T) {
    router := setupTestRouter()
    
    tests := []struct {
        name           string
        requestBody    interface{}
        expectedStatus int
        expectedBody   string
    }{
        {
            name: "valid_request",
            requestBody: map[string]string{
                "username": "testuser",
                "email":    "<EMAIL>",
                "password": "password123",
            },
            expectedStatus: 201,
        },
        // 更多測試用例...
    }
}
```

## 📋 Week 3: 高級測試和CI集成

### Day 15-17: 性能和負載測試

#### 1. 基準測試
創建 `tests/performance/benchmark_test.go`:
```go
func BenchmarkUserService_CreateUser(b *testing.B) {
    service := setupBenchmarkService()
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        user := createTestUser()
        _, err := service.CreateUser(context.Background(), user)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

#### 2. 負載測試
使用工具如 `hey` 或 `wrk` 進行API負載測試:
```bash
# API負載測試腳本
hey -n 1000 -c 10 -m POST \
    -H "Content-Type: application/json" \
    -d '{"username":"test","email":"<EMAIL>","password":"pass"}' \
    http://localhost:8080/api/v1/auth/register
```

### Day 18-19: 測試數據管理

#### 1. 測試數據工廠
創建 `tests/fixtures/user_factory.go`:
```go
package fixtures

import (
    "languagelearning/domain/user/entity"
    "github.com/google/uuid"
)

type UserFactory struct{}

func (f *UserFactory) CreateUser(options ...UserOption) *entity.User {
    user := &entity.User{
        ID:       uuid.New(),
        Username: "defaultuser",
        Email:    "<EMAIL>",
        IsActive: true,
    }
    
    for _, option := range options {
        option(user)
    }
    
    return user
}

type UserOption func(*entity.User)

func WithUsername(username string) UserOption {
    return func(u *entity.User) {
        u.Username = username
    }
}
```

### Day 20-21: CI/CD集成

#### 1. GitHub Actions配置
創建 `.github/workflows/test.yml`:
```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Go
      uses: actions/setup-go@v2
      with:
        go-version: 1.21
    
    - name: Run tests
      run: |
        go test -v -race -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
      with:
        file: ./coverage.out
```

## 🎯 測試質量指標

### 覆蓋率目標
- **總體覆蓋率**: 80%+
- **業務邏輯**: 90%+
- **API端點**: 85%+
- **Repository**: 75%+

### 測試質量指標
- **測試執行時間**: < 2分鐘
- **測試穩定性**: 99%+ 通過率
- **Mock覆蓋率**: 100% 外部依賴

### 持續改進
- 每週測試覆蓋率報告
- 新功能必須包含測試
- 代碼審查包含測試質量檢查
- 定期重構和優化測試代碼

這個測試改進計劃將顯著提升代碼質量和開發信心，為後續的架構改進提供堅實基礎。
