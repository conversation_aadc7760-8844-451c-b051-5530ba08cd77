package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"languagelearning/domain/core/event"

	"github.com/google/uuid"
)

// ExampleEventHandler 示例事件处理器
type ExampleEventHandler struct {
	name string
}

func NewExampleEventHandler(name string) *ExampleEventHandler {
	return &ExampleEventHandler{name: name}
}

func (h *ExampleEventHandler) Handle(e event.Event) error {
	log.Printf("[%s] Handling event: %s, AggregateID: %s, Data: %v",
		h.name, e.GetEventType(), e.GetAggregateID(), e.GetData())
	return nil
}

func main() {
	ctx := context.Background()

	// 示例 1: 使用内存事件总线
	fmt.Println("=== 示例 1: 内存事件总线 ===")
	memoryBusExample(ctx)

	// 示例 2: 使用 Redis 事件总线
	fmt.Println("\n=== 示例 2: Redis 事件总线 ===")
	redisBusExample(ctx)

	// 示例 3: 使用 RabbitMQ 事件总线
	fmt.Println("\n=== 示例 3: RabbitMQ 事件总线 ===")
	rabbitMQBusExample(ctx)

	// 示例 4: 使用 Kafka 事件总线
	fmt.Println("\n=== 示例 4: Kafka 事件总线 ===")
	kafkaBusExample(ctx)

	// 示例 5: 使用工厂模式
	fmt.Println("\n=== 示例 5: 工厂模式 ===")
	factoryExample(ctx)
}

// memoryBusExample 内存事件总线示例
func memoryBusExample(ctx context.Context) {
	// 创建内存事件总线
	bus := event.NewDefaultEventBus()

	// 订阅事件
	handler := NewExampleEventHandler("Memory Handler")
	bus.Subscribe("user.registered", handler)

	// 发布事件
	userEvent := event.NewBaseEvent(
		"user.registered",
		uuid.New(),
		"User",
		map[string]interface{}{
			"username": "john_doe",
			"email":    "<EMAIL>",
		},
	)

	if err := bus.Publish(ctx, userEvent); err != nil {
		log.Printf("Failed to publish event: %v", err)
	}

	// 等待事件处理
	time.Sleep(time.Millisecond * 100)
}

// redisBusExample Redis 事件总线示例
func redisBusExample(ctx context.Context) {
	// 创建 Redis 配置
	config := event.RedisEventBusConfig("localhost:6379", "", 0)

	// 创建工厂
	factory := event.NewEventBusFactory(config)

	// 创建事件总线
	bus, err := factory.CreateEventBus(ctx)
	if err != nil {
		log.Printf("Failed to create Redis event bus: %v", err)
		return
	}
	defer func() {
		if closer, ok := bus.(interface{ Close() error }); ok {
			closer.Close()
		}
	}()

	// 订阅事件
	handler := NewExampleEventHandler("Redis Handler")
	bus.Subscribe("lesson.completed", handler)

	// 发布事件
	lessonEvent := event.NewBaseEvent(
		"lesson.completed",
		uuid.New(),
		"Lesson",
		map[string]interface{}{
			"lesson_id": uuid.New().String(),
			"user_id":   uuid.New().String(),
			"score":     85,
		},
	)

	if err := bus.Publish(ctx, lessonEvent); err != nil {
		log.Printf("Failed to publish event: %v", err)
	}

	// 等待事件处理
	time.Sleep(time.Second * 2)
}

// rabbitMQBusExample RabbitMQ 事件总线示例
func rabbitMQBusExample(ctx context.Context) {
	// 创建 RabbitMQ 配置
	config := event.RabbitMQEventBusConfig("amqp://guest:guest@localhost:5672/")

	// 创建工厂
	factory := event.NewEventBusFactory(config)

	// 创建事件总线
	bus, err := factory.CreateEventBus(ctx)
	if err != nil {
		log.Printf("Failed to create RabbitMQ event bus: %v", err)
		return
	}
	defer func() {
		if closer, ok := bus.(interface{ Close() error }); ok {
			closer.Close()
		}
	}()

	// 订阅事件
	handler := NewExampleEventHandler("RabbitMQ Handler")
	bus.Subscribe("exercise.attempted", handler)

	// 发布事件
	exerciseEvent := event.NewBaseEvent(
		"exercise.attempted",
		uuid.New(),
		"Exercise",
		map[string]interface{}{
			"exercise_id": uuid.New().String(),
			"user_id":     uuid.New().String(),
			"is_correct":  true,
			"duration":    30,
		},
	)

	if err := bus.Publish(ctx, exerciseEvent); err != nil {
		log.Printf("Failed to publish event: %v", err)
	}

	// 等待事件处理
	time.Sleep(time.Second * 2)
}

// kafkaBusExample Kafka 事件总线示例
func kafkaBusExample(ctx context.Context) {
	// 创建 Kafka 配置
	config := event.KafkaEventBusConfig([]string{"localhost:9092"})

	// 创建工厂
	factory := event.NewEventBusFactory(config)

	// 创建事件总线
	bus, err := factory.CreateEventBus(ctx)
	if err != nil {
		log.Printf("Failed to create Kafka event bus: %v", err)
		return
	}
	defer func() {
		if closer, ok := bus.(interface{ Close() error }); ok {
			closer.Close()
		}
	}()

	// 订阅事件
	handler := NewExampleEventHandler("Kafka Handler")
	bus.Subscribe("evaluation.completed", handler)

	// 发布事件
	evaluationEvent := event.NewBaseEvent(
		"evaluation.completed",
		uuid.New(),
		"Evaluation",
		map[string]interface{}{
			"evaluation_id": uuid.New().String(),
			"user_id":       uuid.New().String(),
			"overall_score": 78,
			"level":         "intermediate",
		},
	)

	if err := bus.Publish(ctx, evaluationEvent); err != nil {
		log.Printf("Failed to publish event: %v", err)
	}

	// 等待事件处理
	time.Sleep(time.Second * 2)
}

// factoryExample 工厂模式示例
func factoryExample(ctx context.Context) {
	// 创建不同类型的事件总线配置
	configs := []*event.EventBusConfig{
		event.DefaultEventBusConfig(),
		event.RedisEventBusConfig("localhost:6379", "", 0),
		event.RabbitMQEventBusConfig("amqp://guest:guest@localhost:5672/"),
		event.KafkaEventBusConfig([]string{"localhost:9092"}),
	}

	for i, config := range configs {
		fmt.Printf("Testing config %d: %s\n", i+1, config.Type)

		// 创建工厂
		factory := event.NewEventBusFactory(config)

		// 创建事件总线
		bus, err := factory.CreateEventBus(ctx)
		if err != nil {
			log.Printf("Failed to create event bus for config %d: %v", i+1, err)
			continue
		}

		// 健康检查
		if healthChecker, ok := bus.(interface{ Health(context.Context) error }); ok {
			if err := healthChecker.Health(ctx); err != nil {
				log.Printf("Health check failed for config %d: %v", i+1, err)
			} else {
				log.Printf("Health check passed for config %d", i+1)
			}
		}

		// 获取统计信息
		if statsProvider, ok := bus.(interface {
			GetStats(context.Context) (map[string]interface{}, error)
		}); ok {
			if stats, err := statsProvider.GetStats(ctx); err == nil {
				log.Printf("Stats for config %d: %+v", i+1, stats)
			}
		}

		// 关闭事件总线
		if closer, ok := bus.(interface{ Close() error }); ok {
			closer.Close()
		}
	}
}

// 性能测试示例
func performanceTest(ctx context.Context, bus event.EventBus, eventCount int) {
	start := time.Now()

	// 订阅事件
	handler := NewExampleEventHandler("Performance Handler")
	bus.Subscribe("performance.test", handler)

	// 发布大量事件
	for i := 0; i < eventCount; i++ {
		testEvent := event.NewBaseEvent(
			"performance.test",
			uuid.New(),
			"Test",
			map[string]interface{}{
				"index": i,
				"data":  fmt.Sprintf("test_data_%d", i),
			},
		)

		if err := bus.Publish(ctx, testEvent); err != nil {
			log.Printf("Failed to publish event %d: %v", i, err)
		}
	}

	duration := time.Since(start)
	log.Printf("Published %d events in %v (%.2f events/sec)",
		eventCount, duration, float64(eventCount)/duration.Seconds())
}
