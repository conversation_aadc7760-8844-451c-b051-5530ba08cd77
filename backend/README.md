# Language Learning App Backend

This is the backend for the Language Learning App, built with Go and Gin framework.

## Features

- RESTful API for the Language Learning App
- JWT authentication
- User management
- Exercise management (grammar, listening, speaking, vocabulary)
- Lesson management
- Achievement system
- Progress tracking
- Statistics and analytics

## Prerequisites

- Go 1.21 or higher
- PostgreSQL

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   go mod download
   ```
3. Create a PostgreSQL database:
   ```
   createdb languagelearning
   ```
4. Configure the environment variables in `.env` file
5. Run database migrations:
   ```
   go run cmd/api/main.go -migrate
   ```
6. Seed the database with sample data (optional):
   ```
   go run cmd/api/main.go -seed
   ```
7. Run the application:
   ```
   go run cmd/api/main.go
   ```

## Database Management

The application provides separate commands for database management:

- **Run migrations**: `go run cmd/migrate/main.go` or `make migrate`
- **Seed database**: `go run cmd/seed/main.go` or `make seed`
- **Reset database** (drop all tables and run migrations): `go run cmd/reset/main.go` or `make reset`

**Warning**: The reset command will delete all data in the database.

## Event Worker

The application includes an event worker for processing domain events:

- **Run worker**: `go run cmd/worker/main.go` or `make worker`

The worker processes events from the EventBus system and handles:
- Learning progress updates
- Lesson completions
- Exercise attempts
- Achievement unlocks
- User notifications
- Email queue processing

See `cmd/worker/README.md` for detailed configuration options.

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Server configuration
PORT=8080
GIN_MODE=debug

# JWT configuration
JWT_SECRET=your-secret-key-change-this-in-production
JWT_EXPIRATION_HOURS=24

# Database configuration
DATABASE_URL=postgres://postgres:postgres@localhost:5432/languagelearning?sslmode=disable

# API configuration
API_BASE_URL=https://api.languagelearningapp.com
```

## API Endpoints

### Health Checks

- `GET /health` - Get service health status
- `GET /readiness` - Check if service is ready to accept traffic
- `GET /liveness` - Check if service is alive

### Authentication

- `POST /api/v1/auth/login` - Login
- `POST /api/v1/auth/register` - Register
- `POST /api/v1/auth/reset-password` - Reset password
- `DELETE /api/v1/auth/logout` - Logout
- `PUT /api/v1/auth/change-password` - Change password

### User

- `GET /api/v1/user/profile` - Get user profile
- `PUT /api/v1/user/profile` - Update user profile
- `GET /api/v1/user/settings` - Get user settings
- `PUT /api/v1/user/settings` - Update user settings
- `GET /api/v1/user/stats` - Get user statistics
- `GET /api/v1/user/streak` - Get learning streak
- `GET /api/v1/user/progress/:period` - Get progress report
- `GET /api/v1/user/words` - Get user word list
- `GET /api/v1/user/achievements` - Get user achievements

### Exercises

- `GET /api/v1/grammar/exercises` - Get grammar exercises
- `POST /api/v1/grammar/exercises/:id/submit` - Submit grammar answer
- `GET /api/v1/speaking/exercises` - Get speaking exercises
- `POST /api/v1/speaking/exercises/:id/submit` - Submit speaking answer
- `GET /api/v1/listening/exercises` - Get listening exercises
- `POST /api/v1/listening/exercises/:id/submit` - Submit listening answer
- `GET /api/v1/word/exercises` - Get word exercises
- `POST /api/v1/word/exercises/:id/submit` - Submit word answer

### Lessons

- `GET /api/v1/lessons` - Get lessons
- `GET /api/v1/lessons/:id` - Get lesson detail
- `GET /api/v1/lessons/:id/progress` - Get lesson progress
- `PUT /api/v1/lessons/:id/progress` - Update lesson progress
- `GET /api/v1/lessons/favorites` - Get favorite lessons
- `PATCH /api/v1/lessons/:id/favorite` - Toggle favorite lesson

### Achievements

- `GET /api/v1/achievements` - Get achievements
- `PATCH /api/v1/achievements/:id/claim` - Claim achievement reward

### Words

- `GET /api/v1/words` - Get words
- `GET /api/v1/words/:id` - Get word detail
- `PUT /api/v1/words/:id/learned` - Mark word as learned

### Practice

- `GET /api/v1/practice/history` - Get practice history
- `GET /api/v1/practice/recommended` - Get recommended practice
- `POST /api/v1/practice/session` - Save practice session

## API Documentation

The API is documented using Swagger/OpenAPI. You can access the Swagger UI at:

```
http://localhost:8080/swagger/index.html
```

### Generating API Documentation

To generate or update the API documentation:

```bash
# Using make
make swagger

# Or using the script
./scripts/generate_openapi.sh
```

This will generate the Swagger documentation and create an `openapi.json` file in the root directory.

### Using the Swagger UI

The Swagger UI provides an interactive interface to:
- Browse all available API endpoints
- See request and response schemas
- Test API endpoints directly from the browser
- Understand authentication requirements

## Project Structure

```
backend/
├── config/                # Configuration
├── docs/                  # API documentation (Swagger)
├── controllers/           # API controllers
├── middleware/            # Middleware
├── migrations/            # Database migrations
├── models/                # Database models
├── seeds/                 # Database seed data
├── utils/                 # Utility functions
├── .air.toml              # Air configuration for hot reloading
├── .dockerignore          # Docker ignore file
├── .env                   # Environment variables
├── docker-compose.yml     # Docker Compose configuration with Air
├── docker-compose.simple.yml # Simple Docker Compose configuration
├── Dockerfile.dev         # Development Dockerfile
├── go.mod                 # Go modules
├── go.sum                 # Go modules checksums
├── main.go                # Entry point
├── Makefile               # Make commands for development
└── README.md              # Documentation
```

## Docker Development Environment

This project includes Docker configuration for easy development setup.

### Prerequisites

- Docker
- Docker Compose

### Getting Started with Docker

1. Start the development environment:
   ```
   make docker-up
   ```
   or
   ```
   docker-compose up -d
   ```

2. Initialize the database (migrations and seed data):
   ```
   make init
   ```

3. View logs:
   ```
   make logs
   ```
   or
   ```
   docker-compose logs -f
   ```

4. Stop the development environment:
   ```
   make docker-down
   ```
   or
   ```
   docker-compose down
   ```

### Docker Setup Options

This project provides two Docker setup options:

#### 1. Standard Setup with Air (Hot Reloading)

Uses Air for hot reloading, which automatically rebuilds and restarts the application when code changes are detected.

```
# Start with Air hot reloading
make docker-up
make init

# Or use these commands
make dev-docker     # Start and show logs
```

#### 2. Simple Setup (Without Air)

A simpler setup that doesn't rely on Air, useful if you're having compatibility issues with Air.

```
# Start with simple setup
make docker-simple-up
make init-simple

# Or use this command
make dev-docker-simple  # Start and show logs
```

### Docker Services

- **api**: Go backend service (with or without hot reloading)
- **postgres**: PostgreSQL database
- **pgadmin**: Web interface for PostgreSQL management (optional)
- **api-test**: Service for running basic API tests
- **adaptive-test**: Service for running adaptive learning tests

### Accessing Services

- API: http://localhost:8080
- PgAdmin: http://localhost:5050 (Email: <EMAIL>, Password: admin)

### API Flow

The language learning system follows a streamlined API flow that emphasizes automation and personalization:

#### Initial Setup Flow
1. **User Registration**: `POST /auth/register` - Creates a new user account
2. **Initiate Personalized Learning**: `POST /personalized-learning/initiate` - Starts the personalized learning process and creates an evaluation
3. **Complete Evaluation**:
   - `POST /evaluations/:id/start` - Begins the evaluation
   - `POST /evaluations/:id/answer` - Submits answers to evaluation questions
   - `POST /evaluations/:id/complete` - Completes the evaluation
   - System automatically creates a personalized learning path based on evaluation results

#### Exercise Flow
1. **Get Next Exercise**: `GET /learning-paths/:id/next-exercise` - Retrieves the next exercise in the learning path
   - If all exercises are completed, returns `{"completed": true}`
   - Otherwise, returns the next exercise details
2. **Complete Exercise**: `POST /learning-paths/:id/complete-exercise/:lessonId` - Marks an exercise as completed
   - System automatically updates the exercise set based on performance
   - System automatically adjusts difficulty and focus areas based on performance
3. **Repeat**: Continue getting and completing exercises
   - Each new exercise is automatically customized based on previous performance
   - No need to manually create new learning paths or exercise sets

The system follows a "complete then next" pattern where after completing an exercise, you simply request the next one. The system automatically adapts to user performance in real-time without requiring manual API calls to update exercise sets or create new learning paths.

For more details on the API flow, see the [tests/README.md](tests/README.md) file.

### Running Tests

The project includes automated tests to verify the functionality of the API and the adaptive learning system.

#### Basic API Tests

These tests verify the basic flow of the system, from user registration to personalized learning path creation and exercise completion.

```bash
# Run basic API tests
./run_tests.sh
# or
./run_tests.sh --basic
```

#### Adaptive Learning Tests

These tests focus on the adaptive learning capabilities of the system, testing how the system adapts to different user performance levels.

```bash
# Run adaptive learning tests
./run_tests.sh --adaptive
```

For more information about the tests, see the [tests/README.md](tests/README.md) file.

### Using Makefile

The project includes a Makefile with useful commands:

```
# General commands
make help               # Show all available commands

# Standard setup with Air
make docker-up          # Start Docker containers with Air
make docker-down        # Stop Docker containers
make dev-docker         # Start development environment with Air
make init               # Initialize the environment with Air
make logs               # Show logs from Docker containers

# Simple setup without Air
make docker-simple-up   # Start simple Docker containers
make docker-simple-down # Stop simple Docker containers
make dev-docker-simple  # Start simple development environment
make init-simple        # Initialize the simple environment
make logs-simple        # Show logs from simple Docker containers
```

## License

This project is licensed under the MIT License.
