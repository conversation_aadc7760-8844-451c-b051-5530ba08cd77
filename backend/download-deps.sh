#!/bin/sh

# Download all required dependencies
echo "Downloading dependencies..."

# Download direct dependencies
go mod download github.com/gin-contrib/cors
go mod download github.com/gin-gonic/gin
go mod download github.com/joho/godotenv
go mod download github.com/golang-jwt/jwt/v5
go mod download github.com/google/uuid
go mod download golang.org/x/crypto/bcrypt
go mod download gorm.io/driver/postgres
go mod download gorm.io/gorm
go mod download gorm.io/gorm/logger

# Get internal packages to resolve their dependencies
go get languagelearning/config
go get languagelearning/controllers
go get languagelearning/middleware
go get languagelearning/migrations
go get languagelearning/models
go get languagelearning/seeds
go get languagelearning/utils

# Tidy up the go.mod file
go mod tidy

echo "Dependencies downloaded successfully!"
