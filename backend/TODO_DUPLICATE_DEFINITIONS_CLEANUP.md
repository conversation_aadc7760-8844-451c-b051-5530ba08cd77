# TODO: 重复定义清理计划

## 🎯 目标
清理代码库中的重复实体定义，遵循DDD原则，确保每个实体只在其所属的领域中定义。

## 📊 当前状态概览

| 任务 | 状态 | 优先级 | 复杂度 | 预估工时 |
|------|------|--------|--------|----------|
| Repository模式迁移 | ✅ 完成 | 高 | 高 | 已完成 |
| ExerciseRelation清理 | ✅ 完成 | 中 | 低 | 已完成 |
| Seeds向后兼容性修复 | 🔄 进行中 | 高 | 中 | 4-6小时 |
| Lesson重复定义清理 | ⏳ 待开始 | 中 | 高 | 8-12小时 |
| 值对象重复定义清理 | ⏳ 待开始 | 低 | 中 | 4-6小时 |
| 接口命名标准化 | ⏳ 待开始 | 低 | 低 | 2-4小时 |

## ✅ 已完成的清理

### 1. Repository模式迁移 ✅ (2024-01-XX)
**成果**: 所有服务层都已迁移到Repository模式
- ✅ 移除了所有直接的GORM数据访问调用
- ✅ 添加了缺失的repository方法
- ✅ 保持了事务管理在服务层
- ✅ 统一的接口设计模式
- ✅ 支持Context传递

### 2. ExerciseRelation 重复定义 ✅ (2024-01-XX)
- **保留**: `domain/exercise/entity/exercise_relation.go` (更完善的版本)
- **移除**: `domain/learning/entity/exercise_relation.go` (已删除)

## 🔄 待处理的重复定义

### 1. Seeds向后兼容性修复 🔄 (优先级: 高)
**问题描述**: Seeds中的字段使用语法与嵌入结构体不匹配
- `seeds/english_grammar_seed.go` - 字段使用错误
- `seeds/grammar_seed.go` - 字段使用错误
- `seeds/listening_seed.go` - 字段使用错误
- `seeds/english_listening_seed.go` - 字段使用错误
- `seeds/english_speaking_seed.go` - 字段使用错误
- `seeds/seed.go` - 字段使用错误

**具体错误**:
```go
// 错误的使用方式
exercises := []models.GrammarExercise{
    {
        ID: uuid.New(),           // 错误：ID字段在嵌入的Exercise中
        Title: "Present Simple",  // 错误：Title字段在嵌入的Exercise中
        // ...
    },
}

// 正确的使用方式应该是
exercises := []models.GrammarExercise{
    {
        Exercise: models.Exercise{
            ID: uuid.New(),
            Title: "Present Simple",
            // ...
        },
        GrammarRule: "specific grammar rule",
    },
}
```

**解决方案**:
1. **选项A**: 修复seeds语法使用嵌入结构体
2. **选项B**: 创建helper函数简化seeds创建
3. **选项C**: 重新设计Exercise结构避免嵌入

**推荐**: 选项B - 创建helper函数

**实施步骤**:
1. 创建 `seeds/helpers/exercise_helper.go`
2. 实现 `NewGrammarExercise()`, `NewListeningExercise()` 等helper函数
3. 更新所有seed文件使用helper函数
4. 测试编译和数据库seeding

### 2. Lesson 重复定义 ⏳ (优先级: 中, 复杂度: 高)
**当前状态**: 有4个不同的Lesson定义
- `models/lesson.go` - 数据库模型 (保留)
- `domain/lesson/entity/lesson.go` - 课程领域实体 (简洁版)
- `domain/learning/entity/lesson.go` - 学习领域实体 (复杂版，包含练习和资源)
- `domain/learning/lesson/entity/lesson.go` - 学习领域课程子模块

**使用情况分析**:
- `domain/learning/lesson/entity/lesson.go` 被5个文件使用
- `domain/learning/entity/lesson.go` 只被1个文件使用
- `domain/lesson/entity/lesson.go` 使用情况待分析

**冲突分析**:
```go
// domain/learning/entity/lesson.go (复杂版)
type Lesson struct {
    ID          uuid.UUID
    Title       string
    Description string
    Exercises   []interface{} // TODO: 临时解决方案
    Resources   []Resource
    // ... 更多字段
}

// domain/learning/lesson/entity/lesson.go (子模块版)
type Lesson struct {
    ID       uuid.UUID
    Title    string
    Content  string
    Level    Level
    Category Category
    // ... 不同的字段结构
}
```

**建议方案**:
1. **阶段1**: 分析所有使用情况
2. **阶段2**: 统一Lesson定义到最合适的位置
3. **阶段3**: 迁移所有引用
4. **阶段4**: 移除重复定义

**详细实施计划**:
1. 创建使用情况分析脚本
2. 设计统一的Lesson实体结构
3. 创建迁移脚本
4. 逐步迁移引用
5. 清理重复定义

### 3. 值对象重复定义 ⏳ (优先级: 低, 复杂度: 中)
**问题描述**: 多个值对象在不同领域中重复定义

**重复定义列表**:
1. **Duration** - 时长值对象
   - `domain/exercise/entity/duration.go`
   - `domain/learning/entity/duration.go`
   - `models/` 中可能也有相关定义

2. **Language** - 语言值对象
   - `domain/core/entity/language.go`
   - `models/language.go`
   - 可能在其他领域中也有定义

3. **Difficulty** - 难度值对象
   - `models/exercise.go` 中的 `Difficulty` 类型
   - `domain/exercise/entity/` 中可能有类似定义
   - `domain/learning/entity/` 中可能有类似定义

**建议方案**:
1. **共享值对象**: 将通用值对象移到 `domain/shared/valueobject/` 包
2. **领域特定值对象**: 保留在各自领域中，但确保命名不冲突
3. **统一导入**: 创建统一的值对象导入路径

**实施计划**:
1. 创建 `domain/shared/valueobject/` 包
2. 移动通用值对象（Language, Duration等）
3. 更新所有导入引用
4. 移除重复定义

### 4. 接口命名标准化 ⏳ (优先级: 低, 复杂度: 低)
**问题描述**: Repository接口方法命名不一致

**当前不一致的命名**:
- `FindByID` vs `GetByID`
- `FindByEmail` vs `GetByEmail`
- `FindAll` vs `GetAll`

**建议标准**:
- 使用 `FindXxx` 用于可能返回空结果的查询
- 使用 `GetXxx` 用于期望必须有结果的查询
- 使用 `ListXxx` 用于返回列表的查询

**实施计划**:
1. 制定接口命名规范文档
2. 创建重命名脚本
3. 批量更新接口定义
4. 更新所有实现

## 🚀 执行策略

### 阶段1: 完成核心功能验证 ✅ (已完成)
- ✅ 完成repository模式迁移
- ✅ 验证所有服务层都使用repository模式
- ✅ 测试核心功能正常工作
- ✅ 编写测试验证repository模式

### 阶段2: 高优先级修复 🔄 (当前阶段)
**目标**: 确保项目完全可编译和运行

#### 2.1 Seeds向后兼容性修复 (预估: 4-6小时)
**步骤**:
1. **创建helper函数** (1-2小时)
   ```bash
   mkdir -p seeds/helpers
   # 创建 exercise_helper.go
   ```
2. **实现helper函数** (2-3小时)
   - `NewGrammarExercise(title, question, options, correctAnswer, explanation string, difficulty models.Difficulty) models.GrammarExercise`
   - `NewListeningExercise(title, audioURL, transcript string, questions []models.ListeningQuestion) models.ListeningExercise`
   - `NewSpeakingExercise(title, targetPhrase string, expectedDuration int) models.SpeakingExercise`
3. **更新seed文件** (1小时)
   - 替换所有直接结构体初始化为helper函数调用
4. **测试验证** (30分钟)
   - 编译测试
   - 数据库seeding测试

#### 2.2 编译错误修复 (预估: 1-2小时)
- 修复所有编译错误
- 确保 `go build ./...` 成功
- 验证主要功能可以启动

### 阶段3: 中优先级清理 ⏳ (下一阶段)
**目标**: 清理主要的重复定义

#### 3.1 Lesson重复定义清理 (预估: 8-12小时)
**详细步骤**:
1. **分析阶段** (2-3小时)
   - 创建使用情况分析脚本
   - 分析所有Lesson定义的使用情况
   - 制定统一方案
2. **设计阶段** (2-3小时)
   - 设计统一的Lesson实体结构
   - 确定最终的Lesson定义位置
   - 制定迁移计划
3. **实施阶段** (3-4小时)
   - 创建新的统一Lesson定义
   - 逐步迁移所有引用
   - 更新导入路径
4. **清理阶段** (1-2小时)
   - 移除重复定义
   - 测试验证

#### 3.2 值对象重复定义清理 (预估: 4-6小时)
1. **创建共享包** (1小时)
2. **移动通用值对象** (2-3小时)
3. **更新引用** (1-2小时)

### 阶段4: 低优先级优化 ⏳ (未来)
**目标**: 代码质量和一致性提升

#### 4.1 接口命名标准化 (预估: 2-4小时)
#### 4.2 文档更新 (预估: 2-3小时)
#### 4.3 性能测试 (预估: 4-6小时)

## 📝 注意事项

1. **向后兼容性**: 确保清理过程不破坏现有功能
2. **测试覆盖**: 每个清理步骤都需要有测试验证
3. **渐进式**: 一次只处理一个重复定义，避免大规模破坏性更改
4. **文档**: 及时更新相关文档和注释
5. **备份**: 在进行大规模更改前创建分支备份
6. **团队协调**: 确保团队成员了解重构计划，避免冲突

## 🛠️ 工具和脚本

### 已创建的工具
- ✅ `scripts/verify_repo_pattern.go` - Repository模式验证脚本
- ✅ `tests/repository_pattern_test.go` - Repository模式测试

### 需要创建的工具
- ⏳ `scripts/analyze_lesson_usage.go` - Lesson使用情况分析脚本
- ⏳ `scripts/find_duplicate_definitions.go` - 重复定义查找脚本
- ⏳ `seeds/helpers/exercise_helper.go` - Exercise创建helper函数
- ⏳ `scripts/migrate_lesson_references.go` - Lesson引用迁移脚本

## 🔗 相关文件

### 高优先级需要修复的文件
- `seeds/english_grammar_seed.go` - 🔴 编译错误，需要修复字段使用语法
- `seeds/grammar_seed.go` - 🔴 编译错误，需要修复字段使用语法
- `seeds/listening_seed.go` - 🔴 编译错误，需要修复字段使用语法
- `seeds/english_listening_seed.go` - 🔴 编译错误，需要修复字段使用语法
- `seeds/english_speaking_seed.go` - 🔴 编译错误，需要修复字段使用语法
- `seeds/seed.go` - 🔴 编译错误，需要修复字段使用语法
- `migrations/migrations.go` - 🟡 可能需要更新

### 核心领域文件
- `domain/exercise/entity/exercise.go` - ✅ 练习领域实体 (已完善)
- `domain/lesson/entity/lesson.go` - 🟡 课程领域实体 (需要分析使用情况)
- `domain/learning/entity/lesson.go` - 🔴 学习领域实体 (重复定义，待清理)
- `domain/learning/lesson/entity/lesson.go` - 🟡 学习领域课程子模块 (需要分析)

### Repository相关文件 (已完成)
- ✅ `domain/user/repository/user_repository.go` - 用户Repository接口
- ✅ `domain/user/repository/impl/user_repository_impl.go` - 用户Repository实现
- ✅ `domain/achievement/repository/achievement_repository.go` - 成就Repository接口
- ✅ `domain/achievement/repository/impl/achievement_repository_impl.go` - 成就Repository实现
- ✅ 其他所有Repository接口和实现

## 📊 进度跟踪

### 完成情况统计
- **总任务数**: 6个主要任务
- **已完成**: 2个 (33%)
- **进行中**: 1个 (17%)
- **待开始**: 3个 (50%)

### 工时统计
- **已投入**: ~12-16小时 (Repository模式迁移)
- **预估剩余**: ~18-28小时
- **总预估**: ~30-44小时

### 里程碑
- ✅ **里程碑1**: Repository模式迁移完成 (2024-01-XX)
- 🔄 **里程碑2**: 项目完全可编译 (目标: 2024-01-XX)
- ⏳ **里程碑3**: 主要重复定义清理完成 (目标: 2024-01-XX)
- ⏳ **里程碑4**: 代码质量优化完成 (目标: 2024-01-XX)

## 🎯 下一步行动

### 立即行动 (今天)
1. **创建seeds helper函数** - 开始修复编译错误
2. **修复1-2个seed文件** - 验证helper函数可行性
3. **测试编译** - 确保修复方向正确

### 本周行动
1. **完成所有seeds修复** - 确保项目完全可编译
2. **开始Lesson使用情况分析** - 为下一阶段做准备
3. **编写更多测试** - 确保重构安全性

### 下周行动
1. **开始Lesson重复定义清理** - 主要重构工作
2. **值对象重复定义分析** - 准备下一个清理目标
