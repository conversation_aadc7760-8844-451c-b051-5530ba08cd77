#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
TEST_TYPE="basic"
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -a|--adaptive) TEST_TYPE="adaptive"; shift ;;
        -b|--basic) TEST_TYPE="basic"; shift ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  -a, --adaptive    Run adaptive learning tests"
            echo "  -b, --basic       Run basic API tests (default)"
            echo "  -h, --help        Show this help message"
            exit 0
            ;;
        *) echo "Unknown parameter: $1"; exit 1 ;;
    esac
done

if [ "$TEST_TYPE" = "basic" ]; then
    echo -e "${YELLOW}Starting Language Learning Basic API Tests${NC}"
else
    echo -e "${BLUE}Starting Language Learning Adaptive Learning Tests${NC}"
fi

# Make sure Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Docker Compose is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if the services are already running
if docker-compose ps | grep -q "languagelearning-api"; then
    echo -e "${YELLOW}Services are already running. Proceeding with tests...${NC}"
else
    echo -e "${YELLOW}Starting services...${NC}"
    docker-compose up -d postgres api migrate

    echo -e "${YELLOW}Waiting for database migrations to complete...${NC}"
    sleep 10

    echo -e "${YELLOW}Seeding the database...${NC}"
    docker-compose up -d seed
    sleep 5

    echo -e "${YELLOW}Applying additional migrations for testing...${NC}"
    # We need to restart the migrate service to apply the new migrations
    docker-compose restart migrate
    sleep 5
fi

# Make sure the test scripts are executable
echo -e "${YELLOW}Making test scripts executable...${NC}"
chmod +x tests/*.sh

if [ "$TEST_TYPE" = "basic" ]; then
    echo -e "${YELLOW}Running basic API tests...${NC}"
    docker-compose up --build api-test
else
    echo -e "${BLUE}Running adaptive learning tests...${NC}"
    docker-compose up --build adaptive-test
fi

# Check if tests were successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Tests completed successfully!${NC}"
else
    echo -e "${RED}Tests failed. Please check the logs for details.${NC}"
fi

echo -e "${YELLOW}You can now access the API at http://localhost:8080${NC}"
echo -e "${YELLOW}To stop all services, run: docker-compose down${NC}"
