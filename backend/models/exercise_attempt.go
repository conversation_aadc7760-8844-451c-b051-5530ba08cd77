package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ExerciseAttempt 表示用户尝试练习的记录
type ExerciseAttempt struct {
	ID             uuid.UUID     `gorm:"type:uuid;primary_key" json:"id"`
	UserID         uuid.UUID     `gorm:"type:uuid;not null" json:"userId"`
	ExerciseID     uuid.UUID     `gorm:"type:uuid;not null" json:"exerciseId"`
	UserAnswer     string        `gorm:"size:500;not null" json:"userAnswer"`
	IsCorrect      bool          `gorm:"not null" json:"isCorrect"`
	Score          int           `gorm:"not null" json:"score"`
	TimeSpent      int           `gorm:"not null" json:"timeSpent"` // 秒
	AttemptedAt    time.Time     `gorm:"not null" json:"attemptedAt"`
	Feedback       string        `gorm:"size:500" json:"feedback,omitempty"`
	LessonID       uuid.NullUUID `gorm:"type:uuid" json:"lessonId,omitempty"`
	LearningPathID uuid.NullUUID `gorm:"type:uuid" json:"learningPathId,omitempty"`
	CreatedAt      time.Time     `json:"createdAt"`
	UpdatedAt      time.Time     `json:"updatedAt"`
}

// BeforeCreate will set a UUID rather than numeric ID and set timestamps
func (attempt *ExerciseAttempt) BeforeCreate(tx *gorm.DB) error {
	if attempt.ID == uuid.Nil {
		attempt.ID = uuid.New()
	}
	now := time.Now()
	if attempt.CreatedAt.IsZero() {
		attempt.CreatedAt = now
	}
	if attempt.UpdatedAt.IsZero() {
		attempt.UpdatedAt = now
	}
	if attempt.AttemptedAt.IsZero() {
		attempt.AttemptedAt = now
	}
	return nil
}

// BeforeUpdate will update the updated time
func (attempt *ExerciseAttempt) BeforeUpdate(tx *gorm.DB) error {
	attempt.UpdatedAt = time.Now()
	return nil
}

// ExerciseAttemptSummary 表示用户练习尝试的统计摘要
type ExerciseAttemptSummary struct {
	UserID           uuid.UUID `json:"userId"`
	TotalAttempts    int64     `json:"totalAttempts"`
	CorrectAttempts  int64     `json:"correctAttempts"`
	SuccessRate      float64   `json:"successRate"`
	AverageScore     float64   `json:"averageScore"`
	AverageTimeSpent float64   `json:"averageTimeSpent"`
	TotalTimeSpent   int64     `json:"totalTimeSpent"`
}
