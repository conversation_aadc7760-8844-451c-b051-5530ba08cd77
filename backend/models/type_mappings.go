package models

import (
	exerciseEntity "languagelearning/domain/exercise/entity"
)

// ExerciseTypeMapping 提供数据库层和领域层之间的类型映射
var ExerciseTypeMapping = map[ExerciseType]exerciseEntity.ExerciseType{
	ExMultipleChoice: exerciseEntity.MultipleChoice,
	ExFillInBlank:    exerciseEntity.FillInBlank,
	ExMatching:       exerciseEntity.Matching,
	ExTrueFalse:      exerciseEntity.TrueFalse,
	ExOpenEnded:      exerciseEntity.OpenEnded,
	ExSpeaking:       exerciseEntity.SpeakingExercise,
	ExListening:      exerciseEntity.ListeningExercise,
	ExWriting:        exerciseEntity.WritingExercise,
	ExReading:        exerciseEntity.ReadingExercise,
	ExVocabulary:     exerciseEntity.VocabularyExercise,
	ExGrammar:        exerciseEntity.GrammarExercise,
}

// ReverseExerciseTypeMapping 提供领域层到数据库层的反向映射
var ReverseExerciseTypeMapping = map[exerciseEntity.ExerciseType]ExerciseType{
	exerciseEntity.MultipleChoice:     ExMultipleChoice,
	exerciseEntity.FillInBlank:        ExFillInBlank,
	exerciseEntity.Matching:           ExMatching,
	exerciseEntity.TrueFalse:          ExTrueFalse,
	exerciseEntity.OpenEnded:          ExOpenEnded,
	exerciseEntity.SpeakingExercise:   ExSpeaking,
	exerciseEntity.ListeningExercise:  ExListening,
	exerciseEntity.WritingExercise:    ExWriting,
	exerciseEntity.ReadingExercise:    ExReading,
	exerciseEntity.VocabularyExercise: ExVocabulary,
	exerciseEntity.GrammarExercise:    ExGrammar,
}

// MapToEntityType 将数据库类型映射到领域类型
func MapToEntityType(dbType ExerciseType) exerciseEntity.ExerciseType {
	if entityType, exists := ExerciseTypeMapping[dbType]; exists {
		return entityType
	}
	return exerciseEntity.ExerciseType(dbType) // 默认直接转换
}

// MapToDBType 将领域类型映射到数据库类型
func MapToDBType(entityType exerciseEntity.ExerciseType) ExerciseType {
	if dbType, exists := ReverseExerciseTypeMapping[entityType]; exists {
		return dbType
	}
	return ExerciseType(entityType) // 默认直接转换
}
