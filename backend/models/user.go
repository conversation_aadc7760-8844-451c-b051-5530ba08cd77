package models

import (
	"time"

	"languagelearning/domain/user/entity"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID          uuid.UUID     `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Username    string        `gorm:"size:100;not null;uniqueIndex" json:"username"`
	Email       string        `gorm:"size:100;not null;uniqueIndex" json:"email"`
	Password    string        `gorm:"size:100;not null" json:"-"`
	AvatarURL   string        `gorm:"size:255" json:"avatarUrl,omitempty"`
	IsActive    bool          `gorm:"default:false" json:"isActive"`
	CreatedAt   time.Time     `gorm:"not null" json:"createdAt"`
	LastLoginAt time.Time     `json:"lastLoginAt,omitempty"`
	Settings    *UserSettings `gorm:"foreignKey:UserID" json:"settings,omitempty"`
	Profile     *UserProfile  `gorm:"foreignKey:UserID" json:"profile,omitempty"`
	Stats       *UserStats    `gorm:"foreignKey:UserID" json:"stats,omitempty"`
}

// UserResponse represents the response for a user
type UserResponse struct {
	ID          uuid.UUID     `json:"id"`
	Username    string        `json:"username"`
	Email       string        `json:"email"`
	AvatarURL   string        `json:"avatarUrl,omitempty"`
	IsActive    bool          `json:"isActive"`
	CreatedAt   time.Time     `json:"createdAt"`
	LastLoginAt time.Time     `json:"lastLoginAt,omitempty"`
	Settings    *UserSettings `json:"settings,omitempty"`
	Profile     *UserProfile  `json:"profile,omitempty"`
	Stats       *UserStats    `json:"stats,omitempty"`
	Token       string        `json:"token,omitempty"`
}

// UserSettings represents user settings
type UserSettings struct {
	ID                   uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID               uuid.UUID `gorm:"type:uuid;not null;uniqueIndex" json:"userId"`
	NotificationsEnabled bool      `gorm:"default:true" json:"notificationsEnabled"`
	DarkModeEnabled      bool      `gorm:"default:false" json:"darkModeEnabled"`
	DailyGoal            int       `gorm:"default:3" json:"dailyGoal"`
	PreferredLanguage    string    `gorm:"size:10;default:'zh-CN'" json:"preferredLanguage"`
	UpdatedAt            time.Time `gorm:"autoUpdateTime" json:"updatedAt"`
}

// UserProfile represents a user's profile information
type UserProfile struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID    uuid.UUID `gorm:"type:uuid;not null;uniqueIndex" json:"userId"`
	Bio       string    `gorm:"type:text" json:"bio,omitempty"`
	Location  string    `gorm:"size:100" json:"location,omitempty"`
	Website   string    `gorm:"size:255" json:"website,omitempty"`
	Social    Social    `gorm:"embedded" json:"social,omitempty"`
	UpdatedAt time.Time `gorm:"not null" json:"updatedAt"`
}

// Social represents social media links
type Social struct {
	Twitter  string `gorm:"size:100" json:"twitter,omitempty"`
	Facebook string `gorm:"size:100" json:"facebook,omitempty"`
	LinkedIn string `gorm:"size:100" json:"linkedIn,omitempty"`
}

// UserStats represents a user's learning statistics
type UserStats struct {
	ID                  uuid.UUID     `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID              uuid.UUID     `gorm:"type:uuid;not null;uniqueIndex" json:"userId"`
	CurrentStreak       int           `gorm:"default:0" json:"currentStreak"`
	LongestStreak       int           `gorm:"default:0" json:"longestStreak"`
	VocabularyCount     int           `gorm:"default:0" json:"vocabularyCount"`
	ListeningCount      int           `gorm:"default:0" json:"listeningCount"`
	SpeakingCount       int           `gorm:"default:0" json:"speakingCount"`
	GrammarCount        int           `gorm:"default:0" json:"grammarCount"`
	TotalPoints         int           `gorm:"default:0" json:"totalPoints"`
	ChallengesCompleted int           `gorm:"default:0" json:"challengesCompleted"`
	HelpedUsers         int           `gorm:"default:0" json:"helpedUsers"`
	TotalPracticeTime   time.Duration `gorm:"default:0" json:"totalPracticeTime"` // in seconds
	AverageScore        float64       `gorm:"default:0" json:"averageScore"`
	LearningDays        int           `gorm:"default:0" json:"learningDays"`
	CompletedLessons    int           `gorm:"default:0" json:"completedLessons"`
	LastActive          time.Time     `gorm:"not null" json:"lastActive"`
	UpdatedAt           time.Time     `gorm:"not null" json:"updatedAt"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (user *User) BeforeCreate(tx *gorm.DB) error {
	if user.ID == uuid.Nil {
		user.ID = uuid.New()
	}
	return nil
}

// HashPassword hashes the user's password
func (user *User) HashPassword() error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user.Password = string(hashedPassword)
	return nil
}

// CheckPassword checks if the provided password matches the stored hash
func (user *User) CheckPassword(password string) error {
	return bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
}

// ToEntity converts the model to a domain entity
func (user *User) ToEntity() *entity.User {
	userEntity := &entity.User{
		ID:          user.ID,
		Username:    user.Username,
		Email:       user.Email,
		Password:    user.Password,
		AvatarURL:   user.AvatarURL,
		CreatedAt:   user.CreatedAt,
		LastLoginAt: user.LastLoginAt,
	}

	if user.Settings != nil {
		userEntity.Settings = user.Settings.ToEntity()
	}

	return userEntity
}

// FromEntity converts a domain entity to a model
func (user *User) FromEntity(e *entity.User) {
	user.ID = e.ID
	user.Username = e.Username
	user.Email = e.Email
	user.Password = e.Password
	user.AvatarURL = e.AvatarURL
	user.CreatedAt = e.CreatedAt
	user.LastLoginAt = e.LastLoginAt

	if e.Settings != nil {
		if user.Settings == nil {
			user.Settings = &UserSettings{}
		}
		user.Settings.FromEntity(e.Settings)
	}
}

// ToEntity converts UserSettings model to domain entity
func (s *UserSettings) ToEntity() *entity.UserSettings {
	return &entity.UserSettings{
		NotificationsEnabled: s.NotificationsEnabled,
		DarkModeEnabled:      s.DarkModeEnabled,
		DailyGoal:            s.DailyGoal,
		PreferredLanguage:    s.PreferredLanguage,
	}
}

// FromEntity converts domain entity to UserSettings model
func (s *UserSettings) FromEntity(e *entity.UserSettings) {
	s.NotificationsEnabled = e.NotificationsEnabled
	s.DarkModeEnabled = e.DarkModeEnabled
	s.DailyGoal = e.DailyGoal
	s.PreferredLanguage = e.PreferredLanguage
	s.UpdatedAt = time.Now()
}

// ToEntity converts UserProfile model to domain entity
func (p *UserProfile) ToEntity() *entity.UserProfile {
	return &entity.UserProfile{
		ID:       p.ID,
		UserID:   p.UserID,
		Bio:      p.Bio,
		Location: p.Location,
		Website:  p.Website,
		Social: entity.Social{
			Twitter:  p.Social.Twitter,
			Facebook: p.Social.Facebook,
			LinkedIn: p.Social.LinkedIn,
		},
		UpdatedAt: p.UpdatedAt,
	}
}

// FromEntity converts domain entity to UserProfile model
func (p *UserProfile) FromEntity(e *entity.UserProfile) {
	p.ID = e.ID
	p.UserID = e.UserID
	p.Bio = e.Bio
	p.Location = e.Location
	p.Website = e.Website
	p.Social = Social{
		Twitter:  e.Social.Twitter,
		Facebook: e.Social.Facebook,
		LinkedIn: e.Social.LinkedIn,
	}
	p.UpdatedAt = time.Now()
}

// ToEntity converts UserStats model to domain entity
func (s *UserStats) ToEntity() *entity.UserStats {
	return &entity.UserStats{
		ID:                  s.ID,
		UserID:              s.UserID,
		CurrentStreak:       s.CurrentStreak,
		LongestStreak:       s.LongestStreak,
		VocabularyCount:     s.VocabularyCount,
		ListeningCount:      s.ListeningCount,
		SpeakingCount:       s.SpeakingCount,
		GrammarCount:        s.GrammarCount,
		TotalPoints:         s.TotalPoints,
		ChallengesCompleted: s.ChallengesCompleted,
		HelpedUsers:         s.HelpedUsers,
		TotalPracticeTime:   int64(s.TotalPracticeTime.Seconds()),
		AverageScore:        s.AverageScore,
		LearningDays:        s.LearningDays,
		CompletedLessons:    s.CompletedLessons,
		LastActive:          s.LastActive,
		UpdatedAt:           s.UpdatedAt,
	}
}

// FromEntity converts domain entity to UserStats model
func (s *UserStats) FromEntity(e *entity.UserStats) {
	s.ID = e.ID
	s.UserID = e.UserID
	s.CurrentStreak = e.CurrentStreak
	s.LongestStreak = e.LongestStreak
	s.VocabularyCount = e.VocabularyCount
	s.ListeningCount = e.ListeningCount
	s.SpeakingCount = e.SpeakingCount
	s.GrammarCount = e.GrammarCount
	s.TotalPoints = e.TotalPoints
	s.ChallengesCompleted = e.ChallengesCompleted
	s.HelpedUsers = e.HelpedUsers
	s.TotalPracticeTime = time.Duration(e.TotalPracticeTime) * time.Second
	s.AverageScore = e.AverageScore
	s.LearningDays = e.LearningDays
	s.CompletedLessons = e.CompletedLessons
	s.LastActive = e.LastActive
	s.UpdatedAt = time.Now()
}

// ToResponse converts the User model to a UserResponse
func (user *User) ToResponse(token string) UserResponse {
	return UserResponse{
		ID:          user.ID,
		Username:    user.Username,
		Email:       user.Email,
		AvatarURL:   user.AvatarURL,
		IsActive:    user.IsActive,
		CreatedAt:   user.CreatedAt,
		LastLoginAt: user.LastLoginAt,
		Settings:    user.Settings,
		Profile:     user.Profile,
		Stats:       user.Stats,
		Token:       token,
	}
}
