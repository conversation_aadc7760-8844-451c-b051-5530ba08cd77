package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AchievementType represents the type of achievement
type AchievementType string

const (
	AchStreak     AchievementType = "连续学习"
	AchVocabulary AchievementType = "词汇量"
	AchListening  AchievementType = "听力练习"
	AchSpeaking   AchievementType = "口语练习"
	AchLessons    AchievementType = "课程完成"
	AchPoints     AchievementType = "积分"
	AchChallenges AchievementType = "挑战"
	AchSocial     AchievementType = "社交"
)

// Achievement represents an achievement that can be unlocked
type Achievement struct {
	ID               uuid.UUID         `gorm:"type:uuid;primary_key" json:"id"`
	Type             AchievementType   `gorm:"size:50;not null" json:"type"`
	Title            string            `gorm:"size:100;not null" json:"title"`
	Description      string            `gorm:"size:500;not null" json:"description"`
	Icon             string            `gorm:"size:50;not null" json:"icon"`
	Color            string            `gorm:"size:20;not null" json:"color"`
	Requirement      int               `gorm:"not null" json:"requirement"`
	Reward           int               `gorm:"not null" json:"reward"`
	UserAchievements []UserAchievement `gorm:"foreignKey:AchievementID" json:"-"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (achievement *Achievement) BeforeCreate(tx *gorm.DB) error {
	if achievement.ID == uuid.Nil {
		achievement.ID = uuid.New()
	}
	return nil
}

// UserAchievement represents a user's progress towards an achievement
type UserAchievement struct {
	ID              uuid.UUID       `gorm:"type:uuid;primary_key" json:"id"`
	UserID          uuid.UUID       `gorm:"type:uuid;not null" json:"userId"`
	AchievementID   uuid.UUID       `gorm:"type:uuid;not null" json:"achievementId"`
	AchievementType AchievementType `gorm:"size:50;not null" json:"achievementType"`
	Progress        int             `gorm:"default:0" json:"progress"`
	IsUnlocked      bool            `gorm:"default:false" json:"isUnlocked"`
	RewardClaimed   bool            `gorm:"default:false" json:"rewardClaimed"`
	UnlockedDate    time.Time       `json:"unlockedDate,omitempty"`
	Reward          int             `gorm:"not null" json:"reward"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (userAchievement *UserAchievement) BeforeCreate(tx *gorm.DB) error {
	if userAchievement.ID == uuid.Nil {
		userAchievement.ID = uuid.New()
	}
	return nil
}

// IsCompleted returns whether the achievement is completed
func (userAchievement *UserAchievement) IsCompleted() bool {
	return userAchievement.IsUnlocked && userAchievement.RewardClaimed
}

// DateCompleted returns the date the achievement was completed
func (userAchievement *UserAchievement) DateCompleted() *time.Time {
	if userAchievement.IsCompleted() {
		return &userAchievement.UnlockedDate
	}
	return nil
}

// UserAchievementResponse represents the response for a user achievement
type UserAchievementResponse struct {
	ID            uuid.UUID       `json:"id"`
	Type          AchievementType `json:"type"`
	Title         string          `json:"title"`
	Description   string          `json:"description"`
	Icon          string          `json:"icon"`
	Color         string          `json:"color"`
	Requirement   int             `json:"requirement"`
	Reward        int             `json:"reward"`
	Progress      int             `json:"progress"`
	IsUnlocked    bool            `json:"isUnlocked"`
	RewardClaimed bool            `json:"rewardClaimed"`
	UnlockedDate  time.Time       `json:"unlockedDate,omitempty"`
}
