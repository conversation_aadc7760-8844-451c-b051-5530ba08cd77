package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AssessmentProgress represents a user's progress through an assessment
type AssessmentProgress struct {
	ID              uuid.UUID       `gorm:"type:uuid;primary_key" json:"id"`
	UserID          uuid.UUID       `gorm:"type:uuid;not null" json:"userId"`
	EvaluationID    uuid.UUID       `gorm:"type:uuid;not null" json:"evaluationId"`
	CurrentSection  int             `gorm:"default:0" json:"currentSection"` // Index of current section
	CurrentQuestion int             `gorm:"default:0" json:"currentQuestion"` // Index of current question within section
	SessionToken    string          `gorm:"size:100" json:"sessionToken"` // Token to identify the session
	LastUpdated     time.Time       `json:"lastUpdated"`
	CreatedAt       time.Time       `json:"createdAt"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (progress *AssessmentProgress) BeforeCreate(tx *gorm.DB) error {
	if progress.ID == uuid.Nil {
		progress.ID = uuid.New()
	}
	return nil
}

// AssessmentProgressResponse represents the response structure for assessment progress
type AssessmentProgressResponse struct {
	ID              uuid.UUID       `json:"id"`
	EvaluationID    uuid.UUID       `json:"evaluationId"`
	CurrentSection  int             `json:"currentSection"`
	CurrentQuestion int             `json:"currentQuestion"`
	SessionToken    string          `json:"sessionToken"`
	LastUpdated     time.Time       `json:"lastUpdated"`
}

// ToResponse converts an AssessmentProgress to an AssessmentProgressResponse
func (progress *AssessmentProgress) ToResponse() AssessmentProgressResponse {
	return AssessmentProgressResponse{
		ID:              progress.ID,
		EvaluationID:    progress.EvaluationID,
		CurrentSection:  progress.CurrentSection,
		CurrentQuestion: progress.CurrentQuestion,
		SessionToken:    progress.SessionToken,
		LastUpdated:     progress.LastUpdated,
	}
}
