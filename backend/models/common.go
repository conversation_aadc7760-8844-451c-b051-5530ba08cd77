package models

// Difficulty represents the difficulty level (shared across models)
type Difficulty string

const (
	Easy   Difficulty = "easy"
	Medium Difficulty = "medium"
	Hard   Difficulty = "hard"
	Expert Difficulty = "expert"
)

// String returns the string representation of the difficulty
func (d Difficulty) String() string {
	return string(d)
}

// IsValid checks if the difficulty level is valid
func (d Difficulty) IsValid() bool {
	switch d {
	case Easy, Medium, Hard, Expert:
		return true
	default:
		return false
	}
}
