package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Language represents a language that can be learned in the system
type Language struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Code        string    `gorm:"type:varchar(10);unique;not null" json:"code"`
	Name        string    `gorm:"type:varchar(100);not null" json:"name"`
	NativeName  string    `gorm:"type:varchar(100);not null" json:"nativeName"`
	Description string    `gorm:"type:text" json:"description"`
	Flag        string    `gorm:"type:varchar(255)" json:"flag"`
	IsActive    bool      `gorm:"default:true" json:"isActive"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (l *Language) BeforeCreate(tx *gorm.DB) error {
	if l.ID == uuid.Nil {
		l.ID = uuid.New()
	}
	return nil
}

// LanguageLevel represents a proficiency level for a language
type LanguageLevel string

const (
	Beginner     LanguageLevel = "beginner"
	Elementary   LanguageLevel = "elementary"
	Intermediate LanguageLevel = "intermediate"
	Advanced     LanguageLevel = "advanced"
	Proficient   LanguageLevel = "proficient"
	Native       LanguageLevel = "native"
)

// UserLanguage represents a user's proficiency in a language
type UserLanguage struct {
	ID         uuid.UUID     `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID     uuid.UUID     `gorm:"type:uuid;not null" json:"userId"`
	LanguageID uuid.UUID     `gorm:"type:uuid;not null" json:"languageId"`
	Level      LanguageLevel `gorm:"type:varchar(20);not null" json:"level"`
	IsLearning bool          `gorm:"default:true" json:"isLearning"`
	IsNative   bool          `gorm:"default:false" json:"isNative"`
	CreatedAt  time.Time     `json:"createdAt"`
	UpdatedAt  time.Time     `json:"updatedAt"`

	// Relationships
	Language Language `gorm:"foreignKey:LanguageID" json:"language,omitempty"`
	User     User     `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (ul *UserLanguage) BeforeCreate(tx *gorm.DB) error {
	if ul.ID == uuid.Nil {
		ul.ID = uuid.New()
	}
	return nil
}
