package models

import (
	"time"

	exerciseEntity "languagelearning/domain/exercise/entity"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// ExerciseType represents the type of exercise (数据库层)
type ExerciseType string

const (
	// 基础练习类型 (数据库常量，使用英文)
	ExMultipleChoice ExerciseType = "multiple_choice"
	ExFillInBlank    ExerciseType = "fill_in_blank"
	ExMatching       ExerciseType = "matching"
	ExTrueFalse      ExerciseType = "true_false"
	ExOpenEnded      ExerciseType = "open_ended"

	// 技能练习类型
	ExSpeaking  ExerciseType = "speaking"
	ExListening ExerciseType = "listening"
	ExWriting   ExerciseType = "writing"
	ExReading   ExerciseType = "reading"

	// 学科练习类型
	ExVocabulary ExerciseType = "vocabulary"
	ExGrammar    ExerciseType = "grammar"
)

// Exercise 练习数据库模型 (仅用于数据库映射)
type Exercise struct {
	ID              uuid.UUID      `gorm:"type:uuid;primary_key" json:"id"`
	Type            ExerciseType   `gorm:"size:50;not null" json:"type"`
	Title           string         `gorm:"size:200" json:"title,omitempty"`
	Description     string         `gorm:"size:1000" json:"description,omitempty"`
	Content         string         `gorm:"type:text" json:"content,omitempty"`
	Question        string         `gorm:"size:500;not null" json:"question"`
	Instruction     string         `gorm:"size:500" json:"instruction,omitempty"`
	Options         pq.StringArray `gorm:"type:text[]" json:"options"`
	CorrectAnswer   string         `gorm:"size:500;not null" json:"correctAnswer"`
	Explanation     string         `gorm:"size:1000" json:"explanation,omitempty"`
	Points          int            `gorm:"default:10" json:"points"`
	AudioURL        string         `gorm:"size:255" json:"audioURL,omitempty"`
	ImageURL        string         `gorm:"size:255" json:"imageURL,omitempty"`
	Tags            pq.StringArray `gorm:"type:text[]" json:"tags"`
	Category        string         `gorm:"size:50" json:"category,omitempty"`
	Difficulty      Difficulty     `gorm:"size:20" json:"difficulty,omitempty"`
	ExampleSentence string         `gorm:"size:500" json:"exampleSentence,omitempty"`
	LanguageID      uuid.UUID      `gorm:"type:uuid" json:"languageId,omitempty"`
	AuthorID        uuid.UUID      `gorm:"type:uuid" json:"authorId"`
	IsPublished     bool           `gorm:"default:false" json:"isPublished"`
	CreatedAt       time.Time      `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt       time.Time      `gorm:"autoUpdateTime" json:"updatedAt"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (exercise *Exercise) BeforeCreate(tx *gorm.DB) error {
	if exercise.ID == uuid.Nil {
		exercise.ID = uuid.New()
	}
	return nil
}

// Specific exercise types for backward compatibility
// These are kept for migrations and seeds

// GrammarExercise represents a grammar exercise
type GrammarExercise struct {
	Exercise
	GrammarRule string `gorm:"size:200" json:"grammarRule,omitempty"`
}

// ListeningExercise represents a listening exercise
type ListeningExercise struct {
	Exercise
	AudioDuration int                 `gorm:"default:0" json:"audioDuration,omitempty"` // in seconds
	Questions     []ListeningQuestion `gorm:"foreignKey:ListeningExerciseID" json:"questions,omitempty"`
}

// SpeakingExercise represents a speaking exercise
type SpeakingExercise struct {
	Exercise
	ExpectedDuration int    `gorm:"default:30" json:"expectedDuration,omitempty"` // in seconds
	TargetPhrase     string `gorm:"size:500" json:"targetPhrase,omitempty"`
}

// ListeningQuestion represents a question in a listening exercise
type ListeningQuestion struct {
	ID                  uuid.UUID      `gorm:"type:uuid;primary_key" json:"id"`
	ListeningExerciseID uuid.UUID      `gorm:"type:uuid" json:"listeningExerciseId"`
	Question            string         `gorm:"size:500;not null" json:"question"`
	Options             pq.StringArray `gorm:"type:text[]" json:"options"`
	CorrectAnswer       int            `gorm:"not null" json:"correctAnswer"`
	Points              int            `gorm:"default:1" json:"points"`
	CreatedAt           time.Time      `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt           time.Time      `gorm:"autoUpdateTime" json:"updatedAt"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (q *ListeningQuestion) BeforeCreate(tx *gorm.DB) error {
	if q.ID == uuid.Nil {
		q.ID = uuid.New()
	}
	return nil
}

// ToExerciseEntity converts the database model to a domain entity
func (e *Exercise) ToExerciseEntity() *exerciseEntity.Exercise {
	return &exerciseEntity.Exercise{
		ID:            e.ID,
		Type:          MapToEntityType(e.Type), // Use type mapping
		Title:         e.Title,
		Description:   e.Description,
		Content:       e.Content,
		Question:      e.Question,
		Instructions:  e.Instruction,
		Options:       e.Options,
		CorrectAnswer: e.CorrectAnswer,
		Explanation:   e.Explanation,
		Points:        e.Points,
		MediaURL:      e.AudioURL, // Map AudioURL to MediaURL
		MediaType:     "audio",    // Default media type
		Tags:          e.Tags,
		AuthorID:      e.AuthorID,
		IsPublished:   e.IsPublished,
		CreatedAt:     e.CreatedAt,
		UpdatedAt:     e.UpdatedAt,
	}
}

// FromExerciseEntity converts a domain entity to the database model
func (e *Exercise) FromExerciseEntity(entity *exerciseEntity.Exercise) {
	e.ID = entity.ID
	e.Type = MapToDBType(entity.Type) // Use type mapping
	e.Title = entity.Title
	e.Description = entity.Description
	e.Content = entity.Content
	e.Question = entity.Question
	e.Instruction = entity.Instructions
	e.Options = entity.Options
	e.CorrectAnswer = entity.CorrectAnswer
	e.Explanation = entity.Explanation
	e.Points = entity.Points
	e.AudioURL = entity.MediaURL
	e.Tags = entity.Tags
	e.AuthorID = entity.AuthorID
	e.IsPublished = entity.IsPublished
	e.CreatedAt = entity.CreatedAt
	e.UpdatedAt = entity.UpdatedAt
}
