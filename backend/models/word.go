package models

import (
	"languagelearning/domain/user/entity"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Word represents a vocabulary word
type Word struct {
	ID              uuid.UUID  `gorm:"type:uuid;primary_key" json:"id"`
	Word            string     `gorm:"size:100;not null" json:"word"`
	Translation     string     `gorm:"size:100;not null" json:"translation"`
	Pronunciation   string     `gorm:"size:100" json:"pronunciation,omitempty"`
	Definition      string     `gorm:"size:500;not null" json:"definition"`
	ExampleSentence string     `gorm:"size:500" json:"exampleSentence,omitempty"`
	Category        string     `gorm:"size:50" json:"category,omitempty"`
	Difficulty      Difficulty `gorm:"size:20;not null" json:"difficulty"`
	ImageURL        string     `gorm:"size:255" json:"imageURL,omitempty"`
	AudioURL        string     `gorm:"size:255" json:"audioURL,omitempty"`
	LanguageID      uuid.UUID  `gorm:"type:uuid;not null" json:"languageId"`

	// Relationships
	Language  Language   `gorm:"foreignKey:LanguageID" json:"language,omitempty"`
	UserWords []UserWord `gorm:"foreignKey:WordID" json:"-"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (word *Word) BeforeCreate(tx *gorm.DB) error {
	if word.ID == uuid.Nil {
		word.ID = uuid.New()
	}
	return nil
}

// UserWord represents a user's relationship with a word
type UserWord struct {
	ID         uuid.UUID `gorm:"type:uuid;primary_key" json:"-"`
	UserID     uuid.UUID `gorm:"type:uuid;not null" json:"userId"`
	WordID     uuid.UUID `gorm:"type:uuid;not null" json:"wordId"`
	IsLearned  bool      `gorm:"default:false" json:"isLearned"`
	IsFavorite bool      `gorm:"default:false" json:"isFavorite"`
}

// FromEntity converts a domain entity to a model
func (user *UserWord) FromEntity(e *entity.UserWord) {
	user.ID = e.ID
	user.UserID = e.UserID
	user.WordID = e.WordID
	user.IsLearned = e.IsLearned
	user.IsFavorite = e.IsFavorite
}

// ToEntity converts a model to a domain entity
func (user *UserWord) ToEntity() *entity.UserWord {
	return &entity.UserWord{
		ID:         user.ID,
		UserID:     user.UserID,
		WordID:     user.WordID,
		IsLearned:  user.IsLearned,
		IsFavorite: user.IsFavorite,
	}
}

// UserWordResponse represents a word with user-specific data
type UserWordResponse struct {
	Word       Word `json:"word"`
	IsLearned  bool `json:"isLearned"`
	IsFavorite bool `json:"isFavorite"`
}
