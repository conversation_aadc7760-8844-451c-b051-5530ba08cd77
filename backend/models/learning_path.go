package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// LearningPathStatus represents the status of a learning path
type LearningPathStatus string

const (
	PathActive    LearningPathStatus = "active"
	PathCompleted LearningPathStatus = "completed"
	PathArchived  LearningPathStatus = "archived"
)

// LearningPath represents a personalized learning path for a user
type LearningPath struct {
	ID                uuid.UUID          `gorm:"type:uuid;primary_key" json:"id"`
	UserID            uuid.UUID          `gorm:"type:uuid;not null" json:"userId"`
	Title             string             `gorm:"size:100;not null" json:"title"`
	Description       string             `gorm:"size:500" json:"description"`
	Status            LearningPathStatus `gorm:"size:20;not null;default:'active'" json:"status"`
	EvaluationID      uuid.UUID          `gorm:"type:uuid" json:"evaluationId,omitempty"`
	Level             LessonLevel        `gorm:"size:50;not null" json:"level"`
	FocusAreas        pq.StringArray     `gorm:"type:text[]" json:"focusAreas"`     // vocabulary, grammar, etc.
	EstimatedDuration int                `gorm:"not null" json:"estimatedDuration"` // in days
	Progress          int                `gorm:"default:0" json:"progress"`         // percentage
	StartDate         time.Time          `json:"startDate"`
	CompletedDate     time.Time          `json:"completedDate,omitempty"`
	CreatedAt         time.Time          `json:"createdAt"`
	UpdatedAt         time.Time          `json:"updatedAt"`
	// Relationships
	Lessons []LearningPathLesson `gorm:"foreignKey:LearningPathID" json:"lessons"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (path *LearningPath) BeforeCreate(tx *gorm.DB) error {
	if path.ID == uuid.Nil {
		path.ID = uuid.New()
	}
	return nil
}

// LearningPathLesson represents a lesson in a learning path
type LearningPathLesson struct {
	ID             uint      `gorm:"primary_key" json:"-"`
	LearningPathID uuid.UUID `gorm:"type:uuid;not null" json:"learningPathId"`
	LessonID       uuid.UUID `gorm:"type:uuid;not null" json:"lessonId"`
	Order          int       `gorm:"not null" json:"order"` // order in the learning path
	IsRequired     bool      `gorm:"default:true" json:"isRequired"`
	IsCompleted    bool      `gorm:"default:false" json:"isCompleted"`
	CompletedDate  time.Time `json:"completedDate,omitempty"`
	// Relationships
	Lesson Lesson `gorm:"foreignKey:LessonID" json:"lesson"`
}

// LearningPathSummary represents a summary of a learning path
type LearningPathSummary struct {
	ID                uuid.UUID          `json:"id"`
	Title             string             `json:"title"`
	Description       string             `json:"description"`
	Status            LearningPathStatus `json:"status"`
	Level             LessonLevel        `json:"level"`
	FocusAreas        []string           `json:"focusAreas"`
	EstimatedDuration int                `json:"estimatedDuration"`
	Progress          int                `json:"progress"`
	StartDate         time.Time          `json:"startDate"`
	CompletedDate     time.Time          `json:"completedDate,omitempty"`
	TotalLessons      int                `json:"totalLessons"`
	CompletedLessons  int                `json:"completedLessons"`
	CreatedAt         time.Time          `json:"createdAt"`
}

// LearningPathRecommendation represents a recommended learning path
type LearningPathRecommendation struct {
	Title                string      `json:"title"`
	Description          string      `json:"description"`
	Level                LessonLevel `json:"level"`
	FocusAreas           []string    `json:"focusAreas"`
	EstimatedDuration    int         `json:"estimatedDuration"`
	SampleLessons        []Lesson    `json:"sampleLessons"`
	RecommendationReason string      `json:"recommendationReason"`
}

// LearningPathStats represents statistics for a learning path
type LearningPathStats struct {
	TotalLessons       int64 `json:"totalLessons"`
	CompletedLessons   int64 `json:"completedLessons"`
	ProgressPercentage int   `json:"progressPercentage"`
}

// CreateLearningPathRequest represents a request to create a learning path
type CreateLearningPathRequest struct {
	Title             string      `json:"title" binding:"required"`
	Description       string      `json:"description"`
	Level             LessonLevel `json:"level" binding:"required"`
	FocusAreas        []string    `json:"focusAreas" binding:"required"`
	EstimatedDuration int         `json:"estimatedDuration" binding:"required"`
}
