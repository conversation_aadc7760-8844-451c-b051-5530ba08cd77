package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// EvaluationType represents the type of evaluation
type EvaluationType string

const (
	EvalPlacement  EvaluationType = "placement"
	EvalProgress   EvaluationType = "progress"
	EvalSkill      EvaluationType = "skill"
	EvalCertificate EvaluationType = "certificate"
)

// Evaluation represents a formal assessment
type Evaluation struct {
	ID              uuid.UUID       `gorm:"type:uuid;primary_key" json:"id"`
	UserID          uuid.UUID       `gorm:"type:uuid;not null" json:"userId"`
	Type            EvaluationType  `gorm:"size:50;not null" json:"type"`
	Title           string          `gorm:"size:100;not null" json:"title"`
	Description     string          `gorm:"size:500" json:"description"`
	TotalQuestions  int             `gorm:"not null" json:"totalQuestions"`
	PassingScore    int             `gorm:"not null" json:"passingScore"` // percentage
	Duration        int             `gorm:"not null" json:"duration"` // in minutes
	IsCompleted     bool            `gorm:"default:false" json:"isCompleted"`
	StartedAt       time.Time       `json:"startedAt,omitempty"`
	CompletedAt     time.Time       `json:"completedAt,omitempty"`
	Score           int             `json:"score,omitempty"` // percentage
	Sections        []EvalSection   `gorm:"foreignKey:EvaluationID" json:"sections"`
	CreatedAt       time.Time       `json:"createdAt"`
	UpdatedAt       time.Time       `json:"updatedAt"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (eval *Evaluation) BeforeCreate(tx *gorm.DB) error {
	if eval.ID == uuid.Nil {
		eval.ID = uuid.New()
	}
	return nil
}

// EvalSection represents a section in an evaluation
type EvalSection struct {
	ID              uint            `gorm:"primary_key" json:"-"`
	EvaluationID    uuid.UUID       `gorm:"type:uuid;not null" json:"evaluationId"`
	Title           string          `gorm:"size:100;not null" json:"title"`
	Skill           string          `gorm:"size:50;not null" json:"skill"` // vocabulary, grammar, etc.
	Weight          int             `gorm:"not null" json:"weight"` // percentage weight in total score
	Questions       []EvalQuestion  `gorm:"foreignKey:SectionID" json:"questions"`
	Score           int             `json:"score,omitempty"` // percentage
}

// EvalQuestion represents a question in an evaluation section
type EvalQuestion struct {
	ID              uint            `gorm:"primary_key" json:"-"`
	SectionID       uint            `gorm:"not null" json:"sectionId"`
	Type            string          `gorm:"size:50;not null" json:"type"` // multiple-choice, fill-in, etc.
	Content         string          `gorm:"size:1000;not null" json:"content"`
	Options         pq.StringArray  `gorm:"type:text[]" json:"options,omitempty"`
	CorrectAnswer   string          `gorm:"size:500;not null" json:"correctAnswer"`
	UserAnswer      string          `json:"userAnswer,omitempty"`
	IsCorrect       bool            `json:"isCorrect,omitempty"`
	Points          int             `gorm:"not null" json:"points"`
}

// EvaluationResult represents the detailed result of an evaluation
type EvaluationResult struct {
	ID              uuid.UUID       `gorm:"type:uuid;primary_key" json:"id"`
	EvaluationID    uuid.UUID       `gorm:"type:uuid;not null" json:"evaluationId"`
	UserID          uuid.UUID       `gorm:"type:uuid;not null" json:"userId"`
	OverallScore    int             `gorm:"not null" json:"overallScore"` // percentage
	PassingScore    int             `gorm:"not null" json:"passingScore"` // percentage
	IsPassed        bool            `gorm:"not null" json:"isPassed"`
	CompletedAt     time.Time       `gorm:"not null" json:"completedAt"`
	SectionScores   []SectionScore  `gorm:"foreignKey:ResultID" json:"sectionScores"`
	Feedback        string          `gorm:"size:1000" json:"feedback"`
	Recommendations pq.StringArray  `gorm:"type:text[]" json:"recommendations"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (result *EvaluationResult) BeforeCreate(tx *gorm.DB) error {
	if result.ID == uuid.Nil {
		result.ID = uuid.New()
	}
	return nil
}

// SectionScore represents the score for a section in an evaluation result
type SectionScore struct {
	ID              uint            `gorm:"primary_key" json:"-"`
	ResultID        uuid.UUID       `gorm:"type:uuid;not null" json:"-"`
	SectionTitle    string          `gorm:"size:100;not null" json:"sectionTitle"`
	Skill           string          `gorm:"size:50;not null" json:"skill"`
	Score           int             `gorm:"not null" json:"score"` // percentage
	Strengths       pq.StringArray  `gorm:"type:text[]" json:"strengths,omitempty"`
	Weaknesses      pq.StringArray  `gorm:"type:text[]" json:"weaknesses,omitempty"`
}
