package models

import (
	"log"
	"time"

	"languagelearning/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// Define custom errors
var ErrEmailAlreadyExists = gorm.ErrDuplicatedKey
var ErrInvalidPeriod = gorm.ErrInvalidField

// ConnectDatabase initializes the database connection
func ConnectDatabase() *gorm.DB {
	cfg := config.LoadConfig()

	// Configure GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// Connect to database
	var err error
	DB, err = gorm.Open(postgres.Open(cfg.Database.URL), gormConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Configure connection pool
	sqlDB, err := DB.DB()
	if err != nil {
		log.Fatalf("Failed to get database connection: %v", err)
	}

	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConn)
	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConn)
	sqlDB.SetConnMaxLifetime(time.Hour)

	log.Println("Database connected successfully")
	return DB
}
