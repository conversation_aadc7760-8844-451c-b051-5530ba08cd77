package models

import (
	"time"

	"github.com/lib/pq"

	"github.com/google/uuid"
	"gorm.io/gorm"

	learningEntity "languagelearning/domain/learning/entity"
)

// LessonCategory represents the lesson category
type LessonCategory string

const (
	LessonVocabulary    LessonCategory = "vocabulary"
	LessonGrammar       LessonCategory = "grammar"
	LessonListening     LessonCategory = "listening"
	LessonSpeaking      LessonCategory = "speaking"
	LessonReading       LessonCategory = "reading"
	LessonWriting       LessonCategory = "writing"
	LessonUncategorized LessonCategory = "uncategorized"
)

// LessonLevel represents the lesson level
type LessonLevel string

const (
	LessonBeginner     LessonLevel = "beginner"
	LessonIntermediate LessonLevel = "intermediate"
	LessonAdvanced     LessonLevel = "advanced"
)

// Lesson represents a learning lesson
type Lesson struct {
	ID          uuid.UUID      `gorm:"type:uuid;primary_key" json:"id"`
	Title       string         `gorm:"size:100;not null" json:"title"`
	Description string         `gorm:"size:500;not null" json:"description"`
	Content     string         `gorm:"type:text" json:"content"`
	Language    string         `gorm:"size:50" json:"language"`
	Level       LessonLevel    `gorm:"size:50;not null" json:"level"`
	Category    LessonCategory `gorm:"size:50;not null" json:"category"`
	Difficulty  Difficulty     `gorm:"size:20;not null" json:"difficulty"`
	Duration    int            `gorm:"not null" json:"duration"` // in minutes
	Points      int            `gorm:"not null" json:"points"`
	Tags        pq.StringArray `gorm:"type:text[]" json:"tags"`
	Status      string         `gorm:"size:20;not null;default:'draft'" json:"status"`
	CreatedAt   time.Time      `json:"createdAt"`
	UpdatedAt   time.Time      `json:"updatedAt"`
	PublishedAt *time.Time     `json:"publishedAt,omitempty"`
	// Relationships
	Progress []LessonProgress `gorm:"foreignKey:LessonID" json:"-"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (lesson *Lesson) BeforeCreate(tx *gorm.DB) error {
	if lesson.ID == uuid.Nil {
		lesson.ID = uuid.New()
	}
	return nil
}

// ToLearningEntity converts the database model to a learning domain entity
func (lesson *Lesson) ToLearningEntity() *learningEntity.Lesson {
	// Create difficulty value object
	var difficultyLevel learningEntity.Difficulty
	switch lesson.Difficulty {
	case Easy:
		difficultyLevel = learningEntity.Easy
	case Medium:
		difficultyLevel = learningEntity.Medium
	case Hard:
		difficultyLevel = learningEntity.Hard
	default:
		difficultyLevel = learningEntity.Easy
	}

	difficulty, _ := learningEntity.NewExerciseDifficulty(difficultyLevel)

	// Create duration value object
	var duration *learningEntity.Duration
	if lesson.Duration > 0 {
		duration, _ = learningEntity.NewDuration(lesson.Duration)
	}

	// Convert lesson type
	var lessonType learningEntity.LessonType
	switch lesson.Category {
	case LessonVocabulary:
		lessonType = learningEntity.VocabularyLesson
	case LessonGrammar:
		lessonType = learningEntity.GrammarLesson
	case LessonListening:
		lessonType = learningEntity.ListeningLesson
	case LessonSpeaking:
		lessonType = learningEntity.SpeakingLesson
	case LessonReading:
		lessonType = learningEntity.ReadingLesson
	case LessonWriting:
		lessonType = learningEntity.WritingLesson
	default:
		lessonType = learningEntity.ComprehensiveLesson
	}

	// Convert status
	var status learningEntity.LessonStatus
	switch lesson.Status {
	case "draft":
		status = learningEntity.DraftStatus
	case "review":
		status = learningEntity.ReviewStatus
	case "published":
		status = learningEntity.PublishedStatus
	case "archived":
		status = learningEntity.ArchivedStatus
	default:
		status = learningEntity.DraftStatus
	}

	return &learningEntity.Lesson{
		ID:          lesson.ID,
		Title:       lesson.Title,
		Description: lesson.Description,
		Type:        lessonType,
		Level:       string(lesson.Level),
		Difficulty:  difficulty,
		Content:     lesson.Content,
		Duration:    duration,
		Tags:        lesson.Tags,
		Status:      status,
		IsPublished: lesson.Status == "published",
		PublishedAt: lesson.PublishedAt,
		CreatedAt:   lesson.CreatedAt,
		UpdatedAt:   lesson.UpdatedAt,
	}
}

// FromLearningEntity converts a learning domain entity to the database model
func (lesson *Lesson) FromLearningEntity(e *learningEntity.Lesson) {
	lesson.ID = e.ID
	lesson.Title = e.Title
	lesson.Description = e.Description
	lesson.Content = e.Content
	lesson.Level = LessonLevel(e.Level)
	lesson.Status = string(e.Status)
	lesson.Tags = e.Tags
	lesson.CreatedAt = e.CreatedAt
	lesson.UpdatedAt = e.UpdatedAt
	lesson.PublishedAt = e.PublishedAt

	// Convert lesson type to category
	switch e.Type {
	case learningEntity.VocabularyLesson:
		lesson.Category = LessonVocabulary
	case learningEntity.GrammarLesson:
		lesson.Category = LessonGrammar
	case learningEntity.ListeningLesson:
		lesson.Category = LessonListening
	case learningEntity.SpeakingLesson:
		lesson.Category = LessonSpeaking
	case learningEntity.ReadingLesson:
		lesson.Category = LessonReading
	case learningEntity.WritingLesson:
		lesson.Category = LessonWriting
	default:
		lesson.Category = LessonUncategorized
	}

	// Convert difficulty
	if e.Difficulty != nil {
		switch e.Difficulty.Level() {
		case learningEntity.Easy:
			lesson.Difficulty = Easy
		case learningEntity.Medium:
			lesson.Difficulty = Medium
		case learningEntity.Hard:
			lesson.Difficulty = Hard
		default:
			lesson.Difficulty = Easy
		}
	}

	// Convert duration
	if e.Duration != nil {
		lesson.Duration = e.Duration.Minutes()
	}
}

// LessonProgress represents a user's progress in a lesson
type LessonProgress struct {
	ID            uuid.UUID `gorm:"type:uuid;primary_key;" json:"id"`
	UserID        uuid.UUID `gorm:"type:uuid;not null" json:"userId"`
	LessonID      uuid.UUID `gorm:"type:uuid;not null" json:"lessonId"`
	Progress      int       `gorm:"not null;default:0" json:"progress"`
	Completed     bool      `gorm:"default:false" json:"completed"`
	Favorited     bool      `gorm:"default:false" json:"favorited"`
	CurrentScore  int       `gorm:"default:0" json:"currentScore"`
	CompletedDate time.Time `json:"completedDate,omitempty"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// ToLearningEntity converts the database model to a learning domain entity
func (progress *LessonProgress) ToLearningEntity() *learningEntity.LessonProgress {
	var completedAt *time.Time
	if !progress.CompletedDate.IsZero() {
		completedAt = &progress.CompletedDate
	}

	return &learningEntity.LessonProgress{
		ID:          progress.ID,
		UserID:      progress.UserID,
		LessonID:    progress.LessonID,
		Progress:    progress.Progress,
		Completed:   progress.Completed,
		Score:       progress.CurrentScore,
		CompletedAt: completedAt,
		CreatedAt:   progress.CreatedAt,
		UpdatedAt:   progress.UpdatedAt,
	}
}

// FromLearningEntity converts a learning domain entity to the database model
func (progress *LessonProgress) FromLearningEntity(e *learningEntity.LessonProgress) {
	progress.ID = e.ID
	progress.UserID = e.UserID
	progress.LessonID = e.LessonID
	progress.Progress = e.Progress
	progress.Completed = e.Completed
	progress.Favorited = false // Default value, not in learning entity
	progress.CurrentScore = e.Score
	if e.CompletedAt != nil {
		progress.CompletedDate = *e.CompletedAt
	} else {
		progress.CompletedDate = time.Time{}
	}
	progress.CreatedAt = e.CreatedAt
	progress.UpdatedAt = e.UpdatedAt
}

// UserLessonExercise represents a user's completion status for a specific exercise within a lesson
type UserLessonExercise struct {
	ID            uint      `gorm:"primary_key" json:"-"`
	UserID        uuid.UUID `gorm:"type:uuid;not null;index:idx_user_lesson_exercise,unique" json:"userId"`
	LessonID      uuid.UUID `gorm:"type:uuid;not null;index:idx_user_lesson_exercise,unique" json:"lessonId"`
	ExerciseID    uuid.UUID `gorm:"type:uuid;not null;index:idx_user_lesson_exercise,unique" json:"exerciseId"` // Assuming ExerciseID is a string UUID
	IsCompleted   bool      `gorm:"default:false" json:"isCompleted"`
	Score         int       `gorm:"default:0" json:"score"`
	CompletedDate time.Time `json:"completedDate,omitempty"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// UserLessonProgress represents a user's progress in a lesson with lesson details
type UserLessonProgress struct {
	Lesson       Lesson         `json:"lesson"`
	Progress     LessonProgress `json:"progress"`
	IsCompleted  bool           `json:"isCompleted"`
	IsFavorite   bool           `json:"isFavorite"`
	CurrentScore int            `json:"currentScore"`
}

// LessonExercise represents the many-to-many relationship between lessons and exercises
// 用于初始化课程和练习的基础关系
// 不包含用户信息
type LessonExercise struct {
	LessonID   uuid.UUID `gorm:"type:uuid;not null;index:idx_lesson_exercise,unique" json:"lessonId"`
	ExerciseID uuid.UUID `gorm:"type:uuid;not null;index:idx_lesson_exercise,unique" json:"exerciseId"`
}
