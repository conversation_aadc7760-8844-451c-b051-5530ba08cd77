package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// RelationType represents the type of relation between exercises
type RelationType string

const (
	RelPrerequisite RelationType = "prerequisite" // Exercise A must be completed before Exercise B
	RelReinforces   RelationType = "reinforces"   // Exercise A reinforces concepts in Exercise B
	RelBuildsUpon   RelationType = "builds_upon"  // Exercise A builds upon concepts in Exercise B
	RelAlternative  RelationType = "alternative"  // Exercise A is an alternative to Exercise B
	RelSimilar      RelationType = "similar"      // Exercise A is similar to Exercise B
)

// RelationStrength represents the strength of relation between exercises
type RelationStrength int

const (
	RelWeak   RelationStrength = 1 // Weak relation
	RelMedium RelationStrength = 2 // Medium relation
	RelStrong RelationStrength = 3 // Strong relation
)

// ExerciseRelation represents a relation between two exercises
type ExerciseRelation struct {
	ID              uint             `gorm:"primary_key" json:"-"`
	SourceID        uuid.UUID        `gorm:"type:uuid;not null" json:"sourceId"`
	TargetID        uuid.UUID        `gorm:"type:uuid;not null" json:"targetId"`
	SourceType      string           `gorm:"size:50;not null" json:"sourceType"` // grammar, listening, speaking, vocabulary
	TargetType      string           `gorm:"size:50;not null" json:"targetType"` // grammar, listening, speaking, vocabulary
	RelationType    RelationType     `gorm:"size:50;not null" json:"relationType"`
	Strength        RelationStrength `gorm:"not null" json:"strength"`
	Description     string           `gorm:"size:500" json:"description,omitempty"`
	CreatedAt       time.Time        `json:"createdAt"`
	UpdatedAt       time.Time        `json:"updatedAt"`
}

// ExerciseDifficultyMetadata represents additional metadata about exercise difficulty
type ExerciseDifficultyMetadata struct {
	ID              uint      `gorm:"primary_key" json:"-"`
	ExerciseID      uuid.UUID `gorm:"type:uuid;not null" json:"exerciseId"`
	ExerciseType    string    `gorm:"size:50;not null" json:"exerciseType"` // grammar, listening, speaking, vocabulary
	ComplexityScore float64   `gorm:"not null" json:"complexityScore"`      // 1-10 scale
	TimeToComplete  int       `gorm:"not null" json:"timeToComplete"`       // in seconds
	SuccessRate     float64   `gorm:"not null" json:"successRate"`          // percentage of users who complete successfully
	AttemptCount    int       `gorm:"not null" json:"attemptCount"`         // number of times this exercise has been attempted
	SuccessCount    int       `gorm:"not null" json:"successCount"`         // number of successful completions
	Tags            []string  `gorm:"type:text[]" json:"tags"`              // tags for categorizing exercises
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

// BeforeCreate will set created and updated time
func (relation *ExerciseRelation) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	relation.CreatedAt = now
	relation.UpdatedAt = now
	return nil
}

// BeforeUpdate will update the updated time
func (relation *ExerciseRelation) BeforeUpdate(tx *gorm.DB) error {
	relation.UpdatedAt = time.Now()
	return nil
}

// BeforeCreate will set created and updated time
func (metadata *ExerciseDifficultyMetadata) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	metadata.CreatedAt = now
	metadata.UpdatedAt = now
	return nil
}

// BeforeUpdate will update the updated time
func (metadata *ExerciseDifficultyMetadata) BeforeUpdate(tx *gorm.DB) error {
	metadata.UpdatedAt = time.Now()
	return nil
}

// ExerciseWithRelations represents an exercise with its relations
type ExerciseWithRelations struct {
	ID                uuid.UUID                 `json:"id"`
	Type              string                    `json:"type"`
	Title             string                    `json:"title"`
	Difficulty        Difficulty                `json:"difficulty"`
	Category          string                    `json:"category"`
	DifficultyMetadata *ExerciseDifficultyMetadata `json:"difficultyMetadata,omitempty"`
	Prerequisites     []RelatedExercise         `json:"prerequisites,omitempty"`
	Reinforces        []RelatedExercise         `json:"reinforces,omitempty"`
	BuildsUpon        []RelatedExercise         `json:"buildsUpon,omitempty"`
	Alternatives      []RelatedExercise         `json:"alternatives,omitempty"`
	Similar           []RelatedExercise         `json:"similar,omitempty"`
}

// RelatedExercise represents a related exercise with relation metadata
type RelatedExercise struct {
	ID              uuid.UUID        `json:"id"`
	Type            string           `json:"type"`
	Title           string           `json:"title"`
	Difficulty      Difficulty       `json:"difficulty"`
	Category        string           `json:"category"`
	RelationType    RelationType     `json:"relationType"`
	Strength        RelationStrength `json:"strength"`
	Description     string           `json:"description,omitempty"`
}

// ExerciseRecommendation represents a recommended exercise
type ExerciseRecommendation struct {
	Exercise         ExerciseWithRelations `json:"exercise"`
	RecommendationScore float64           `json:"recommendationScore"`
	Reason           string               `json:"reason"`
}
