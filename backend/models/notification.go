package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	// NotificationTypeSystem is for system notifications
	NotificationTypeSystem NotificationType = "system"
	// NotificationTypeAchievement is for achievement notifications
	NotificationTypeAchievement NotificationType = "achievement"
	// NotificationTypeLesson is for lesson notifications
	NotificationTypeLesson NotificationType = "lesson"
	// NotificationTypeStreak is for streak notifications
	NotificationTypeStreak NotificationType = "streak"
	// NotificationTypeReminder is for reminder notifications
	NotificationTypeReminder NotificationType = "reminder"
	// NotificationTypeLearning is for learning notifications
	NotificationTypeLearning NotificationType = "learning"
)

// NotificationStatus represents the status of a notification
type NotificationStatus string

const (
	// NotificationStatusUnread is for unread notifications
	NotificationStatusUnread NotificationStatus = "unread"
	// NotificationStatusRead is for read notifications
	NotificationStatusRead NotificationStatus = "read"
	// NotificationStatusArchived is for archived notifications
	NotificationStatusArchived NotificationStatus = "archived"
)

// Notification represents a notification sent to a user
type Notification struct {
	ID         uuid.UUID          `gorm:"type:uuid;primary_key" json:"id"`
	UserID     uuid.UUID          `gorm:"type:uuid;not null" json:"userId"`
	Type       NotificationType   `gorm:"size:50;not null" json:"type"`
	Title      string             `gorm:"size:100;not null" json:"title"`
	Message    string             `gorm:"size:500;not null" json:"message"`
	Status     NotificationStatus `gorm:"size:50;not null;default:'unread'" json:"status"`
	Data       string             `gorm:"type:jsonb" json:"data"`
	CreatedAt  time.Time          `json:"createdAt"`
	ReadAt     *time.Time         `json:"readAt"`
	ArchivedAt *time.Time         `json:"archivedAt"`

	// Relationships
	User User `gorm:"foreignKey:UserID" json:"-"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (notification *Notification) BeforeCreate(tx *gorm.DB) error {
	if notification.ID == uuid.Nil {
		notification.ID = uuid.New()
	}
	return nil
}

// MarkAsRead marks the notification as read
func (notification *Notification) MarkAsRead() {
	now := time.Now()
	notification.Status = NotificationStatusRead
	notification.ReadAt = &now
}

// MarkAsArchived marks the notification as archived
func (notification *Notification) MarkAsArchived() {
	now := time.Now()
	notification.Status = NotificationStatusArchived
	notification.ArchivedAt = &now
}

// NotificationPreference represents a user's notification preferences
type NotificationPreference struct {
	ID                        uint      `gorm:"primary_key" json:"-"`
	UserID                    uuid.UUID `gorm:"type:uuid;not null" json:"userId"`
	EmailNotificationsEnabled bool      `gorm:"default:true" json:"emailNotificationsEnabled"`
	PushNotificationsEnabled  bool      `gorm:"default:true" json:"pushNotificationsEnabled"`
	AchievementNotifications  bool      `gorm:"default:true" json:"achievementNotifications"`
	LessonNotifications       bool      `gorm:"default:true" json:"lessonNotifications"`
	StreakNotifications       bool      `gorm:"default:true" json:"streakNotifications"`
	ReminderNotifications     bool      `gorm:"default:true" json:"reminderNotifications"`
	DailyReminderTime         string    `gorm:"size:5;default:'09:00'" json:"dailyReminderTime"` // Format: "HH:MM"

	// Relationships
	User User `gorm:"foreignKey:UserID" json:"-"`
}

// EmailTemplate represents an email template
type EmailTemplate struct {
	ID          uint      `gorm:"primary_key" json:"id"`
	Name        string    `gorm:"size:100;not null;unique" json:"name"`
	Subject     string    `gorm:"size:200;not null" json:"subject"`
	HTMLContent string    `gorm:"type:text;not null" json:"htmlContent"`
	TextContent string    `gorm:"type:text;not null" json:"textContent"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// EmailLog represents a log of sent emails
type EmailLog struct {
	ID           uuid.UUID `gorm:"type:uuid;primary_key" json:"id"`
	UserID       uuid.UUID `gorm:"type:uuid;not null" json:"userId"`
	TemplateID   uint      `json:"templateId"`
	Subject      string    `gorm:"size:200;not null" json:"subject"`
	Recipient    string    `gorm:"size:200;not null" json:"recipient"`
	SentAt       time.Time `json:"sentAt"`
	Status       string    `gorm:"size:50;not null;default:'pending'" json:"status"`
	ErrorMessage string    `gorm:"type:text" json:"errorMessage"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (log *EmailLog) BeforeCreate(tx *gorm.DB) error {
	if log.ID == uuid.Nil {
		log.ID = uuid.New()
	}
	log.CreatedAt = time.Now()
	log.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate will update the UpdatedAt timestamp
func (log *EmailLog) BeforeUpdate(tx *gorm.DB) error {
	log.UpdatedAt = time.Now()
	return nil
}
