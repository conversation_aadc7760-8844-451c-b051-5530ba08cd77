package models

import (
	"time"

	"github.com/google/uuid"
)

// PracticeSession represents a user's practice session
type PracticeSession struct {
	ID        uint      `gorm:"primary_key" json:"-"`
	UserID    uuid.UUID `gorm:"type:uuid;not null" json:"userId"`
	Type      string    `gorm:"size:50;not null" json:"type"`
	Duration  int       `gorm:"not null" json:"duration"` // in seconds
	Score     int       `gorm:"not null" json:"score"`
	CreatedAt time.Time `json:"createdAt"`
}

// PracticeStats represents a user's practice statistics
type PracticeStats struct {
	Streak              int           `json:"streak"`
	VocabularyCount     int           `json:"vocabulary_count"`
	ListeningCount      int           `json:"listening_count"`
	SpeakingCount       int           `json:"speaking_count"`
	GrammarCount        int           `json:"grammar_count"`
	TotalPoints         int           `json:"total_points"`
	ChallengesCompleted int           `json:"challenges_completed"`
	HelpedUsers         int           `json:"helped_users"`
	LastActive          time.Time     `json:"last_active"`
	AverageScore        float64       `json:"average_score"`
	TotalPracticeTime   time.Duration `json:"total_practice_time"`
	LearningDays        int           `json:"learning_days"`
	CompletedLessons    int           `json:"completed_lessons"`
}

// ProgressReport represents a user's progress report for a specific period
type ProgressReport struct {
	Period            string    `json:"period"`
	StartDate         time.Time `json:"startDate"`
	EndDate           time.Time `json:"endDate"`
	TotalPracticeTime int       `json:"totalPracticeTime"` // in seconds
	CompletedLessons  int       `json:"completedLessons"`
	NewWords          int       `json:"newWords"`
	PointsEarned      int       `json:"pointsEarned"`
	AverageScore      float64   `json:"averageScore"`
	DailyActivity     []int     `json:"dailyActivity"` // activity level for each day
}

// RecommendedPractice represents a recommended practice for a user
type RecommendedPractice struct {
	Type        string     `json:"type"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Duration    int        `json:"duration"` // in minutes
	Difficulty  Difficulty `json:"difficulty"`
	Points      int        `json:"points"`
}
