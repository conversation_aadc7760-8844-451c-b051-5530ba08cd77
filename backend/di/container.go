package di

import (
	"context"
	"fmt"
	"languagelearning/config"
	"languagelearning/controllers"
	v1 "languagelearning/controllers/v1"
	achievementevent "languagelearning/domain/achievement/event"
	coredi "languagelearning/domain/core/di"
	"languagelearning/domain/core/event"
	exerciseevent "languagelearning/domain/exercise/event"
	"languagelearning/domain/learning/event/handlers"
	lessonevent "languagelearning/domain/learning/lesson/event"
	learningRepoImpl "languagelearning/domain/learning/repository/impl"
	learningSvcImpl "languagelearning/domain/learning/service/impl"
	notificationevent "languagelearning/domain/notification/event"
	notification "languagelearning/domain/notification/service"
	userevent "languagelearning/domain/user/event"
	userRepoImpl "languagelearning/domain/user/repository/impl"
	userSvcImpl "languagelearning/domain/user/service/impl"
	"languagelearning/models"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"sync"

	"go.uber.org/dig"

	achievementSvcImpl "languagelearning/domain/achievement/service/impl"
	authSvcImpl "languagelearning/domain/auth/service/impl"
	evaluationSvcImpl "languagelearning/domain/evaluation/service/impl"
	notificationRepoImpl "languagelearning/domain/notification/repository/impl"
	notificationSvcImpl "languagelearning/domain/notification/service/impl"
)

// EventBusType 事件总线类型
type EventBusType string

const (
	// InMemoryBusType 内存事件总线（无持久化）
	InMemoryBusType EventBusType = "memory"
	// RedisBusType Redis 事件总线
	RedisBusType EventBusType = "redis"
	// RabbitMQBusType RabbitMQ 事件总线
	RabbitMQBusType EventBusType = "rabbitmq"
	// KafkaBusType Kafka 事件总线
	KafkaBusType EventBusType = "kafka"
)

// Container is a dependency injection container
type Container struct {
	container        *dig.Container
	modularContainer *ModularContainer
	mu               sync.Mutex
	useModular       bool
}

// NewContainer creates a new container using the legacy approach
func NewContainer() *Container {
	return &Container{
		container:  dig.New(),
		useModular: false,
	}
}

// NewModularContainerWrapper 創建模塊化容器的包裝器，保持向後兼容
func NewModularContainerWrapper() *Container {
	modularContainer := NewModularContainer()
	return &Container{
		container:        modularContainer.container,
		modularContainer: modularContainer,
		useModular:       true,
	}
}

// registerServices 注册所有服务到容器
func (c *Container) RegisterServices() error {

	// Register services that need EventBus
	if err := c.container.Provide(learningSvcImpl.NewAdaptiveLearningService); err != nil {
		return err
	}

	if err := c.container.Provide(learningSvcImpl.NewLearningPathService); err != nil {
		return err
	}

	if err := c.container.Provide(learningSvcImpl.NewPracticeService); err != nil {
		return err
	}

	if err := c.container.Provide(learningSvcImpl.NewPersonalizedLearningService); err != nil {
		return err
	}

	if err := c.container.Provide(learningSvcImpl.NewExerciseService); err != nil {
		return err
	}

	if err := c.container.Provide(learningSvcImpl.NewExerciseRelationService); err != nil {
		return err
	}

	if err := c.container.Provide(learningSvcImpl.NewLessonService); err != nil {
		return err
	}
	// Register AchievementService
	if err := c.container.Provide(achievementSvcImpl.NewAchievementService); err != nil {
		return err
	}
	// Register WordService
	if err := c.container.Provide(learningSvcImpl.NewWordService); err != nil {
		return err
	}

	if err := c.container.Provide(authSvcImpl.NewAuthService); err != nil {
		return err
	}
	// Register UserService (needs EventPublisher)
	if err := c.container.Provide(userSvcImpl.NewUserService); err != nil {
		return err
	}

	// Register domain NotificationService (needs EventPublisher)
	if err := c.container.Provide(notificationSvcImpl.NewNotificationService); err != nil {
		return err
	}

	// Register EvaluationService (needs EventBus)
	if err := c.container.Provide(evaluationSvcImpl.NewEvaluationService); err != nil {
		return err
	}

	return nil
}

func (c *Container) RegisterRepositories() error {
	if err := c.RegisterLearningRepositories(); err != nil {
		return err
	}
	if err := c.RegisterUserRepositories(); err != nil {
		return err
	}

	if err := c.container.Provide(notificationRepoImpl.NewNotificationRepository); err != nil {
		return err
	}

	if err := c.container.Provide(notificationRepoImpl.NewNotificationPreferenceRepository); err != nil {
		return err
	}

	return nil
}

// registerLearningRepositories 注册学习领域的存储库
func (c *Container) RegisterLearningRepositories() error {
	// 注册课程存储库
	if err := c.container.Provide(learningRepoImpl.NewGormLessonRepository); err != nil {
		return err
	}

	// 注册课程进度存储库
	if err := c.container.Provide(learningRepoImpl.NewGormLessonProgressRepository); err != nil {
		return err
	}

	// 注册练习存储库
	if err := c.container.Provide(learningRepoImpl.NewGormExerciseRepository); err != nil {
		return err
	}

	// 注册练习尝试存储库
	if err := c.container.Provide(learningRepoImpl.NewGormExerciseAttemptRepository); err != nil {
		return err
	}

	// 注册练习关系存储库
	if err := c.container.Provide(learningRepoImpl.NewGormExerciseRelationRepository); err != nil {
		return err
	}

	// 注册难度元数据存储库
	if err := c.container.Provide(learningRepoImpl.NewGormDifficultyMetadataRepository); err != nil {
		return err
	}

	// 注册练习会话存储库
	if err := c.container.Provide(learningRepoImpl.NewPracticeRepository); err != nil {
		return err
	}

	// 注册单词存储库
	if err := c.container.Provide(learningRepoImpl.NewWordRepository); err != nil {
		return err
	}

	// 注册评估存储库
	if err := c.container.Provide(learningRepoImpl.NewEvaluationRepository); err != nil {
		return err
	}

	// 注册评估结果存储库
	if err := c.container.Provide(learningRepoImpl.NewEvaluationResultRepository); err != nil {
		return err
	}

	// 注册学习路径存储库
	if err := c.container.Provide(learningRepoImpl.NewModelsLessonRepository); err != nil {
		return err
	}

	// 注册学习路径课程存储库
	if err := c.container.Provide(learningRepoImpl.NewModelsLearningPathRepository); err != nil {
		return err
	}

	// 注册学习路径课程存储库
	if err := c.container.Provide(learningRepoImpl.NewModelsLearningPathLessonRepository); err != nil {
		return err
	}

	return nil
}

// registerUserRepositories 注册用户领域的存储库
func (c *Container) RegisterUserRepositories() error {
	// 注册事务管理器
	if err := c.container.Provide(userRepoImpl.NewTransactionManager); err != nil {
		return err
	}

	// 注册用户存储库
	if err := c.container.Provide(userRepoImpl.NewUserRepository); err != nil {
		return err
	}

	// 注册用户配置存储库
	if err := c.container.Provide(userRepoImpl.NewUserProfileRepository); err != nil {
		return err
	}

	// 注册用户偏好存储库
	if err := c.container.Provide(userRepoImpl.NewUserStatsRepository); err != nil {
		return err
	}

	// 注册用户词彙存储库
	if err := c.container.Provide(userRepoImpl.NewUserWordRepository); err != nil {
		return err
	}

	return nil
}

// registerControllers 注册控制器
func (c *Container) RegisterControllers() error {
	// 注册课程控制器
	if err := c.container.Provide(controllers.NewLessonController); err != nil {
		return err
	}
	// 注册课程控制器
	if err := c.container.Provide(controllers.NewExerciseRelationController); err != nil {
		return err
	}

	// 注册用户控制器
	if err := c.container.Provide(controllers.NewUserController); err != nil {
		return err
	}

	// 注册认证控制器
	if err := c.container.Provide(controllers.NewAuthController); err != nil {
		return err
	}
	// 注册练习控制器
	if err := c.container.Provide(controllers.NewExerciseController); err != nil {
		return err
	}

	// 注册通知控制器
	if err := c.container.Provide(controllers.NewNotificationController); err != nil {
		return err
	}

	// 注册个性化学习控制器
	if err := c.container.Provide(controllers.NewPersonalizedLearningController); err != nil {
		return err
	}

	// 注册学习路径控制器
	if err := c.container.Provide(controllers.NewLearningPathController); err != nil {
		return err
	}

	// 注册成就控制器
	if err := c.container.Provide(controllers.NewAchievementController); err != nil {
		return err
	}

	// 注册评估控制器
	if err := c.container.Provide(controllers.NewEvaluationController); err != nil {
		return err
	}

	// 注册单词控制器
	if err := c.container.Provide(controllers.NewWordController); err != nil {
		return err
	}

	// 注册练习控制器
	if err := c.container.Provide(controllers.NewPracticeController); err != nil {
		return err
	}

	// 注册API路由
	if err := c.container.Provide(v1.NewAPIRouter); err != nil {
		return err
	}

	return nil
}

func (c *Container) RegisterEventBus() error {
	// 提供事件总线配置
	if err := c.container.Provide(c.createEventBusConfig); err != nil {
		return err
	}

	// 提供事件总线工厂
	if err := c.container.Provide(c.createEventBusFactory); err != nil {
		return err
	}

	// 提供 EventBus 接口
	if err := c.container.Provide(c.createEventBus); err != nil {
		return err
	}

	// 提供 EventPublisher 接口
	if err := c.container.Provide(func(eventBus event.EventBus) event.EventPublisher {
		return eventBus
	}); err != nil {
		return err
	}

	return nil
}

// RegisterEventHandlers 注册所有事件处理器
func (c *Container) RegisterEventHandlers() error {
	return c.container.Invoke(c.registerAllEventHandlers)
}

// registerAllEventHandlers 注册所有事件处理器的内部方法
func (c *Container) registerAllEventHandlers(eventBus event.EventBus, notificationService notification.NotificationService) error {
	// 注册学习相关事件处理器
	if err := c.registerLearningEventHandlers(eventBus, notificationService); err != nil {
		return fmt.Errorf("failed to register learning event handlers: %w", err)
	}

	// 注册课程相关事件处理器
	if err := c.registerLessonEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register lesson event handlers: %w", err)
	}

	// 注册练习相关事件处理器
	if err := c.registerExerciseEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register exercise event handlers: %w", err)
	}

	// 注册通知相关事件处理器
	if err := c.registerNotificationEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register notification event handlers: %w", err)
	}

	// 注册用户相关事件处理器
	if err := c.registerUserEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register user event handlers: %w", err)
	}

	// 注册成就相关事件处理器
	if err := c.registerAchievementEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register achievement event handlers: %w", err)
	}

	log.Printf("Successfully registered all event handlers")
	return nil
}

// registerLearningEventHandlers 注册学习相关事件处理器
func (c *Container) registerLearningEventHandlers(eventBus event.EventBus, notificationService notification.NotificationService) error {
	return handlers.RegisterAllHandlers(eventBus, notificationService)
}

// registerLessonEventHandlers 注册课程相关事件处理器
func (c *Container) registerLessonEventHandlers(eventBus event.EventBus) error {
	return lessonevent.RegisterLessonEventHandlers(eventBus)
}

// registerExerciseEventHandlers 注册练习相关事件处理器
func (c *Container) registerExerciseEventHandlers(eventBus event.EventBus) error {
	return exerciseevent.RegisterExerciseEventHandlers(eventBus)
}

// registerNotificationEventHandlers 注册通知相关事件处理器
func (c *Container) registerNotificationEventHandlers(eventBus event.EventBus) error {
	return notificationevent.RegisterNotificationEventHandlers(eventBus)
}

// registerUserEventHandlers 注册用户相关事件处理器
func (c *Container) registerUserEventHandlers(eventBus event.EventBus) error {
	return userevent.RegisterUserEventHandlers(eventBus)
}

// registerAchievementEventHandlers 注册成就相关事件处理器
func (c *Container) registerAchievementEventHandlers(eventBus event.EventBus) error {
	return achievementevent.RegisterAchievementEventHandlers(eventBus)
}

// createEventBusConfig 创建事件总线配置
func (c *Container) createEventBusConfig() *event.EventBusConfig {
	// 从环境变量读取配置
	busType := os.Getenv("EVENT_BUS_TYPE")
	if busType == "" {
		busType = "memory" // 默认使用内存事件总线
	}

	config := &event.EventBusConfig{
		Type: event.EventBusType(busType),
	}

	// 设置重试配置
	if retryAttempts := os.Getenv("EVENT_BUS_RETRY_ATTEMPTS"); retryAttempts != "" {
		if attempts, err := strconv.Atoi(retryAttempts); err == nil {
			config.RetryAttempts = attempts
		}
	}

	if retryDelay := os.Getenv("EVENT_BUS_RETRY_DELAY"); retryDelay != "" {
		if delay, err := time.ParseDuration(retryDelay); err == nil {
			config.RetryDelay = delay
		}
	}

	// 根据类型设置具体配置
	switch EventBusType(busType) {
	case RedisBusType:
		config.Redis = &event.RedisConfig{
			Addr:          getEnvOrDefault("REDIS_ADDR", "localhost:6379"),
			Password:      os.Getenv("REDIS_PASSWORD"),
			DB:            getEnvAsIntOrDefault("REDIS_DB", 0),
			StreamKey:     getEnvOrDefault("REDIS_STREAM_KEY", "events"),
			ConsumerGroup: getEnvOrDefault("REDIS_CONSUMER_GROUP", "event-handlers"),
			ConsumerName:  getEnvOrDefault("REDIS_CONSUMER_NAME", "handler-1"),
			PoolSize:      getEnvAsIntOrDefault("REDIS_POOL_SIZE", 10),
		}

	case RabbitMQBusType:
		config.RabbitMQ = &event.RabbitMQConfig{
			URL:               getEnvOrDefault("RABBITMQ_URL", "amqp://guest:guest@localhost:5672/"),
			Exchange:          getEnvOrDefault("RABBITMQ_EXCHANGE", "events"),
			ConnectionName:    getEnvOrDefault("RABBITMQ_CONNECTION_NAME", "event-bus"),
			QueueDurable:      getEnvAsBoolOrDefault("RABBITMQ_QUEUE_DURABLE", true),
			MessagePersistent: getEnvAsBoolOrDefault("RABBITMQ_MESSAGE_PERSISTENT", true),
			PrefetchCount:     getEnvAsIntOrDefault("RABBITMQ_PREFETCH_COUNT", 10),
		}

	case KafkaBusType:
		brokers := getEnvOrDefault("KAFKA_BROKERS", "localhost:9092")
		config.Kafka = &event.KafkaConfig{
			Brokers: strings.Split(brokers, ","),
			Topic:   getEnvOrDefault("KAFKA_TOPIC", "events"),
			ProducerConfig: &event.KafkaProducerConfig{
				BatchSize:    getEnvAsIntOrDefault("KAFKA_PRODUCER_BATCH_SIZE", 100),
				Compression:  getEnvOrDefault("KAFKA_PRODUCER_COMPRESSION", "gzip"),
				MaxRetries:   getEnvAsIntOrDefault("KAFKA_PRODUCER_MAX_RETRIES", 3),
				RequiredAcks: getEnvAsIntOrDefault("KAFKA_PRODUCER_REQUIRED_ACKS", 1),
			},
			ConsumerConfig: &event.KafkaConsumerConfig{
				GroupID:     getEnvOrDefault("KAFKA_CONSUMER_GROUP_ID", "event-handlers"),
				StartOffset: getEnvAsInt64OrDefault("KAFKA_CONSUMER_START_OFFSET", -1), // LastOffset
			},
		}
	}

	return config
}

// createEventBusFactory 创建事件总线工厂
func (c *Container) createEventBusFactory(config *event.EventBusConfig) *event.EventBusFactory {
	return event.NewEventBusFactory(config)
}

// createEventBus 创建事件总线
func (c *Container) createEventBus(factory *event.EventBusFactory) event.EventBus {
	ctx := context.Background()
	eventBus, err := factory.CreateEventBus(ctx)
	if err != nil {
		// 如果创建失败，回退到内存事件总线
		log.Printf("Failed to create configured event bus, falling back to in-memory: %v", err)
		return event.NewDefaultEventBus()
	}
	return eventBus
}

// 辅助函数
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsIntOrDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsInt64OrDefault(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBoolOrDefault(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// DefaultEventBusConfig 返回默认的事件总线配置
func DefaultEventBusConfig() *event.EventBusConfig {
	return &event.EventBusConfig{
		Type:          event.EventBusType(InMemoryBusType),
		RetryAttempts: 3,
		RetryDelay:    time.Second,
		BatchSize:     100,
		FlushInterval: time.Second * 5,
	}
}

// RedisEventBusConfig 返回 Redis 事件总线配置
func RedisEventBusConfig(addr, password string, db int) *event.EventBusConfig {
	return &event.EventBusConfig{
		Type: event.EventBusType(RedisBusType),
		Redis: &event.RedisConfig{
			Addr:          addr,
			Password:      password,
			DB:            db,
			StreamKey:     "events",
			ConsumerGroup: "event-handlers",
			ConsumerName:  "handler-1",
			PoolSize:      10,
			DialTimeout:   time.Second * 5,
			ReadTimeout:   time.Second * 3,
			WriteTimeout:  time.Second * 3,
		},
		RetryAttempts: 3,
		RetryDelay:    time.Second,
		BatchSize:     100,
		FlushInterval: time.Second * 5,
	}
}

// RabbitMQEventBusConfig 返回 RabbitMQ 事件总线配置
func RabbitMQEventBusConfig(url string) *event.EventBusConfig {
	return &event.EventBusConfig{
		Type: event.EventBusType(RabbitMQBusType),
		RabbitMQ: &event.RabbitMQConfig{
			URL:               url,
			Exchange:          "events",
			ConnectionName:    "event-bus",
			Heartbeat:         time.Second * 10,
			QueueDurable:      true,
			QueueAutoDelete:   false,
			QueueExclusive:    false,
			MessagePersistent: true,
			PrefetchCount:     10,
		},
		RetryAttempts: 3,
		RetryDelay:    time.Second,
		BatchSize:     100,
		FlushInterval: time.Second * 5,
	}
}

// KafkaEventBusConfig 返回 Kafka 事件总线配置
func KafkaEventBusConfig(brokers []string) *event.EventBusConfig {
	return &event.EventBusConfig{
		Type: event.EventBusType(KafkaBusType),
		Kafka: &event.KafkaConfig{
			Brokers: brokers,
			Topic:   "events",
			ProducerConfig: &event.KafkaProducerConfig{
				BatchSize:    100,
				BatchTimeout: time.Millisecond * 10,
				Compression:  "gzip",
				MaxRetries:   3,
				RequiredAcks: 1,
			},
			ConsumerConfig: &event.KafkaConsumerConfig{
				GroupID:          "event-handlers",
				StartOffset:      -1, // LastOffset
				CommitInterval:   time.Second,
				SessionTimeout:   time.Second * 10,
				HeartbeatTimeout: time.Second * 3,
				MaxBytes:         10e6,
			},
		},
		RetryAttempts: 3,
		RetryDelay:    time.Second,
		BatchSize:     100,
		FlushInterval: time.Second * 5,
	}
}

// Invoke 调用一个函数，注入其所需的依赖
func (c *Container) Invoke(function interface{}) error {
	return c.container.Invoke(function)
}

// Build 构建应用程序
func (c *Container) Build() error {
	if c.useModular {
		// 使用模塊化架構
		return c.modularContainer.Build()
	}

	// 使用傳統架構
	// 注册配置
	if err := c.container.Provide(config.LoadConfig); err != nil {
		return err
	}

	// 注册数据库连接
	if err := c.container.Provide(models.ConnectDatabase); err != nil {
		return err
	}

	// 注册事件总线
	if err := c.RegisterEventBus(); err != nil {
		return err
	}

	// 注册领域仓库
	if err := c.RegisterRepositories(); err != nil {
		return err
	}

	// 注册领域服务
	if err := c.RegisterServices(); err != nil {
		return err
	}

	// 注册事件处理器
	if err := c.RegisterEventHandlers(); err != nil {
		return err
	}

	// 注册控制器
	if err := c.RegisterControllers(); err != nil {
		return err
	}

	return nil
}

// EnableFeature 啟用功能特性（僅在模塊化模式下有效）
func (c *Container) EnableFeature(feature coredi.ModuleFeature) {
	if c.useModular && c.modularContainer != nil {
		c.modularContainer.EnableFeature(feature)
	}
}

// DisableFeature 禁用功能特性（僅在模塊化模式下有效）
func (c *Container) DisableFeature(feature coredi.ModuleFeature) {
	if c.useModular && c.modularContainer != nil {
		c.modularContainer.DisableFeature(feature)
	}
}

// GetModuleLoadOrder 獲取模塊加載順序（僅在模塊化模式下有效）
func (c *Container) GetModuleLoadOrder() []string {
	if c.useModular && c.modularContainer != nil {
		return c.modularContainer.GetLoadOrder()
	}
	return nil
}

// GetEnabledModules 獲取啟用的模塊列表（僅在模塊化模式下有效）
func (c *Container) GetEnabledModules() []coredi.Module {
	if c.useModular && c.modularContainer != nil {
		return c.modularContainer.GetEnabledModules()
	}
	return nil
}

// IsModular 檢查是否使用模塊化架構
func (c *Container) IsModular() bool {
	return c.useModular
}
