package di

import (
	"fmt"
	"log"
	"sync"

	controllerdi "languagelearning/controllers/di"
	achievementdi "languagelearning/domain/achievement/di"
	achievementevent "languagelearning/domain/achievement/event"
	authdi "languagelearning/domain/auth/di"
	coredi "languagelearning/domain/core/di"
	"languagelearning/domain/core/event"
	evaluationdi "languagelearning/domain/evaluation/di"
	exerciseevent "languagelearning/domain/exercise/event"
	learningdi "languagelearning/domain/learning/di"
	"languagelearning/domain/learning/event/handlers"
	lessonevent "languagelearning/domain/learning/lesson/event"
	notificationdi "languagelearning/domain/notification/di"
	notificationevent "languagelearning/domain/notification/event"
	notification "languagelearning/domain/notification/service"
	userdi "languagelearning/domain/user/di"
	userevent "languagelearning/domain/user/event"

	"go.uber.org/dig"
)

// ModularContainer 模塊化依賴注入容器
type ModularContainer struct {
	container *dig.Container
	registry  *coredi.ModuleRegistry
	mu        sync.Mutex
}

// NewModularContainer 創建新的模塊化容器
func NewModularContainer() *ModularContainer {
	return &ModularContainer{
		container: dig.New(),
		registry:  coredi.NewModuleRegistry(),
	}
}

// EnableFeature 啟用功能特性
func (c *ModularContainer) EnableFeature(feature coredi.ModuleFeature) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.registry.EnableFeature(feature)
}

// DisableFeature 禁用功能特性
func (c *ModularContainer) DisableFeature(feature coredi.ModuleFeature) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.registry.DisableFeature(feature)
}

// RegisterModules 註冊所有模塊
func (c *ModularContainer) RegisterModules() error {

	// 註冊核心模塊
	if err := c.registry.RegisterModule(coredi.NewCoreModule()); err != nil {
		return fmt.Errorf("failed to register core module: %w", err)
	}

	// 註冊用戶模塊
	if err := c.registry.RegisterModule(userdi.NewUserModule()); err != nil {
		return fmt.Errorf("failed to register user module: %w", err)
	}

	// 註冊學習模塊
	if err := c.registry.RegisterModule(learningdi.NewLearningModule()); err != nil {
		return fmt.Errorf("failed to register learning module: %w", err)
	}

	// 註冊成就模塊
	if err := c.registry.RegisterModule(achievementdi.NewAchievementModule()); err != nil {
		return fmt.Errorf("failed to register achievement module: %w", err)
	}

	// 註冊認證模塊
	if err := c.registry.RegisterModule(authdi.NewAuthModule()); err != nil {
		return fmt.Errorf("failed to register auth module: %w", err)
	}

	// 註冊通知模塊
	if err := c.registry.RegisterModule(notificationdi.NewNotificationModule()); err != nil {
		return fmt.Errorf("failed to register notification module: %w", err)
	}

	// 註冊評估模塊
	if err := c.registry.RegisterModule(evaluationdi.NewEvaluationModule()); err != nil {
		return fmt.Errorf("failed to register evaluation module: %w", err)
	}

	// 註冊控制器模塊
	if err := c.registry.RegisterModule(controllerdi.NewControllerModule()); err != nil {
		return fmt.Errorf("failed to register controller module: %w", err)
	}

	return nil
}

// Build 構建容器
func (c *ModularContainer) Build() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 註冊所有模塊
	if err := c.RegisterModules(); err != nil {
		return fmt.Errorf("failed to register modules: %w", err)
	}

	// 將模塊註冊到容器
	if err := c.registry.RegisterToContainer(c.container); err != nil {
		return fmt.Errorf("failed to register modules to container: %w", err)
	}

	// 註冊事件處理器
	if err := c.RegisterEventHandlers(); err != nil {
		return fmt.Errorf("failed to register event handlers: %w", err)
	}

	return nil
}

// RegisterEventHandlers 註冊事件處理器
func (c *ModularContainer) RegisterEventHandlers() error {
	// 使用原來的事件處理器註冊邏輯
	return c.container.Invoke(c.registerAllEventHandlers)
}

// Invoke 調用函數並注入依賴
func (c *ModularContainer) Invoke(function interface{}) error {
	return c.container.Invoke(function)
}

// GetRegistry 獲取模塊註冊表（用於調試和監控）
func (c *ModularContainer) GetRegistry() *coredi.ModuleRegistry {
	return c.registry
}

// GetLoadOrder 獲取模塊加載順序（用於調試）
func (c *ModularContainer) GetLoadOrder() []string {
	return c.registry.GetLoadOrder()
}

// GetEnabledModules 獲取啟用的模塊列表（用於調試）
func (c *ModularContainer) GetEnabledModules() []coredi.Module {
	return c.registry.GetEnabledModules()
}

// registerAllEventHandlers 註冊所有事件處理器的內部方法
// 這裡複製原來容器的事件處理器註冊邏輯
func (c *ModularContainer) registerAllEventHandlers(eventBus event.EventBus, notificationService notification.NotificationService) error {
	// 註冊學習相關事件處理器
	if err := c.registerLearningEventHandlers(eventBus, notificationService); err != nil {
		return fmt.Errorf("failed to register learning event handlers: %w", err)
	}

	// 註冊課程相關事件處理器
	if err := c.registerLessonEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register lesson event handlers: %w", err)
	}

	// 註冊練習相關事件處理器
	if err := c.registerExerciseEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register exercise event handlers: %w", err)
	}

	// 註冊通知相關事件處理器
	if err := c.registerNotificationEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register notification event handlers: %w", err)
	}

	// 註冊用戶相關事件處理器
	if err := c.registerUserEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register user event handlers: %w", err)
	}

	// 註冊成就相關事件處理器
	if err := c.registerAchievementEventHandlers(eventBus); err != nil {
		return fmt.Errorf("failed to register achievement event handlers: %w", err)
	}

	log.Printf("Successfully registered all event handlers")
	return nil
}

// registerLearningEventHandlers 註冊學習相關事件處理器
func (c *ModularContainer) registerLearningEventHandlers(eventBus event.EventBus, notificationService notification.NotificationService) error {
	return handlers.RegisterAllHandlers(eventBus, notificationService)
}

// registerLessonEventHandlers 註冊課程相關事件處理器
func (c *ModularContainer) registerLessonEventHandlers(eventBus event.EventBus) error {
	return lessonevent.RegisterLessonEventHandlers(eventBus)
}

// registerExerciseEventHandlers 註冊練習相關事件處理器
func (c *ModularContainer) registerExerciseEventHandlers(eventBus event.EventBus) error {
	return exerciseevent.RegisterExerciseEventHandlers(eventBus)
}

// registerNotificationEventHandlers 註冊通知相關事件處理器
func (c *ModularContainer) registerNotificationEventHandlers(eventBus event.EventBus) error {
	return notificationevent.RegisterNotificationEventHandlers(eventBus)
}

// registerUserEventHandlers 註冊用戶相關事件處理器
func (c *ModularContainer) registerUserEventHandlers(eventBus event.EventBus) error {
	return userevent.RegisterUserEventHandlers(eventBus)
}

// registerAchievementEventHandlers 註冊成就相關事件處理器
func (c *ModularContainer) registerAchievementEventHandlers(eventBus event.EventBus) error {
	return achievementevent.RegisterAchievementEventHandlers(eventBus)
}
