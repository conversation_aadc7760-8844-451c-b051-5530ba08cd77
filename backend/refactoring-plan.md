# 语言学习应用后端重构计划

## 一、代码结构现状

项目是一个基于Go和Gin框架的语言学习应用后端，主要组件包括：

1. **模型层(models)** - 包含数据结构和数据库操作
2. **控制器层(controllers)** - 处理HTTP请求和响应
3. **服务层(services)** - 包含业务逻辑
4. **中间件(middleware)** - 处理认证等横切关注点
5. **工具类(utils)** - 提供通用功能

## 二、问题分析

1. **代码重复** - 控制器中存在大量类似的请求处理和验证逻辑
2. **依赖注入混乱** - registry.go集中管理所有依赖，但结构复杂且难以维护
3. **服务层过于复杂** - 一些服务(如adaptive_learning_service.go)职责过多
4. **错误处理不一致** - 不同控制器有不同的错误处理方式
5. **配置管理分散** - 配置管理不集中，环境变量分布在各处
6. **测试不足** - 缺乏完整的单元测试和集成测试
7. **缺乏API版本管理** - 没有明确的API版本升级策略

## 三、重构目标

1. **提高代码复用性** - 减少重复代码，抽取共用组件
2. **简化依赖管理** - 使用更现代的依赖注入方式
3. **分离关注点** - 重新划分服务边界以减少复杂性
4. **统一错误处理** - 实现一致的错误处理机制
5. **改进配置管理** - 集中化配置，使用结构化配置对象
6. **增强可测试性** - 设计更易于测试的架构
7. **引入API版本控制** - 为未来的API变更做准备

## 四、具体重构计划

### 1. 架构重构

1. **引入领域驱动设计(DDD)概念**: (进行中)
   - 已按领域（evaluation, learning, user）创建 domain/ 目录结构 (进行中)
   - 每个领域内部包含自己的repo、service和domain模型 (进行中)

2. **应用依赖注入框架**: (进行中)
   - 已引入 uber-go/dig 框架，部分组件已注册
   - 简化controllers/registry.go的复杂依赖关系 (进行中)

3. **重构控制器层**: (进行中)
   - 已创建 controllers/v1/base_controller.go，开始实现基础控制器 (进行中)
   - 标准化请求验证和响应生成 (未开始)

### 2. 业务逻辑优化

1. **拆分复杂服务**:
   - 将services/learning/adaptive_learning_service.go拆分为多个职责单一的服务
   - 增加服务接口定义，便于未来替换实现

2. **重构学习路径服务**:
   - learning_path_service.go职责过多，应拆分为路径管理、进度跟踪等多个服务

3. **优化评估机制**:
   - 将evaluation相关逻辑重构为独立的评估引擎

### 3. 数据层优化

1. **标准化数据访问模式**: (进行中)
   - 已在 domain/ 目录下引入 repository 模式 (进行中)
   - 将models目录重组为domain models和database models (未开始)

2. **优化数据库查询**:
   - 审查现有查询性能，特别是N+1查询问题
   - 实现数据预加载策略

### 4. 基础设施改进

1. **统一错误处理**:
   - 实现结构化错误系统
   - 确保错误信息包含足够上下文

2. **配置管理**:
   - 创建集中化配置管理，支持多环境
   - 使用结构体映射代替直接访问环境变量

3. **日志系统升级**:
   - 实现结构化日志
   - 添加请求跟踪和性能监控

### 5. 质量保障

1. **增加测试覆盖率**:
   - 为核心服务编写单元测试
   - 实现集成测试框架
   - 添加API级别的测试

2. **代码静态分析**:
   - 集成golangci-lint
   - 制定并执行编码规范

### 6. API改进

1. **API版本管理**: (进行中)
   - 已创建 controllers/v1/ 目录，开始实现 API 版本化机制 (进行中)
   - 考虑使用URL前缀或Accept header

2. **API文档升级**:
   - 更新Swagger/OpenAPI文档
   - 确保所有API端点文档化

## 五、实施路线图

### 第一阶段：基础设施改进（4周）
1. 设置依赖注入框架 (进行中)
2. 实现统一错误处理
3. 改进配置管理
4. 升级日志系统
5. 增加基本测试框架

### 第二阶段：领域模型重构（6周）
1. 将服务层按领域重组 (进行中 - 80%完成)
2. 实现repository模式 (进行中 - 70%完成)
3. 重构数据模型 (进行中 - 60%完成)
4. 拆分复杂服务 (进行中 - 75%完成)

### 第三阶段：控制器优化（4周）
1. 标准化请求处理
2. 实现基础控制器
3. 重构API路由 (进行中)
4. 实现API版本管理 (进行中)

### 第四阶段：质量提升（3周）
1. 增加测试覆盖率
2. 性能测试和优化
3. 完善API文档
4. 代码审查和清理

## 六、风险与缓解策略

1. **重构过程中功能破坏**:
   - 实施渐进式重构，保持向后兼容性
   - 增加测试覆盖率，确保核心功能不受影响

2. **性能退化**:
   - 执行性能基准测试
   - 监控关键API的响应时间

3. **团队适应新架构的学习曲线**:
   - 提供重构文档和知识分享
   - 在小范围内先试点新模式
