# App与Backend不一致问题修复实施计划

## 概述

本文档提供了修复app和backend之间不一致问题的详细实施计划，包括具体的代码修改、测试策略和部署步骤。

## 第一阶段：高优先级修复 (第1周)

### Day 1-2: API端点路径统一

#### 任务1.1: 修复App端API路径
**文件**: `app/LanguageLearningApp/API/APIEndpoint.swift`

```swift
// 修改前
case lessons
case lessonDetail(id: UUID)
case words(category: String?, difficulty: String?)

// 修改后 - 添加API版本前缀
case lessons
case lessonDetail(id: UUID)  
case words(category: String?, difficulty: String?)

// 在url计算属性中统一添加/api/v1前缀
public var url: URL {
    switch self {
    case .lessons:
        return Self.baseURL.appendingPathComponent("/api/v1/lessons")
    case .lessonDetail(let id):
        return Self.baseURL.appendingPathComponent("/api/v1/lessons/\(id.uuidString)")
    case .words:
        return Self.baseURL.appendingPathComponent("/api/v1/words")
    // ... 其他端点
    }
}
```

#### 任务1.2: 添加Backend缺失端点
**文件**: `backend/controllers/v1/router.go`

```go
// 在registerAuthenticatedRoutes中添加
router.GET("/lessons/progress", r.LessonController.GetLessonProgress)
router.GET("/lessons/favorites", r.LessonController.GetFavoriteLessons)
router.GET("/vocabulary/categories", r.WordController.GetCategories)
```

**文件**: `backend/controllers/lesson_controller.go`

```go
// 添加新方法
func (c *LessonController) GetLessonProgress(ctx *gin.Context) {
    // 实现课程进度获取逻辑
}

func (c *LessonController) GetFavoriteLessons(ctx *gin.Context) {
    // 实现收藏课程获取逻辑
}
```

#### 任务1.3: 验证端点修复
- [ ] 创建端点测试用例
- [ ] 验证所有路径正确匹配
- [ ] 确保现有功能不受影响

### Day 3-4: 认证机制同步

#### 任务2.1: App端JWT Token处理
**文件**: `app/LanguageLearningApp/API/NetworkService.swift`

```swift
// 修改createRequest方法
private func createRequest(for endpoint: APIEndpoint) throws -> URLRequest {
    var request = URLRequest(url: endpoint.url)
    request.httpMethod = endpoint.method
    
    // 设置基础headers
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    request.setValue("application/json", forHTTPHeaderField: "Accept")
    
    // 添加JWT token (如果存在)
    if let token = AuthManager.shared.currentToken {
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
    }
    
    // 添加endpoint特定的headers
    for (key, value) in endpoint.headers {
        request.setValue(value, forHTTPHeaderField: key)
    }
    
    return request
}
```

#### 任务2.2: 创建AuthManager
**文件**: `app/LanguageLearningApp/Managers/AuthManager.swift`

```swift
class AuthManager: ObservableObject {
    static let shared = AuthManager()
    
    @Published var currentToken: String?
    @Published var isAuthenticated: Bool = false
    
    private init() {
        loadTokenFromKeychain()
    }
    
    func saveToken(_ token: String) {
        currentToken = token
        isAuthenticated = true
        // 保存到Keychain
    }
    
    func clearToken() {
        currentToken = nil
        isAuthenticated = false
        // 从Keychain删除
    }
    
    private func loadTokenFromKeychain() {
        // 从Keychain加载token
    }
}
```

#### 任务2.3: 处理认证失败
**文件**: `app/LanguageLearningApp/API/NetworkService.swift`

```swift
// 在performRequest方法中添加401处理
private func performRequest(_ endpoint: APIEndpoint) async throws -> Data {
    // ... 现有代码
    
    if httpResponse.statusCode == 401 {
        // 清除过期token
        AuthManager.shared.clearToken()
        throw AppError.authenticationRequired
    }
    
    // ... 其他状态码处理
}
```

### Day 5: API响应格式标准化

#### 任务3.1: 统一App端响应解析
**文件**: `app/LanguageLearningApp/API/APIResponseWrapper.swift`

```swift
struct APIResponseWrapper<T: Decodable>: Decodable {
    let success: Bool
    let message: String?
    let data: T?
    let error: ErrorInfo?
    let traceId: String?
    
    enum CodingKeys: String, CodingKey {
        case success, message, data, error
        case traceId = "traceId"
    }
}

struct ErrorInfo: Decodable {
    let type: String
    let code: String
    let message: String
    let details: [String: String]?
}
```

#### 任务3.2: 更新NetworkService解析逻辑
```swift
public func request<T: Decodable>(_ endpoint: APIEndpoint) async throws -> T {
    let data = try await performRequest(endpoint)
    
    let decoder = JSONDecoder()
    decoder.dateDecodingStrategy = .iso8601
    
    let wrapper = try decoder.decode(APIResponseWrapper<T>.self, from: data)
    
    if wrapper.success, let data = wrapper.data {
        return data
    } else if let error = wrapper.error {
        throw AppError.serverError(error.message)
    } else {
        throw AppError.invalidResponse
    }
}
```

## 第二阶段：中优先级修复 (第2-3周)

### Week 2: 数据模型字段对齐

#### 任务4.1: 统一User模型
**App端修改**: `app/LanguageLearningApp/Features/User/Models/User.swift`

```swift
public struct User: Codable, Identifiable {
    public let id: UUID
    public let username: String
    public let email: String
    public let avatarUrl: String?        // 统一字段名
    public let isActive: Bool           // 添加缺失字段
    public let createdAt: Date?
    public let lastLoginAt: Date?
    public let settings: UserSettings?
    public let stats: UserStats?        // 分离统计信息
    public let token: String?           // 仅在登录响应中使用
    
    enum CodingKeys: String, CodingKey {
        case id, username, email, isActive, createdAt, lastLoginAt, settings, stats, token
        case avatarUrl = "avatarUrl"
    }
}
```

#### 任务4.2: 统一Exercise模型
**App端修改**: `app/LanguageLearningApp/Models/Exercise.swift`

```swift
struct Exercise: Identifiable, Codable {
    let id: UUID
    let type: ExerciseType
    let title: String?
    let description: String?
    let question: String
    let instruction: String?
    let options: [String]
    let correctAnswer: String
    let explanation: String?
    let points: Int
    let audioURL: String?
    let imageURL: String?
    let tags: [String]
    let difficulty: Difficulty
    let isPublished: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum ExerciseType: String, Codable {
        case multipleChoice = "multiple_choice"
        case fillInBlank = "fill_in_blank"
        case matching = "matching"
        case trueFalse = "true_false"
        case openEnded = "open_ended"
        case speaking = "speaking"
        case listening = "listening"
        case writing = "writing"
        case reading = "reading"
        case vocabulary = "vocabulary"
        case grammar = "grammar"
    }
}
```

#### 任务4.3: 统一Achievement模型
**App端修改**: `app/LanguageLearningApp/Models/Achievement.swift`

```swift
public struct Achievement: Identifiable, Codable, Sendable {
    public let id: UUID
    public let type: AchievementType
    public let title: String           // 直接使用title而不是titleKey
    public let description: String     // 直接使用description而不是descriptionKey
    public let icon: String
    public let color: String
    public let requirement: Int
    public let reward: Int
    
    public enum AchievementType: String, Codable {
        case streak = "连续学习"
        case vocabulary = "词汇量"
        case listening = "听力练习"
        case speaking = "口语练习"
        case lessons = "课程完成"
        case points = "积分"
        case challenges = "挑战"
        case social = "社交"
    }
}
```

### Week 3: 错误处理标准化

#### 任务5.1: 扩展App端错误类型
**文件**: `app/LanguageLearningApp/Core/Errors/AppError.swift`

```swift
enum AppError: Error, LocalizedError {
    case validationError(String, [String: String]?)
    case authenticationRequired
    case authorizationFailed
    case notFound(String)
    case serverError(String)
    case networkError
    case decodingError
    case invalidResponse
    case noInternetConnection
    
    var errorDescription: String? {
        switch self {
        case .validationError(let message, _):
            return message
        case .authenticationRequired:
            return "Authentication required"
        case .authorizationFailed:
            return "Authorization failed"
        case .notFound(let resource):
            return "\(resource) not found"
        case .serverError(let message):
            return message
        case .networkError:
            return "Network error occurred"
        case .decodingError:
            return "Failed to decode response"
        case .invalidResponse:
            return "Invalid response format"
        case .noInternetConnection:
            return "No internet connection"
        }
    }
}
```

## 第三阶段：低优先级优化 (第4-6周)

### Week 4-5: API版本控制

#### 任务6.1: 实现版本管理
- 在App端添加API版本配置
- 在Backend端实现版本路由
- 添加向后兼容性支持

### Week 6: 数据验证一致性

#### 任务7.1: 统一验证规则
- 同步字段长度限制
- 统一必填字段验证
- 标准化验证错误消息

## 测试计划

### 单元测试
- [ ] API端点路径测试
- [ ] 数据模型序列化测试
- [ ] 认证token处理测试
- [ ] 错误处理测试

### 集成测试
- [ ] 端到端API调用测试
- [ ] 用户认证流程测试
- [ ] 数据同步测试

### 手动测试
- [ ] 用户登录/注册
- [ ] 课程数据加载
- [ ] 练习功能
- [ ] 成就系统
- [ ] 错误场景

## 部署策略

### 阶段性部署
1. **第1周末**: 部署高优先级修复
2. **第3周末**: 部署中优先级修复
3. **第6周末**: 部署低优先级优化

### 回滚计划
- 保留修复前的代码分支
- 准备快速回滚脚本
- 监控关键指标

## 风险缓解

### 技术风险
- **数据不兼容**: 渐进式修改，保持向后兼容
- **功能中断**: 充分测试，分阶段部署
- **性能影响**: 性能基准测试

### 业务风险
- **用户体验**: 在测试环境充分验证
- **数据丢失**: 备份关键数据
- **服务中断**: 选择低峰时段部署

## 成功指标

### 技术指标
- API调用成功率 > 95%
- 认证成功率 > 99%
- 数据同步成功率 > 98%
- 错误率 < 2%

### 业务指标
- 用户登录成功率提升
- 功能使用率稳定
- 用户投诉减少
- 应用崩溃率降低
